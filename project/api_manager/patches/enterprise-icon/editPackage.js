/**
 * 修改node_modules/@kid/enterprise-icon/package.json
 * @type {module:fs}
 */
const fs = require('fs');
const path = require('path');
const dirArr = __dirname.split('/');
const index = dirArr.findIndex(it => it.includes('patches'));
index > -1 && dirArr.splice(index, dirArr.length - index);
const dirPath = dirArr.join('/');
fs.readFile(path.join(dirPath, './node_modules/@kid/enterprise-icon/package.json'), 'utf8', function (err, data) {
    if (err) throw err;
    let list = JSON.parse(data);
    if (!list.hasOwnProperty('sideEffects')) {
        list['sideEffects'] = false;
        let newContent = JSON.stringify(list);
        fs.writeFile(path.join(dirPath, './node_modules/@kid/enterprise-icon/package.json'), newContent, 'utf8', (err) => {
            if (err) throw err;
        });
    }
});
