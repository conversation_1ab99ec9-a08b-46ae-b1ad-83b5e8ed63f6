#!/usr/bin/env bash

yarn add @babel/cli --dev
yarn add @babel/core --dev     # babel.config 支持 ES6 写法
yarn add @babel/register --dev # babel.config 支持 ES6 写法
yarn add @babel/plugin-proposal-class-properties --dev
yarn add @babel/plugin-proposal-decorators --dev
yarn add @babel/plugin-proposal-object-rest-spread --dev
yarn add @babel/plugin-syntax-dynamic-import --dev
yarn add @babel/runtime
yarn add @babel/plugin-transform-runtime --dev
yarn add @babel/preset-env --dev

yarn add @babel/plugin-proposal-optional-chaining --dev
yarn add @babel/plugin-proposal-nullish-coalescing-operator --dev


yarn add @babel/preset-typescript --dev
yarn add @babel/preset-react --dev

yarn add typescript --dev
yarn add fork-ts-checker-webpack-plugin --dev

yarn add @types/nprogress --dev
yarn add @types/react --dev
yarn add @types/react-dom --dev
yarn add @types/react-router --dev
yarn add @types/react-router-dom --dev
yarn add @types/query-string
yarn add @types/classnames --dev

yarn add core-js@2 # antd 等第三方库使用2，不能使用其他2以上的版本
yarn add @types/core-js@2 --dev

yarn add babel-loader --dev
yarn add cross-env --dev

yarn add nprogress
yarn add query-string

yarn add react
yarn add react-dom
yarn add react-router-dom
yarn add classnames
yarn add mobx
yarn add mobx-react

yarn add antd
# yarn add antd-mobile

yarn add babel-plugin-import --dev

yarn add moment
yarn add lodash-decorators

yarn add webpack --dev
yarn add webpack-merge --dev
yarn add webpack-cli --dev
yarn add webpack-dev-server --dev
yarn add webpack-bundle-analyzer --dev

yarn add style-loader --dev
yarn add css-loader --dev
yarn add url-loader --dev
yarn add less --dev
yarn add less-loader --dev
yarn add mini-css-extract-plugin --dev

yarn add happypack --dev

yarn add tslint --dev
yarn add tslint-react --dev
yarn add tslint-loader --dev

yarn add html-webpack-plugin --dev
yarn add case-sensitive-paths-webpack-plugin --dev
yarn add copy-webpack-plugin --dev
yarn add clean-webpack-plugin --dev

yarn add terser-webpack-plugin --dev

yarn add optimize-css-assets-webpack-plugin --dev

yarn add echarts
yarn add @types/echarts --dev

yarn add json5 --dev

# Git commit 日志标准
yarn add pre-commit --dev
yarn add commitizen@2 --dev
yarn add validate-commit-msg@2 --dev
yarn add conventional-changelog-cli@2 --dev
yarn add husky --dev

yarn add lodash-webpack-plugin babel-plugin-lodash --dev

# video
yarn add video.js
yarn add @types/video.js --dev
