#!/usr/bin/env bash
STEP_BEGIN_DATE=`date +%F`" [Project 日期] 开始构建任务"
STEP_BEGIN=`date +%H:%M:%S`" [Project BEGIN] 开始"
DIR_dist="./dist"
DIR_build="./output"
DIR_subdir=$DIR_build

echo "----- CDN文件存储路径：【$CDN_DIR】----"
if [ ! -d $DIR_subdir ]; then
    echo "---------- add " $DIR_subdir " ----------"
    mkdir $DIR_subdir
fi

echo "---------- 清理 【"$DIR_subdir"】 ----------"
rm -rf $DIR_subdir/*

if [ -d $DIR_dist ]; then
    echo "---------- 清理 【"$DIR_dist"】 ----------"
    rm -rf $DIR_dist/*
fi

echo "---------- webpack 编译中... ----------"

CDN_DIR=$CDN_DIR pnpm build &&
{
    echo "---------- webpack 编译完毕"

    echo "---------- 【"$DIR_dist"/assets】的 文件列表 ----------"
    du -ha dist
    # (cd $DIR_dist/assets && )

    echo "---------- 【"$DIR_dist"/assets】的 文件数量 ----------"
    (cd $DIR_dist && find ./ -type f -name '*.*' | wc -l)

    # cp Dockerfile $DIR_subdir
    # cp -rf nginx $DIR_subdir
    mv $DIR_dist $DIR_subdir

    echo $STEP_BEGIN_DATE
    echo $STEP_BEGIN

    STEP_END=`date +%H:%M:%S`" [Project END] 结束时间"
    echo $STEP_END
}
