## 一、分支分类
  - Git 主分支（保护分支）：`master`、`test`
  - Git 辅助分支（临时分支）：`feat_*`、`fix_*`

## 二、分支简介
  - `master` 主分支、`test` 测试分支
    - `master` 对应线上（正式环境）的代码
    - `test` 分支对应线下（测试环境）的代码

  - `feat_*` 辅助分支
    - 从 `master` 拉取，用于新需求（版本）开发
    - 一般情况： `*` 号为 `yyyymmdd_feature 名称`，如 `feat_20200413_testCase`
    - 如 feat 从 team 上提出，则为 `yyyymmdd_id`，如 `feat_20200413_T337556` <br>参考：https://team.corp.kuaishou.com/web/task/T348966

  - `fix_*` 辅助分支
    - 从 `master` 拉取，用于快速修复线上Bug
    - 一般情况： `*` 号为 `yyyymmdd_bug名称`，如 `fix_20200413_caseAddTag`
    - 如 bug 从 team 上提出，则为 `yyyymmdd_id`，如 `fix_20200413_B99000` <br>参考：https://team.corp.kuaishou.com/web/task/B99000

 
