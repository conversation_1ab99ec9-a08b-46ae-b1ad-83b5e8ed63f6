// include dependencies
const express = require('express');
const path = require('path');
const JSON5 = require('json5');
const fs = require('fs');

/**
 * All http-proxy options can be used, along with some extra http-proxy-middleware options.
 * https://github.com/http-party/node-http-proxy#options
 * https://github.com/chimurai/http-proxy-middleware#readme
 */
function getCookies(nameOnBase) {
    const filename = path.resolve(process.cwd(), nameOnBase);
    if (!fs.existsSync(filename)) {
        return '';
    }
    const cookieJSON = JSON5.parse(fs.readFileSync(filename));
    const cookieStr = Object.keys(cookieJSON).reduce((pre, cur) => {
        pre += `${cur}=${cookieJSON[cur]};`;
        return pre;
    }, '');
    return cookieStr;
}

function getJSON(nameOnBase) {
    const filename = path.resolve(process.cwd(), nameOnBase);
    if (!fs.existsSync(filename)) {
        return null;
    }
    const json = JSON5.parse(fs.readFileSync(filename));
    return json;
}

const COOKIE = getCookies('cookie.json');
// const TARGET = getJSON('target.json');
const proxy = require('http-proxy-middleware');

const CONFIG = {
    Port: 8051,
};

const app = express();

app.use('/api/kdev', proxy(Object.assign({
    proxyTimeout: 100 * 1000, // 文件上传可能较慢
    changeOrigin: true,
    onProxyReq: (proxyReq, req, res) => {
        proxyReq.setHeader('Cookie', COOKIE);
    },
}, { target: process.env.KDEV_HTTP_PROXY })));

app.use('/api/mock', proxy(Object.assign({
    proxyTimeout: 100 * 1000, // 文件上传可能较慢
    changeOrigin: true,
    onProxyReq: (proxyReq, req, res) => {
        proxyReq.setHeader('Cookie', COOKIE);
    },
}, { target: process.env.KDEV_HTTP_PROXY })));

app.use('/api/ares', proxy(Object.assign({
    proxyTimeout: 100 * 1000, // 文件上传可能较慢
    changeOrigin: true,
    onProxyReq: (proxyReq, req, res) => {
        proxyReq.setHeader('Cookie', COOKIE);
    },
}, { target: process.env.KDEV_HTTP_PROXY })));

const server = app.listen(CONFIG.Port);
