import path from 'path';
import webpackMerge from 'webpack-merge';

import optimization from './webpack-compontent/webpack.optimization';
import resolve from './webpack/webpack.resolve';
import plugins from './webpack-compontent/webpack.plugins';
import module from './webpack/webpack.module';

import HappyPack from 'happypack';
import packageJ<PERSON><PERSON> from '../../package.json';
import { time } from 'console';

function getUsedCpus(count) {
    if (count === 1) { // 1
        return 1;
    }
    if (count <= 4) { // 1(2) - 3(4)
        return count - 1;
    }
    if (count <= 8) { // 3(5) - 5(8)
        return Math.floor(count / 2 + 1);
    }
    // 4(9) - 11(32)
    return Math.floor(count / 3 + 1);
}

const ENV = {
    USED_CPUS: getUsedCpus(require('os').cpus().length),
    IS_DEV: process.env.NODE_ENV === 'development',
    ROOT_PATH: process.cwd(), // path.resolve(__dirname, '../'),
    PORT: process.env.PORT
};

Object.assign(ENV, {
    NODE_MODULES_PATH: path.resolve(ENV.ROOT_PATH, 'node_modules'),
    DIST_PATH: path.resolve(ENV.ROOT_PATH, 'api-mgr-compontents/compontents'),
    HappyPackPool: HappyPack.ThreadPool({ size: ENV.USED_CPUS })
});

export default webpackMerge({
    // Expose ENV.ROOT_PATH to allow automatically setting basename.
    context: ENV.ROOT_PATH,
    devtool: 'source-map',
    entry: {
        main: ['./src/apiMgrCompontents']
    },
    output: {
        filename: '[name].js',
        chunkFilename: '[name].js',
        path: ENV.DIST_PATH,
        publicPath: '/',
        library: 'api-mgr-compontents',
        libraryTarget: 'umd',
    },
    mode: process.env.NODE_ENV,
    stats: {
        timings: true,
        children: false // 关闭 Child mini-css-extract-plugin 的输出日志
    },
    externals: {
        'react': {
            commonjs: 'react',
            commonjs2: 'react',
            amd: 'React',
            root: 'React'
        },
        'react-dom': {
            commonjs: 'react-dom',
            commonjs2: 'react-dom',
            amd: 'ReactDOM',
            root: 'ReactDOM'
        }
    },
    performance: {
        hints: false // 关闭入口大于 244k 的警告
        // maxEntrypointSize: 512000,
        // maxAssetSize: 512000
    }
    // ecordsPath: path.join(ENV.ROOT_PATH, 'records.json'),
},
    resolve(ENV),
    plugins(ENV),
    module(ENV),
    optimization(ENV)
);
