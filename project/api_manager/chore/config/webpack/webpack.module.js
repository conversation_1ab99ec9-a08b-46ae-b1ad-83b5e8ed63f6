import path from "path";
import MiniCssExtractPlugin from 'mini-css-extract-plugin';
import HappyPack from 'happypack';

import rules from './loaders/webpack.rules';

export default function module(ENV) {
    const styleLoader = ENV.IS_DEV ? 'style-loader' : 'style-loader';
    /*const styleLoader = {
        loader: MiniCssExtractPlugin.loader,
        options: {
            hmr: ENV.IS_DEV,
            // if hmr does not work, this is a forceful method.
            // reloadAll: true,
        },
    };*/
    return {
        plugins: [
            new MiniCssExtractPlugin({
                ignoreOrder: true,
                filename: 'assets/[name].css?v=[contenthash:8]',
            }),
            new HappyPack({
                id: 'css',
                threadPool: ENV.HappyPackPool,
                loaders: [
                    {
                        loader: 'css-loader',
                        options: {
                            // minimize: true
                        },
                    },
                ],
            }),
        ],
        module: {
            rules: [
                ...rules(ENV),
                {
                    test: /\.(png|jpg|jpeg|gif)$/,
                    use: {
                        loader: 'url-loader',
                        options: {
                            limit: 1024 * 100,
                        },
                    },
                },
                {
                    test: /\.(woff[2]?|eot|ttf|svg)$/,
                    use: 'url-loader',
                },
                {
                    test: /\.css$/,
                    use: [
                        styleLoader,
                        'happypack/loader?id=css',
                    ],
                    exclude: [
                        path.resolve(ENV.ROOT_PATH, 'src'),
                    ],
                },
                // { test: /\.css$/, loader: 'style-loader!css-loader' },
                {
                    test: /\.less$/,
                    use: [
                        styleLoader,
                        {
                            loader: 'css-loader',
                            options: {
                                // minimize: true,
                                modules: {
                                    localIdentName: '[name]__[local]___[hash:base64:5]',
                                },
                                importLoaders: 1,
                            },
                        },
                        {
                            loader: 'less-loader',
                            options: {
                                javascriptEnabled: true,
                            },
                        },
                    ],
                    include: [
                        path.resolve(ENV.NODE_MODULES_PATH, 'ks-serveree-fe-common'),
                        path.resolve(ENV.ROOT_PATH, 'src'),
                    ],
                },
            ],
        },
    };
}
