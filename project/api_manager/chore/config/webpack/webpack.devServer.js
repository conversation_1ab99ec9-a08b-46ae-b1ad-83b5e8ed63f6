export default function devServer(ENV) {
    const devServer = {};

    if (ENV.IS_DEV) {
        const PROXY_DEF = {
            secure: true,
            logLevel: 'debug',
            changeOrigin: true,
            xfwd: true,
            // headers: {
            //     Cookie: `TEST_NONE=${1};`,
            // },
        };

        Object.assign(devServer, {
            host: 'localhost',
            disableHostCheck: true,
            contentBase: ENV.DIST_PATH,
            port: ENV.PORT || 8050,
            historyApiFallback: true,
            hot: true,
            compress: true,
            open: true,
            proxy: {
                '/api/*': {
                    target: "http://127.0.0.1:8051",
                    ...PROXY_DEF,
                },
                '/review/api/*': {
                    target: "http://127.0.0.1:8051",
                    ...PROXY_DEF,
                },
                '/disposal/*': {
                    target: "http://127.0.0.1:8051",
                    ...PROXY_DEF,
                },
            },
        });
    }
    return {
        devServer: devServer,
    };
}
