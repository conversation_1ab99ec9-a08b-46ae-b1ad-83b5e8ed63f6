import path from 'path';
import fs from 'fs';
import JSON5 from 'json5';

/**
 * 依据 tsconfig.json 中 compilerOptions.paths 一节 生成 webpack 的 alias 配置
 *
 * 示例如下：
 * 依据 tsconfig.json 中的：
   "paths": {
        "*": [
            "*",
            "src/*",
            "chore/tests/*"
        ],
        "@apps/*": ["src/apps/*"],
        "@shared/*": ["src/shared/*"],
        "@link-all-libs/*": ["src/libs/*"]
    }
 *
 * 生成到 webpack 中的：
 * alias: {
       '@apps': path.resolve(ENV.ROOT_PATH, 'src/apps'),
       '@shared': path.resolve(ENV.ROOT_PATH, 'src/shared'),
       '@link-all-libs': path.resolve(ENV.ROOT_PATH, 'src/libs')
   }
 *
 */
function getAlias(ENV) {
    const tsconfig = JSON5.parse(
        fs.readFileSync(path.resolve(ENV.ROOT_PATH, 'tsconfig.json'))
    );
    const paths = tsconfig.compilerOptions.paths;
    const alias = {};
    Object.keys(paths).forEach(it => {
        const [key, emp] = it.split('/*');
        if (emp != null) {
            /**
             * 由于 webpack 的限制，单个 alias 只能代表一个路径，因此要求 tsconfig 中 paths 的每个 key 也只能配置一个
             */
            alias[key] = path.resolve(ENV.ROOT_PATH, paths[it][0].split('/*')[0]);
        }
    });
    return alias;
}


export default function resolve(ENV) {
    /**
     * 来自 tsconfig.compilerOptions.paths
     */
    const alias = getAlias(ENV);

    return {
        resolve: {
            extensions: ['.tsx', '.ts', '.js', '.jsx', '.css', '.less', '.json'],
            modules: [
                path.resolve(ENV.ROOT_PATH, 'src'),
                path.resolve(ENV.ROOT_PATH, 'chore/tests'),
                path.resolve(ENV.ROOT_PATH, 'static'),
                ENV.NODE_MODULES_PATH
            ],
            alias: {
                ...alias,
            }
        }
    };
}
