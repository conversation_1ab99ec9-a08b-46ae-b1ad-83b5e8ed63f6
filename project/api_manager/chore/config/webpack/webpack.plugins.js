import path from "path";
import CaseSensitivePathsPlugin from 'case-sensitive-paths-webpack-plugin';
import webpack from 'webpack';
import HtmlWebpackPlugin from 'html-webpack-plugin';
import git from 'git-rev-sync';
import { BundleAnalyzerPlugin } from 'webpack-bundle-analyzer';
import CopyWebpackPlugin from 'copy-webpack-plugin';
// import { CleanWebpackPlugin } from 'clean-webpack-plugin';
import ForkTsCheckerWebpackPlugin from 'fork-ts-checker-webpack-plugin';
//
import LodashModuleReplacementPlugin from 'lodash-webpack-plugin';

import CdnFallbackPlugin from '@ks-infra/webpack-plugin-cdn-fallback'

import HappyPack from 'happypack';
import JSON5 from 'json5';
import fs from "fs";
const RadarSeedScriptPlugin = require('@ks-dmo/radar-seed-webpack-plugin')
const BlankScreenDetectorWebpack = require('@ad/blank-screen-detector-webpack');

export default function plugins(ENV) {
    const babelrc = JSON5.parse(
        fs.readFileSync(path.resolve(ENV.ROOT_PATH, '.babelrc')),
    );
    const packageJSON = JSON5.parse(
        fs.readFileSync(path.resolve(ENV.ROOT_PATH, 'package.json')),
    );
    const plugins = [];
    if (ENV.IS_DEV) {
        plugins.push(
            new webpack.HotModuleReplacementPlugin(),
        );
        plugins.push(
            new ForkTsCheckerWebpackPlugin({
                tsconfig: path.resolve(ENV.ROOT_PATH, 'tsconfig.json'),
                tslint: path.resolve(ENV.ROOT_PATH, 'chore/config/tslint.dev.json'),
                checkSyntacticErrors: true,
                // async: ENV.IS_DEV,
                reportFiles: [
                    '!**/node_modules/**',
                    '!**/dist/**'
                ]
            }),
        );
    } else {
        plugins.push(
            new BundleAnalyzerPlugin({
                analyzerMode: 'static',
                reportFilename: `${packageJSON.gatewayPrefix}assets/size.html`,
                openAnalyzer: false,
            }),
        );
        plugins.push(
            new CdnFallbackPlugin({
                fallbackUrlBase: `/`,
                urlBase: `https://cdnfile.corp.kuaishou.com/kc/files/a/kdev-apimgr/dist/`,
                fallbackTimeout: 30 * 1000,
                useMonitor: true
            })
        );
        plugins.push(
            new BlankScreenDetectorWebpack({
                /** 这里写的白屏检测的容器，根据项目具体配置，更多参数参照上面的文档 */
                containers: ['#LA_Entry_ID'],
                // 这个可以根据项目来判定，kfc接入时发现一些用户在没数据的场景下也被判定为白屏，具体可以见 KFC 接入白屏检测
                // closeSameElesCheck: true,
                // sameElementsPercent: 0.9
            })
        );
        plugins.push(
            /** 这里引入种子包插件 */
            new RadarSeedScriptPlugin({
                projectId: "1e5cc1ac8e", // 雷达项目 id
                env: "production"
            })
        )
    }

    return {
        module: {
            rules: [
                {
                    test: /\.(js|ts)x?$/, // (?!(\.min))
                    use: ['happypack/loader?id=babel'],
                    sideEffects: false,
                    exclude: [
                        // ENV.NODE_MODULES_PATH,
                        /node_modules\/(?!ks-serveree-fe-common)/,
                        /\.min\.js/,
                    ],
                    include: [
                        path.resolve(ENV.NODE_MODULES_PATH, 'ks-serveree-fe-common'),
                        path.resolve(ENV.ROOT_PATH, 'src'),
                        path.resolve(ENV.ROOT_PATH, 'chore/tests'),
                        // path.resolve(ENV.ROOT_PATH, 'mock')
                    ],
                },
            ],
        },
        plugins: [
            ...plugins,
            new LodashModuleReplacementPlugin({
                currying: true,
                paths: true,
            }),
            new HappyPack({
                id: 'babel',
                loaders: [
                    // { loader: 'babel-loader' },
                    {
                        loader: 'babel-loader',
                        options: {
                            ...babelrc
                        }
                    },
                ],
                threadPool: ENV.HappyPackPool,
            }),
            new CaseSensitivePathsPlugin(),
            new webpack.ContextReplacementPlugin(/(\/|\\)src(\/|\\|$)/, path.resolve(ENV.ROOT_PATH, 'src')),
            new HtmlWebpackPlugin({
                title: packageJSON.title,
                description: packageJSON.description,
                keywords: packageJSON.keywords.join(','),
                template: 'chore/config/template.html',
                filename: 'index.html',
                inject: true,
            }),

            new webpack.DefinePlugin({
                SENTRY_RELEASE_GITCOMMITHASH: JSON.stringify(git.short())
            }),

            new CopyWebpackPlugin([{
                from: path.resolve(ENV.ROOT_PATH, 'static'),
                to: path.resolve(ENV.DIST_PATH, packageJSON.gatewayPrefix),
                toType: 'dir',
            }]),
            // 通过模块调用次数给模块分配ids，常用的ids就会分配更短的id，使ids可预测，减小文件大小，推荐使用
            // new webpack.optimize.OccurrenceOrderPlugin(true),
            /**
             * 提升(hoist)或者预编译所有模块到一个闭包中，提升你的代码在浏览器中的执行速度。这种连结行为被称为“作用域提升(scope hoisting)”
             * By default this plugin is already enabled in production mode and disabled otherwise.
             */
            // new webpack.optimize.ModuleConcatenationPlugin(),
            // new CleanWebpackPlugin({ verbose: true }),
            new webpack.ContextReplacementPlugin(
                /moment[\\\/]locale$/,
                /^\.\/(zh-cn)$/,
            ),
        ],
    };
}
