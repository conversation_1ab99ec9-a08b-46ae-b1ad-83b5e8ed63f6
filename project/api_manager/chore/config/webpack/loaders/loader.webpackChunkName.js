/**
 * https://v4.webpack.js.org/contribute/writing-a-loader/#guidelines
 *
 * Keep them simple.
 * Utilize chaining.
 * Emit modular output.
 * Make sure they're stateless.
 * Employ loader utilities.
 * Mark loader dependencies.
 * Resolve module dependencies.
 * Extract common code.
 * Avoid absolute paths.
 * Use peer dependencies.
 * */
'use strict';

const regQuick = /import\s*\(/;
const reg = /import\s*\((?!\s*\/\*\s*webpackChunkName[\s\S]*?\*\/)[\s\S]*?['|"]([\s\S]*?)['|"][\s\S]*?\)/g;
module.exports = function (source) {
    if (this.cacheable) {
        this.cacheable();
    }
    let source2 = source;
    if (regQuick.test(source)) {
        source2 = source.replace(reg, (match, p1) => {
            if (p1.indexOf('./') > -1) {
                throw 'import path cannot be a relative path!';
            }
            const paths = p1.split('\/');
            if (paths.length > 3) {
                paths.splice(2, paths.length - 3);
            }
            if (paths.length > 2 && paths[1].length > 6) {
                paths[1] = paths[1].substring(0, 6);
            }
            return `import(/* webpackChunkName: "A.${paths.map(it => it.replace(/@/g, '')).join('.')}" */'${p1}')`;
        });
    }
    this.callback(null, source2);
};
