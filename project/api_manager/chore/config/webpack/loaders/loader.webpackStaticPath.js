/**
 * https://v4.webpack.js.org/contribute/writing-a-loader/#guidelines
 *
 * Keep them simple.
 * Utilize chaining.
 * Emit modular output.
 * Make sure they're stateless.
 * Employ loader utilities.
 * Mark loader dependencies.
 * Resolve module dependencies.
 * Extract common code.
 * Avoid absolute paths.
 * Use peer dependencies.
 * */
import packageJSON from '../../../../package.json';
'use strict';

const regQuick = /\/assets/;
const reg = /\/assets/g;

const regQuick2 = /'libs/;
const reg2 = /from\s*'libs/g;

module.exports = function (source) {
    if (this.cacheable) {
        this.cacheable();
    }
    let source2 = source;
    if (regQuick.test(source)) {
        source2 = source.replace(reg, (match, p1) => {
            return `/${packageJSON.gatewayPrefix}/assets`;
        });
    }

    if (regQuick2.test(source2)) {
        source2 = source2.replace(reg2, (match, p1) => {
            return 'from \'@libs';
        });
    }

    this.callback(null, source2);
};
