import path from "path";

export default function rules(ENV) {
    return [
        {
            test: /\.(js|ts)x?$/,
            // test: /routerConfig\.tsx?$/,
            // include: [ path.resolve(ENV.ROOT_PATH, 'src') ],
            // exclude: [ ENV.NODE_MODULES_PATH ],
            include: [
                path.resolve(ENV.NODE_MODULES_PATH, 'ks-serveree-fe-common'),
                path.resolve(ENV.ROOT_PATH, 'src'),
            ],
            exclude: [
                /node_modules\/(?!ks-serveree-fe-common)/,
            ],

            use: [
                path.resolve(ENV.ROOT_PATH, 'chore/config/webpack/loaders/loader.webpackChunkName.js'),
                path.resolve(ENV.ROOT_PATH, 'chore/config/webpack/loaders/loader.webpackStaticPath.js'),
            ]
        }
    ];
}
