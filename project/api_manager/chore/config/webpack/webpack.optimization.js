import TerserPlugin from 'terser-webpack-plugin';
import OptimizeCssAssetsPlugin from 'optimize-css-assets-webpack-plugin';
// import path from "path";

export default function optimization(ENV) {
    const minimizer = [];
    if (!ENV.IS_DEV) {
        minimizer.push(
            new TerserPlugin({
                cache: true,
                parallel: ENV.USED_CPUS,
                sourceMap: true,
                exclude: [
                    // 针对 [输出文件] 的文件名，结尾不能加 [$]；
                    /\.min\.js/
                ]
            })
        );
        minimizer.push(new OptimizeCssAssetsPlugin({}));
    }

    return {
        resolve: {
            alias: {
                // '@ant-design/icons/lib/dist$': path.resolve(ENV.NODE_MODULES_PATH, '@ant-design/icons/lib/umd.js'),
            }
        },
        optimization: {
            usedExports: true,
            sideEffects: true,
            splitChunks: {
                chunks: 'async', //默认作用于异步chunk，值为all/initial/async/function(chunk),值为function时第一个参数为遍历所有入口chunk时的chunk模块，chunk._modules为gaichunk所有依赖的模块，通过chunk的名字和所有依赖模块的resource可以自由配置,会抽取所有满足条件chunk的公有模块，以及模块的所有依赖模块，包括css
                minSize: 1024 * 512,  //默认值是30kb
                minChunks: 2,  //被多少模块共享
                maxAsyncRequests: 5,  //所有异步请求不得超过5个
                maxInitialRequests: 3,  //初始话并行请求不得超过3个
                name: true,  //打包后的名称，默认是chunk的名字通过分隔符（默认是～）分隔开，如vendor~
                automaticNameDelimiter: '-',
                cacheGroups: {
                    // styles: {
                    //     name: 'styles',
                    //     test: /\.(less|css)$/,
                    //     chunks: 'all',
                    //     enforce: true
                    // },
                    // antdicons: {
                    //     name: 'antdicons.min',
                    //     test: /@ant-design\/icons\//,
                    //     chunks: 'all',
                    //     enforce: true
                    // },
                    jsBeautify: {
                        name: 'js-beautify',
                        test: /[\\/]node_modules[\\/]_?js-beautify/,
                    }
                },
            },
            minimizer: minimizer
        }
    };
}
