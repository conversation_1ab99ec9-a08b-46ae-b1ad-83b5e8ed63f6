const path = require('path');
const { merge } = require('webpack-merge');
const CdnFallbackPlugin = require('@ks-infra/webpack-plugin-cdn-fallback');
const HtmlWebpackPlugin = require('html-webpack-plugin');
const BundleAnalyzerPlugin = require('webpack-bundle-analyzer').BundleAnalyzerPlugin;
const RadarSeedScriptPlugin = require('@ks-dmo/radar-seed-webpack-plugin')
const BlankScreenDetectorWebpack = require('@ad/blank-screen-detector-webpack');
const JSON5 = require('json5');
const fs = require("fs");


// 这里只定义这个webpack.bebel.js 需要的env，
const ENV = {
    IS_DEV: process.env.IS_DEV === '1' || process.env.NODE_ENV === 'development',
    ROOT_PATH: process.cwd(), // path.resolve(__dirname, '../'),
    CDN_DIR: process.env.CDN_DIR,
};

Object.assign(ENV, {
    ROOT_NODE_MODULES_PATH: path.resolve(ENV.ROOT_PATH, '../../node_modules'),
});

const packageJSON = JSON5.parse(
    fs.readFileSync(path.resolve(ENV.ROOT_PATH, 'package.json')),
);

// 引用 Webpack 配置文件
const getWebpackCommonConfig = require('kdev-webpack-common-config');

const webpackCommonConfig = getWebpackCommonConfig({ ENVParams: ENV });


const mergedConfig = merge(webpackCommonConfig, {
    externals: {
        // 'react': 'React',
        // 'react-dom': 'ReactDOM'
    },
    output: {
        publicPath: ENV.IS_DEV ? '/' : `https://h2.static.yximgs.com/udata/pkg/QA-SERVEREE-FE-OUT/${ENV.CDN_DIR}/dist/`,
        // publicPath: ENV.IS_DEV ? '/' : `https://cdnfile.corp.kuaishou.com/kc/files/a/kdev-workbench/${ENV.CDN_DIR}/dist/`
        // publicPath: ENV.IS_DEV ? '/' : `https://cdnfile.corp.kuaishou.com/kc/files/a/KDev-codeSearch/${ENV.CDN_DIR}/dist/`
    },
    plugins: [
        new HtmlWebpackPlugin({
            title: packageJSON.title,
            description: packageJSON.description,
            subAppPath: packageJSON.gatewayPrefix,
            keywords: packageJSON.keywords.join(','),
            template: 'chore/config/template.html',
            filename: 'index.html',
            inject: true,
        }),
        /** 注意引入顺序，webpack插件从下到上执行 */
        /** 这里引入白屏检测的插件逻辑 */
        !ENV.IS_DEV && new BlankScreenDetectorWebpack({
            /** 这里写的白屏检测的容器，根据项目具体配置，更多参数参照上面的文档 */
            containers: ['#LA_Entry_ID'],
            // 这个可以根据项目来判定，kfc接入时发现一些用户在没数据的场景下也被判定为白屏，具体可以见 KFC 接入白屏检测
            // closeSameElesCheck: true,
            // sameElementsPercent: 0.9
            // 这个要设置 才能拿到3s白屏率数据
            close123SDetect: false,
            // 这个要设置 才能拿到3s白屏率数据
            close3SDetect: false,
        }),
        /** 这里引入种子包插件 */
        !ENV.IS_DEV && new RadarSeedScriptPlugin({
            projectId: "1e5cc1ac8e", // 雷达项目 id
            env: "production"
        }),
        !ENV.IS_DEV && new CdnFallbackPlugin({
            thirdPartyAssets: [{
                // 模板HTML中使用的CDN文件
                url: 'https://h1.static.yximgs.com/udata/pkg/QA-SERVEREE-FE-OUT/common-assets/5.18.2-swagger-ui-bundle.js',
                // 模板CDN文件如果不好使了，替代用的CDN文件
                fallbackUrl: 'https://h2.static.yximgs.com/udata/pkg/QA-SERVEREE-FE-OUT/common-assets/5.18.2-swagger-ui-bundle.js',
                // 类型，支持js和css两种
                type: 'js',
            }, {
                url: 'https://h1.static.yximgs.com/udata/pkg/QA-SERVEREE-FE-OUT/common-assets/5.18.2-swagger-ui-standalone-preset.js',
                fallbackUrl: "https://h2.static.yximgs.com/udata/pkg/QA-SERVEREE-FE-OUT/common-assets/5.18.2-swagger-ui-standalone-preset.js",
                type: 'css'
            }]
        }),
    ].filter(Boolean),
});
const config = mergedConfig;


module.exports = config;

