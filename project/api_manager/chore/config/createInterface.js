// npm install node-fetch
const fetch = require('node-fetch');

if (process.argv.length < 3) {
    console.log('未获取 url');
} else {
    const [workspace, filepath, ...params] = Array.from(process.argv);
    urlToInterface(params.join('&'));
}

// 'http://*************:8080/v1/disposal/shenfang/list?city_id=132&company_id=&end_time=1571646437000&start_time=1570328000000';
// node chore/config/createInterface.js 'http://localhost:8000/api/saas/apisvr/v1/car/list?city_id=162&company_id='
function urlToInterface(url) {
    console.log('url: ', url);
    if (url.indexOf('http') === 0) {
        fetch(url)
            .then(res => res.json())
            .then(
                json => {
                    jsonToInterface(json);
                },
            );
        return;
    }
    const json = require(url);
    jsonToInterface(json);
}

function jsonToInterface(json) {
    const str = valueToTypescriptType(json);
    var proc = require('child_process').spawn('pbcopy'); // mac
    proc.stdin.write(str);
    proc.stdin.end();
    // require('child_process').exec(`echo ${str} | clip`); // windows
    console.log('========== 已复制到剪切板 ==========');
    console.log(str);
    console.log('========== 已复制到剪切板 ==========');
}

function isObject(val) {
    return Object.prototype.toString.call(val) === '[object Object]';
}

function isArray(val) {
    return Object.prototype.toString.call(val) === '[object Array]';
}

function notSimpleType(val) {
    return !isObject(val) && !isArray(val);
}

function getSpace(level) {
    return Array.from({ length: level }).map(it => '    ').join('');
}

function simpleObjectToInterface(simpleObj, level = 0) {
    return Object.keys(simpleObj)
        .map(key => {
            const val = simpleObj[key];
            const tsType = valueToTypescriptType(val, level);
            const valStr = notSimpleType(val) ? ' // ' + JSON.stringify(val).replace(/"/g, '\'') : '';

            return `${getSpace(level)}${key}: ${tsType};${valStr}`;
        })
        .join('\n');
}

function valueToTypescriptType(val, level = 0) {
    const objectType = Object.prototype.toString.call(val);
    switch (true) {
        default:
            return 'any';
        case objectType === '[object String]':
            return 'string';
        case objectType === '[object Number]':
            return 'number';
        case objectType === '[object Boolean]':
            return 'boolean';
        case objectType === '[object Date]':
            return 'Date';
        case objectType === '[object RegExp]':
            return 'RegExp';
        case objectType === '[object Array]': {
            if (val.length > 0) {
                return `Array<{
${simpleObjectToInterface(val[0], level + 1)}
${getSpace(level)}}>`;
            }
            return 'Array<any>';
        }
        case objectType === '[object Object]': {
            return `{
${simpleObjectToInterface(val, level + 1)}
${getSpace(level)}}`;
        }
    }
}

/**
 * 在JavaScript里使用typeof判断数据类型，只能区分基本类型，即：number、string、undefined、boolean、object。
 对于null、array、function、object来说，使用typeof都会统一返回object字符串。
 要想区分对象、数组、函数、单纯使用typeof是不行的。在JS中，可以通过Object.prototype.toString方法，判断某个对象之属于哪种内置类型。
 分为null、string、boolean、number、undefined、array、function、object、date、math。
 1. 判断基本类型

 Object.prototype.toString.call(null); // "[object Null]"
 Object.prototype.toString.call(undefined); // "[object Undefined]"
 Object.prototype.toString.call(“abc”);// "[object String]"
 Object.prototype.toString.call(123);// "[object Number]"
 Object.prototype.toString.call(true);// "[object Boolean]"
 2. 判断原生引用类型

 **函数类型**
 Function fn(){
  console.log(“test”);
}
 Object.prototype.toString.call(fn); // "[object Function]"
 **日期类型**
 var date = new Date();
 Object.prototype.toString.call(date); // "[object Date]"
 **数组类型**
 var arr = [1,2,3];
 Object.prototype.toString.call(arr); // "[object Array]"
 **正则表达式**
 var reg = /[hbc]at/gi;
 Object.prototype.toString.call(reg); // "[object RegExp]"
 **自定义类型**
 function Person(name, age) {
    this.name = name;
    this.age = age;
}
 var person = new Person("Rose", 18);
 Object.prototype.toString.call(arr); // "[object Object]"
 很明显这种方法不能准确判断person是Person类的实例，而只能用instanceof 操作符来进行判断，如下所示：

 console.log(person instanceof Person); // true
 3. 判断原生JSON对象

 var isNativeJSON = window.JSON && Object.prototype.toString.call(JSON);
 console.log(isNativeJSON);// 输出结果为”[object JSON]”说明JSON是原生的，否则不是；
 注意：Object.prototype.toString()本身是允许被修改的，而我们目前所讨论的关于Object.prototype.toString()这个方法的应用都是假设toString()方法未被修改为前提的。
 * */
