<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1" />
    <title>
        <%= htmlWebpackPlugin.options.title %>
    </title>
    <meta name="keywords" content="<%= htmlWebpackPlugin.options.keywords %>" />
    <meta name="description" content="<%= htmlWebpackPlugin.options.description %>" />

    <link rel="shortcut icon" href="https://cdnfile.corp.kuaishou.com/kc/files/a/QA-SERVEREE-FE-IN/logo.png" />
    <link rel="stylesheet"
        href="https://h1.static.yximgs.com/udata/pkg/QA-SERVEREE-FE-OUT/common-assets/5.8.2-swagger-ui.css" />
    <link rel="manifest" href="/web/api-mock/assets/manifest.json">
    <script src="https://docs-h2.corp.kuaishou.com/assets/vodka/sandbox-assets/23379386/ui/index.js"
        crossorigin></script>
</head>

<body>
    <script src="https://h1.static.yximgs.com/udata/pkg/QA-SERVEREE-FE-OUT/common-assets/5.18.2-swagger-ui-bundle.js"
        crossorigin></script>
    <script
        src="https://h1.static.yximgs.com/udata/pkg/QA-SERVEREE-FE-OUT/common-assets/5.18.2-swagger-ui-standalone-preset.js"
        crossorigin></script>
    <script>
        function loadScript(url, callback) {
            var script = document.createElement("script")
            script.type = "text/javascript";
            if (script.readyState) { //IE
                script.onreadystatechange = function () {
                    if (script.readyState == "loaded" || script.readyState == "complete") {
                        script.onreadystatechange = null;
                        if (callback) {
                            callback();
                        }
                    }
                };
            } else { //Others
                script.onload = function () {
                    if (callback) {
                        callback();
                    }
                };
            }
            script.src = url;
            document.querySelector("head").appendChild(script);
        }
        loadScript(`/web/api-mock/assets/iconfont/symbol.js?t=${Date.now()}`);

        if ('serviceWorker' in navigator) {
            window.addEventListener('load', () => {
                navigator.serviceWorker.register('/web/api-mock/assets/service-worker.js')
                    .then(registration => {
                        console.log('ServiceWorker registration successful with scope: ', registration.scope);
                    })
                    .catch(err => {
                        console.log('ServiceWorker registration failed: ', err);
                    });
            });
        }

        // setTimeout(() => {
        //     loadScript(`/accessproxy_statics/wm.js`);
        // }, 300);
    </script>
    <!-- <script defer type="text/javascript" src="/accessproxy_statics/wm.js"></script> -->
</body>

</html>