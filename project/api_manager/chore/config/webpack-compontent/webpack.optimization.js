import TerserPlugin from 'terser-webpack-plugin';
import OptimizeCssAssetsPlugin from 'optimize-css-assets-webpack-plugin';

export default function optimization(ENV) {
    return {
        resolve: {
            alias: {}
        },
        optimization: {
            usedExports: true,
            sideEffects: true,
            splitChunks: {
                chunks: 'async', //默认作用于异步chunk，值为all/initial/async/function(chunk),值为function时第一个参数为遍历所有入口chunk时的chunk模块，chunk._modules为gaichunk所有依赖的模块，通过chunk的名字和所有依赖模块的resource可以自由配置,会抽取所有满足条件chunk的公有模块，以及模块的所有依赖模块，包括css
                minSize: 1024 * 512,  //默认值是30kb
                minChunks: 2,  //被多少模块共享
                maxAsyncRequests: 5,  //所有异步请求不得超过5个
                maxInitialRequests: 3,  //初始话并行请求不得超过3个
                name: true,  //打包后的名称，默认是chunk的名字通过分隔符（默认是～）分隔开，如vendor~
                automaticNameDelimiter: '-',
                cacheGroups: {
                    jsBeautify: {
                        name: 'js-beautify',
                        test: /[\\/]node_modules[\\/]_?js-beautify/,
                    }
                },
            },
            minimizer: []
        }
    };
}
