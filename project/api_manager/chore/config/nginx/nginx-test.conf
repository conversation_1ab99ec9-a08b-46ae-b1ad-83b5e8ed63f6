server {
    listen 8088;
    gzip  on;
    gzip_comp_level 3;
    gzip_types text/plain application/x-javascript application/javascript application/json text/css image/svg+xml image/*;

    set $output "/usr/share/nginx/html/dist";
#    set $assets "/home/<USER>/【unfilled】/dist/assets";

    #access_log   /usr/local/var/log/nginx/saas.access.log main;
    #error_log    /usr/local/var/log/nginx/saas.error.log;

    #NewTaxiSaaS mock api nginx对于静态文件post请求会返回405 使用反向代理解决
#    location /api/ {
#        proxy_method GET;
#        proxy_pass http://127.0.0.1:8088/apiNginxMock/;
#        break;
#    }

    #NewTaxiSaaS assets
    location /assets {
        root        $output;
        break;
    }

#    #NewTaxiSaaS public
#    location ~* ^/(img|iconfont) {
#        root        $assets;
#        break;
#    }

    #NewTaxiSaaS
    location / {
        add_header  Cache-Control  no-store;
        root        $output;
        error_page *********** *********** 504 = "/error/error.html";
        rewrite / "/index.html" break;
        break;
    }

#    # api-【unfilled】
#    location /api/【unfilled】/ {
#        proxy_pass http://【unfilled】/api/【unfilled】/;
#        break;
#    }
}
