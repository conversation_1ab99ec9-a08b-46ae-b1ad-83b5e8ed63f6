# For more information on configuration, see:
#   * Official English Documentation: http://nginx.org/en/docs/
#   * Official Russian Documentation: http://nginx.org/ru/docs/

worker_processes auto;
# error_log /var/log/nginx/error.log;

# Load dynamic modules. See /usr/share/doc/nginx/README.dynamic.
# include /data/nginx/modules/*.conf;

events {
    worker_connections 1024;
}

http {

    #access_log  /var/log/nginx/access.log  main;

    sendfile            on;
    tcp_nopush          on;
    tcp_nodelay         on;
    keepalive_timeout   65;
    types_hash_max_size 2048;

    include             mime.types;
    default_type        application/octet-stream;

    # Load modular configuration files from the /data/nginx/conf.d directory.
    # See http://nginx.org/en/docs/ngx_core_module.html#include
    # for more information.
    include /data/nginx/conf.d/*.conf;

    server {
        listen       80 default_server;
        listen       [::]:80 default_server;
        server_name  _;
        root         /data/nginx/html;
        gzip  on;
        gzip_comp_level 3;
        gzip_types text/plain application/x-javascript application/javascript application/json text/css image/svg+xml image/*;

        set $output "/data/nginx/html/dist";
        location /web/api-mock/web/api-mock/assets {
            alias $output/web/api-mock/assets;
            break;
        }

        location /web/api-mock/assets {
            root        $output;
            break;
        }
        location /web/api-mock {
            add_header  Cache-Control  no-store;
            root        $output;
            error_page 400 403 404 500 502 503 504 = "/error/error.html";
            rewrite / "/index.html" break;
            break;
        }
    }
}
