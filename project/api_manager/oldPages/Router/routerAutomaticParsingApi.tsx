import { RouteSetting } from 'libs/a-component';
import { ERouter } from 'CONFIG';
import React from 'react';
import { CodeSandboxOutlined } from '@ant-design/icons';

const routerAutomaticParsingApi: RouteSetting = new RouteSetting({
    iconType: <CodeSandboxOutlined />,
    title: '自动解析 API',
    path: ERouter.API_MOCK_AUTOMATICPARSINGAPI,
    routeSettings: [
        // {
        //     // iconType: <CodeSandboxOutlined/>,
        //     title: 'GRPC模块',
        //     path: ERouter.API_MOCK_GRPCMOCK_MODULE,
        //     importPromise: () => import('@/pages/automaticParsingApi/grpcModuleMgr'),
        // },
        // {
        //     // iconType: <CodeSandboxOutlined/>,
        //     title: 'GRPC API',
        //     path: ERouter.API_MOCK_GRPCMOCK_INTERFACE,
        //     importPromise: () => import('@/pages/automaticParsingApi/grpcApiMgr'),
        // },
        // {
        //     // iconType: <CodeSandboxOutlined/>,
        //     title: '接口详情',
        //     forbiddenMenu: true,
        //     path: ERouter.API_MOCK_GRPCMOCK_DETAIL,
        //     importPromise: () => import('@/pages/automaticParsingApi/grpcApiDetail'),
        // },
        // {
        //     // iconType: <CodeSandboxOutlined/>,
        //     title: '分支版本对比',
        //     path: ERouter.API_MOCK_GRPCMOCK_REPORT,
        //     forbiddenMenu: true,
        //     importPromise: () => import('@/pages/grpcApi/comparisonReport'),
        // },
        // {
        //     title: 'HTTP模块',
        //     path: ERouter.API_MOCK_HTTP_MODULE_MGR,
        //     importPromise: () => import('@/pages/automaticParsingApi/httpModuleMgr'),
        // },
        // {
        //     title: 'HTTP API',
        //     path: ERouter.API_MOCK_HTTP_API_MGR,
        //     importPromise: () => import('@/pages/automaticParsingApi/httpApiMgr')
        // },
        // {
        //     title: 'HTTP API详情',
        //     forbiddenMenu: true,
        //     path: ERouter.API_MOCK_HTTP_API_DETAIL,
        //     importPromise: () => import('@/pages/automaticParsingApi/httpApiDetail'),
        // }
    ]
});

const routers = [
    routerAutomaticParsingApi
];

export default routers;
