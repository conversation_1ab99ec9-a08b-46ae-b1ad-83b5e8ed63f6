import { RouteSetting } from 'libs/a-component';
import { ERouter } from 'CONFIG';
import React from 'react';
import {
    ClusterOutlined
} from '@ant-design/icons';

// const routerOrganizeSpace: RouteSetting = new RouteSetting({
//     iconType: <ClusterOutlined />,
//     title: 'API 组织空间',
//     path: ERouter.API_MOCK_ORGANIZESPACE,
//     routeSettings: [
//         // {
//         //     // iconType: <CodeSandboxOutlined/>,
//         //     title: 'API 管理',
//         //     path: ERouter.API_MOCK_ORGANIZESPACE_PROJECTMGR,
//         //     importPromise: () => import('@/pages/organizeSpace/apiMgr'),
//         // },
//         // {
//         //     forbiddenMenu: true,
//         //     title: 'Mock代理配置',
//         //     path: ERouter.API_MOCK_PROXY_CONFIG,
//         //     importPromise: () => import('@/pages/organizeSpace/mockProxyConfig')
//         // },
//         // {
//         //     // iconType: <CodeSandboxOutlined/>,
//         //     title: '场景Mock',
//         //     path: ERouter.API_MOCK_ORGANIZESPACE_SCENEMGR,
//         //     importPromise: () => import('@/pages/organizeSpace/sceneMgr'),
//         // },
//         // {
//         //     // iconType: <CodeSandboxOutlined/>,
//         //     title: '部门管理',
//         //     path: ERouter.API_MOCK_ORGANIZESPACE_DEPARTMENTMGR,
//         //     // importPromise: () => import('@/pages/organizeSpace/departmentMgr'),
//         //     importPromise: () => import('@/pages/organizeSpace/departmentPersonnel'),
//         // }
//     ]
// });

// const routerGrpcModule: RouteSetting = new RouteSetting({
//     title: 'Grpc模块',
//     path: ERouter.API_MOCK_PROJECT_GRPC_MODULE,
//     menuKey: ERouter.API_MOCK_ORGANIZESPACE_PROJECTMGR,
//     forbiddenMenu: true,
//     importPromise: () => import('@/pages/organizeSpace/grpcModule'),
// });

// const routerAscene: RouteSetting = new RouteSetting({
//     // title: '我的场景',
//     path: ERouter.API_MOCK_ORGANIZESPACE_ASCENE,
//     menuKey: ERouter.API_MOCK_ORGANIZESPACE_SCENEMGR,
//     forbiddenMenu: true,
//     importPromise: () => import('@/pages/organizeSpace/sceneMgr/ascene'),
// });

const routers = [
    // routerOrganizeSpace,
    // routerGrpcModule,
    // routerAscene
];

export default routers;
