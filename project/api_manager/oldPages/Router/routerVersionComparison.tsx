// import { RouteSetting } from 'libs/a-component';
// import { ERouter } from 'CONFIG';

// const routerVersionComparison: RouteSetting = new RouteSetting({
//     title: 'API版本对比',
//     path: ERouter.API_MOCK_VERSIONCOMPARISON,
//     forbiddenMenu: true,
//     fullScreen: true,
//     // menuKey: ERouter.API_MOCK_ORGANIZESPACE_PROJECTMGR,
//     // importPromise: () => import('@/pages/versionComparison'),
// });

// const routers = [
//     routerVersionComparison,
// ];

// export default routers;
