import { RouteSetting } from 'libs/a-component';
import { ERouter } from 'CONFIG';
import React from 'react';

// const routetrProjectMgr: RouteSetting = new RouteSetting({
//     // iconType: <ClusterOutlined />,
//     title: '项目模块管理',
//     path: ERouter.API_MOCK_PROJECT_MODULEMGR,
//     forbiddenMenu: true,
//     menuKey: ERouter.API_MOCK_ORGANIZESPACE_PROJECTMGR,
//     importPromise: () => import('@/pages/organizeSpace/projectModuleMgr'),
// });

// const routerApiStatistics: RouteSetting = new RouteSetting({
//     title: 'API统计',
//     path: ERouter.API_MOCK_MODULEMGR_APISTATISTICS,
//     forbiddenMenu: true,
//     menuKey: ERouter.API_MOCK_ORGANIZESPACE_PROJECTMGR,
//     importPromise: () => import('@/pages/organizeSpace/apiStatistics'),
// });

// const routerModuleDetail: RouteSetting = new RouteSetting({
//     // iconType: <ClusterOutlined />,
//     title: '项目模块详情',
//     path: ERouter.API_MOCK_PROJECT_MODULEDETAIL,
//     forbiddenMenu: true,
//     menuKey: ERouter.API_MOCK_ORGANIZESPACE_PROJECTMGR,
//     importPromise: () => import('@/pages/moduleDetail'),
// });

const routers = [
    // routetrProjectMgr,
    // routerModuleDetail,
    // routerApiStatistics
];

export default routers;
