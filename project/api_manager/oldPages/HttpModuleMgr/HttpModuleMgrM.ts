import APageModel from '@/pages/APageModel';
import { action, observable, runInAction } from 'mobx';
import { IModuleList, IQuery } from './configure';
import { MoveInProjectM } from '../httpMoveInProject/MoveInProjectM';
import { ModuleDetailM } from './ModuleDetail/ModuleDetailM';
import { CreateModuleModalModel } from '@/business/createModuleModal/CreateModuleModalModel';
import { nsMockManageKoasApiManageModuleUnbindModuleToProjectPost, nsMockManageKoasApiManageModuleGetHttpModuleListGet } from '@/remote';
import { message } from 'antd';

export class HttpModuleMgrM extends APageModel<IQuery> {
    @observable public key: string = '';
    @observable public currentPage: number = 1;
    @observable public pageSize: number = 10;
    @observable public total: number = 0;
    @observable public moduleList: IModuleList[] = [];
    @observable public tableLoading: boolean = false;

    public moveInProjectM = new MoveInProjectM();
    public moduleDetailM = new ModuleDetailM();
    public createModuleModalM = new CreateModuleModalModel();

    constructor(query: IQuery) {
        super(query);
        this.initByQueryFields(query);
        this.init();
    }

    protected getQueryFields(): Array<keyof IQuery> {
        return [];
    }

    @action
    private init() {
        this.onChangePageInfo(1);
    }

    // 分页查询
    @action.bound
    public onChangePageInfo(currentPage: number, pageSize?: number): void {
        this.currentPage = currentPage;
        this.pageSize = pageSize || this.pageSize;
        this.getHttpModuleList();
    }

    // 移入项目
    @action.bound
    public onOpenMoveInProject(moduleId: number): void {
        this.moveInProjectM.init(moduleId);
        this.moveInProjectM.onMoveInProjectCallback = this.getHttpModuleList;
    }

    // 打开详情
    @action.bound
    public onOpenModuleDetail(moduleId: number): void {
        this.moduleDetailM.init(moduleId);
    }

    // 编辑模块
    @action.bound
    public onOpenCreateModuleModal(record): void {
        this.createModuleModalM.initLoading(2, record.projectId, record.moduleId);
        this.createModuleModalM.onCloseCallback = this.getHttpModuleList;
    }

    // 解除项目绑定
    @action.bound
    public async unbindModuleToProject(record) {
        try {
            const params = {
                moduleId: record.moduleId,
                projectId: record.projectId
            };
            await nsMockManageKoasApiManageModuleUnbindModuleToProjectPost.remote(params);
            message.success('解除绑定成功～');
            this.getHttpModuleList();
        } catch (e) {
        }
    }

    // 获取HTTP模块列表
    @action.bound
    private async getHttpModuleList() {
        this.tableLoading = true;
        try {
            const params = {
                departmentId: 0,
                key: this.key,
                pageSize: this.pageSize,
                currentPage: this.currentPage
            };
            const result = await nsMockManageKoasApiManageModuleGetHttpModuleListGet.remote(params);
            runInAction(() => {
                this.moduleList = result?.list || [];
                this.total = result?.total;
                this.tableLoading = false;
            });
        } catch (e) {
            runInAction(() => {
                this.tableLoading = false;
            });
        }
    }
}
