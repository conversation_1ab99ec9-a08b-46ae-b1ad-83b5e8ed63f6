.moveInPorjectModal {
  .rowLabel {
    margin: 24px 0 8px;

    .required {
      color: #ff4d4f;
    }
  }

  .projectTree {
    border: 1px solid #d9d9d9;
    border-radius: 4px;
    margin: 0;
    padding: 12px 0;
    
    .titleWrap{
      display: flex;
      // width: 100%;

      .editProjectName {
        flex: 1;
      }

      .editCloseBtn {
        color: #ff4d4f;
        font-size: 18px;
      }

      .editOkBtn {
        color: #31bf30;
        font-size: 18px;
      }
    }

    :global{
      .ant-empty-description {
        opacity: .5;
      }

      .ant-tree-node-content-wrapper{
        display: flex;
        height: 32px;
        align-items: center;
      }

      .ant-tree-title {
        width: 100%;
      }
    }
  }

  .createProjectBtn {
    padding-top: 8px;
    display: flex;
  }

  .rowWidth100 {
    width: 100%;
  }

  :global {
    .ant-modal-body {
      padding-top: 0;
    }
  }
}