import { AViewModel } from '@libs/mvvm';
import { observable, action, runInAction } from 'mobx';
import {
    nsMockManageApiManageDepartmentQueryAllDepartmentListPost, nsMockManageKoasApiManageProjectQueryProjectListPost,
    nsMockManageKoasApiManageModuleBindModuleToProjectPost, nsMockManageKoasApiManageProjectCreateProjectPost,
    nsMockManageKoasApiManageModuleGetHttpModuleBranchGet
} from '@/remote';
import { eachNode } from '@libs/utils';
import { message } from 'antd';
import { Bind } from 'lodash-decorators';

interface IDepartmentList {
    departmentId: number;
    departmentName: string;
    key?: string;
    value?: number;
    title?: string;
    children?: IDepartmentList[];
}

interface IProjectList {
    projectId: number;
    projectName: string;
    key: string;
    selectable?: boolean;
}

export class MoveInProjectM extends AViewModel {
    @observable public visible: boolean = false;
    private moduleId: number = 0;
    @observable public departmentId: number = 0;
    @observable public departmentList: IDepartmentList[] = [];
    @observable public projectId: string = '';
    @observable public projectList: IProjectList[] = [];
    @observable public projectName: string = '';
    @observable public projectDesc: string = '';
    @observable public branchName: string = '最新分支';
    @observable public branchNameList: string[] = [];
    @observable public saveProjectLoading: boolean = false;
    @observable public moveLoading: boolean = false;

    public onMoveInProjectCallback?(parmas: nsMockManageKoasApiManageModuleBindModuleToProjectPost.IParams): void;

    @action.bound
    public init(moduleId: number): void {
        this.moduleId = moduleId;
        this.visible = true;
        if (!this.departmentList.length) {
            this.queryAllDepartmentList();
        }
        this.getHttModuleBranch();
    }

    @action.bound
    public onCloseMoveInProjectModal(): void {
        this.visible = false;
        this.moduleId = 0;
        this.departmentId = 0;
        // this.departmentList = [];
        this.projectId = '';
        this.projectList = [];
        this.projectName = '';
        this.projectDesc = '';
    }

    @action.bound
    public onSelectProject(selectedKeys): void {
        this.projectId = selectedKeys[0];
    }

    @action.bound
    public onSelectBranch(branchName: string): void {
        this.branchName = branchName;
    }

    @action.bound
    public addProjectRow(): void {
        if (this.projectList[0]?.key !== '0' || !this.projectList.length) {
            this.projectList = [...[
                { projectId: 0, key: '0', projectName: '', selectable: false }
            ], ...this.projectList];
        }
    }

    @action.bound
    public onCloseEditProject(): void {
        this.projectList = this.projectList.splice(1);
    }

    @action.bound
    public onSelectDepartment(departmentId: number): void {
        if (this.departmentId !== departmentId) {
            this.departmentId = departmentId;
            this.queryProjectList('');
        }
    }

    @action.bound
    private formatProject(projectId: number): void {
        this.projectList.map(item => {
            if (item.key === '0') {
                item.key = projectId.toString();
                item.projectId = projectId;
                item.projectName = this.projectName;
                item.selectable = true;
            }
        });
        this.projectName = '';
        this.projectId = projectId.toString();
        this.projectList = [...this.projectList];
    }

    @action.bound
    public checkParams(): void {
        if (!this.departmentId) {
            message.warn('请选择部门');
            return;
        }
        if (!this.projectId) {
            message.warn('请选择项目');
            return;
        }
        this.bindModuleToProject();
    }

    // 获取全部部门列表
    @action.bound
    private async queryAllDepartmentList() {
        try {
            const result = await nsMockManageApiManageDepartmentQueryAllDepartmentListPost.remote();
            runInAction(() => {
                this.departmentList = result.treeInfos || [];
                eachNode(this.departmentList, item => {
                    item.key = item.departmentId.toString();
                    item.title = item.departmentName;
                    item.value = item.departmentId;
                    return false;
                });
            });
        } catch (e) {
        }
    }

    // 获取部门下的项目
    @action.bound
    public async queryProjectList(key: string) {
        try {
            const params = {
                departmentId: this.departmentId,
                key,
                filter: 1,
                currentPage: 1,
                pageSize: 99999
            };
            const result = await nsMockManageKoasApiManageProjectQueryProjectListPost.remote(params);
            runInAction(() => {
                this.projectList = result?.list || [];
                this.projectName = '';
            });
        } catch (e) {
        }
    }

    // 绑定模块到项目
    @Bind
    private async bindModuleToProject() {
        runInAction(() => this.moveLoading = true);
        try {
            const params = {
                departmentId: this.departmentId,
                moduleId: this.moduleId,
                projectId: Number(this.projectId),
                branchName: this.branchName
            };
            await nsMockManageKoasApiManageModuleBindModuleToProjectPost.remote(params);
            message.success('接口信息正在后台迁移，迁移完成时会kim通知您！');
            runInAction(() => this.moveLoading = false);
            // this.onMoveInProjectCallback && this.onMoveInProjectCallback(params);
            this.onCloseMoveInProjectModal();
        } catch (e) {
            runInAction(() => this.moveLoading = false);
        }
    }

    @Bind
    public async createProject(): Promise<void> {
        if (!this.departmentId) {
            message.warn('请选择部门～');
            return;
        }
        if (!this.projectName) {
            message.warn('请输入项目名称～');
            return;
        }
        try {
            runInAction(() => this.saveProjectLoading = true);
            const params = {
                projectName: this.projectName,
                projectDesc: '',
                departmentId: this.departmentId
            };
            const result = await nsMockManageKoasApiManageProjectCreateProjectPost.remote(params);
            result?.id && this.formatProject(result?.id);
            runInAction(() => this.saveProjectLoading = false);
        } catch {
            runInAction(() => this.saveProjectLoading = false);
        }
    }

    @Bind
    private async getHttModuleBranch(): Promise<void> {
        try {
            const params = { moduleId: this.moduleId };
            const result = await nsMockManageKoasApiManageModuleGetHttpModuleBranchGet.remote(params);
            runInAction(() => {
                this.branchNameList = ['最新分支', ...result.branchNameList];
            });
        } catch { }
    }
}
