import { AViewModel } from 'libs';
import { observable, action, runInAction, } from 'mobx';
import {
    nsMockPermissionBatchGetUserGet,
    nsMockManageKoasApiManageModuleDetailGet,
    nsMockManageKoasApiManageModuleDropListGet,
    nsMockManageKoasApiManageModuleCreatePost,
    nsMockManageKoasApiManageModuleUpdatePost,
    nsMockManageKoasApiManageModuleMergePost
} from '@/remote';
import { message, Modal } from 'antd';
import { CreateMaintainerModel } from '@/business/createMaintainer/CreateMaintainer';
import { team } from '@/business/global';
import { Debounce } from 'lodash-decorators/debounce';
import { Bind } from 'lodash-decorators';
import { operateTypeEnum } from './configure';

interface ICooperList {
    username: string;
    name: string;
    photo?: string;
    avatarUrl: string;
}

export class CreateModuleModalModel extends AViewModel {
    @observable public type: 0 | 1 | 2 = operateTypeEnum.NEW; // 0：新建 1：编辑 2：编辑部分
    @observable public createModuleModalVisible = false;
    @observable public projectId: number = 0;
    @observable public moduleId: number = 0;
    @observable public name: string = '';
    @observable public cooperList: ICooperList[] = [];
    @observable public moduleType: number = 1; // 1：已有模块 2：新建模块
    @observable public moduleName: string = '';
    @observable public moduleList: nsMockManageKoasApiManageModuleDropListGet.IModuleList[] = [];
    @observable public gitUrl: string = '';
    @observable public basePath: string = '/';
    @observable public mock: string = '';
    @observable public desc: string = '';

    public onCloseCallback?(type: 0 | 1 | 2): void;

    public createMaintainerModel = new CreateMaintainerModel();

    // 新建模块弹框加载
    @action
    public initLoading(type: 0 | 1 | 2, projectId: number, moduleId: number) {
        this.type = type;
        this.showCreateModuleModal(true);
        this.projectId = projectId || 0;
        if (this.type) {
            this.moduleId = moduleId || 0;
            this.detail();
        } else {
            const createUser = {
                username: team.getUserInfo().userName,
                name: team.getUserInfo().chineseName,
                avatarUrl: team.getUserInfo().photo || ''
            };
            this.cooperList = [createUser];
            this.dropList(this.moduleName);
        }
    }

    @action.bound
    public showCreateModuleModal(visible: boolean): void {
        this.createModuleModalVisible = visible;
    }

    // 关闭新建模块弹框
    @action.bound
    public onCloseCreateModuleModal() {
        this.initData();
        this.showCreateModuleModal(false);
        // this.onSearchModuleList && this.onSearchModuleList();
    }

    // 初始化数据
    @action.bound
    public initData(): void {
        this.type = operateTypeEnum.NEW;
        this.projectId = 0;
        this.moduleId = 0;
        this.name = '';
        this.cooperList = [];
        this.moduleType = 1;
        this.moduleName = '';
        this.gitUrl = '';
        this.basePath = '/';
        this.mock = '';
        this.desc = '';
        this.moduleList = [];
    }

    @action.bound
    public onChangeCooper(cooperList: ICooperList[]) {
        this.cooperList = cooperList;
    }

    // 切换关联模块
    @action.bound
    public onChangeModule(moduleId: number) {
        this.moduleId = moduleId;
    }

    // 保存模块
    @action.bound
    public onSave() {
        this.type ? this.update() : this.create();
    }

    // 搜索模块信息（防抖）
    @Debounce(300)
    @action
    public searchDropList(moduleName: string) {
        this.dropList(moduleName);
    }

    // 获取头像
    @action
    protected async batchGetUser(usernames: string[]) {
        if (!usernames?.length) {
            return;
        }
        try {
            const params = {
                usernames: usernames.join(',')
            };
            const result = await nsMockPermissionBatchGetUserGet.remote(params);
            runInAction(() => {
                this.cooperList = result?.list.map(item => {
                    return {
                        name: item.name,
                        username: item.username,
                        avatarUrl: item?.photo || ''
                    };
                }) || [];
            });
        } catch (e) {
        }
    }

    // 获取详情
    @action
    protected async detail() {
        try {
            const params = {
                moduleId: this.moduleId
            };
            const result = await nsMockManageKoasApiManageModuleDetailGet.remote(params);
            runInAction(() => {
                this.name = result.name;
                this.batchGetUser(result.cooperUsers);
                this.moduleType = result.moduleType;
                this.moduleName = result.moduleName;
                this.gitUrl = result.gitUrl;
                this.basePath = result.basePath;
                this.mock = result.mock;
                this.desc = result.desc;
                this.dropList(this.moduleName);
            });
        } catch (e) {
        }
    }

    // 获取已有模块列表
    @action
    protected async dropList(moduleName: string) {
        try {
            const params = {
                moduleName
            };
            const result = await nsMockManageKoasApiManageModuleDropListGet.remote(params);
            runInAction(() => {
                this.moduleList = result?.moduleList || [];
            });
        } catch (e) {
        }
    }

    // 校验参数必填项
    @action.bound
    protected checkParams() {
        if (!this.name) {
            message.warn('请填写模块名称～');
            return true;
        }
        if (this.moduleType === 1 && !this.moduleId) {
            message.warn('请选择已有模块');
            return true;
        }
        if (this.moduleType === 2 && !this.moduleName) {
            message.warn('请填写module name');
            return true;
        }
        return false;
    }

    @Bind
    public getParams(): nsMockManageKoasApiManageModuleCreatePost.IParams {
        const cooper: string[] = this.cooperList.map(item => item.username);
        const params = {
            projectId: this.projectId,
            name: this.name,
            cooper,
            moduleType: this.moduleType,
            moduleId: this.moduleId,
            moduleName: this.moduleType === 2 ? this.moduleName : '',
            gitUrl: this.moduleType === 2 ? this.gitUrl : '',
            basePath: this.basePath,
            mock: this.mock,
            desc: this.desc
        };
        if (!this.type) {
            params['moduleId'] = this.moduleType === 1 ? this.moduleId : 0;
        }
        return params;
    }

    // 新建模块关联项目
    @action
    protected async create() {
        if (this.checkParams()) {
            return;
        }
        try {
            const params = this.getParams();
            await nsMockManageKoasApiManageModuleCreatePost.remote(params);
            runInAction(() => {
                this.onCloseCreateModuleModal();
                this.onCloseCallback && this.onCloseCallback(this.type);
                message.success('关联成功');
            });
        } catch (e) {
        }
    }

    // 编辑模块
    @Bind
    protected async update(): Promise<void> {
        if (this.checkParams()) {
            return;
        }
        try {
            const params = this.getParams();
            const result = await nsMockManageKoasApiManageModuleUpdatePost.remote(params);
            if (result?.result) {
                this.onCloseCreateModuleModal();
                this.onCloseCallback && this.onCloseCallback(this.type);
                message.success('修改成功');
            } else if (this.type !== operateTypeEnum.EDIT_PART) {
                runInAction(() => {
                    this.showCreateModuleModal(false);
                    Modal.confirm({
                        content: '该gitUrl与moduleName已存在，是否将两个模块进行合并',
                        onOk: () => this.merge(),
                        onCancel: () => this.showCreateModuleModal(true)
                    });
                });
            }
        } catch (e) {
        }
    }

    @action.bound
    public async merge(): Promise<void> {
        try {
            const params = this.getParams();
            await nsMockManageKoasApiManageModuleMergePost.remote(params);
            this.onCloseCreateModuleModal();
            this.onCloseCallback && this.onCloseCallback(this.type);
            message.success('修改成功');
        } catch {
        }
    }
}
