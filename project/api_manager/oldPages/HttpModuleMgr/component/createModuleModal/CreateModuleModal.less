.createModuleModalWrap {

  .formRowLabel {
    color: #898a8c;
    margin: 24px 0 8px 0;

    .formRowLabelRequired {
      color: #ff4d4f;
    }
  }

  .width100 {
    width: 100%;
  }

  .nameTag {
    background-color: #ffffff;
    padding: 3px;
    border-radius: 15px;
    border: 0;
    font-size: 14px;

    .closeIcon {
      visibility: hidden;
      color: #bbbdbf;
      position: relative;
      top: -11px;
      left: 8px;
      font-size: 15px;
      margin-left: -8px;
      transition: all ease 0.1s;
      cursor: pointer;
    }

    :global {
      .ant-tag-close-icon {
        display: none;
      }
    }
  }

  .nameTag:hover {
    background-color: #ebefff;

    .closeIcon {
      visibility: unset;
    }
  }

  :global {
    .ant-modal-body {
      padding-top: 0;
    }
  }
}
