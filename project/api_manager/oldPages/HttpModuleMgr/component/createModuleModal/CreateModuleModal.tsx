import { AView } from 'libs';
import React from 'react';
import { CreateModuleModalModel } from './CreateModuleModalModel';
import { observer } from 'mobx-react';
import {
    Input, Modal, Radio, Select, Tooltip
} from 'antd';
import Bind from 'lodash-decorators/bind';
import css from './CreateModuleModal.less';
import { bindObserver } from '@libs/mvvm';
import { KDevParticipants } from '../../../../src/business/commonComponents/KDevParticipants';
import { QuestionCircleOutlined } from '@ant-design/icons';
import { operateTypeEnum } from './configure';

const { TextArea } = Input;
const { Option } = Select;

const Input_name = bindObserver(Input, 'name');
const RadioGroup_moduleType = bindObserver(Radio.Group, 'moduleType');
const Input_moduleName = bindObserver(Input, 'moduleName');
const Input_gitUrl = bindObserver(Input, 'gitUrl');
const Input_basePath = bindObserver(Input, 'basePath');
const Input_mock = bindObserver(Input, 'mock');
const TextArea_desc = bindObserver(TextArea, 'desc');

@observer
export class CreateModuleModal extends AView<CreateModuleModalModel> {

    // 搜索模块信息列表
    @Bind
    protected onSearchDropList(moduleName: string) {
        this.model.searchDropList(moduleName);
    }

    @Bind
    protected renderModuleInfo(model): React.ReactNode {
        if (model.moduleType === 1) {
            return <>
                <div className={css.formRowLabel}>模块信息 <span className={css.formRowLabelRequired}>*</span></div>
                <Select
                    className={css.width100}
                    value={model.moduleId || undefined}
                    onChange={model.onChangeModule}
                    placeholder={'请选择已有模块'}
                    showSearch
                    onSearch={this.onSearchDropList}
                    filterOption={false}
                    disabled={model.type === operateTypeEnum.EDIT_PART}
                >
                    {
                        model.moduleList.map(item => {
                            return <Option key={item.id} value={item.id}>{item.moduleName}</Option>;
                        })
                    }
                </Select>
            </>;
        } else {
            return <>
                <div className={css.formRowLabel}>
                    module name <span className={css.formRowLabelRequired}>*</span>
                </div>
                <Input_moduleName
                    model={model}
                    placeholder={'请输入'}
                    disabled={model.type === operateTypeEnum.EDIT_PART}
                />
                <div className={css.formRowLabel}>git地址：</div>
                <Input_gitUrl
                    model={model}
                    placeholder={'请输入git地址'}
                    disabled={model.type === operateTypeEnum.EDIT_PART}
                />
            </>;
        }
    }

    public render(): React.ReactNode {
        const model = this.model;
        return (
            <Modal
                title={model.moduleId ? '编辑模块' : '关联模块'}
                visible={model.createModuleModalVisible}
                className={css.createModuleModalWrap}
                onCancel={model.onCloseCreateModuleModal}
                onOk={model.onSave}
                maskClosable={false}
            >
                <div className={css.formRowLabel}>模块名称 <span className={css.formRowLabelRequired}>*</span></div>
                <Input_name
                    model={model}
                    placeholder={'请输入模块名称'}
                    autoFocus
                    disabled={model.type === operateTypeEnum.EDIT_PART}
                />
                <div className={css.formRowLabel}>参与人</div>
                <KDevParticipants
                    value={model.cooperList}
                    onChange={model.onChangeCooper}
                />
                <div className={css.formRowLabel}>模块来源</div>
                <RadioGroup_moduleType model={model} disabled={Boolean(model.type)}>
                    <Radio value={1}>已有模块</Radio>
                    <Radio value={2}>新建模块</Radio>
                </RadioGroup_moduleType>
                {this.renderModuleInfo(model)}
                <div className={css.formRowLabel}>API基本路径</div>
                <Input_basePath model={model} placeholder={'请输入'} />
                <div className={css.formRowLabel}>
                    后端服务地址 <Tooltip title={'后端服务地址可以是域名，也可以是IP，录入时，要以http://或者https://开头'}>
                        <QuestionCircleOutlined />
                    </Tooltip>
                </div>
                <Input_mock model={model} placeholder={'请输入后端服务地址'} />
                <div className={css.formRowLabel}>描述</div>
                <TextArea_desc model={model} className={css.width450} placeholder={'描述信息'} />
            </Modal>
        );
    }
}
