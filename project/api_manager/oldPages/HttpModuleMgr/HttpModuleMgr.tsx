import React from 'react';
import APage from '@/pages/APage';
import { observer } from 'mobx-react';
import { bindObserver, router } from '@libs/mvvm';
import { Bind } from 'lodash-decorators';
import { HttpModuleMgrM } from './HttpModuleMgrM';
import css from './HttpModuleMgr.less';
import {
    Button, Input, Modal, Table, Tooltip
} from 'antd';
import { KdevIconFont } from '@/business/commonComponents/KdevIconFont';
import { IQuery } from './configure';
import { MoveInProject } from '../httpMoveInProject/MoveInProject';
import { ModuleDetail } from './ModuleDetail/ModuleDetail';
import { CreateModuleModal } from '@/business/createModuleModal/CreateModuleModal';
import { ERouter } from 'CONFIG';

const { Search } = Input;

const Search_key = bindObserver(Search, 'key');

@observer
export default class HttpMockApi extends APage<IQuery, HttpModuleMgrM> {
    protected createModel(): HttpModuleMgrM {
        return new HttpModuleMgrM(this.query);
    }

    @Bind
    private columns(): any[] {
        const column: any[] = [
            {
                title: '模块名称',
                // dataIndex: 'moduleName',
                width: 120,
                render: this.renderModuleName
            },
            {
                title: 'git项目',
                dataIndex: 'gitUrl',
                width: 200,
                render: text => <span className={css.gitUrl}>{text}</span>
            },
            {
                title: '创建人',
                dataIndex: 'createUser',
                width: 100
            },
            {
                title: '参与人',
                dataIndex: 'cooperUser',
                width: 120,
                render: text => text.join('、')
            },
            {
                title: '最后更新时间',
                dataIndex: 'lastUpdateTime',
                width: 120
            },
            {
                title: '操作',
                width: 120,
                render: this.renderOperate
            }
        ];
        return column;
    }

    @Bind
    private renderModuleName(record): React.ReactNode {
        const goApiList = (): void => {
            router.push(ERouter.API_MOCK_HTTP_API_MGR, {
                moduleId: record.moduleId,
                moduleName: record.moduleName
            });
        };
        return (
            <a onClick={goApiList}>
                {record.moduleName}
            </a>
        );
    }

    @Bind
    private renderOperate(record): React.ReactNode {
        const model = this.model;
        return (
            <>
                <Tooltip title={'详情'}>
                    <Button
                        icon={<KdevIconFont id={'#iconxiangqing'} />}
                        className={css.margR8}
                        onClick={() => model.onOpenModuleDetail(record.moduleId)}
                    />
                </Tooltip>
                <Tooltip title={'编辑'}>
                    <Button
                        icon={<KdevIconFont id={'#iconxingzhuangjiehe'} />}
                        className={css.margR8}
                        onClick={() => model.onOpenCreateModuleModal(record)}
                    />
                </Tooltip>
                <Tooltip title={'移入项目'}>
                    <Button
                        icon={<KdevIconFont id={'#iconAddfolder'} />}
                        onClick={() => model.onOpenMoveInProject(record.moduleId)}
                    />
                </Tooltip>
            </>
        );
    }

    @Bind
    private renderTop(): React.ReactNode {
        const model = this.model;
        return (
            <Search_key
                model={model}
                placeholder={'支持 模块名和git地址 搜索'}
                className={css.keyWord}
                onSearch={() => model.onChangePageInfo(1)}
            />
        );
    }

    @Bind
    private renderTable(): React.ReactNode {
        const model = this.model;
        return (
            <Table
                columns={this.columns()}
                dataSource={model.moduleList}
                className={css.table}
                rowKey={'moduleId'}
                bordered
                loading={model.tableLoading}
                pagination={{
                    current: model.currentPage,
                    pageSize: model.pageSize,
                    showTotal: total => `共 ${total} 条`,
                    total: this.model.total,
                    showSizeChanger: true,
                    onChange: model.onChangePageInfo,
                }}
            />
        );
    }

    public renderContent(): React.ReactNode {
        const model = this.model;
        return (
            <div className={css.httpModuleMgrWrap}>
                {this.renderTop()}
                {this.renderTable()}
                <MoveInProject model={model.moveInProjectM} />
                <ModuleDetail model={model.moduleDetailM} />
                <CreateModuleModal model={model.createModuleModalM} />
            </div>
        );
    }
}
