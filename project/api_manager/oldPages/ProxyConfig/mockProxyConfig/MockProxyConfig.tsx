import React from 'react';
import { observer } from 'mobx-react';
import APage from '@/pages/APage';
import { BackHeader } from '@/business/commonComponents';
import { IQuery, MockProxyConfigM } from './MockProxyConfigM';
import { MockProxyConfigs } from '@/business/httpApiComponents/mockProxyConfigs/MockProxyConfigs';
import css from './MockProxyConfig.less';

@observer
export default class MockProxyConfig extends APage<IQuery, MockProxyConfigM> {
    protected createModel(): MockProxyConfigM {
        return new MockProxyConfigM(this.query);
    }

    private renderProxyConfigDesc(): React.ReactNode {
        return (
            <div className={css.proxyConfigTop}>
                <span>代理配置</span>
                <div className={css.desc}>
                    web mock代理工具是专门为前端开发的代理接口，可以等同于webpack的proxyTable工具。<br />
                    相比proxyTable，其优点是可以动态的配置请求的代理地址，并支持一键将接口path代理到对应的mock接口中，减少了频繁复制粘贴地址的麻烦。
                </div>
            </div>
        );
    }

    public renderContent(): React.ReactNode {
        const model = this.model;
        return (
            <div className={css.mockProxyConfig}>
                <BackHeader
                    title={this.renderProxyConfigDesc()}
                    url={model.goBackUrl}
                    hrefType={2}
                    onGoBack={model.isChange}
                />
                {
                    this.model.mockProxyConfigsM &&
                    <MockProxyConfigs model={this.model.mockProxyConfigsM} className={css.mockProxyConfigsWrap} />
                }
            </div>
        );
    }
}
