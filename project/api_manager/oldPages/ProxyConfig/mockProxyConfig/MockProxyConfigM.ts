import APageModel from '@/pages/APageModel';
import { action } from 'mobx';
import { ERouter } from 'CONFIG';
import { MockProxyConfigsM } from 'oldPages/OrganizeSpace/component/mockProxyConfigs/MockProxyConfigsM';

export interface IQuery {
    moduleId: number;
}

export class MockProxyConfigM extends APageModel<IQuery> {
    public moduleId: number = 0;
    public goBackUrl: string = `${ERouter.API_MOCK_ORGANIZESPACE_PROJECTMGR}${location.search}`;

    public mockProxyConfigsM: MockProxyConfigsM | null = null;

    protected getQueryFields(): Array<keyof IQuery> {
        return ['moduleId'];
    }

    constructor(query: IQuery) {
        super(query);
        this.initByQueryFields(query);
        this.init();
    }

    @action
    private init() {
        this.moduleId = Number(this.moduleId) || 0;
        if (!this.mockProxyConfigsM) {
            this.mockProxyConfigsM = new MockProxyConfigsM(this.moduleId);
        }
    }

    @action.bound
    public isChange(): void {
        this.mockProxyConfigsM?.isChange();
    }

}
