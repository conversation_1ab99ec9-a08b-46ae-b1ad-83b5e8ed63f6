import React from 'react';
import APage from '@/pages/APage';
import { observer, Provider } from 'mobx-react';
import { GrpcModuleM } from './GrpcModuleM';
import css from './GrpcModule.less';
import { Bind } from 'lodash-decorators';
import { Select, Tabs, Breadcrumb } from 'antd';
import { bindObserver } from '@libs/mvvm';
import { KdevIconFont } from '@/business/commonComponents';
import { moduleActiveKeyEnum, IQuery } from './configure';
import { ERouter } from 'CONFIG';
import { ModuleDetail } from './moduleDetail/ModuleDetail';
import { ApiMgr } from './apiMgr/ApiMgr';

const { TabPane } = Tabs;

const Select_moduleId = bindObserver(Select, 'moduleId');

@observer
export default class GrpcModule extends APage<IQuery, GrpcModuleM> {
    protected createModel(): GrpcModuleM {
        return new GrpcModuleM(this.query);
    }

    @Bind
    private renderModule(): React.ReactChild {
        const model = this.model;
        return (
            <Select_moduleId
                model={model}
                bordered={false}
                options={model.moduleList}
                onChange={model.changeModuleId}
                dropdownMatchSelectWidth={false}
            />
        );
    }

    @Bind
    private renderBreadcrumb(): React.ReactChild {
        const model = this.model;
        const moduleMgrUrl = `${ERouter.API_MOCK_PROJECT_MODULEMGR}?projectId=${model.projectId}&activeKey=2`;
        return (
            <Breadcrumb separator="" className={css.breadcrumb}>
                <Breadcrumb.Item href={moduleMgrUrl}>
                    <KdevIconFont id="#iconyemian-fanhui1" className={css.backBtn} />
                </Breadcrumb.Item>
                <Breadcrumb.Separator>
                    <KdevIconFont id="#iconvertical-bar" className={css.verticalBarIcon} />
                </Breadcrumb.Separator>
                <Breadcrumb.Item href={`${ERouter.API_MOCK_ORGANIZESPACE_PROJECTMGR}`} onClick={model.setDepartment}>
                    {model.departmentFullName}
                </Breadcrumb.Item>
                <Breadcrumb.Separator>
                    <KdevIconFont id="#iconqianjin1" className={css.forwardIcon} />
                </Breadcrumb.Separator>
                <Breadcrumb.Item href={moduleMgrUrl}>{model.projectName}</Breadcrumb.Item>
                <Breadcrumb.Separator>
                    <KdevIconFont id="#iconqianjin1" className={css.forwardIcon} />
                </Breadcrumb.Separator>
                <Breadcrumb.Item>{this.renderModule()}</Breadcrumb.Item>
            </Breadcrumb>
        );
    }

    public renderContent(): React.ReactNode {
        const model = this.model;
        return (
            <div className={css.grpcModule}>
                {this.renderBreadcrumb()}
                <Tabs
                    activeKey={model.activeKey}
                    onChange={model.changeActiveKey}
                    className={css.tabs}
                >
                    <TabPane tab="模块概况" key={moduleActiveKeyEnum.DETAIL}>
                        {model.moduleDetailM && <ModuleDetail model={model.moduleDetailM} />}
                    </TabPane>
                    <TabPane tab="API列表" key={moduleActiveKeyEnum.API}>
                        {model.apiMgrM && <ApiMgr model={model.apiMgrM} />}
                    </TabPane>
                </Tabs>
            </div>
        );
    }
}
