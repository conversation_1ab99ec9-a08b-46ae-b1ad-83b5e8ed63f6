import APageModel from '@/pages/APageModel';
import { action, observable, runInAction } from 'mobx';
import {
    nsMockManageApiManageRpcGetModulesByProjectIdPost,
    nsMockManageKoasApiManageModuleQueryDepartmentAndProjectByIdGet
} from '@/remote';
import { moduleActiveKeyEnum, IQuery, IOption } from './configure';
import { Bind } from 'lodash-decorators';
import { pushKey } from '@/index.config/tools';
import { ModuleDetailM } from './moduleDetail/ModuleDetailM';
import { ApiMgrM } from './apiMgr/ApiMgrM';
import { showTypeEnum } from './apiMgr/configure';
import { departmentCascader } from '@/business/global/departmentCascader';

export class GrpcModuleM extends APageModel<IQuery> {
    @observable public projectId: number = 0;
    @observable public moduleId: number = 0;
    @observable public moduleList: IOption[] = [];
    @observable public activeKey: string = moduleActiveKeyEnum.API;

    private departmentIds: number[] = [];
    @observable public departmentFullName: string = '';
    @observable public projectName: string = '';

    public moduleDetailM?: ModuleDetailM;
    public apiMgrM?: ApiMgrM;

    protected getQueryFields(): Array<keyof IQuery> {
        return [
            'projectId',
            'moduleId',
            'activeKey',
        ];
    }

    constructor(query: IQuery) {
        super(query);
        this.initByQueryFields(query);
        this.initParams();
        this.initLoading();
    }

    @action
    private initParams(): void {
        this.projectId = Number(this.projectId) || 0;
        this.moduleId = Number(this.moduleId) || 0;
        this.changeActiveKey(this.activeKey);
    }

    @action
    private initLoading(): void {
        this.getModulesByProjectId();
        this.queryDepartmentAndProjectById();
    }

    // 切换tabs
    @action.bound
    public changeActiveKey(activeKey: string) {
        this.activeKey = activeKey;
        pushKey({ activeKey: this.activeKey });
        switch (activeKey) {
            case moduleActiveKeyEnum.DETAIL:
                if (!this.moduleDetailM) {
                    this.moduleDetailM = new ModuleDetailM();
                }
                break;
            case moduleActiveKeyEnum.API:
                if (!this.apiMgrM) {
                    this.apiMgrM = new ApiMgrM();
                }
                break;
        }
    }

    // 获取rpc模块列表
    @Bind
    private async getModulesByProjectId(): Promise<void> {
        try {
            const params = {
                projectId: this.projectId
            };
            const result = await nsMockManageApiManageRpcGetModulesByProjectIdPost.remote(params);
            runInAction(() => {
                this.moduleList = [];
                result?.list?.forEach(item => {
                    this.moduleList.push({
                        label: item.moduleName,
                        value: item.id
                    });
                });
            });
        } catch {
        }
    }

    // 切换模块
    @action.bound
    public changeModuleId(): void {
        pushKey({
            moduleId: this.moduleId,
            showType: showTypeEnum.NONE,
            selectedKey: ''
        });
        if (this.moduleDetailM) {
            this.moduleDetailM.initParams();
            this.moduleDetailM.initLoading();
        }
        if (this.apiMgrM) {
            this.apiMgrM.setApiTreeSelectedKey('');
            this.apiMgrM.initParams();
            this.apiMgrM.initLoading();
        }
    }

    // 获取部门项目信息
    @Bind
    private async queryDepartmentAndProjectById(): Promise<void> {
        try {
            const params = {
                projectId: this.projectId
            };
            const result = await nsMockManageKoasApiManageModuleQueryDepartmentAndProjectByIdGet.remote(params);
            runInAction(() => {
                this.departmentIds = result?.departmentIds || [];
                this.departmentFullName = result?.departmentNames?.join('/') || '';
                this.projectName = result?.projectName || '';
            });
        } catch {}
    }

    // 设置部门
    @action.bound
    public setDepartment() {
        if (this.departmentIds.length && this.departmentFullName) {
            departmentCascader.setDepartment(this.departmentIds, this.departmentFullName);
        }
    }
}
