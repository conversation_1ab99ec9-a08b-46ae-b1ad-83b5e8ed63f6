import { <PERSON><PERSON>ie<PERSON> } from 'libs';
import React from 'react';
import { ApiDetailM } from './ApiDetailM';
import { observer } from 'mobx-react';
import css from './ApiDetail.less';
import { Tabs, Divider, Tooltip, Button } from 'antd';
import { Bind } from 'lodash-decorators';
import { KdevIconFont } from '@/business/commonComponents';
import { GrpcBaseInfo } from './grpcBaseInfo/GrpcBaseInfo';
import { GrpcRequest } from './grpcRequest/GrpcRequest';
import { GrpcResponse } from './grpcResponse/GrpcResponse';
import { showTypeEnum } from '../configure';

const { TabPane } = Tabs;

@observer
export class ApiDetail extends AView<ApiDetailM> {

    @Bind
    private renderApiTitle(): React.ReactNode {
        const model = this.model;
        return (
            <div className={css.apiTitle}>
                <span onClick={() => model.onChangeShowType(showTypeEnum.NONE)}>
                    <KdevIconFont id="#iconyemian-fanhui1" className={css.backIcon}/>
                </span>
                <Divider type={'vertical'} className={css.dividerVertical} />
                <Tooltip title={model.methodName}>
                    <div className={css.methodName}>
                        {model.methodName}
                    </div>
                </Tooltip>
                <span className={css.updateTime}>更新时间：{model.updateTime}</span>
            </div>
        );
    }

    @Bind
    private renderTabBarExtraContent(): React.ReactChild {
        return (
            <Button
                icon={<KdevIconFont id="#iconxingzhuangjiehe"/>}
                onClick={() => this.model.onChangeShowType(showTypeEnum.EDIT)}
            />
        );
    }

    @Bind
    private renderApiTabs(): React.ReactChild {
        const model = this.model;
        return (
            <Tabs className={css.apiTabs} tabBarExtraContent={this.renderTabBarExtraContent()}>
                <TabPane tab="API详情">
                    <GrpcBaseInfo model={model.grpcBaseInfoM} />
                    <GrpcRequest model={model.grpcRequestM} />
                    <GrpcResponse model={model.grpcResponseM} />
                </TabPane>
            </Tabs>
        );
    }

    public render(): React.ReactNode {
        return (
            <div className={css.apiDetail}>
                {this.renderApiTitle()}
                {this.renderApiTabs()}
            </div>
        );
    }
}
