import { AView } from 'libs';
import React from 'react';
import { observer } from 'mobx-react';
import { ViewJsonTableM } from './ViewJsonTableM';
import Bind from 'lodash-decorators/bind';
import css from './ViewJsonTable.less';
import { ResizableTable, KEYS_TABLE } from '@/business/commonComponents/ResizableTable';

interface IProps {
    unifiedId?: KEYS_TABLE;
    className?: string;
    valueTitle?: string;
    checkColumnKey?: string;
    enableResize?: boolean;
}

@observer
export class ViewJsonTable extends AView<ViewJsonTableM, IProps> {

    @Bind
    public componentDidMount(): void {
        this.model.onChangeTableLoading(true);
    }

    @Bind
    private renderName(record): React.ReactNode {
        return (
            <div className={css.name}>
                {record.name}
            </div>
        );
    }

    @Bind
    private renderValue(record): React.ReactNode {
        if (this.model.type === 'form' && record.type === 'file' && record.value) {
            return (
                <a href={`${record.value.split('|')[1]}`}>{record.value.split('|')[0]}</a>
            );
        }
        if (record.type === 'boolean') {
            return `${record.value}`;
        }
        return record.value;
    }

    @Bind
    public columns(): any[] {
        const column: any[] = [
            {
                title: '名称',
                // dataIndex: 'name',
                key: 'name',
                width: 400,
                disabled: true,
                render: this.renderName
            },
            {
                title: '类型',
                dataIndex: 'type',
                key: 'type',
                width: 100,
                disabled: true,
            },
            {
                title: '必填',
                dataIndex: 'required',
                key: 'required',
                width: 100,
                disabled: true,
                render: text => text ? '是' : '否'
            },
            {
                title: '参数值',
                // dataIndex: 'value',
                key: 'value',
                width: 100,
                disabled: true,
                render: this.renderValue
            },
            {
                title: '备注',
                dataIndex: 'description',
                width: 100,
                key: 'description',
            }
        ];
        return column;
    }

    // 添加行className
    @Bind
    private rowClassName(record): string {
        if (record.children) {
            return '';
        }
        return css.indentBorder;
    }

    public render(): React.ReactNode {
        const model = this.model;
        return (
            <div>
                {
                    model.tableLoading &&
                    <ResizableTable
                        columns={this.columns()}
                        dataSource={model.list}
                        bordered
                        pagination={false}
                        rowKey={'key'}
                        defaultExpandAllRows
                        rowClassName={this.rowClassName}
                        expandable={{
                            expandedRowKeys: model.expandedRowKeys,
                            onExpand: model.onExpandRowKeys
                        }}
                        className={`${this.props.className} ${css.viewJsonTable}`}
                        enableResize={this.props.enableResize}
                    />
                }
            </div>
        );
    }
}
