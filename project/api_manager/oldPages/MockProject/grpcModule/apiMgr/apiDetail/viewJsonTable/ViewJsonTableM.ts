import { AViewModel } from 'libs';
import { observable, action } from 'mobx';
import { formatPath } from '@/index.config/tools';

interface IList {
    key: string;
    name: string;
    type: string;
    value: string;
    required: boolean;
    description: string;
    children?: IList[];
}

export class ViewJsonTableM extends AViewModel {
    @observable public list: IList[] = [];
    @observable public expandedRowKeys: string[] = [];
    @observable public tableLoading: boolean = false;
    @observable public type: string = '';

    constructor(query: string = '') {
        super();
        this.init(query);
    }

    @action.bound
    private init(type: string) {
        this.type = type;
    }

    // column加载完毕再渲染table
    @action
    public onChangeTableLoading(tableLoading: boolean): void {
        this.tableLoading = tableLoading;
    }

    @action.bound
    public setData(list?: IList[]): void {
        this.list = list || this.list;
    }

    // 初始化数据
    @action.bound
    public initData() {
        this.list = [];
        this.expandedRowKeys = [];
    }

    // 控制展开行
    @action.bound
    public onExpandRowKeys(expanded: boolean, record): void {
        if (expanded) {
            if (!this.expandedRowKeys.includes(record.key)) {
                this.expandedRowKeys.push(record.key);
                this.expandedRowKeys = [...this.expandedRowKeys];
            }
        } else {
            this.expandedRowKeys = this.expandedRowKeys.filter(item => item !== record.key);
        }
    }

    @action.bound
    public onExpandAllKeys(boo?: boolean): void {
        if (boo) {
            this.expandedRowKeys = formatPath(this.list)[1];
        } else {
            this.expandedRowKeys = [];
        }
    }

}
