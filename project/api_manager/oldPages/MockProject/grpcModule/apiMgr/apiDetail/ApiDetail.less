.apiDetail {
    height: calc(100% - 58px);
    padding: 0 16px;

    .apiTitle {
        display: flex;
        // justify-content: space-between;
        align-items: center;
        padding-top: 16px;

        .backIcon {
            color: #252626;
            font-size: 26px;
            cursor: pointer;
        }

        .dividerVertical {
            height: 20px;
        }

        .methodName {
            flex: 1;
            font-weight: bolder;
            font-size: 16px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }

        .updateTime {
            color: #898a8c;
            font-size: 12px;
            margin-left: 8px;
        }
    }

    .apiTabs {
        height: 100%;
        :global {
            .ant-tabs-content-holder {
                height: 100%;
                overflow: auto;
            }
        }
    }

}