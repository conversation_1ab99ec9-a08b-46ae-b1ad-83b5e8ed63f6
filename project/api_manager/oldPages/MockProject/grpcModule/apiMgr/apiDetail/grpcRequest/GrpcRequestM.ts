import { AViewModel } from 'libs';
import { observable, action } from 'mobx';
import { JsonDetailM } from '@/business/commonComponents';
import { ViewJsonTableM } from '../viewJsonTable/ViewJsonTableM';
import { nsMockManageApiManageRpcQueryApiDetailByIdPost } from '@/remote';
import { formatPath } from '@/index.config/tools';

export class GrpcRequestM extends AViewModel {
    @observable public isShowBodyTable: boolean = false;

    public bodyViewJsonTableM = new ViewJsonTableM();
    public jsonDetailM = new JsonDetailM();

    @action.bound
    public setData(request: nsMockManageApiManageRpcQueryApiDetailByIdPost.IRequest): void {
        this.initData();
        const [list] = formatPath([request]);
        this.isShowBodyTable = Boolean(list.length);
        this.bodyViewJsonTableM.setData(list as any[]);
        this.isExpandAllKeys(true);
    }

    @action.bound
    public initData(): void {
        this.isShowBodyTable = false;
        this.bodyViewJsonTableM.initData();
    }

    @action.bound
    public isExpandAllKeys(boo: boolean): void {
        this.bodyViewJsonTableM.onExpandAllKeys(boo);
    }
}
