import { AViewModel } from 'libs';
import { action, observable, runInAction } from 'mobx';
import { Bind } from 'lodash-decorators';
import { nsMockPermissionBatchGetUserGet, nsMockManageApiManageRpcGetApiAttributesPost } from '@/remote';
import { IUserInfo } from './configure';
import { readOnlyEnum } from '@/business/grpcComponents/grpcGlobalDefine';

export class GrpcBaseInfoM extends AViewModel {
    private apiId: number = 0;
    @observable public name: string = '';
    @observable public serviceName: string = '';
    @observable public kessName: string = '';
    @observable public ownerInfo: IUserInfo[] = [];
    @observable public version: string = '';
    @observable public priority: string = '';
    @observable public readOnly: string = readOnlyEnum.READ;
    @observable public moduleType: 'KRPC' | 'GRPC' = 'GRPC';
    @observable public methodName: string = '';

    @action.bound
    public setBaseInfo(baseInfo): void {
        this.initData();
        this.apiId = baseInfo.id;
        this.name = baseInfo.name;
        this.serviceName = baseInfo.serviceName;
        this.kessName = baseInfo.kessName;
        this.version = baseInfo.version;
        this.methodName = baseInfo.methodName;
        this.moduleType = baseInfo.moduleType;
        this.getOwnerInfo(baseInfo.owner);
        this.getApiAttributes();
    }

    @action
    private initData() {
        this.name = '';
        this.serviceName = '';
        this.kessName = '';
        this.ownerInfo = [];
        this.version = '';
        this.priority = '';
        this.readOnly = readOnlyEnum.READ;
    }

    @Bind
    private async getOwnerInfo(userName: string): Promise<void> {
        const users = await nsMockPermissionBatchGetUserGet.batchGetUser(userName);
        runInAction(() => {
            this.ownerInfo = users;
        });
    }

    @Bind
    private async getApiAttributes(): Promise<void> {
        if (!this.apiId) {
            return;
        }
        try {
            const params = {
                apiId: this.apiId
            };
            const result = await nsMockManageApiManageRpcGetApiAttributesPost.remote(params);
            runInAction(() => {
                result.list.forEach(item => {
                    if (item.key === 'priority') {
                        this.priority = item.val;
                    }
                    if (item.key === 'readOnly') {
                        this.readOnly = item.val;
                    }
                });
            });
        } catch {}
    }
}
