import React from 'react';
import { observer } from 'mobx-react';
import { AView } from 'libs';
import { Row, Col } from 'antd';
import { CopyBtn, AvatarList, ApiCollapse } from '@/business/commonComponents';
import { GrpcBaseInfoM } from './GrpcBaseInfoM';
import css from './GrpcBaseInfo.less';
import { readOnlyEnum } from '@/business/grpcComponents/grpcGlobalDefine';
import classNames from 'classnames';

@observer
export class GrpcBaseInfo extends AView<GrpcBaseInfoM> {

    private renderServiceNameAndModuleType() {
        const model = this.model;
        return (
            <Row className={classNames(css.contentRow, css.margT0)}>
                方法名：
                <span className={css.moduleType}>{model.moduleType}</span>
                {model.methodName}
            </Row>
        );
    }

    private renderApiName(): React.ReactNode {
        const model = this.model;
        return (
            <div className={css.nameWrap}>
                <div className={css.name}>API名称：{model.name}</div>
                <CopyBtn copyContent={model.name} />
            </div>
        );
    }

    private renderServiceName(): React.ReactNode {
        const model = this.model;
        return (
            <Row className={css.contentRow}>
                ServiceName：{model.serviceName}
                <CopyBtn copyContent={model.serviceName} />
            </Row>
        );
    }

    private renderKessName(): React.ReactNode {
        const model = this.model;
        return (
            <Row className={css.contentRow}>
                KessName：{model.kessName}
                <CopyBtn copyContent={model.kessName} />
            </Row>
        );
    }

    private renderOwnerAndVersion(): React.ReactNode {
        const model = this.model;
        return (
            <Row className={css.contentRow}>
                <Col span={6}>最后编译人：<AvatarList reviewers={model.ownerInfo} /></Col>
                <Col span={6}>版本：{model.version}</Col>
                <Col span={6}>优先级：{model.priority}</Col>
                <Col span={6}>读写属性：{model.readOnly === readOnlyEnum.READ ? '读' : '写'}</Col>
            </Row>
        );
    }

    public render(): React.ReactNode {
        return (
            <ApiCollapse header="基本信息" className={css.grpcBaseInfo}>
                {this.renderServiceNameAndModuleType()}
                {this.renderApiName()}
                {this.renderServiceName()}
                {this.renderKessName()}
                {this.renderOwnerAndVersion()}
            </ApiCollapse>
        );
    }
}
