import { AViewModel } from 'libs';
import { observable, action, runInAction, } from 'mobx';
import { nsMockManageApiManageRpcQueryApiDetailByIdPost } from '@/remote';
import * as QS from 'query-string';
import { Bind } from 'lodash-decorators';
import { getKey0OrKey1, pushKey } from '@/index.config/tools';
import { GrpcBaseInfoM } from './grpcBaseInfo/GrpcBaseInfoM';
import { GrpcRequestM } from './grpcRequest/GrpcRequestM';
import { GrpcResponseM } from './grpcResponse/GrpcResponseM';
import { ApiMgrM } from '../ApiMgrM';
import { showTypeEnum } from '../configure';
import moment from 'moment';

export class ApiDetailM extends AViewModel {
    private groupId: number = 0;
    private apiId: number = 0;
    @observable public methodName: string = '';
    @observable public updateTime: string = '';

    public grpcBaseInfoM = new GrpcBaseInfoM();
    public grpcRequestM = new GrpcRequestM();
    public grpcResponseM = new GrpcResponseM();

    private parentThis: ApiMgrM;

    constructor(parentThis: ApiMgrM) {
        super();
        this.parentThis = parentThis;
    }

    @action
    public initParams(): void {
        const urlParams: object = QS.parse(location.search);
        this.groupId = getKey0OrKey1(urlParams['selectedKey'] || '')[0] || this.groupId;
        this.apiId = getKey0OrKey1(urlParams['selectedKey'] || '')[1] || this.apiId;
    }

    @action.bound
    public initLoading() {
        this.queryApiDetailById();
    }

    // 获取API详情
    @Bind
    private async queryApiDetailById() {
        try {
            const params = {
                id: this.apiId
            };
            const result = await nsMockManageApiManageRpcQueryApiDetailByIdPost.remote(params);
            runInAction(() => {
                if (result?.baseInfo) {
                    this.methodName = result?.baseInfo?.methodName || '';
                    this.updateTime = moment(result?.baseInfo?.updateTime || '').format('YYYY-MM-DD HH:mm:ss');
                    this.grpcBaseInfoM.setBaseInfo(result.baseInfo);
                }
                result?.request && this.grpcRequestM.setData(result.request);
                result?.response && this.grpcResponseM.setData(result.response);
            });
        } catch (e) {
        }
    }

    @Bind
    public onChangeShowType(showType: string): void {
        const pushObj = {
            showType
        };
        if (showType === showTypeEnum.NONE) {
            pushObj['selectedKey'] = `${this.groupId}`;
            this.parentThis.setApiTreeSelectedKey(pushObj['selectedKey']);
        }
        pushKey(pushObj);
        this.parentThis.onChangeShowType(showType);
    }

}
