import React from 'react';
import { AView } from 'libs';
import { Bind } from 'lodash-decorators';
import { observer } from 'mobx-react';
import { ApiCollapse, KdevTitle } from '@/business/commonComponents';
import { ViewJsonTable } from '../viewJsonTable/ViewJsonTable';
import { GrpcResponseM } from './GrpcResponseM';
import css from './GrpcResponse.less';

@observer
export class GrpcResponse extends AView<GrpcResponseM> {
    @Bind
    private renderKdevTitleExtra(): React.ReactNode {
        const model = this.model;
        return (
            <span className={css.kdevTitleExtra}>
                <a onClick={() => model.isExpandAllKeys(true)}>全部展开</a>/
                <a onClick={() => model.isExpandAllKeys(false)}>全部收起</a>
            </span>
        );
    }

    @Bind
    private renderBodyParams(): React.ReactNode {
        const model = this.model;
        if (model.isShowBodyTable) {
            return (
                <>
                    <KdevTitle
                        text={'Body参数'}
                        size={'small'}
                        className={css.kdevTitle}
                        extra={this.renderKdevTitleExtra()}
                    />
                    <ViewJsonTable
                        className={css.viewJsonTable}
                        model={model.bodyViewJsonTableM}
                        enableResize
                    />
                </>
            );
        }
    }

    public render(): React.ReactNode {
        return (
            <ApiCollapse header="返回参数" className={css.grpcResponseApiCollapse}>
                {this.renderBodyParams()}
            </ApiCollapse>
        );
    }
}
