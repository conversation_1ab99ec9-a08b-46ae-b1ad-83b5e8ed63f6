import { AView } from 'libs';
import React from 'react';
import { ApiMgrM } from './ApiMgrM';
import { observer } from 'mobx-react';
import css from './ApiMgr.less';
import SplitPane from 'react-split-pane';
import Pane from 'react-split-pane/lib/Pane';
import { ApiTree } from './apiTree/ApiTree';
import { ApiTable } from './apiTable/ApiTable';
import { ApiDetail } from './apiDetail/ApiDetail';
import { EditApi } from './editApi/EditApi';
import { Bind } from 'lodash-decorators';
import { showTypeEnum } from './configure';

@observer
export class ApiMgr extends AView<ApiMgrM> {

    @Bind
    private renderRightContent(): React.ReactNode {
        const model = this.model;
        if (model.showType === showTypeEnum.NONE) {
            return <ApiTable model={model.apiTableM}/>;
        }
        if (model.showType === showTypeEnum.VIEW) {
            return <ApiDetail model={model.apiDetailM}/>;
        }
        if (model.showType === showTypeEnum.EDIT) {
            return <EditApi model={model.editApiM}/>;
        }
    }

    public render(): React.ReactNode {
        const model = this.model;
        return (
            <div className={css.apiMgr}>
                <SplitPane>
                    <Pane minSize={model.leftPaneMinSize} size={model.leftPaneSize}>
                        <ApiTree model={model.apiTreeM}/>
                    </Pane>
                    <Pane minSize={'991px'}>
                        {this.renderRightContent()}
                    </Pane>
                </SplitPane>
            </div>
        );
    }
}
