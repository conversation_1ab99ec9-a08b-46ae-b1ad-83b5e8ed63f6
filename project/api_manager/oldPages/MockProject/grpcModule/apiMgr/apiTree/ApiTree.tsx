import { AView } from 'libs';
import React from 'react';
import { ApiTreeM } from './ApiTreeM';
import { observer } from 'mobx-react';
import css from './ApiTree.less';
import { Skeleton, Tree, Input, Tooltip } from 'antd';
import { Bind } from 'lodash-decorators';
import { bindObserver } from '@libs/mvvm';
import { KdevIconFont } from '@/business/commonComponents';

const { Search } = Input;
const { DirectoryTree } = Tree;

const Search_keyWord = bindObserver(Search, 'keyWord');

@observer
export class ApiTree extends AView<ApiTreeM> {

    @Bind
    public componentWillMount() {
        this.model.onResizeTreeHeight();
        window.addEventListener('resize', this.model.onResizeTreeHeight);
    }

    @Bind
    public componentWillUnmount() {
        window.removeEventListener('resize', this.model.onResizeTreeHeight);
    }

    @Bind
    private renderSearchKeyWord(): React.ReactChild {
        const model = this.model;
        return (
            <div className={css.keyWordWrap}>
                <Search_keyWord
                    model={model}
                    placeholder="支持分组/API搜索"
                    onSearch={model.initLoading}
                />
            </div>
        );
    }

    @Bind
    private renderTreeNode(nodeData): React.ReactChild {
        if (nodeData.groupId) {
            return (
                <div>
                    <KdevIconFont id="#iconfolder" className={css.iconfolder} />
                    {nodeData.groupName}
                </div>
            );
        } else {
            return (
                <Tooltip title={nodeData.methodName} placement="right">
                    <div className={css.treeNode}>{nodeData.methodName}</div>
                </Tooltip>
            );
        }
    }

    @Bind
    private renderDirectoryTree(): React.ReactChild {
        const model = this.model;
        if (model.loading) {
            return <Skeleton className={css.skeleton} />;
        }
        return (
            <DirectoryTree
                blockNode
                className={css.treeData}
                treeData={model.treeData}
                titleRender={this.renderTreeNode}
                height={model.treeHeigth}
                icon={false}
                defaultExpandAll
                selectedKeys={[model.selectedKey]}
                onSelect={model.onSelectedKeys}
                expandAction="doubleClick"
            />
        );
    }

    public render(): React.ReactNode {
        const model = this.model;
        return (
            <div className={css.apiTree}>
                {this.renderSearchKeyWord()}
                <div className={css.treeWrap}>
                    {this.renderDirectoryTree()}
                </div>
            </div>
        );
    }
}
