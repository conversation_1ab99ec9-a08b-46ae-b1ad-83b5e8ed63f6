.apiTree {
    height: 100%;
    .keyWordWrap {
        margin: 16px;
    }

    .treeWrap {
        height: calc(100% - 84px);
        overflow: hidden;

        .treeData{
            overflow-y: auto;
            overflow-x: hidden;

            .treeNode {
                text-overflow: ellipsis;
                overflow: hidden;
                direction: rtl;
                padding-right: 8px;
            }

            .iconfolder {
                margin-right: 4px;
            }

            :global {
                .ant-tree-list-holder-inner .ant-tree-node-content-wrapper {
                    width: calc(100% - 48px);
                    overflow: hidden;
                }
            }
        }

        .skeleton {
            padding: 0 16px;
        }
    }
}
