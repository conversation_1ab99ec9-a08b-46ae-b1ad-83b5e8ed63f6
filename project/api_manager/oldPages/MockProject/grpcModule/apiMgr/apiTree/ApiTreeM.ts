import { AViewModel } from 'libs';
import { observable, action, runInAction, } from 'mobx';
import { nsMockManageApiManageRpcGetApiInfoByModuleIdPost } from '@/remote';
import * as QS from 'query-string';
import { Bind } from 'lodash-decorators';
import { ITreeNode } from './configure';
import { pushKey } from '@/index.config/tools';
import { showTypeEnum } from '../configure';
import { ApiMgrM } from '../ApiMgrM';

export class ApiTreeM extends AViewModel {
    private moduleId: number = 0;
    @observable public keyWord: string = '';
    @observable public treeHeigth: number = 0;
    @observable public treeData: ITreeNode[] = [];
    @observable public loading: boolean = false;
    @observable public selectedKey: string = '';

    private parentThis: ApiMgrM;

    constructor(parentThis: ApiMgrM) {
        super();
        this.parentThis = parentThis;
    }

    @action.bound
    public initParams(): void {
        const urlParams: object = QS.parse(location.search);
        this.moduleId = urlParams['moduleId'] || this.moduleId;
        this.selectedKey = urlParams['selectedKey'] || this.selectedKey;
    }

    @action.bound
    public initLoading() {
        this.getApiInfoByModuleId();
    }

    // 获取treeData数据
    @Bind
    private async getApiInfoByModuleId(): Promise<void> {
        runInAction(() => this.loading = true);
        try {
            const params = {
                moduleId: this.moduleId,
                keyWord: this.keyWord
            };
            const result = await nsMockManageApiManageRpcGetApiInfoByModuleIdPost.remote(params);
            this.formatTreeData(result?.list);
            runInAction(() => this.loading = false);
        } catch {
            runInAction(() => this.loading = false);
        }
    }

    @action
    private formatTreeData(treeData: ITreeNode[]): void {
        this.treeData = treeData;
        if (!this.selectedKey) {
            this.onSelectedKeys([treeData[0].key]);
        }
    }

    @action.bound
    public onResizeTreeHeight(): void {
        this.treeHeigth = document.body.clientHeight - 250;
    }

    // 选中节点
    @action.bound
    public onSelectedKeys(selectedKeys): void {
        if (this.selectedKey !== selectedKeys[0]) {
            this.setSelectedKey(selectedKeys[0]);
            const isApiDoc = /-/.test(selectedKeys[0]);
            const pushObj = {
                selectedKey: this.selectedKey,
                showType: isApiDoc ? showTypeEnum.VIEW : showTypeEnum.NONE
            };
            pushKey(pushObj);
            this.parentThis.onChangeShowType(pushObj['showType']);
        }
    }

    // 手动设置选中节点key
    @action
    public setSelectedKey(selectedKey: string): void {
        this.selectedKey = selectedKey;
    }

}
