import { AViewModel } from 'libs';
import { observable, action } from 'mobx';
import * as QS from 'query-string';
import { Bind, Debounce } from 'lodash-decorators';
import { showTypeEnum } from './configure';
import { API_MGR_PANE_SIZE_KEYS, getLoaclStoragePaneSize, setLoaclStoragePaneSize } from '@/index.config/setLocalStorageFun';
import { ApiTreeM } from './apiTree/ApiTreeM';
import { ApiTableM } from './apiTable/ApiTableM';
import { ApiDetailM } from './apiDetail/ApiDetailM';
import { EditApiM } from './editApi/EditApiM';

export class ApiMgrM extends AViewModel {
    private moduleId: number = 0;
    @observable public leftPaneSize: string = '246px';
    @observable public leftPaneMinSize: string = '246px';
    @observable public showType: string = showTypeEnum.NONE;

    public apiTreeM: ApiTreeM = new ApiTreeM(this);
    public apiTableM: ApiTableM = new ApiTableM(this);
    public apiDetailM: ApiDetailM = new ApiDetailM(this);
    public editApiM: EditApiM = new EditApiM(this);

    constructor() {
        super();
        this.initLeftPaneSize();
        this.initParams();
        this.initLoading();
    }

    @action
    public initParams(): void {
        const urlParams: object = QS.parse(location.search);
        this.moduleId = urlParams['moduleId'] || this.moduleId;
        this.showType = urlParams['showType'] || this.showType;
    }

    @action.bound
    public initLoading() {
        this.apiTreeInitLoading();
    }

    // 初始化左侧tree
    @Bind
    private apiTreeInitLoading(): void {
        this.apiTreeM.initParams();
        this.apiTreeM.initLoading();
        this.onChangeShowType(this.showType);
    }

    // 设置apiTree选中节点key
    @Bind
    public setApiTreeSelectedKey(selectedKey: string) {
        this.apiTreeM.setSelectedKey(selectedKey);
    }

    // 切换右侧展示内容
    @action.bound
    public onChangeShowType(showType: string = ''): void {
        this.showType = showType;
        switch (this.showType) {
            case showTypeEnum.NONE:
                this.apiTableInitLoading();
                break;
            case showTypeEnum.VIEW:
                this.apiDetailInitLoading();
                break;
            case showTypeEnum.EDIT:
                this.editApiInitLoading();
                break;
        }
    }

    // 初始化右侧table
    @Bind
    private apiTableInitLoading(): void {
        this.apiTableM.initParams();
        this.apiTableM.initLoading();
    }

    // 初始化右侧详情
    @Bind
    private apiDetailInitLoading(): void {
        this.apiDetailM.initParams();
        this.apiDetailM.initLoading();
    }

    // 初始化编辑界面
    @Bind
    private editApiInitLoading(): void {
        this.editApiM.initParams();
        this.editApiM.initLoading();
    }

    // pane组件size受控
    @action.bound
    public onChangeSplitPane(paneSize) {
        if (paneSize[0] !== this.leftPaneSize) {
            this.leftPaneSize = paneSize[0];
            this.setLeftPaneSize();
        }
    }

    // 记录上次宽度
    @Debounce(300)
    @action
    protected setLeftPaneSize() {
        setLoaclStoragePaneSize(API_MGR_PANE_SIZE_KEYS.MODULE_DETAIL_API_TREE, this.leftPaneSize);
    }

    // 初始化左侧Pane宽度
    @action
    private initLeftPaneSize(): void {
        const leftPaneSize = getLoaclStoragePaneSize()[API_MGR_PANE_SIZE_KEYS.MODULE_DETAIL_API_TREE] || '';
        // 判断获取到的值是否以px结尾，如果不是不适用
        if (leftPaneSize && leftPaneSize.substring(leftPaneSize.length - 2) === 'px') {
            this.leftPaneSize = leftPaneSize;
        }
        setLoaclStoragePaneSize(API_MGR_PANE_SIZE_KEYS.MODULE_DETAIL_API_TREE, this.leftPaneSize);
    }

}
