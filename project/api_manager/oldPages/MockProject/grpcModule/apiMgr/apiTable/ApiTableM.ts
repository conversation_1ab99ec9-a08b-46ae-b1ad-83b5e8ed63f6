import { AViewModel } from 'libs';
import { observable, action, runInAction, } from 'mobx';
import { nsMockManageApiManageRpcGetApisByGroupIdPost } from '@/remote';
import * as QS from 'query-string';
import { Bind } from 'lodash-decorators';
import { IApiItem } from './configure';
import { getKey0OrKey1, pushKey } from '@/index.config/tools';
import { ApiMgrM } from '../ApiMgrM';

export class ApiTableM extends AViewModel {
    private groupId: number = 0;
    @observable public loading: boolean = false;
    @observable public apiList: IApiItem[] = [];

    private parentThis: ApiMgrM;

    constructor(parentThis: ApiMgrM) {
        super();
        this.parentThis = parentThis;
    }

    @action
    public initParams(): void {
        const urlParams: object = QS.parse(location.search);
        this.groupId = getKey0OrKey1(urlParams['selectedKey'] || '')[0] || this.groupId;
    }

    @action.bound
    public initLoading() {
        this.getApisByGroupId();
    }

    @Bind
    private async getApisByGroupId(): Promise<void> {
        if (!this.groupId) {
            return;
        }
        runInAction(() => this.loading = true);
        try {
            const params = {groupId: this.groupId};
            const result = await nsMockManageApiManageRpcGetApisByGroupIdPost.remote(params);
            runInAction(() => {
                this.apiList = result?.list;
                this.loading = false;
            });
        } catch {
            runInAction(() => this.loading = false);
        }
    }

    // 展示API详情
    @Bind
    public onChangeShowType(apiId: number, showType: string): void {
        const selectedKey = `${this.groupId}-${apiId}`;
        pushKey({
            selectedKey,
            showType
        });
        this.parentThis.setApiTreeSelectedKey(selectedKey);
        this.parentThis.onChangeShowType(showType);
    }

}
