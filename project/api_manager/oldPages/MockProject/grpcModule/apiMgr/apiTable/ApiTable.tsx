import { AView } from 'libs';
import React from 'react';
import { ApiTableM } from './ApiTableM';
import { observer } from 'mobx-react';
import css from './ApiTable.less';
import { Button, Table } from 'antd';
import { Bind } from 'lodash-decorators';
import { KdevIconFont } from '@/business/commonComponents';
import { IApiItem } from './configure';
import { showTypeEnum } from '../configure';
import { readOnlyEnum } from '@/business/grpcComponents/grpcGlobalDefine';

@observer
export class ApiTable extends AView<ApiTableM> {

    @Bind
    private renderMethodName(record: IApiItem): React.ReactNode {
        return (
            <a
                className={css.methodName}
                onClick={() => this.model.onChangeShowType(record.id, showTypeEnum.VIEW)}
            >
                {record.methodName}
            </a>
        );
    }

    @Bind
    private renderServiceName(serviceName: string): React.ReactNode {
        return (
            <div className={css.serviceName}>{serviceName}</div>
        );
    }

    @Bind
    private renderReadOnly(readOnly: string) {
        return <span>{readOnly === readOnlyEnum.WRITE ? '写' : '读'}</span>;
    }

    @Bind
    private renderOperate(record: IApiItem): React.ReactChild {
        return (
            <Button
                icon={<KdevIconFont id="#iconxingzhuangjiehe" />}
                onClick={() => this.model.onChangeShowType(record.id, showTypeEnum.EDIT)}
            />
        );
    }

    @Bind
    private columns(): any[] {
        const columns = [
            {
                title: '方法名',
                key: 'methodName',
                // dataIndex: 'methodName',
                width: 240,
                render: this.renderMethodName
            },
            {
                title: '服务名',
                key: 'serviceName',
                width: 160,
                dataIndex: 'serviceName',
                render: this.renderServiceName
            },
            {
                title: '优先级',
                key: 'priority',
                width: 90,
                dataIndex: 'priority'
            },
            {
                title: '读写属性',
                key: 'readOnly',
                width: 100,
                dataIndex: 'readOnly',
                render: this.renderReadOnly
            },
            {
                title: '操作',
                key: 'operate',
                width: 80,
                render: this.renderOperate
            }
        ];
        return columns;
    }

    public render(): React.ReactNode {
        const model = this.model;
        return (
            <div className={css.apiTable}>
                <Table
                    columns={this.columns()}
                    dataSource={model.apiList}
                    className={css.table}
                    bordered
                    loading={model.loading}
                />
            </div>
        );
    }
}
