import { AViewModel } from 'libs';
import { observable, action, runInAction, } from 'mobx';
import { nsMockManageApiManageRpcQueryApiDetailByIdPost, nsMockManageApiManageRpcUpdateApiInfoPost } from '@/remote';
import * as QS from 'query-string';
import { Bind } from 'lodash-decorators';
import { BaseInfoM } from './baseInfo/BaseInfoM';
import { GrpcRequestM } from '../apiDetail/grpcRequest/GrpcRequestM';
import { GrpcResponseM } from '../apiDetail/grpcResponse/GrpcResponseM';
import { getKey0OrKey1, pushKey } from '@/index.config/tools';
import { ApiMgrM } from '../ApiMgrM';
import { message } from 'antd';
import { showTypeEnum } from '../configure';

export class EditApiM extends AViewModel {
    // private groupId: number = 0;
    private apiId: number = 0;
    @observable public saveLoading: boolean = false;

    public baseInfoM = new BaseInfoM();
    public grpcRequestM = new GrpcRequestM();
    public grpcResponseM = new GrpcResponseM();

    private parentThis: ApiMgrM;

    constructor(parentThis: ApiMgrM) {
        super();
        this.parentThis = parentThis;
    }

    @action
    public initParams(): void {
        const urlParams: object = QS.parse(location.search);
        // this.groupId = getKey0OrKey1(urlParams['selectedKey'] || '')[0] || this.groupId;
        this.apiId = getKey0OrKey1(urlParams['selectedKey'] || '')[1] || this.apiId;
    }

    @action.bound
    public initLoading() {
        this.queryApiDetailById();
    }

    // 获取API详情
    @Bind
    private async queryApiDetailById() {
        try {
            const params = {
                id: this.apiId
            };
            const result = await nsMockManageApiManageRpcQueryApiDetailByIdPost.remote(params);
            runInAction(() => {
                if (result?.baseInfo) {
                    this.baseInfoM.setBaseInfo(result.baseInfo);
                }
                result?.request && this.grpcRequestM.setData(result.request);
                result?.response && this.grpcResponseM.setData(result.response);
            });
        } catch (e) {
        }
    }

    // 展示API详情
    @Bind
    public onChangeShowType(showType: string): void {
        pushKey({
            showType
        });
        this.parentThis.onChangeShowType(showType);
    }

    @Bind
    public onSave(): void {
        this.updateApiInfo();
    }

    // 更新文档读写属性
    @Bind
    private async updateApiInfo(): Promise<void> {
        runInAction(() => this.saveLoading = true);
        try {
            const params = this.getParams();
            await nsMockManageApiManageRpcUpdateApiInfoPost.remote(params);
            message.success('保存成功～');
            runInAction(() => this.saveLoading = false);
            this.onChangeShowType(showTypeEnum.VIEW);
        } catch {
            runInAction(() => this.saveLoading = false);
        }
    }

    // 获取参数信息
    @Bind
    private getParams() {
        const parmas = {
            apiId: this.apiId,
            attributes: this.baseInfoM.getAttributes()
        };
        return parmas;
    }

}
