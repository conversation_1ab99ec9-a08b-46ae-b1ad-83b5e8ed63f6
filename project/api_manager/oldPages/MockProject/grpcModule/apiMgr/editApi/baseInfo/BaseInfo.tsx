import React from 'react';
import { observer } from 'mobx-react';
import { AView } from 'libs';
import { Row, Col, Select } from 'antd';
import { CopyBtn, AvatarList, ApiCollapse } from '@/business/commonComponents';
import { BaseInfoM } from './BaseInfoM';
import css from './BaseInfo.less';
import { Bind } from 'lodash-decorators';
import { priorityOptions, readOnlyOptions } from '@/business/grpcComponents/grpcGlobalDefine';

@observer
export class BaseInfo extends AView<BaseInfoM> {

    private renderApiName(): React.ReactNode {
        const model = this.model;
        return (
            <div className={css.nameWrap}>
                <div className={css.name}>API名称：{model.name}</div>
                <CopyBtn copyContent={model.name}/>
            </div>
        );
    }

    @Bind
    private renderPriority(): React.ReactNode {
        const model = this.model;
        return (
            <Col span={12}>
                <span className={css.label}>优先级</span>
                <Select
                    value={model.priority || undefined}
                    options={priorityOptions}
                    className={css.prioritySelect}
                    allowClear
                    onChange={model.onChangePriority}
                    placeholder={'请选择'}
                />
            </Col>
        );
    }

    @Bind
    private renderReadOnly(): React.ReactNode {
        const model = this.model;
        return (
            <Col span={12}>
                <span className={css.label}>读写属性</span>
                <Select
                    value={model.readOnly}
                    options={readOnlyOptions}
                    className={css.prioritySelect}
                    // allowClear
                    onChange={model.onChangeReadOnly}
                    placeholder={'请选择'}
                />
            </Col>
        );
    }

    private renderServiceAndKess(): React.ReactNode {
        const model = this.model;
        return (
            <Row className={css.contentRow}>
                <Col span={12}>ServiceName：{model.serviceName}</Col>
                <Col span={12}>KessName：{model.kessName}</Col>
            </Row>
        );
    }

    private renderOwnerAndVersion(): React.ReactNode {
        const model = this.model;
        return (
            <Row className={css.contentRow}>
                <Col span={12}>最后编译人：<AvatarList reviewers={model.ownerInfo} /></Col>
                <Col span={12}>版本：{model.version}</Col>
            </Row>
        );
    }

    public render(): React.ReactNode {
        return (
            <ApiCollapse header="基本信息" className={css.grpcBaseInfo}>
                {this.renderApiName()}
                {this.renderServiceAndKess()}
                {this.renderOwnerAndVersion()}
                <Row className={css.contentRow}>
                    {this.renderPriority()}
                    {this.renderReadOnly()}
                </Row>
            </ApiCollapse>
        );
    }
}
