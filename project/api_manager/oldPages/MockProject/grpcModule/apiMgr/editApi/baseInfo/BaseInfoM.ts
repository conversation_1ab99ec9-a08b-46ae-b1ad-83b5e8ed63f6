import { AViewModel } from 'libs';
import { action, observable, runInAction } from 'mobx';
import { Bind } from 'lodash-decorators';
import {
    nsMockPermissionBatchGetUserGet, nsMockManageApiManageRpcGetApiAttributesPost,
    nsMockManageKoasApiManageHttpApiCanEditApiPriorityGet
} from '@/remote';
import { IUserInfo } from './configure';
import { readOnlyEnum } from '@/business/grpcComponents/grpcGlobalDefine';
import { Modal } from 'antd';
import css from './BaseInfo.less';

export class BaseInfoM extends AViewModel {
    private apiId: number = 0;
    @observable public name: string = '';
    @observable public serviceName: string = '';
    @observable public kessName: string = '';
    @observable public ownerInfo: IUserInfo[] = [];
    @observable public version: string = '';
    @observable public priority: string = '';
    @observable public readOnly: string = readOnlyEnum.READ;

    @action.bound
    public setBaseInfo(baseInfo): void {
        this.apiId = baseInfo.id;
        this.name = baseInfo.name;
        this.serviceName = baseInfo.serviceName;
        this.kessName = baseInfo.kessName;
        this.version = baseInfo.version;
        this.getOwnerInfo(baseInfo.owner);
        this.getApiAttributes();
    }

    @Bind
    private async getOwnerInfo(userName: string): Promise<void> {
        const users = await nsMockPermissionBatchGetUserGet.batchGetUser(userName);
        runInAction(() => {
            this.ownerInfo = users;
        });
    }

    @Bind
    private async getApiAttributes(): Promise<void> {
        if (!this.apiId) {
            return;
        }
        try {
            const params = {
                apiId: this.apiId
            };
            const result = await nsMockManageApiManageRpcGetApiAttributesPost.remote(params);
            runInAction(() => {
                result.list.forEach(item => {
                    if (item.key === 'priority') {
                        this.priority = item.val;
                    }
                    if (item.key === 'readOnly') {
                        this.readOnly = item.val;
                    }
                });
            });
        } catch {}
    }

    // 选择优先级
    @action.bound
    public onChangePriority(priority: string = ''): void {
        const newPriority = this.priority;
        this.priority = priority;
        if (priority) {
            this.canEditApiPriority(newPriority);
        }
    }

    @Bind
    private async canEditApiPriority(priority: string) {
        try {
            const params = {
                apiId: this.apiId,
                priority: this.priority,
                type: 1 as 1
            };
            const result = await nsMockManageKoasApiManageHttpApiCanEditApiPriorityGet.remote(params);
            if (!result.canEdit) {
                Modal.warning({
                    className: css.canEditModal,
                    content: result.msg
                });
                runInAction(() => {
                    this.priority = priority;
                });
            }
        } catch {
        }
    }

    // 选择读写属性
    @action.bound
    public onChangeReadOnly(readOnly: string): void {
        this.readOnly = readOnly;
    }

    @Bind
    public getAttributes() {
        const attributes = [
            {key: 'priority', val: this.priority},
            {key: 'readOnly', val: this.readOnly}
        ];
        return attributes;
    }
}
