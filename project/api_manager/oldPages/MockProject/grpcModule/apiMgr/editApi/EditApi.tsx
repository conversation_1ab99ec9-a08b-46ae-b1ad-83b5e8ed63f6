import { AView } from 'libs';
import React from 'react';
import { EditApiM } from './EditApiM';
import { observer } from 'mobx-react';
import css from './EditApi.less';
import { BaseInfo } from './baseInfo/BaseInfo';
import { GrpcRequest } from '../apiDetail/grpcRequest/GrpcRequest';
import { GrpcResponse } from '../apiDetail/grpcResponse/GrpcResponse';
import { Bind } from 'lodash-decorators';
import { Button, Space } from 'antd';
import { showTypeEnum } from '../configure';

@observer
export class EditApi extends AView<EditApiM> {

    @Bind
    private footer(): React.ReactChild {
        const model = this.model;
        return (
            <Space className={css.footer}>
                <Button type="primary" onClick={model.onSave} loading={model.saveLoading}>保存</Button>
                <Button onClick={() => model.onChangeShowType(showTypeEnum.VIEW)}>取消</Button>
            </Space>
        );
    }

    public render(): React.ReactNode {
        const model = this.model;
        return (
            <div className={css.editApi}>
                <div className={css.content}>
                    <BaseInfo model={model.baseInfoM} />
                    <GrpcRequest model={model.grpcRequestM} />
                    <GrpcResponse model={model.grpcResponseM} />
                </div>
                {this.footer()}
            </div>
        );
    }
}
