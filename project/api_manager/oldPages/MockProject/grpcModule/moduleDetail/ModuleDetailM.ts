import { AViewModel } from 'libs';
import { observable, action, runInAction, } from 'mobx';
import { nsMockManageApiManageRpcGetModuleInfoByIdPost, nsMockPermissionBatchGetUserGet } from '@/remote';
import * as QS from 'query-string';
import moment from 'moment';
import { Bind } from 'lodash-decorators';
import { IUsersInfo } from './configure';

export class ModuleDetailM extends AViewModel {
    private moduleId: number = 0;
    @observable public name: string = '';
    @observable public moduleName: string = '';
    private createUser: string = '';
    @observable public createUsers: IUsersInfo[] = [];
    private updateUser: string = '';
    @observable public updateUsers: IUsersInfo[] = [];
    @observable public updateTime: string = '';
    @observable public gitUrl: string = '';
    @observable public loading: boolean = false;

    constructor() {
        super();
        this.initParams();
        this.initLoading();
    }

    @action
    public initParams(): void {
        const urlParams: object = QS.parse(location.search);
        this.moduleId = urlParams['moduleId'] || this.moduleId;
    }

    @action.bound
    public initLoading() {
        this.getModuleInfoById();
    }

    // 获取详情
    @Bind
    private async getModuleInfoById() {
        runInAction(() => this.loading = true);
        try {
            const params = {
                moduleId: this.moduleId
            };
            const result = await nsMockManageApiManageRpcGetModuleInfoByIdPost.remote(params);
            runInAction(() => {
                this.name = result.moduleName;
                this.moduleName = result.moduleName;
                this.gitUrl = result.gitUrl;
                this.createUser = result?.createUser;
                this.updateUser = result?.updateUser;
                this.updateTime = result?.updateTime ? moment(result?.updateTime).format('YYYY-MM-DD HH:mm:ss') : '';
                this.batchGetUser(result.updateUser);
            });
        } catch (e) {
            runInAction(() => this.loading = false);
        }
    }

    // 获取最后编译人信息
    @Bind
    private async batchGetUser(usernames: string): Promise<void> {
        const usersInfo: IUsersInfo[] = await nsMockPermissionBatchGetUserGet.batchGetUser(usernames);
        this.formatUsersInfo(usersInfo);
    }

    @action
    private formatUsersInfo(usersInfo: IUsersInfo[]): void {
        this.createUsers = [];
        this.updateUsers = [];
        usersInfo.forEach(item => {
            if (item.username === this.createUser) {
                this.createUsers.push(item);
            }
            if (item.username === this.updateUser) {
                this.updateUsers.push(item);
            }
        });
        this.loading = false;
    }
}
