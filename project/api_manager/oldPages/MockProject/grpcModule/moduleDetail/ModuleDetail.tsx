import { AView } from 'libs';
import React from 'react';
import { ModuleDetailM } from './ModuleDetailM';
import { observer } from 'mobx-react';
import css from './ModuleDetail.less';
import { AvatarList } from '@/business/commonComponents';
import { Skeleton } from 'antd';

@observer
export class ModuleDetail extends AView<ModuleDetailM> {

    public render(): React.ReactNode {
        const model = this.model;
        if (model.loading) {
            return (<Skeleton className={css.moduleDetail} />);
        }
        return (
            <div className={css.moduleDetail}>
                <div className={css.row}>
                    <span className={css.label}>模块更新时间：</span>
                    {model.updateTime}
                </div>
                <div className={css.row}>
                    <span className={css.label}>模块名称：</span>
                    {model.name}
                </div>
                <div className={css.row}>
                    <span className={css.label}>git地址：</span>
                    {model.gitUrl}
                </div>
                <div className={css.row}>
                    <div className={css.rowLeft}>
                        <span className={css.label}>模块创建人：</span>
                        <AvatarList reviewers={model.createUsers} />
                    </div>
                    <div className={css.rowRight}>
                        <span className={css.label}>模块参与人：</span>
                        <AvatarList reviewers={model.updateUsers} />
                    </div>
                </div>
            </div>
        );
    }
}
