import { <PERSON><PERSON>ie<PERSON> } from 'libs';
import React from 'react';
import { TeamsSelectM } from './TeamsSelectM';
import { observer } from 'mobx-react';
import { Select, Tag, Spin, Tooltip } from 'antd';
import { SelectProps } from 'antd/lib/select';
import classNames from 'classnames';
import css from './TeamsSelect.less';
import { ITeamItem } from './configure';

interface IProps<T> extends SelectProps<T> { }

const { Option } = Select;

@observer
export class TeamsSelect extends AView<TeamsSelectM, IProps<any>> {

    public componentWillMount() {
        const { value } = this.props;
        if (value && value.length) {
            this.model.queryTeams(value.join(','));
        } else {
            this.model.queryAllTeams();
        }
    }

    public render(): React.ReactNode {
        const model = this.model;
        return (
            <Select
                placeholder="请选择"
                mode="multiple"
                maxTagCount={1}
                // optionLabelProp="value"
                dropdownClassName={css.teamsSelectDrop}
                // open={true}
                filterOption={false}
                value={model.teamIds}
                onSearch={model.onSearchTeams}
                onChange={model.onChangeTeamIds}
                loading={model.loading}
                notFoundContent={model.loading && <Spin size="small" />}
                {...this.props}
                className={classNames(css.teamsSelect, this.props.className)}
            >
                {
                    model.teams.map(item => (
                        <Option value={item.taskId} key={item.taskId} label={item.title}>
                            <Tooltip title={item.title} placement="left">
                                <span className={css.title}>{item.title}</span>
                            </Tooltip>
                            <Tag color={'#666f8014'} className={css.taskId}>{item.taskId}</Tag>
                        </Option>
                    ))
                }
            </Select>
        );
    }
}
