import { AViewModel } from 'libs';
import { observable, action, runInAction } from 'mobx';
import { Bind, Debounce } from 'lodash-decorators';
import { nsMockManageKoasApiManageHttpApiQueryAllTeamsGet, nsMockManageKoasApiManageHttpApiQueryTeamsGet } from '@/remote';
import { ITeamItem } from './configure';

export class TeamsSelectM extends AViewModel {
    @observable public teams: ITeamItem[] = [];
    @observable public teamIds: string[] = [];
    @observable public loading: boolean = false;
    @observable public selectedRows: ITeamItem[] = [];

    // constructor() {
    //     super();
    // }

    @action.bound
    public onChangeTeamIds(teamIds: string[]) {
        this.teamIds = teamIds;
    }

    @Bind
    public getTeamIds() {
        return this.teamIds;
    }

    @Debounce(300)
    @Bind
    public onSearchTeams(key: string) {
        this.queryAllTeams(key);
    }

    // 获取team列表
    @Bind
    public async queryAllTeams(key: string = '') {
        if (!key) {
            return;
        }
        runInAction(() => this.loading = true);
        try {
            const result = await nsMockManageKoasApiManageHttpApiQueryAllTeamsGet.remote({key});
            runInAction(() => {
                const searchTeams = result?.result.filter(item => !this.teamIds.includes(item.taskId)) || [];
                this.teams = [...this.selectedRows, ...searchTeams];
                this.loading = false;
            });
        } catch {
            runInAction(() => this.loading = false);
        }
    }

    // 获取选中team列表
    @Bind
    public async queryTeams(teamIdsStr: string) {
        try {
            const result = await nsMockManageKoasApiManageHttpApiQueryTeamsGet.remote({teamIds: teamIdsStr});
            runInAction(() => {
                this.teams = result?.result || [];
                this.setSelectedRows(result?.result || []);
            });
        } catch {}
    }

    // 设置选中行
    @action.bound
    public setSelectedRows(selectedRows: ITeamItem[]) {
        this.selectedRows = selectedRows;
        if (this.teamIds.length !== this.selectedRows.length) {
            this.teamIds = this.selectedRows.map(item => item.taskId);
        }
    }
}
