import { AViewModel } from 'libs';
import { observable, action } from 'mobx';
import {
    nsMockManageKoasApiManageMockAllConfigGet
} from '@/remote';
import arrayMoveImmutable from 'array-move';
import { DynamicJsM } from '../dynamicJsModal/DynamicJsM';

export class MyConfigTableM extends AViewModel {
    @observable public configs: nsMockManageKoasApiManageMockAllConfigGet.IConfigs[] = [];
    @observable public configLength: number = 0;

    public dynamicJsM = new DynamicJsM();

    // 初始化
    @action.bound
    public initLoading(configs: nsMockManageKoasApiManageMockAllConfigGet.IConfigs[]): void {
        this.configs = configs;
        this.configLength = this.configs.length;
    }

    // 重新渲染配置表格
    @action
    private renderConfigTable(): void {
        this.configs = [...this.configs];
    }

    // 拖拽移动表格位置
    @action.bound
    public onSortEnd({ oldIndex, newIndex }) {
        const configs = [...this.configs];
        if (oldIndex !== newIndex) {
            const newData = arrayMoveImmutable(configs, oldIndex, newIndex).map((item, index) => {
                item.order = index;
                return item;
            });
            this.configs = newData;
        }
    }

    // 改变Input value值
    @action.bound
    public onChangeInputValue(record, key: string, e): void {
        record[key] = e.target.value;
        this.renderConfigTable();
    }

    // 修改启用状态
    @action.bound
    public onChangeStatus(record, status: boolean): void {
        record['status'] = status ? 1 : 0;
        this.renderConfigTable();
    }

    // 一键启动
    @action.bound
    public onOpenAllStatus(): void {
        this.configs.map(item => item.status = 1);
        this.renderConfigTable();
    }

    // 打开编辑动态JS弹框
    @action.bound
    public onOpenDynamicJs(record): void {
        this.dynamicJsM.initLoading(record);
    }

    // 添加代理配置
    @action.bound
    public onAddConfig(): void {
        this.configs.push({
            path: '',
            address: '',
            status: 1,
            order: ++this.configLength
        });
        this.renderConfigTable();
    }

    // 删除代理配置
    @action.bound
    public onDeleteConfig(order: number): void {
        this.configs = this.configs.filter(item => item.order !== order);
    }

    // 获取参数
    @action.bound
    public getParams(): nsMockManageKoasApiManageMockAllConfigGet.IConfigs[] {
        const configs: nsMockManageKoasApiManageMockAllConfigGet.IConfigs[] = this.configs.map(item => {
            if (!item['dynamicJs']) {
                item['dynamicJs'] = '';
            }
            return item;
        });
        return configs;
    }
}
