import React from 'react';
import { observer } from 'mobx-react';
import { Bind } from 'lodash-decorators';
import { Space, Input, Table, Button, Switch, Divider } from 'antd';
import { MenuOutlined } from '@ant-design/icons';
import { SortableContainer, SortableElement, SortableHandle } from 'react-sortable-hoc';

import { AView } from 'libs';
import { DynamicJs } from '../dynamicJsModal/DynamicJs';
import { MyConfigTableM } from './MyConfigTableM';
import css from './MyConfigTable.less';

const SortableItem = SortableElement(props => <tr {...props} />);
const SortableBody = SortableContainer(props => <tbody {...props} />);
const DragHandle = SortableHandle(() => <MenuOutlined style={{ cursor: 'grab' }} />);

@observer
export class MyConfigTable extends AView<MyConfigTableM> {

    @Bind
    private renderPath(record): React.ReactNode {
        return (
            <Input
                value={record.path}
                onChange={this.model.onChangeInputValue.bind(this, record, 'path')}
                allowClear
            />
        );
    }

    @Bind
    private renderAddress(record): React.ReactNode {
        return (
            <Input
                value={record.address}
                onChange={this.model.onChangeInputValue.bind(this, record, 'address')}
                allowClear
            />
        );
    }

    @Bind
    private renderStatus(record): React.ReactNode {
        return (
            <Switch
                checked={Boolean(record.status)}
                checkedChildren="开"
                unCheckedChildren="关"
                onChange={this.model.onChangeStatus.bind(this, record)}
            />
        );
    }

    @Bind
    private renderOperate(record): React.ReactNode {
        return (
            <>
                <Button
                    type="link"
                    onClick={() => this.model.onOpenDynamicJs(record)}
                >
                    动态JS
                </Button>
                <Button
                    type="link"
                    onClick={() => this.model.onDeleteConfig(record.order)}
                >
                    删除
                </Button>
            </>
        );
    }

    private columns(): any[] {
        const columns = [
            {
                width: 30,
                render: () => <DragHandle />,
            },
            {
                title: '路径',
                // dataIndex: 'path',
                key: 'path',
                width: '40%',
                render: this.renderPath
            },
            {
                title: '代理地址',
                // dataIndex: 'address',
                key: 'address',
                width: '40%',
                render: this.renderAddress
            },
            {
                title: '是否启用',
                // dataIndex: 'status',
                key: 'status',
                width: 100,
                render: this.renderStatus
            },
            {
                title: '操作',
                key: 'operate',
                width: 170,
                render: this.renderOperate
            }
        ];
        return columns;
    }

    private renderMyConfigTitle(): React.ReactNode {
        return (
            <div className={css.configTitle}>
                <Space>
                    <Button type="primary" onClick={this.model.onAddConfig}>
                        添加代理
                    </Button>
                    <Button type="primary" onClick={this.model.onOpenAllStatus}>一键启动</Button>
                </Space>
                <span>可以拖动调整代理顺序，排名靠前的代理优先匹配</span>
            </div>
        );
    }

    @Bind
    private draggableContainer(props) {
        return (
            <SortableBody
                useDragHandle
                helperClass={css.myConfigRowDragging}
                onSortEnd={this.model.onSortEnd}
                {...props}
            />
        );
    }

    @Bind
    private draggableBodyRow({ className, style, ...restProps }) {
        const { configs } = this.model;
        const index = configs.findIndex(x => x.order === restProps['data-row-key']);
        return <SortableItem index={index} {...restProps} />;
    }

    public render() {
        const model = this.model;
        return (
            <div className={css.myConfig}>
                <Divider orientation="left">我的配置</Divider>
                {this.renderMyConfigTitle()}
                <Table
                    columns={this.columns()}
                    bordered
                    dataSource={this.model.configs}
                    rowKey="order"
                    pagination={false}
                    components={{
                        body: {
                            wrapper: this.draggableContainer,
                            row: this.draggableBodyRow
                        }
                    }}
                />
                <DynamicJs model={model.dynamicJsM} />
            </div>
        );
    }
}
