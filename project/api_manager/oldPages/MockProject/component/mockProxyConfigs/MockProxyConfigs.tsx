import { AView } from 'libs';
import React from 'react';
import { observer } from 'mobx-react';
import { Bind } from 'lodash-decorators';
import classNames from 'classnames';
import { Button, Skeleton } from 'antd';
import { CopyBtn } from '@/business/commonComponents';
import { MyConfigTable } from './myConfig/MyConfigTable';
import { CookieConfig } from './cookieConfig/CookieConfig';
import { MockProxyConfigsM } from './MockProxyConfigsM';
import css from './MockProxyConfigs.less';

interface IProps {
    className?: string;
}

@observer
export class MockProxyConfigs extends AView<MockProxyConfigsM, IProps> {

    private renderProxyAddress(): React.ReactNode {
        const model = this.model;
        return (
            <div className={css.proxyAddress}>
                当前项目我的代理地址：<br />
                {model.address}
                <CopyBtn
                    copyContent={model.address}
                    className={css.copyAddressBtn}
                    text="复制地址"
                    type="primary"
                    icon={false}
                    size="small"
                />
            </div>
        );
    }

    @Bind
    private renderFooter(): React.ReactNode {
        return (
            <div className={css.footer}>
                <Button
                    type="primary"
                    onClick={this.model.editConfig}
                    loading={this.model.saveLoading}
                >
                    保存配置
                </Button>
            </div>
        );
    }

    @Bind
    private renderMainContent(): React.ReactNode {
        if (this.model.queryDetailLoading) {
            return <Skeleton className={css.mainContent}/>;
        }
        return (
            <div className={css.mainContent}>
                {this.renderProxyAddress()}
                <MyConfigTable model={this.model.myConfigTableM} />
                <CookieConfig model={this.model.cookieConfigM} />
            </div>
        );
    }

    public render(): React.ReactNode {
        const model = this.model;
        return (
            <div className={classNames(css.mockProxyConfigs, this.props.className)}>
                {this.renderMainContent()}
                {this.renderFooter()}
            </div>
        );
    }
}
