import { AViewModel } from 'libs';
import { observable, action, runInAction } from 'mobx';
import { Bind } from 'lodash-decorators';
import { ERouter } from 'CONFIG';
import { JSONbigStringify } from '@/index.config/tools';
import { goBack } from '@/business/commonComponents/GoBack';
import {
    nsMockManageKoasApiManageMockAllConfigGet, nsMockManageKoasApiManageMockEditConfigPost
} from '@/remote';
import { message, Modal } from 'antd';
import { MyConfigTableM } from './myConfig/MyConfigTableM';
import { CookieConfigM } from './cookieConfig/CookieConfigM';

export class MockProxyConfigsM extends AViewModel {
    @observable public moduleId: number = 0;
    @observable public address: string = '';

    public goBackUrl: string = `${ERouter.API_MOCK_ORGANIZESPACE_PROJECTMGR}${location.search}`;
    private originParams: string = '';

    @observable public saveLoading: boolean = false;
    @observable public queryDetailLoading: boolean = false;

    public myConfigTableM = new MyConfigTableM();
    public cookieConfigM = new CookieConfigM();

    constructor(moduleId: number) {
        super(moduleId);
        this.initLoading(moduleId);
    }

    // 初始化
    @action.bound
    public initLoading(moduleId: number): void {
        if (!moduleId) {
            return;
        }
        this.moduleId = moduleId;
        this.queryAllConfig();
    }

    // 获取参数
    @action.bound
    private getParams() {
        const params = {
            koasModuleId: this.moduleId,
            configs: this.myConfigTableM.getParams(),
            cookies: this.cookieConfigM.getParams()
        };
        return params;
    }

    @action.bound
    public isChange(): void {
        if (this.originParams === this.getOriginParams()) {
            goBack(this.goBackUrl, 2);
            return;
        } else {
            Modal.confirm({
                content: '配置未保存，确认离开？',
                onOk: () => goBack(this.goBackUrl, 2)
            });
        }
    }

    @action.bound
    private getOriginParams(): string {
        const params = {
            address: this.address,
            configs: this.myConfigTableM.getParams(),
            cookies: this.cookieConfigM.getParams()
        };
        return JSONbigStringify(params);
    }

    // 获取配置信息
    @Bind
    private async queryAllConfig(): Promise<void> {
        runInAction(() => this.queryDetailLoading = true);
        try {
            const result = await nsMockManageKoasApiManageMockAllConfigGet.remote({ moduleId: this.moduleId });
            runInAction(() => {
                this.address = result?.address || '';
                this.myConfigTableM.initLoading(result?.configs || []);
                this.cookieConfigM.initLoading(result?.cookies || []);
                this.queryDetailLoading = false;
                this.originParams = this.getOriginParams();
            });
        } catch {
            runInAction(() => this.queryDetailLoading = false);
        }
    }

    // 保存配置信息
    @Bind
    public async editConfig(): Promise<void> {
        if (this.cookieConfigM.checkParams()) {
            return;
        }
        runInAction(() => this.saveLoading = true);
        try {
            await nsMockManageKoasApiManageMockEditConfigPost.remote(this.getParams());
            message.success('保存成功～');
            runInAction(() => {
                this.saveLoading = false;
                this.originParams = this.getOriginParams();
            });
        } catch {
            runInAction(() => this.saveLoading = false);
        }
    }
}
