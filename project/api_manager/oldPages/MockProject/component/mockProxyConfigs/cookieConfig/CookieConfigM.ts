import { AViewModel } from 'libs';
import { observable, action } from 'mobx';
import {
    nsMockManageKoasApiManageMockAllConfigGet, nsMockManageKoasApiManageMockEditConfigPost
} from '@/remote';

interface INewCookies extends nsMockManageKoasApiManageMockEditConfigPost.ICookies {
    index: number;
}

export class CookieConfigM extends AViewModel {
    @observable public cookies: INewCookies[] = [];
    @observable public cookieLength: number = 0;

    @action.bound
    public initLoading(cookies: nsMockManageKoasApiManageMockAllConfigGet.ICookies[]): void {
        const newCookies: INewCookies[] = [];
        cookies.forEach((item, index) => {
            item['index'] = index + 1;
            newCookies.push({...item, ...{index: index + 1}});
        });
        this.cookies = newCookies;
        this.cookieLength = this.cookies.length;
    }

    // 改变Input value值
    @action.bound
    public onChangeInputValue(record, key: string, e): void {
        record[key] = e.target.value;
        this.renderCookieList();
    }

    // 重新渲染cookie配置
    @action
    private renderCookieList(): void {
        this.cookies = [...this.cookies];
    }

    // 添加cookie配置
    @action.bound
    public onAddCookie(): void {
        this.cookies.push({
            host: '',
            cookie: '',
            index: ++this.cookieLength
        });
        this.renderCookieList();
    }

    // 删除cookie
    @action.bound
    public onDeleteCookie(index: number): void {
        this.cookies = this.cookies.filter(item => item.index !== index);
    }

    @action.bound
    public getParams() {
        return this.cookies;
    }

    @action.bound
    public checkParams(): boolean {
        if (this.cookies.length) {
            const errIndex: number = this.cookies.findIndex(item => !item['cookie'] || !item['host']);
            if (errIndex > -1) {
                this.cookies[errIndex]['isShowErr'] = true;
                this.renderCookieList();
                return true;
            }
        }
        return false;
    }
}
