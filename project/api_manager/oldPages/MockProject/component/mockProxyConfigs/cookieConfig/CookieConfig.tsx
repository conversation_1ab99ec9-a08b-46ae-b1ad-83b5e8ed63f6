import React from 'react';
import { observer } from 'mobx-react';
import { Bind } from 'lodash-decorators';
import classnames from 'classnames';
import { Input, Button, Divider } from 'antd';

import { AView } from 'libs';
import { CookieConfigM } from './CookieConfigM';
import css from './CookieConfig.less';

@observer
export class CookieConfig extends AView<CookieConfigM> {

    @Bind
    private renderMyConfigTitle(): React.ReactNode {
        return (
            <Button type="primary" className={css.addCookieConfigBtn} onClick={this.model.onAddCookie}>
                添加cookie
            </Button>
        );
    }

    @Bind
    private renderHost(item): React.ReactNode {
        const isShowErr: boolean = item['isShowErr'] && !item.host;
        return (
            <div className={css.cookieCol}>
                <span className={css.label}>域名</span>
                <Input
                    value={item.host}
                    className={isShowErr ? classnames(css.content, css.error) : css.content}
                    onChange={this.model.onChangeInputValue.bind(this, item, 'host')}
                    allowClear
                />
                {isShowErr && <span className={css.errorDesc}>请输入域名</span>}
            </div>
        );
    }

    @Bind
    private renderCookie(item): React.ReactNode {
        const isShowErr: boolean = item['isShowErr'] && !item.cookie;
        return (
            <div className={css.cookieCol}>
                <span className={css.label}>Cookie</span>
                <Input
                    value={item.cookie}
                    className={isShowErr ? classnames(css.content, css.error) : css.content}
                    onChange={this.model.onChangeInputValue.bind(this, item, 'cookie')}
                    allowClear
                />
                {isShowErr && <span className={css.errorDesc}>请输入Cookie</span>}
            </div>
        );
    }

    @Bind
    private renderCookies(): React.ReactNode {
        return (
            this.model.cookies.map(item => {
                return (
                    <div className={css.cookie} key={item.index}>
                        {this.renderHost(item)}
                        {this.renderCookie(item)}
                        <Button
                            type="link"
                            onClick={this.model.onDeleteCookie.bind(this, item.index)}
                        >
                            删除
                        </Button>
                    </div>
                );
            })
        );
    }

    public render() {
        const model = this.model;
        return (
            <div className={css.cookieConfig}>
                <Divider orientation="left">配置Cookie</Divider>
                {this.renderMyConfigTitle()}
                {this.renderCookies()}
            </div>
        );
    }
}
