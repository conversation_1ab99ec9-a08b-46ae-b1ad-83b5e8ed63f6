import { AViewModel } from 'libs';
import { action, observable } from 'mobx';

interface IRowRecord {
    dynamicJs?: string;
}

const responseTemplate: string =
    `function mock(response) {
    //请在这里修改response数据 如下示例
    //response.hader.foo = "1234"
    //response.body = {
    //  data: {},
    //  result: 1
    //}
    //最后返回结果
    return response;
}`;

export class DynamicJsM extends AViewModel<object> {
    @observable public visible: boolean = false;
    @observable public dynamicJs: string = responseTemplate;

    private rowRecord: IRowRecord = {};

    @action.bound
    public initLoading(record): void {
        this.rowRecord = record;
        record?.dynamicJs && this.onChangeDynamicJs(record?.dynamicJs);
        this.visible = true;
    }

    @action.bound
    public onChangeDynamicJs(dynamicJs: string = ''): void {
        this.dynamicJs = dynamicJs;
    }

    @action.bound
    public closeDynamicJs(): void {
        this.dynamicJs = responseTemplate;
        this.rowRecord = {};
        this.visible = false;
    }

    @action.bound
    public onSaveDynamicJs(): void {
        this.rowRecord['dynamicJs'] = this.dynamicJs;
        this.closeDynamicJs();
    }
}
