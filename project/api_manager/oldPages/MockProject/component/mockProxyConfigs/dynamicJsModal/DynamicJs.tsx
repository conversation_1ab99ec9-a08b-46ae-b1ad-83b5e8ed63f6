import React from 'react';
import { observer } from 'mobx-react';
import { AView } from 'libs';
import { Modal } from 'antd';
import { AceDrag } from '@/business/aceDrag/AceDrag';
import { DynamicJsM } from './DynamicJsM';

@observer
export class DynamicJs extends AView<DynamicJsM> {
    public render() {
        const model = this.model;
        return (
            <Modal
                visible={model.visible}
                width={800}
                title="动态JS"
                onCancel={model.closeDynamicJs}
                onOk={model.onSaveDynamicJs}
            >
                <AceDrag
                    value={model.dynamicJs}
                    onChange={model.onChangeDynamicJs}
                    mode="typescript"
                />
            </Modal>
        );
    }
}
