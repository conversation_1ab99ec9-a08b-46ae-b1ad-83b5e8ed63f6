import { AViewModel } from 'libs';
import { observable, action, runInAction } from 'mobx';
import {
    nsMockManageKoasApiManageCommonGetEnumGet,
    nsMockManageKoasApiManageHttpApiUpdateApiStatusPost,
} from '@/remote';
import { Bind } from 'lodash-decorators';

export class ApiStatusModel extends AViewModel {
    @observable public code: number = -999;
    protected apiId: number = 0;
    @observable public visible: boolean = false;
    @observable public confirmLoading: boolean = false;
    @observable public docId: number = 0;
    @observable public apiStatuses: nsMockManageKoasApiManageCommonGetEnumGet.IResult[] = [];
    public onSaveCallback?(status: number, statusDesc: string, apiId: number): void;

    @action.bound
    public init(apiId: number, code: number) {
        this.apiId = apiId;
        this.code = code;
        this.visible = true;
        this.queryApiStatus();
    }

    // 关闭弹层
    @action.bound
    public onCloseModal() {
        this.visible = false;
    }

    // 确认修改API状态
    @Bind
    public async updateApiStatus() {
        runInAction(() => {
            this.visible = true;
            this.confirmLoading = true;
        });
        try {
            const params = {
                docId: this.apiId,
                nextState: this.code,
            };
            const result = await nsMockManageKoasApiManageHttpApiUpdateApiStatusPost.remote(params);
            runInAction(() => {
                if (result.result) {
                    const item = this.apiStatuses.find(apiStatus => apiStatus.code === this.code);
                    this.onSaveCallback && this.onSaveCallback(this.code, item?.desc || '', this.apiId);
                }
                this.confirmLoading = false;
                this.onCloseModal();
            });
        } catch (e) {
            runInAction(() => {
                this.confirmLoading = false;
                this.onCloseModal();
            });
        }
    }

    // 切换APi状态
    @action.bound
    public onCodeChange(e) {
        this.code = e.target.value;
    }

    // 获取API状态列表
    @Bind
    private async queryApiStatus() {
        try {
            const params = {
                className: 'EnumApiStatus'
            };
            const result = await nsMockManageKoasApiManageCommonGetEnumGet.remote(params);
            runInAction(() => {
                this.apiStatuses = result?.result || [];
            });
        } catch (e) {
        }
    }
}
