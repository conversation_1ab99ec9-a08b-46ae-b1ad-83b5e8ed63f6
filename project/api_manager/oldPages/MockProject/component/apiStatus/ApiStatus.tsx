import { AView } from 'libs';
import React from 'react';
import { ApiStatusModel } from './ApiStatusModel';
import { observer } from 'mobx-react';
import { Modal, Radio, Tag } from 'antd';
import { apiStatusEnum } from '@/business/toolFun/ToolFun';
import css from './ApiStatus.less';
@observer
export class ApiStatus extends AView<ApiStatusModel> {

    public render(): React.ReactNode {
        const model = this.model;
        return (
            <Modal
                title="编辑API状态"
                visible={model.visible}
                onOk={model.updateApiStatus}
                confirmLoading={model.confirmLoading}
                onCancel={model.onCloseModal}
            >
                <Radio.Group onChange={model.onCodeChange} value={model.code} className={css.group}>
                    {
                        model.apiStatuses.map(item => {
                            return (
                                <Radio key={item.code} value={item.code}>
                                    <Tag color={apiStatusEnum[item.code].color}>{item.desc}</Tag>
                                </Radio>
                            );
                        })
                    }
                </Radio.Group>
            </Modal>
        );
    }
}
