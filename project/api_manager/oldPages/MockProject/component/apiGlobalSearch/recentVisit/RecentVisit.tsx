import { AView } from 'libs';
import React from 'react';
import { observer } from 'mobx-react';
import { RecentVisitM } from './RecentVisitM';
import css from './RecentVisit.less';
import { List, Empty } from 'antd';
import { Bind } from 'lodash-decorators';
// import { SearchOutlined } from '@ant-design/icons';
// import { bindObserver } from '@libs/mvvm';
import { SearchEmptyIcon } from '@/business/commonIcon';
import { ListItem } from '../ListItem';

@observer
export class RecentVisit extends AView<RecentVisitM> {

    @Bind
    public componentDidMount(): void {
        this.model.globalSearchCacheApi();
    }

    @Bind
    private renderApiItem(item): React.ReactNode {
        return (
            <ListItem
                title={ item.name }
                createUser={ item.createUser }
                updateTime={ item.updateTime }
                url={ item.uri }
                urlLabel={ 'URL' }
                type={ 1 }
                item={ item }
                projectName={item.projectName}
                moduleName={item.moduleName}
            />
        );
    }

    @Bind
    private renderSearchResult(): React.ReactNode {
        const model = this.model;
        if (model.isEmptyData) {
            return (
                <List
                    header={ '最近访问' }
                    dataSource={ model.apiList }
                    renderItem={ this.renderApiItem }
                />
            );
        }
        return (
            <Empty
                image={ <SearchEmptyIcon /> }
                description={ '没有找到搜索结果' }
                className={ css.searchEmpty }
            />
        );
    }

    public render(): React.ReactNode {
        const model = this.model;
        return (
            <div
                className={ css.recentVisit }
            >
                {
                    this.renderSearchResult()
                }
            </div>
        );
    }
}
