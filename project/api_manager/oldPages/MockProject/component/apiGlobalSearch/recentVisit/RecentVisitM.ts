import { AViewModel } from 'libs';
import { observable, action, computed, runInAction } from 'mobx';
import {
    nsMockManageKoasApiManageCommonGlobalSearchCacheApiGet
} from '@/remote';

interface IApiList {
    docId: number;
    name: string;
    createUser: string;
    updateTime: string;
    moduleId: number;
    url: string;
    groupId: number;
}

export class RecentVisitM extends AViewModel {
    private departmentId: number = 0;
    @observable public currentPage: number = 1;
    @observable public apiList: IApiList[] = [];

    public getKey?(): string;

    @computed
    public get isEmptyData(): boolean {
        return Boolean(this.apiList?.length);
    }

    @action.bound
    public init(departmentId: number): void {
        this.departmentId = departmentId;
    }

    @action.bound
    public async globalSearchCacheApi() {
        try {
            const params = {
                departmentId: this.departmentId,
                key: '',
                currentPage: this.currentPage,
                pageSize: 10
            };
            const result = await nsMockManageKoasApiManageCommonGlobalSearchCacheApiGet.remote(params);
            runInAction(() => {
                this.apiList = result?.apiGlobalSearchRowList || [];
            });
        } catch (e) {
        }
    }
}
