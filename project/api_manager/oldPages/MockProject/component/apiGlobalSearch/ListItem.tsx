import React from 'react';
import { observer } from 'mobx-react';
import css from './GlobalSearch.less';
import { Bind } from 'lodash-decorators';
import { ERouter } from 'CONFIG';
import { runInAction } from 'mobx';
import { Tag } from 'antd';
import { nsMockManageKoasApiManageModuleGetHttModuleDepartmentGet } from '@/remote';
import { departmentCascader } from '@/business/global';
import { apiStatusEnum } from '@/business/toolFun/ToolFun';

interface IPorps {
    title: string;
    urlLabel?: string;
    url?: string;
    createUser: string;
    updateTime: string;
    type: number;
    item: any;
    searchKey?: string;
    moduleName?: string;
    projectName?: string;
    status?: number;
    apiType?: number;
}

@observer
export class ListItem extends React.Component<IPorps> {

    /**
     * 搜索关键词高亮
     * @param content
     */
    @Bind
    private highLight(content: string): string {
        const searchKey: string = this.props?.searchKey || '';
        const reg = new RegExp(searchKey.trim(), 'gi'); // 不区分大小写
        const str = content?.replace(/[<>&"]/g, c => { // 对代码里的标签进行转义
            return { '<': '&lt;', '>': '&gt;', '&': '&amp;', '"': '&quot;' }[c] || c;
        });
        content = str?.replace(reg, txt => {
            return `<span style="color:#1890ff;">${txt}</span>`;
        });
        return content;
    }

    @Bind
    private jumpPage(): void {
        const { item, type } = this.props;
        if (type === 3) {
            window.open(`${ERouter.API_MOCK_PROJECT_MODULEMGR}?projectId=${item?.projectId}`);
        } else if (type === 1 && item.apiType === 1) {
            const branchName = item?.branchName || '';
            const url: string = `${ERouter.API_MOCK_HTTP_API_DETAIL}?moduleId=${item.moduleId}&moduleName=${item.moduleName}&apiId=${item.docId}&branchName=${branchName}`;
            window.open(url, '_blank', 'noreferrer');
        } else {
            this.getHttModuleDepartment(type, item);
        }
    }

    @Bind
    private async getHttModuleDepartment(type: number, item) {
        try {
            const params = {
                moduleId: item.moduleId
            };
            const result = await nsMockManageKoasApiManageModuleGetHttModuleDepartmentGet.remote(params);
            runInAction(() => {
                departmentCascader.setDepartment(result.departmentIdList, result.departmentFullName);
                let url: string = '';
                if (type === 2) {
                    url = `${ERouter.API_MOCK_PROJECT_MODULEDETAIL}?moduleId=${item.moduleId}&projectId=${item.projectId}&showType=`;
                }
                if (type === 1) {
                    url = `${ERouter.API_MOCK_PROJECT_MODULEDETAIL}?groupSelectKey=${item.groupId}-${item.docId}&moduleId=${item.moduleId}&projectId=${item.projectId}&showType=view`;
                }
                window.open(url);
            });
        } catch (e) {
        }
    }

    @Bind
    private renderProjectName(): React.ReactNode {
        const { projectName } = this.props;
        if (projectName) {
            return (
                <span className={css.projectName}>
                    项目：
                    <span dangerouslySetInnerHTML={{ __html: this.highLight(projectName || '') }} />
                </span>
            );
        }
    }

    @Bind
    private renderTitle(): React.ReactNode {
        return (
            <div
                className={css.title}
                dangerouslySetInnerHTML={{ __html: this.highLight(this.props.title) }}
            />
        );
    }

    @Bind
    private renderApiTop(): React.ReactNode {
        const {apiType, status} = this.props;
        return (
            <div className={css.apiTop}>
                <div className={css.apiTitle}>
                    {this.renderTitle()}
                    {
                        typeof status === 'number' && apiType === 0 &&
                        <Tag color={apiStatusEnum[status]?.color} className={css.apiStatus}>
                            {apiStatusEnum[status].statusDesc}
                        </Tag>
                    }
                </div>
                {
                    apiType === 1 &&
                    <a className={css.apiType}>自动解析</a>
                }
            </div>
        );
    }

    public render(): React.ReactNode {
        const props: IPorps = this.props;
        return (
            <div
                className={css.listItem}
                onClick={this.jumpPage}
            >
                {/* <div
                    className={css.title}
                    dangerouslySetInnerHTML={{ __html: this.highLight(props.title) }}
                /> */}
                {this.renderApiTop()}
                <div className={css.main}>
                    {this.renderProjectName()}
                    {
                        props.moduleName &&
                        <span className={css.moduleName}>
                            module name：
                            <span dangerouslySetInnerHTML={{ __html: this.highLight(props.moduleName || '') }} />
                        </span>
                    }
                    {
                        props.urlLabel &&
                        <span className={css.url}>
                            {`${props.urlLabel}：`}
                            <span dangerouslySetInnerHTML={{ __html: this.highLight(props.url || '') }} />
                        </span>
                    }
                </div>
                <div className={css.footer}>
                    <span className={css.createUser}>
                        负责人：
                        <span dangerouslySetInnerHTML={{ __html: this.highLight(props.createUser || '') }} />
                    </span>
                    <span>创建时间：{props.updateTime}</span>
                </div>
            </div>
        );
    }
}
