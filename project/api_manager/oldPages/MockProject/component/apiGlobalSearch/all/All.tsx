import { AView } from 'libs';
import React from 'react';
import { observer } from 'mobx-react';
import { AllM } from './AllM';
import css from './All.less';
import { List, Empty } from 'antd';
import { Bind } from 'lodash-decorators';
// import { SearchOutlined } from '@ant-design/icons';
// import { bindObserver } from '@libs/mvvm';
import { SearchEmptyIcon } from '@/business/commonIcon';
import { ListItem } from '../ListItem';
import { SearchOutlined } from '@ant-design/icons';

@observer
export class All extends AView<AllM> {

    @Bind
    private renderApiItem(item): React.ReactNode {
        return (
            <ListItem
                title={ item.name }
                url={ item.uri }
                urlLabel={ 'URL' }
                createUser={ item.createUser }
                updateTime={ item.updateTime }
                type={ 1 }
                item={ item }
                searchKey={ this.model.getKey ? this.model.getKey() : ''}
                projectName={item.projectName}
                status={item.status}
                apiType={item.apiType}
                moduleName={item.moduleName}
            />
        );
    }

    @Bind
    private renderModuleItem(item): React.ReactNode {
        return (
            <ListItem
                title={ item.title }
                createUser={ item.createUser }
                updateTime={ item.updateTime }
                url={ item.uri }
                urlLabel={ 'git地址' }
                moduleName={ item.name }
                type={ 2 }
                item={ item }
                searchKey={ this.model.getKey ? this.model.getKey() : ''}
                projectName={item.projectName}
            />
        );
    }

    @Bind
    private renderProjectItem(item): React.ReactNode {
        return (
            <ListItem
                title={ item.name }
                createUser={ item.createUser }
                updateTime={ item.createTime }
                type={ 3 }
                item={ item }
                searchKey={ this.model.getKey ? this.model.getKey() : ''}
            />
        );
    }

    @Bind
    private renderListFooter(key: string): React.ReactNode {
        return (
            <div
                className={ css.moreBtn }
                onClick={ () => this.model.clickTabs(key) }
            >
                <SearchOutlined className={ css.moreBtnIcon } /> 更多
            </div>
        );
    }

    @Bind
    private renderSearchResult(): React.ReactNode {
        const model = this.model;
        if (model.isEmptyData) {
            return (
                <>
                    {
                        Boolean(model.apiList?.length) &&
                        <List
                            header={ 'API' }
                            dataSource={ model.apiList }
                            renderItem={ this.renderApiItem }
                            size={ 'large' }
                            bordered={ false }
                            footer={ this.renderListFooter('1') }
                        />
                    }
                    {
                        Boolean(model.moduleList?.length) &&
                        <List
                            header={ '模块' }
                            dataSource={ model.moduleList }
                            renderItem={ this.renderModuleItem }
                            size={ 'large' }
                            bordered={ false }
                            footer={ this.renderListFooter('2') }
                        />
                    }
                    {
                        Boolean(model.projectList?.length) &&
                        <List
                            header={ '项目' }
                            dataSource={ model.projectList }
                            renderItem={ this.renderProjectItem }
                            size={ 'large' }
                            bordered={ false }
                            footer={ this.renderListFooter('3') }
                        />
                    }
                </>
            );
        }
        return (
            <Empty
                image={ <SearchEmptyIcon /> }
                description={ '没有找到搜索结果' }
                className={ css.searchEmpty }
            />
        );
    }

    public render(): React.ReactNode {
        const model = this.model;
        return (
            <div
                className={ css.all }
            >
                {
                    this.renderSearchResult()
                }
            </div>
        );
    }
}
