import { AViewModel } from 'libs';
import { observable, action, computed, runInAction } from 'mobx';
import {
    nsMockManageKoasApiManageCommonGlobalSearchAllGet
} from '@/remote';

interface IApiList {
    docId: number;
    name: string;
    createUser: string;
    updateTime: string;
    moduleId: number;
    url: string;
    groupId: number;
}

interface IModuleList {
    name: string;
    createUser: string;
    updateTime: string;
    moduleId: number;
    title: string;
    projectId: number;
    gitUrl: string;
}

interface IProjectList {
    name: string;
    createUser: string;
    updateTime: string;
    projectId: number;
}

export class AllM extends AViewModel {
    private departmentId: number = 0;
    @observable public currentPage: number = 1;
    @observable public apiList: IApiList[] = [];
    @observable public moduleList: IModuleList[] = [];
    @observable public projectList: IProjectList[] = [];
    @observable public searchScope: number = 0;

    public getKey?(): string;
    public onClickTabs?(key: string): void;

    @computed
    public get isEmptyData(): boolean {
        return Boolean(this.apiList?.length || this.moduleList?.length || this.projectList?.length);
    }

    @action.bound
    public init(departmentId: number, getKey?, searchScope: number = 0): void {
        this.departmentId = departmentId;
        this.searchScope = searchScope;
        if (getKey) {
            this.getKey = getKey;
        }
    }

    @action.bound
    public clickTabs(key: string): void {
        this.onClickTabs && this.onClickTabs(key);
    }

    @action.bound
    public searchResult(): void {
        const key: string = this?.getKey ? this.getKey() : '';
        this.globalSearchAll(key);
    }

    @action.bound
    public async globalSearchAll(key: string) {
        try {
            const params = {
                departmentId: this.departmentId,
                key,
                currentPage: 1,
                pageSize: 3,
                searchScope: this.searchScope
            };
            const result = await nsMockManageKoasApiManageCommonGlobalSearchAllGet.remote(params);
            runInAction(() => {
                this.apiList = result?.apiGlobalSearchRowList || [];
                this.moduleList = result?.moduleGlobalSearchRowList || [];
                this.projectList = result?.projectGlobalSearchRowList || [];
            });
        } catch (e) {
        }
    }
}
