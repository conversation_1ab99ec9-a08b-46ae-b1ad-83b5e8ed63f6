import { AViewModel } from 'libs';
import { observable, action } from 'mobx';
import { AllM } from './all/AllM';
import { RecentVisitM } from './recentVisit/RecentVisitM';
import { ApiM } from './api/ApiM';
import { ModuleM } from './module/ModuleM';
import { ProjectM } from './project/ProjectM';

export class GlobalSearchM extends AViewModel {
    @observable public visible: boolean = false;

    private departmentId: number = 0;
    @observable public searchScope: number = 0;
    @observable public key: string = '';
    @observable public activeKey: string = '0'; // '0'|'1'|'2'|'3'

    public allM = new AllM();
    public recentVisitM = new RecentVisitM();
    public apiM = new ApiM();
    public moduleM = new ModuleM();
    public projectM = new ProjectM();

    @action.bound
    public init(departmentId: number = 0, searchScope: number = 0): void {
        this.departmentId = departmentId;
        this.searchScope = searchScope;
        this.visible = true;
        this.recentVisitM.init(this.departmentId);
        this.allM.init(this.departmentId, this.getKey, this.searchScope);
        this.allM.onClickTabs = this.onClickTabs;
        this.apiM.init(this.departmentId, this.getKey, this.searchScope);
        this.moduleM.init(this.departmentId, this.getKey);
        this.projectM.init(this.departmentId, this.getKey);
    }

    @action.bound
    public onCloseGlobalSearch(): void {
        this.visible = false;
    }

    @action.bound
    public onClickTabs(key: string): void {
        this.activeKey = key;
        this.searchResult();
    }

    @action.bound
    public getKey(): string {
        return this.key;
    }

    @action.bound
    public searchResult(): void {
        if (this.key) {
            if (this.activeKey === '0') {
                this.allM.searchResult();
            }
            if (this.activeKey === '1') {
                this.apiM.globalSearchApi();
            }
            if (this.activeKey === '2') {
                this.moduleM.globalSearchModule();
            }
            if (this.activeKey === '3') {
                this.projectM.globalSearchProject();
            }
        }
    }

    @action.bound
    public onClearSearch(): void {
        this.key = '';
    }
}
