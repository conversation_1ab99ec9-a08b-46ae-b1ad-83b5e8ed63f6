import { AView } from 'libs';
import React from 'react';
import { observer } from 'mobx-react';
import { ProjectM } from './ProjectM';
import css from './Project.less';
import { List, Empty, Spin } from 'antd';
import { Bind } from 'lodash-decorators';
import { SearchEmptyIcon } from '@/business/commonIcon';
import { ListItem } from '../ListItem';
import InfiniteScroll from 'react-infinite-scroller';

@observer
export class Project extends AView<ProjectM> {

    @Bind
    private renderProjectItem(item): React.ReactNode {
        return (
            <ListItem
                title={ item.name }
                createUser={ item.createUser }
                updateTime={ item.createTime }
                type={ 3 }
                item={ item }
                searchKey={ this.model.getKey ? this.model.getKey() : '' }
            />
        );
    }

    @Bind
    private renderSearchResult(): React.ReactNode {
        const model = this.model;
        if (model.isEmptyData) {
            return (
                <InfiniteScroll
                    initialLoad={ false }
                    pageStart={ 0 }
                    useWindow={ false }
                    hasMore={ !model.loading && model.hasMore }
                    loadMore={ model.loadMore }
                >
                    <List
                        dataSource={ model.projectList }
                        renderItem={ this.renderProjectItem }
                    >
                        <div className={ css.loading }>
                        {model.loading && <Spin />}
                            {
                                (!model.loading && !model.hasMore) || model.projectList.length < 20 &&
                                <span>没有更多数据了～</span>
                            }
                        </div>
                    </List>
                </InfiniteScroll>
            );
        }
        return (
            <Empty
                image={ <SearchEmptyIcon /> }
                description={ '没有找到搜索结果' }
                className={ css.searchEmpty }
            />
        );
    }

    public render(): React.ReactNode {
        const model = this.model;
        return (
            <div
                className={ css.project }
            >
                { this.renderSearchResult() }
            </div>
        );
    }
}
