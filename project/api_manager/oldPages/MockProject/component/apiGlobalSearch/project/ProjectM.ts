import { AViewModel } from 'libs';
import { observable, action, computed, runInAction } from 'mobx';
import {
    nsMockManageKoasApiManageCommonGlobalSearchProjectGet
} from '@/remote';

interface IProjectList {
    name: string;
    createUser: string;
    createTime: string;
    projectId: number;
}

export class ProjectM extends AViewModel {
    private departmentId: number = 0;
    @observable public currentPage: number = 1;
    @observable public loading: boolean = false;
    @observable public hasMore: boolean = true;
    @observable public projectList: IProjectList[] = [];

    public getKey?(): string;

    @computed
    public get isEmptyData(): boolean {
        return Boolean(this.projectList?.length);
    }

    @action.bound
    public init(departmentId: number, getKey?): void {
        this.departmentId = departmentId;
        if (getKey) {
            this.getKey = getKey;
        }
    }

    @action.bound
    public loadMore() {
        this.currentPage = this.currentPage + 1;
        this.globalSearchProject(true);
    }

    @action.bound
    private initParams(): void {
        this.currentPage = 1;
        this.hasMore = true;
    }

    @action.bound
    public async globalSearchProject(loadMore?: boolean) {
        if (!loadMore) {
            this.initParams();
        }
        this.loading = true;
        try {
            const key: string = this?.getKey ? this.getKey() : '';
            const params = {
                departmentId: this.departmentId,
                key,
                currentPage: this.currentPage,
                pageSize: 30
            };
            const result = await nsMockManageKoasApiManageCommonGlobalSearchProjectGet.remote(params);
            runInAction(() => {
                if (loadMore) {
                    this.projectList = [...this.projectList, ...result?.projectGlobalSearchRowList] || [];
                } else {
                    this.projectList = result?.projectGlobalSearchRowList || [];
                }
                if (!result?.projectGlobalSearchRowList?.length) {
                    this.hasMore = false;
                }
                this.loading = false;
            });
        } catch (e) {
            runInAction(() => {
                this.loading = false;
            });
        }
    }
}
