import { AViewModel } from 'libs';
import { observable, action, computed, runInAction } from 'mobx';
import {
    nsMockManageKoasApiManageCommonGlobalSearchApiGet
} from '@/remote';

interface IApiList {
    docId: number;
    name: string;
    createUser: string;
    updateTime: string;
    moduleId: number;
    url: string;
    groupId: number;
}

export class ApiM extends AViewModel {
    private departmentId: number = 0;
    @observable public currentPage: number = 1;
    @observable public loading: boolean = false;
    @observable public hasMore: boolean = true;
    @observable public apiList: IApiList[] = [];
    @observable public searchScope: number = 0;

    public getKey?(): string;

    @computed
    public get isEmptyData(): boolean {
        return Boolean(this.apiList?.length);
    }

    @action.bound
    public init(departmentId: number, getKey?, searchScope: number = 0): void {
        this.departmentId = departmentId;
        this.searchScope = searchScope;
        if (getKey) {
            this.getKey = getKey;
        }
    }

    @action.bound
    public loadMore() {
        this.currentPage = this.currentPage + 1;
        this.globalSearchApi(true);
    }

    @action.bound
    private initParams(): void {
        this.currentPage = 1;
        this.hasMore = true;
    }

    @action.bound
    public async globalSearchApi(loadMore?: boolean) {
        if (!loadMore) {
            this.initParams();
        }
        this.loading = true;
        try {
            const key: string = this?.getKey ? this.getKey() : '';
            const params = {
                departmentId: this.departmentId,
                key,
                currentPage: this.currentPage,
                pageSize: 20,
                searchScope: this.searchScope
            };
            const result = await nsMockManageKoasApiManageCommonGlobalSearchApiGet.remote(params);
            runInAction(() => {
                if (loadMore) {
                    this.apiList = [...this.apiList, ...result?.apiGlobalSearchRowList] || [];
                } else {
                    this.apiList = result?.apiGlobalSearchRowList || [];
                }
                if (!result?.apiGlobalSearchRowList?.length) {
                    this.hasMore = false;
                }
                this.loading = false;
            });
        } catch (e) {
            runInAction(() => {
                this.loading = false;
            });
        }
    }
}
