import { AView } from 'libs';
import React from 'react';
import { observer } from 'mobx-react';
import { ApiM } from './ApiM';
import css from './Api.less';
import { List, Empty, Spin } from 'antd';
import { Bind } from 'lodash-decorators';
import { SearchEmptyIcon } from '@/business/commonIcon';
import { ListItem } from '../ListItem';
import InfiniteScroll from 'react-infinite-scroller';

@observer
export class Api extends AView<ApiM> {

    @Bind
    private renderApiItem(item): React.ReactNode {
        return (
            <ListItem
                title={ item.name }
                createUser={ item.createUser }
                updateTime={ item.updateTime }
                url={ item.uri }
                urlLabel={ 'URL' }
                type={ 1 }
                item={ item }
                searchKey={ this.model.getKey ? this.model.getKey() : '' }
                projectName={item.projectName}
                status={item.status}
                apiType={item.apiType}
                moduleName={item.moduleName}
            />
        );
    }

    @Bind
    private renderSearchResult(): React.ReactNode {
        const model = this.model;
        if (model.isEmptyData) {
            return (
                <InfiniteScroll
                    initialLoad={ false }
                    pageStart={ 0 }
                    useWindow={ false }
                    hasMore={ !model.loading && model.hasMore }
                    loadMore={ model.loadMore }
                >
                    <List
                        // header={ '最近访问' }
                        dataSource={ model.apiList }
                        renderItem={ this.renderApiItem }
                    >
                        <div className={ css.loading }>
                            { model.loading && <Spin />}
                            {(!model.loading && !model.hasMore) || model.apiList.length < 20 && <span>没有更多数据了～</span>}
                        </div>
                    </List>
                </InfiniteScroll>
            );
        }
        return (
            <Empty
                image={ <SearchEmptyIcon /> }
                description={ '没有找到搜索结果' }
                className={ css.searchEmpty }
            />
        );
    }

    public render(): React.ReactNode {
        const model = this.model;
        return (
            <div
                className={ css.api }
            >
                {
                    this.renderSearchResult()
                }
            </div>
        );
    }
}
