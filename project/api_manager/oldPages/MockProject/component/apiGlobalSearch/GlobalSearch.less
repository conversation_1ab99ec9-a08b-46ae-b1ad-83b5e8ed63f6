.globalSearchModal {
  .titleWrap {
    width: calc(100% - 40px);
    display: flex;
    align-items: center;

    .titleDivider {
      height: 24px;
      margin: 0;
    }
  }

  .listItem {
    padding: 2px 0;
    cursor: pointer;
    border-radius: 2px;
    margin-top: 4px;
    border-bottom: 1px solid #f5f6f7;

    .apiTop {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .apiTitle {
        display: flex;
        align-items: center;
      }

      .apiStatus {
        transform: scale(.8);
      }

      .apiType {
        margin-right: 8px;
      }
    }

    .title {
      font-weight: bolder;
      margin-bottom: 2px;
    }

    .main {
      margin-bottom: 2px;
      opacity: .8;
      font-size: 12px;
      display: flex;
    }

    .footer {
      display: flex;
      opacity: .8;
      font-size: 12px;
    }

    .createUser,
    .moduleName,
    .projectName {
      display: flex;
      width: 240px;
      min-width: 240px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }

    .url {
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }

  .listItem:hover {
    background-color: #f5f6f7;
  }

  :global {
    .ant-modal-header {
      padding: 16px;
      border-bottom: 1px solid #EBEDF0;
    }

    .ant-modal-close .ant-modal-close-x {
      height: 54px;
      line-height: 54px;
      padding-right: 0;
    }

    .ant-modal-body {
      padding-top: 0;
    }

    .anticon {
      vertical-align: -0.5em;
    }

    .ant-input-suffix {
      cursor: pointer;
      opacity: .6;
    }

    .ant-input-affix-wrapper-lg {
      padding: 6 11px;
    }

    .ant-tabs-top>.ant-tabs-nav,
    .ant-tabs-bottom>.ant-tabs-nav,
    .ant-tabs-top>div>.ant-tabs-nav,
    .ant-tabs-bottom>div>.ant-tabs-nav {
      margin-bottom: 0;
    }

    .ant-list-header {
      padding: 6px 0;
      font-weight: bolder;
      font-size: 16px;
    }
  }
}