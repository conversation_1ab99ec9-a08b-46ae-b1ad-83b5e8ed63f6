import { AView } from 'libs';
import React from 'react';
import { observer } from 'mobx-react';
import { GlobalSearchM } from './GlobalSearchM';
import css from './GlobalSearch.less';
import { Modal, Input, Divider, Tabs } from 'antd';
import { Bind, Debounce } from 'lodash-decorators';
import { SearchOutlined } from '@ant-design/icons';
import { bindObserver } from '@libs/mvvm';
import { All } from './all/All';
import { RecentVisit } from './recentVisit/RecentVisit';
import { Api } from './api/Api';
import { Module } from './module/Module';
import { Project } from './project/Project';

const Input_key = bindObserver(Input, 'key');

const { TabPane } = Tabs;

@observer
export class GlobalSearch extends AView<GlobalSearchM> {

    @Debounce(300)
    @Bind
    private onSearchResult(): void {
        this.model.searchResult();
    }

    @Bind
    private renderClearBtn(): React.ReactNode {
        const model = this.model;
        if (model.key) {
            return (
                <span onClick={ model.onClearSearch }>清除</span>
            );
        }
    }

    @Bind
    private renderSearchTitle(): React.ReactNode {
        const model = this.model;
        return (
            <div className={ css.titleWrap }>
                <Input_key
                    model={ model }
                    placeholder={ '请输入关键字' }
                    size={ 'large' }
                    prefix={ <SearchOutlined /> }
                    suffix={ this.renderClearBtn() }
                    bordered={ false }
                    className={ css.searchInput }
                    onChange={ this.onSearchResult }
                    autoFocus={ true }
                />
                <Divider type={ 'vertical' } className={ css.titleDivider } />
            </div>
        );
    }

    @Bind
    private renderSearchResult(): React.ReactNode {
        const model = this.model;
        if (model.key) {
            return (
                <Tabs activeKey={ model.activeKey } onTabClick={ model.onClickTabs }>
                    <TabPane tab={ '全部' } key={ '0' }>
                        <All model={ model.allM } />
                    </TabPane>
                    <TabPane tab={ 'API' } key={ '1' }>
                        <Api model={ model.apiM } />
                    </TabPane>
                    <TabPane tab={ '模块' } key={ '2' }>
                        <Module model={ model.moduleM } />
                    </TabPane>
                    <TabPane tab={ '项目' } key={ '3' }>
                        <Project model={ model.projectM } />
                    </TabPane>
                </Tabs>
            );
        } else {
            return (
                <RecentVisit model={ model.recentVisitM } />
            );
        }
    }

    public render(): React.ReactNode {
        const model = this.model;
        return (
            <Modal
                title={ this.renderSearchTitle() }
                visible={ model.visible }
                className={ css.globalSearchModal }
                onCancel={ model.onCloseGlobalSearch }
                width={ 900 }
                footer={ false }
                destroyOnClose={ true }
            >
                { this.renderSearchResult() }
            </Modal>
        );
    }
}
