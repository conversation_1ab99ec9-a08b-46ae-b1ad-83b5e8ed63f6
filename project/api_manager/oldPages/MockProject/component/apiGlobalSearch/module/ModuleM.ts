import { AViewModel } from 'libs';
import { observable, action, computed, runInAction } from 'mobx';
import {
    nsMockManageKoasApiManageCommonGlobalSearchModuleGet
} from '@/remote';

interface IModuleList {
    name: string;
    createUser: string;
    updateTime: string;
    moduleId: number;
    title: string;
    projectId: number;
    gitUrl: string;
}

export class ModuleM extends AViewModel {
    private departmentId: number = 0;
    @observable public currentPage: number = 1;
    @observable public loading: boolean = false;
    @observable public hasMore: boolean = true;
    @observable public moduleList: IModuleList[] = [];

    public getKey?(): string;

    @computed
    public get isEmptyData(): boolean {
        return Boolean(this.moduleList?.length);
    }

    @action.bound
    public init(departmentId: number, getKey?): void {
        this.departmentId = departmentId;
        if (getKey) {
            this.getKey = getKey;
        }
    }

    @action.bound
    public loadMore() {
        this.currentPage = this.currentPage + 1;
        this.globalSearchModule(true);
    }

    @action.bound
    private initParams(): void {
        this.currentPage = 1;
        this.hasMore = true;
    }

    @action.bound
    public async globalSearchModule(loadMore?: boolean) {
        if (!loadMore) {
            this.initParams();
        }
        this.loading = true;
        try {
            const key: string = this?.getKey ? this.getKey() : '';
            const params = {
                departmentId: this.departmentId,
                key,
                currentPage: this.currentPage,
                pageSize: 20
            };
            const result = await nsMockManageKoasApiManageCommonGlobalSearchModuleGet.remote(params);
            runInAction(() => {
                if (loadMore) {
                    this.moduleList = [...this.moduleList, ...result?.moduleGlobalSearchRowList] || [];
                } else {
                    this.moduleList = result?.moduleGlobalSearchRowList || [];
                }
                if (!result?.moduleGlobalSearchRowList?.length) {
                    this.hasMore = false;
                }
                this.loading = false;
            });
        } catch (e) {
            runInAction(() => {
                this.loading = false;
            });
        }
    }
}
