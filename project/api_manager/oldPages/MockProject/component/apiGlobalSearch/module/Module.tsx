import { AView } from 'libs';
import React from 'react';
import { observer } from 'mobx-react';
import { ModuleM } from './ModuleM';
import css from './Module.less';
import { List, Empty, Spin } from 'antd';
import { Bind } from 'lodash-decorators';
import { SearchEmptyIcon } from '@/business/commonIcon';
import { ListItem } from '../ListItem';
import InfiniteScroll from 'react-infinite-scroller';

@observer
export class Module extends AView<ModuleM> {

    @Bind
    private renderModuleItem(item): React.ReactNode {
        return (
            <ListItem
                title={item.title}
                createUser={item.createUser}
                updateTime={item.updateTime}
                url={item.gitUrl}
                urlLabel={'git地址'}
                moduleName={item.name}
                type={2}
                item={item}
                searchKey={this.model.getKey ? this.model.getKey() : ''}
                projectName={item.projectName}
            />
        );
    }

    @Bind
    private renderSearchResult(): React.ReactNode {
        const model = this.model;
        if (model.isEmptyData) {
            return (
                <InfiniteScroll
                    initialLoad={false}
                    pageStart={0}
                    useWindow={false}
                    hasMore={!model.loading && model.hasMore}
                    loadMore={model.loadMore}
                >
                    <List
                        dataSource={model.moduleList}
                        renderItem={this.renderModuleItem}
                    >
                        <div className={css.loading}>
                            {model.loading && <Spin />}
                            {
                                (!model.loading && !model.hasMore) || model.moduleList.length < 20 &&
                                <span>没有更多数据了～</span>
                            }
                        </div>
                    </List>
                </InfiniteScroll>
            );
        }
        return (
            <Empty
                image={<SearchEmptyIcon />}
                description={'没有找到搜索结果'}
                className={css.searchEmpty}
            />
        );
    }

    public render(): React.ReactNode {
        const model = this.model;
        return (
            <div
                className={css.module}
            >
                {this.renderSearchResult()}
            </div>
        );
    }
}
