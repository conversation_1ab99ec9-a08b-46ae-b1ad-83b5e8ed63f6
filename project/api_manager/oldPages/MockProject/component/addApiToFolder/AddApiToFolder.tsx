import { AView } from 'libs';
import React from 'react';
import { observer } from 'mobx-react';
import { Bind } from 'lodash-decorators';
import { AddApiToFolderM } from './AddApiToFolderM';
import css from './AddApiToFolder.less';
import { Modal, Tree, Empty } from 'antd';
import { SearchEmptyIcon } from '@/business/commonIcon';
import { KdevIconFont } from '@/business/commonComponents/KdevIconFont';

@observer
export class AddApiToFolder extends AView<AddApiToFolderM> {

    @Bind
    private titleRender(nodeData): React.ReactNode {
        return (
            <div className={css.treeTitle}>
                <KdevIconFont
                    id={'#iconfolder'}
                    className={css.treeTitleFolderIcon}
                />
                {nodeData.name}
            </div>
        );
    }

    @Bind
    private renderModalBody(): React.ReactNode {
        const model = this.model;
        if (model.folderList.length) {
            return (
                <>
                    <div className={css.labelRequired}>选择集合</div>
                    <Tree
                        treeData={model.folderList}
                        className={css.folderTree}
                        titleRender={this.titleRender}
                        blockNode
                        onSelect={model.onSelectFolderId}
                    />
                </>
            );
        }
        return (
            <Empty image={ <SearchEmptyIcon /> }/>
        );
    }

    public render(): React.ReactNode {
        const model = this.model;
        return (
            <Modal
                title={ '添加到集合' }
                visible={ model.visible }
                className={ css.addApiToFolderModal }
                onCancel={ model.onCloseAddApiModal }
                destroyOnClose={ true }
                onOk={model.onAddApiToFolder}
                okButtonProps={{
                    loading: model.addApiBtnLoading
                }}
            >
                {this.renderModalBody()}
            </Modal>
        );
    }
}
