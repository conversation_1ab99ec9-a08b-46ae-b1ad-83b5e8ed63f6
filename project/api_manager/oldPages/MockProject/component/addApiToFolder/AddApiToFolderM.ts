import { AViewModel } from 'libs';
import { observable, action, runInAction } from 'mobx';
import { Bind } from 'lodash-decorators';
import { nsMockManageKoasApiManageFolderQueryFolderListGet, nsMockManageKoasApiManageFolderMoveInFolderRequestPost } from '@/remote';
import { message } from 'antd';

interface IFolderList {
    key: number;
}

export class AddApiToFolderM extends AViewModel {
    @observable public visible: boolean = false;
    private type: number = 1;
    private departmentId: number = 0;
    private docId: number = 0;
    private folderId: number = 0;
    @observable public folderList: IFolderList[] = [];
    @observable public addApiBtnLoading: boolean = false;

    @action.bound
    public init(departmentId: number, docId: number, type: 1 | 2 = 1): void {
        this.departmentId = departmentId;
        this.docId = docId;
        this.visible = true;
        this.queryFolderList();
    }

    @action.bound
    public onCloseAddApiModal(): void {
        this.visible = false;
        this.type = 1;
        this.departmentId = 0;
        this.docId = 0;
        this.folderId = 0;
        this.folderList = [];
    }

    @action.bound
    public onSelectFolderId(selectedKeys): void {
        this.folderId = selectedKeys.length ? selectedKeys[0] : 0;
    }

    @action
    private formatFolderList(folderList): void {
        this.folderList = folderList.map(item => {
            item.key = item.id;
            return item;
        });
    }

    @action.bound
    public onAddApiToFolder(): void {
        if (!this.folderId) {
            message.warn('请选择集合');
            return;
        }
        this.moveInFolderRequest();
    }

    @Bind
    private async queryFolderList(): Promise<void> {
        try {
            const params = {
                departmentId: this.departmentId,
                name: ''
            };
            const result = await nsMockManageKoasApiManageFolderQueryFolderListGet.remote(params);
            result?.folderList && this.formatFolderList(result?.folderList);
        } catch {
        }
    }

    @Bind
    public async moveInFolderRequest(): Promise<void> {
        try {
            runInAction(() => this.addApiBtnLoading = true);
            const params = {
                docIdList: [this.docId],
                folderId: this.folderId
            };
            await nsMockManageKoasApiManageFolderMoveInFolderRequestPost.remote(params);
            message.success('移入成功～');
            runInAction(() => {
                this.addApiBtnLoading = false;
                this.onCloseAddApiModal();
            });
        } catch {
            runInAction(() => this.addApiBtnLoading = false);
        }
    }
}
