import { AViewModel } from 'libs';
import { observable, action, runInAction } from 'mobx';
import { nsMockManageKoasApiManageHttpApiQueryDiffBranchListGet } from '@/remote';
import { Bind } from 'lodash-decorators';

export class ApiDiffBranchM extends AViewModel {
    @observable public visible: boolean = false;
    private docId: number = 0;
    @observable public diffBranchList: nsMockManageKoasApiManageHttpApiQueryDiffBranchListGet.IDiffBranch[] = [];

    @action
    public init(docId: number): void {
        if (docId !== this.docId) {
            this.docId = docId;
            this.queryDiffBranchList();
        }
        this.visible = true;
    }

    @action.bound
    public onCloseApiDiffBranch(): void {
        this.visible = false;
    }

    @Bind
    private async queryDiffBranchList(): Promise<void> {
        try {
            const result = await nsMockManageKoasApiManageHttpApiQueryDiffBranchListGet.remote({docId: this.docId});
            runInAction(() => {
                this.diffBranchList = result?.diffBranchList || [];
            });
        } catch {
        }
    }
}
