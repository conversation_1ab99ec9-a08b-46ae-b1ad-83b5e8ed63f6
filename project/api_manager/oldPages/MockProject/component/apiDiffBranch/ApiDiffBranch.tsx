import { <PERSON><PERSON>ie<PERSON> } from 'libs';
import React from 'react';
import { ApiDiffBranchM } from './ApiDiffBranchM';
import { observer } from 'mobx-react';
import { List, Button, Modal } from 'antd';
import Bind from 'lodash-decorators/bind';
import { KdevIconFont } from '@/business/commonComponents/KdevIconFont';
import { ERouter } from 'CONFIG';

@observer
export class ApiDiffBranch extends AView<ApiDiffBranchM> {

    @Bind
    private renderFooter(): React.ReactNode {
        return (
            <Button onClick={this.model.onCloseApiDiffBranch} type="primary">关闭</Button>
        );
    }

    @Bind
    private renderListItem(nodaData): React.ReactNode {
        return (
            <List.Item>
                <a
                    href={`${location.origin}${ERouter.API_MOCK_VERSIONCOMPARISON}?automicId=${nodaData.automaticId}`}
                    target={'_blank'}
                >
                    <KdevIconFont
                        id={nodaData.isTheSame ? '#iconchenggong' : '#iconshibai'}
                        style={{ color: nodaData.isTheSame ? '#31bf30' : '#ff4d4f', marginRight: '4px' }}
                    />
                    {nodaData.branchName}
                </a>
            </List.Item>
        );
    }

    public render(): React.ReactNode {
        const model = this.model;
        return (
            <Modal
                visible={model.visible}
                title="一致性校验详情"
                footer={this.renderFooter()}
                onCancel={model.onCloseApiDiffBranch}
            >
                <List
                    header={'下列分支中引用API'}
                    bordered
                    dataSource={model.diffBranchList}
                    renderItem={this.renderListItem}
                />
            </Modal>
        );
    }
}
