.apiListWrap {
  height: 100%;
  overflow: auto;

  .leftGroupWrap {
    padding: 16px 0 20px;
    height: 100%;

    .leftGroupTop {
      display: flex;
      margin: 0 12px;

      .createGroupBtn {
        margin-left: 4px;
      }
    }

    .groupType{
      margin: 12px 0 0 12px;
    }

    .groupTree {
      margin-top: 16px;
      margin-right: 12px;
      // height: calc(100% - 72px);
      // overflow: auto;

      .groupTitle {
        display: flex;
        align-items: center;
        justify-content: space-between;
        width: 100%;
        position: relative;
        .groupName {
          word-break: normal;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
          width: 100%;
          position: relative;
          
        }

        .groupMoreOperation {
          float: right;
          z-index: 2;
        }

        .tag {
          position: absolute;
          left: -54px;
          transform: scale(.7);
        }
      }

      .folderIcon {
        color: #ffd400;
        margin-right: 4px;
      }
    }

    .empty {
      padding-top: 40px;

      :global {
        .ant-empty-description {
          opacity: .25;
        }
      }
    }

    :global {
      .ant-tree .ant-tree-treenode {
        overflow: hidden;
        width: 100%;
        display: flex;
        align-items: center;
      }

      .ant-tree.ant-tree-block-node .ant-tree-list-holder-inner .ant-tree-node-content-wrapper {
        width: calc(100% - 24px);
      }

      .ant-skeleton {
        width: calc(100% - 24px);
        margin-left: 12px;
      }
    }
  }

  .rightPane{
    position: relative;

    .expandIcon{
      position: absolute;
      top: 48px;
      left: -32px;
      border: 1px solid #d9d9d9;
      border-radius: 50% 0 0 50%;
      width: 32px;
      padding: 4px 8px 2px;
      cursor: pointer;
      z-index: 1;
      background-color: #fff;
    }

    .rightExpandIcon{
      left: 0;
      border-radius: 0 50% 50% 0;
    }
  }
}

.createEditModalWrap {
  .formRowLabel {
    color: #898a8c;
    margin-bottom: 8px;

    .formRowLabelRequired {
      color: #ff4d4f;
    }
  }
}
