import { <PERSON><PERSON>iew } from 'libs';
import React from 'react';
import { EditApiModel } from './EditApiModel';
import { inject, observer } from 'mobx-react';
import { Collapse, Input, Select, Button, Modal, Avatar, Popover, Tag } from 'antd';
import { Bind } from 'lodash-decorators';
import css from './EditApi.less';
import { bindObserver } from '@libs/mvvm';
import { JsonImport } from '@/business/jsonImportModal/JsonImport';
import { methodOptions, protocolOptions, IProps } from './enum';
import { CreateMaintainer } from '@/business/createMaintainer/CreateMaintainer';
import { KdevIconFont, KDevParticipants } from '@/business/commonComponents';
import { SaveDocNotice } from '@/business/saveDocNotice/SaveDocNotice';
import { RequestParams } from '@/business/httpApiComponents/editApi/requestParams/RequestParams';
import { ResponseParams } from '@/business/httpApiComponents/editApi/responseParams/ResponseParams';
import { priorityOptions, readOnlyOptions } from '@/business/httpApiComponents/httpGlobalDefine';
import { RemarkMarkdownEditor } from '@/business/httpApiComponents/editApi/remarkMarkdownEditor/RemarkMarkdownEditor';
import { TeamsSelect } from 'oldPages/MockProject/component/teamsSelect/TeamsSelect';
import { CustomTag } from '@/business/httpApiComponents/customTag/CustomTag';

// const { TextArea } = Input;
const { Option } = Select;
const { Panel } = Collapse;

const Input_name = bindObserver(Input, 'name');
const Select_protocol = bindObserver(Select, 'protocol');
const Select_method = bindObserver(Select, 'method');
const Input_path = bindObserver(Input, 'path');
// const TextArea_description = bindObserver(TextArea, 'description');

@inject('moduleDetailM')
@observer
export class EditApi extends AView<EditApiModel, IProps> {

    @Bind
    public componentWillUnmount(): void {
        this.model.initData();
    }

    @Bind
    private renderPopoverName(ele) {
        return (
            <a className={css.hoverKim} href={`kim://username?username=${ele.username}`}>
                <img
                    className={css.kimImg}
                    src={'https://cdnfile.corp.kuaishou.com/kc/files/a/QA-SERVEREE-FE-IN/kim.png'}
                />
                {ele.name + ' ' + ele.username}
            </a>
        );
    }

    @Bind
    private renderOwner(): React.ReactNode {
        const model = this.model;
        return (
            <div className={`${css.width50} ${css.margRight}`}>
                <span className={css.label}>负责人 <span className={css.labelRequired}>*</span></span>
                {
                    model.ownerInfo &&
                    <Popover
                        content={this.renderPopoverName(model.ownerInfo)}
                    >
                        <Avatar
                            className={css.owner}
                            src={model.ownerInfo.photo || ''}
                        >
                            {model.ownerInfo.name}
                        </Avatar>
                    </Popover>
                }
                <CreateMaintainer
                    model={model.createMaintainerM}
                    btnNode={
                        <Button
                            // shape={ 'circle' }
                            type={'link'}
                            icon={<KdevIconFont id={'#iconadd-circle'} className={css.addOwnerBtnIcon} />}
                            className={css.addOwnerBtn}
                        />
                    }
                    handelSearchSelect={model.onHandelSearchSelect}
                />
            </div>
        );
    }

    @Bind
    private renderCooper(): React.ReactNode {
        const model = this.model;
        return (
            <div className={css.width50}>
                <span className={css.label}>研发参与人</span>
                <KDevParticipants
                    value={model.cooperList}
                    onChange={model.onChangeCooper}
                />
            </div>
        );
    }

    @Bind
    private renderQaUsers(): React.ReactNode {
        const model = this.model;
        return (
            <div className={css.qaUsers}>
                <span className={css.label}>测试参与人</span>
                <KDevParticipants
                    value={model.qaUsers}
                    onChange={model.onChangeqaUsers}
                />
            </div>
        );
    }

    @Bind
    private renderPriority(): React.ReactNode {
        const model = this.model;
        return (
            <div className={`${css.width50} ${css.margRight}`}>
                <span className={css.label}>优先级</span>
                <Select
                    value={model.priority || undefined}
                    options={priorityOptions}
                    className={css.prioritySelect}
                    allowClear
                    onChange={model.onChangePriority}
                    placeholder={'请选择'}
                />
            </div>
        );
    }

    @Bind
    private renderReadOnly(): React.ReactNode {
        const model = this.model;
        return (
            <div className={css.width50}>
                <span className={css.label}>读写属性</span>
                <Select
                    value={model.readOnly || undefined}
                    options={readOnlyOptions}
                    className={css.prioritySelect}
                    // allowClear
                    onChange={model.onChangeReadOnly}
                    placeholder={'请选择'}
                />
            </div>
        );
    }

    @Bind
    private renderApiName(): React.ReactNode {
        const model = this.model;
        return (
            <div className={`${css.width50} ${css.margRight}`}>
                <span className={css.label}>API名称 <span className={css.labelRequired}>*</span></span>
                <Input_name model={model} placeholder={'请输入API名称'} onChange={model.changeIsLeave} />
            </div>
        );
    }

    @Bind
    private renderPath(): React.ReactNode {
        const model = this.model;
        return (
            <div className={css.width50}>
                <span className={css.label}>URL地址 <span className={css.labelRequired}>*</span></span>
                <Input.Group compact className={css.inputPathGroup}>
                    <Select_protocol
                        model={model}
                        options={protocolOptions}
                    />
                    <Select_method
                        model={model}
                        dropdownMatchSelectWidth={104}
                        onChange={model.onChangeMethod}
                        options={methodOptions}
                    />
                    <Input_path
                        model={model}
                        placeholder={'请输入path'}
                        onChange={model.changeIsLeave}
                    />
                </Input.Group>
            </div>
        );
    }

    @Bind
    private renderBranch(): React.ReactNode {
        const model = this.model;
        return (
            <div className={`${css.width50} ${css.margRight}`}>
                <span className={css.label}>关联分支</span>
                <Select
                    value={model.branchId || model.branchName || undefined}
                    className={css.width100}
                    onSelect={model.onSelectBranchId}
                    optionFilterProp={'label'}
                    showSearch
                >
                    {
                        model.branchList.map(item => {
                            return <Option
                                key={item.id}
                                value={item.id}
                                label={item.branchName}
                            >{item.branchName}</Option>;
                        })
                    }
                </Select>
            </div>
        );
    }

    @Bind
    private renderGroup(): React.ReactNode {
        const model = this.model;
        return (
            <div className={css.width50}>
                <span className={css.label}>分组</span>
                <Select
                    className={css.width100}
                    showSearch
                    value={model.groupId || undefined}
                    optionFilterProp={'label'}
                    onSelect={model.onSelectGroupId}
                // disabled
                >
                    {
                        model.groupList.map(item => {
                            return <Option
                                key={item.id}
                                value={item.id}
                                label={item.groupName}
                            >{item.groupName}</Option>;
                        })
                    }
                </Select>
            </div>
        );
    }

    @Bind
    private renderTeams() {
        const model = this.model;
        return (
            <div className={css.teamsBox}>
                <span className={css.label}>关联Team</span>
                <TeamsSelect
                    model={model.teamsSelectM}
                    value={model.teamIds}
                    onChange={model.onChangeTeamIds}
                    className={css.teams}
                />
            </div>
        );
    }

    @Bind
    protected renderDetail(): React.ReactNode {
        const model = this.model;
        return <div className={css.detail}>
            <Collapse
                ghost
                className={`${css.collapse} ${css.margTop0}`}
                defaultActiveKey={'1'}
                destroyInactivePanel
            >
                <Panel header={'基本信息'} key={'1'}>
                    <div className={css.collapsePanelRow}>
                        {this.renderApiName()}
                        {this.renderPath()}
                    </div>
                    <div className={css.collapsePanelRow}>
                        {this.renderBranch()}
                        {this.renderGroup()}
                    </div>
                    <div className={css.collapsePanelRow}>
                        {this.renderOwner()}
                        {this.renderCooper()}
                    </div>
                    <div className={css.collapsePanelRow}>
                        {this.renderQaUsers()}
                        {this.renderPriority()}
                    </div>
                    <div className={css.collapsePanelRow}>
                        {this.renderTeams()}
                        {this.renderReadOnly()}
                    </div>
                    <CustomTag
                        model={model.customTagM}
                        className={css.collapsePanelRow}
                        label={<span className={css.label}>自定义标签</span>}
                    />
                    <div className={css.collapsePanelRow}>
                        <span className={css.label}>描述</span>
                        {/* <TextArea_description
                            model={model} placeholder={'请输入'}
                            onChange={model.changeIsLeave}
                        /> */}
                        <RemarkMarkdownEditor
                            className={css.remarkMarkdownEditorWrap}
                            markdownValue={model.descMarkdown}
                            queryValueMethod={model.queryValueMethod}
                        />
                    </div>
                </Panel>
            </Collapse>
            <RequestParams model={model.requestParamsM} />
            <ResponseParams model={model.responseParamsM} />
        </div>;
    }

    @Bind
    protected cancelSave() {
        if (this.model.isLeave) {
            Modal.confirm({
                content: '您所编辑的内容还未保存，确认要退出页面吗？',
                okText: '留下',
                cancelText: '退出',
                onCancel: () => this.model.onSaveCancelEditApi('cancel')
            });
        } else {
            this.model.onSaveCancelEditApi('cancel');
        }
    }

    @Bind
    protected renderBottom(model): React.ReactNode {
        return <div className={css.editApiBottom}>
            <Button
                className={css.cancelBtn}
                onClick={this.cancelSave}
            >取消</Button>
            <Button
                type={'primary'}
                onClick={() => {
                    if (model.type !== 'copy' && Boolean(model.apiId)) {
                        // model.update();
                        model.onOpenSaveDocNotice();
                    } else {
                        model.create();
                    }
                }}
                loading={model.saveLoading}
            >
                保存
            </Button>
        </div>;
    }

    public render(): React.ReactNode {
        const model = this.model;
        return (
            <div className={css.editApiWrap}>
                {this.renderDetail()}
                {this.renderBottom(model)}
                <JsonImport model={model.jsonImportM} />
                <SaveDocNotice model={model.saveDocNoticeM} />
            </div>
        );
    }
}
