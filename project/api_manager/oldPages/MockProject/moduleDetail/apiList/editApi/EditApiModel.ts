import { AViewModel } from 'libs';
import { observable, action, runInAction } from 'mobx';
import { Bind } from 'lodash-decorators';
import {
    nsMockManageKoasApiManageModuleQueryAllBranchListGet, nsMockManageKoasApiManageHttpApiQueryDetailGet,
    nsMockManageKoasApiManageGroupQueryModuleGroupListGet, nsMockPermissionBatchGetUserGet,
    nsMockManageKoasApiManageHttpApiCreatePost, nsMockManageKoasApiManageAutoDocQueryDetailGet,
    nsMockManageKoasApiManageHttpApiCanEditApiPriorityGet, nsMockManageKoasApiManageHttpApiQueryTeamsByApiGet
} from '@/remote';
import { message, Modal } from 'antd';
import { JsonImportM } from '@/business/jsonImportModal/JsonImportM';
import { CreateMaintainerModel } from '@/business/createMaintainer/CreateMaintainer';
import { SaveDocNoticeM } from '@/business/saveDocNotice/SaveDocNotice';
import { RequestParamsM } from '@/business/httpApiComponents/editApi/requestParams/RequestParamsM';
import { ResponseParamsM } from '@/business/httpApiComponents/editApi/responseParams/ResponseParamsM';
import { TeamsSelectM } from '@/business/httpApiComponents/teamsSelect/TeamsSelectM';
import { CustomTagM } from '@/business/httpApiComponents/customTag/CustomTagM';

import { IOwner, ITeamItem } from './enum';
import { team } from '@/business/global';
import css from './EditApi.less';
import * as QS from 'query-string';
import { showTypeEnum } from '@/business/httpApiComponents/httpGlobalDefine';

const userInfo = team.getUserInfo();

export class EditApiModel extends AViewModel {
    @observable public moduleId: number = 0;
    @observable protected apiId: number = 0;
    @observable public name: string = '';
    @observable public protocol: string = 'HTTP';
    @observable public method: string = 'GET';
    @observable public path: string = '/';
    @observable public branchId: number = 0;
    @observable public branchName: string = '';
    @observable public groupId: number = 0;
    @observable private owner: string = userInfo.userName;
    @observable public ownerInfo: IOwner = {
        username: userInfo.userName,
        name: userInfo.chineseName,
        photo: userInfo.photo,
    };
    @observable private cooper: string[] = [];
    @observable public cooperList: Array<any> = [];
    @observable public qaList: string[] = [];
    @observable public qaUsers: any[] = [];
    @observable public priority: string = '';
    @observable public readOnly: number = 0;
    // @observable public description: string = '';
    @observable public descMarkdown: string = '';
    @observable public teamIds: string[] = [];
    private showType: string = showTypeEnum.NEW;

    @observable public branchList: nsMockManageKoasApiManageModuleQueryAllBranchListGet.IBranchList[] = [];
    @observable public groupList: nsMockManageKoasApiManageGroupQueryModuleGroupListGet.IGroupItem[] = [];
    private curl: string = '';

    @observable public saveLoading: boolean = false;
    @observable public isLeave: boolean = false;
    @observable private type: string = '';

    public jsonImportM = new JsonImportM();
    public createMaintainerM = new CreateMaintainerModel();
    public saveDocNoticeM = new SaveDocNoticeM();

    public requestParamsM = new RequestParamsM();
    public responseParamsM = new ResponseParamsM();
    public teamsSelectM = new TeamsSelectM();
    public customTagM = new CustomTagM();

    public onSaveCancelCallBack?(apiId: number, type: string, groupId: number): void;

    public getCooperList?(): void;
    private getMarkdownValue?(): string;
    private getHtmlValue?(): string;

    @action.bound
    public initParams(): void {
        const urlParams: object = QS.parse(location.search);
        this.showType = urlParams['showType'] || this.showType;
        if (urlParams['teamIds'] && this.showType === showTypeEnum.NEW) {
            this.teamIds = urlParams['teamIds'].split(',');
        }
    }

    // 加载model
    @action
    public initLoading(apiId: number, groupId: number, moduleId: number, type?: number) {
        this.initParams();
        if (apiId && this.apiId !== apiId) {
            this.apiId = apiId;
            this.groupId = groupId;
            this.moduleId = moduleId;
            this.queryDetail(type || 0);
            this.queryTeamsByApi();
            this.customTagM.initLoading(this.apiId);
        }
        if (!apiId) {
            if (groupId) {
                this.groupId = groupId;
                this.moduleId = moduleId;
                this.queryModuleGroupList();
                this.queryAllBranchList();
                this.teamsSelectM.queryTeams(this.teamIds.join(','));
            }
            this.requestParamsM.init(null, this.moduleId, this.apiId);
            this.responseParamsM.init(null, this.moduleId, this.apiId);
        }
    }

    // 初始化数据
    @action.bound
    public initData() {
        this.moduleId = 0;
        this.apiId = 0;
        this.name = '';
        this.protocol = 'HTTP';
        this.method = 'GET';
        this.path = '/';
        this.branchId = 0;
        this.branchName = '';
        this.groupId = 0;
        this.priority = '';
        this.readOnly = 0;
        // this.description = '';
        this.descMarkdown = '';
        this.branchList = [];
        this.groupList = [];
        this.cooper = [];
        this.cooperList = [];
        this.qaList = [];
        this.qaUsers = [];
        this.isLeave = false;
        this.type = '';
        this.owner = userInfo.userName;
        this.ownerInfo = {
            username: userInfo.userName,
            name: userInfo.chineseName,
            photo: userInfo.photo,
        };
        this.teamIds = [];
        this.requestParamsM.initData();
        this.responseParamsM.initData();
    }

    @action.bound
    public setCurl(curl: string = ''): void {
        this.curl = curl;
    }

    @action.bound
    public onChangeMethod(): void {
        this.changeIsLeave();
        this.requestParamsM.changeRequestType(this.method === 'GET' ? 3 : 2);
    }

    @action.bound
    public changeIsLeave() {
        this.isLeave = true;
    }

    // 基本信息
    @action.bound
    public initBaseInfo(baseInfo, type: number) {
        if (type === 2) {
            this.name = `${baseInfo.name}复制`;
        } else {
            this.name = baseInfo.name;
        }
        this.protocol = baseInfo.protocol;
        this.method = baseInfo.method || 'POST';
        this.path = baseInfo.path;
        this.branchId = baseInfo.branchId;
        this.branchName = baseInfo.branchName;
        if (type) {
            this.type = 'copy';
        }
        this.priority = baseInfo?.priority || '';
        this.readOnly = baseInfo?.readOnly || 0;
        this.descMarkdown = baseInfo.descMarkdown || '';
        this.owner = baseInfo.owner || this.owner;
        this.cooper = baseInfo.cooperList || [];
        this.qaList = baseInfo?.qaList || [];
        let userInfos = baseInfo.cooperList || [];
        if (!this.cooper.includes(this.owner)) {
            userInfos.push(this.owner);
        }
        if (this.qaList && this.qaList.length) {
            userInfos = [...userInfos, ...this.qaList];
        }
        this.batchGetUser(userInfos);
        this.queryAllBranchList();
        this.queryModuleGroupList();
    }

    // 切换编辑态
    @action.bound
    public onSaveCancelEditApi(type: string) {
        if (type === 'cancel') {
            this.onSaveCancelCallBack &&
                this.onSaveCancelCallBack(this.type === 'copy' ? 0 : this.apiId, 'cancel', this.groupId);
        }
        if (type === 'save') {
            this.apiId ? this.onOpenSaveDocNotice() : this.create();
        }
    }

    @action.bound
    public onHandelSearchSelect(ownerInfo: IOwner): void {
        this.ownerInfo = ownerInfo;
        this.owner = ownerInfo.username;
    }

    // 获取
    @action.bound
    public onChangeCooper(cooperList) {
        this.cooperList = cooperList;
        this.changeIsLeave();
    }

    @action.bound
    public onChangeqaUsers(qaUsers): void {
        this.qaUsers = qaUsers;
        this.changeIsLeave();
    }

    // 切换分支
    @action.bound
    public onSelectBranchId(branchId, e) {
        this.branchId = branchId;
        this.branchName = e.label;
        this.changeIsLeave();
    }

    // 切换分组
    @action.bound
    public onSelectGroupId(groupId: number) {
        this.groupId = groupId;
        this.changeIsLeave();
    }

    @action.bound
    public onChangePriority(priority: string = ''): void {
        const newPriority = this.priority;
        this.priority = priority;
        if (priority) {
            this.canEditApiPriority(newPriority);
        }
    }

    @Bind
    private async canEditApiPriority(priority: string) {
        try {
            const params = {
                apiId: this.apiId,
                priority: this.priority,
                type: 0 as 0
            };
            const result = await nsMockManageKoasApiManageHttpApiCanEditApiPriorityGet.remote(params);
            if (!result.canEdit) {
                Modal.warning({
                    className: css.canEditModal,
                    content: result.msg
                });
                runInAction(() => {
                    this.priority = priority;
                });
            }
        } catch {
        }
    }

    @action.bound
    public onChangeReadOnly(readOnly: number): void {
        this.readOnly = readOnly;
    }

    @action.bound
    public expandAllKeys(boo: boolean, type: string): void {
        this[type].onExpandAllKeys(boo);
    }

    @action.bound
    public queryValueMethod(method): void {
        this.getMarkdownValue = method.getMarkdownValue;
        this.getHtmlValue = method.getHtmlValue;
    }

    @action
    private batchGetUserCallback(list: IOwner[]): void {
        list.forEach(item => {
            if (item.username === this.owner) {
                this.ownerInfo = item;
            }
            if (this.cooper.includes(item.username)) {
                this.cooperList.push({
                    name: item.name,
                    username: item.username,
                    avatarUrl: item?.photo || ''
                });
            }
            if (this.qaList.includes(item.username)) {
                this.qaUsers.push({
                    name: item.name,
                    username: item.username,
                    avatarUrl: item?.photo || ''
                });
            }
        });
        this.cooperList = [...this.cooperList];
        this.qaUsers = [...this.qaUsers];
    }

    @action.bound
    protected getParams(): nsMockManageKoasApiManageHttpApiCreatePost.IParams {
        const cooperList = this.cooperList.map(item => item.username);
        const qaList = this.qaUsers.map(item => item.username);
        const baseInfo = {
            name: this.name,
            protocol: this.protocol,
            method: this.method,
            path: this.path,
            branchId: this.branchId,
            branchName: this.branchName,
            groupId: this.groupId,
            cooperList,
            qaList,
            priority: this.priority,
            readOnly: this.readOnly,
            description: this.getHtmlValue ? this.getHtmlValue() : '',
            descMarkdown: this.getMarkdownValue ? this.getMarkdownValue() : '',
            moduleId: this.moduleId,
            owner: this.owner,
            teamIds: this.teamIds
        };
        if (this.apiId) {
            baseInfo['id'] = this.apiId;
        }
        if (this.curl) {
            baseInfo['curl'] = this.curl;
        }
        return {
            baseInfo,
            request: this.requestParamsM.getRequest(),
            response: this.responseParamsM.getResponse()
        };
    }

    // 校验参数
    @action.bound
    protected checkParams() {
        if (!this.name) {
            message.warn('请输入API名称');
            return true;
        }
        if (!this.path) {
            message.warn('请输入URL地址');
            return true;
        }
        if (this.path[0] !== '/') {
            message.warn('URL地址应以"/"开头');
            return true;
        }
        if (this.getHtmlValue && this.getHtmlValue().length > 1000) {
            message.warn('文档描述不能超过1000字符，请重新编辑！');
            return true;
        }
        if (this.requestParamsM.checkParams()) {
            return true;
        }
        if (this.responseParamsM.checkParams()) {
            return true;
        }
        return false;
    }

    private setReqResBodyExpandedKeys(): void {
        const reqBodyExpandedKeys: string[] = this.requestParamsM.getExpandedKeys();
        const resBodyExpandedKeys: string[] = this.responseParamsM.getExpandedKeys();
        localStorage.setItem('reqBodyExpandedKeys', reqBodyExpandedKeys.join(','));
        localStorage.setItem('resBodyExpandedKeys', resBodyExpandedKeys.join(','));
    }

    // 编辑API
    @Bind
    private onSaveDocCallbck() {
        message.success('更新成功～');
        this.setReqResBodyExpandedKeys();
        this.onSaveCancelCallBack && this.onSaveCancelCallBack(this.apiId, 'edit', this.groupId);
    }

    @action.bound
    public onOpenSaveDocNotice(): void {
        if (this.checkParams()) {
            return;
        }
        this.saveDocNoticeM.onOpenSaveDocNotice(this.cooperList);
        this.saveDocNoticeM.getParams = this.getParams;
        this.saveDocNoticeM.onSaveDocCallbck = this.onSaveDocCallbck;
    }

    // 创建API
    @action.bound
    public async create() {
        if (this.checkParams()) {
            return;
        }
        this.saveLoading = true;
        try {
            const params = this.getParams();
            params['baseInfo']['id'] = 0;
            const result: any = await nsMockManageKoasApiManageHttpApiCreatePost.remote(params);
            runInAction(() => {
                message.success('创建成功～');
                this.saveLoading = false;
                this.setReqResBodyExpandedKeys();
                this.onSaveCancelCallBack && this.onSaveCancelCallBack(result, 'new', this.groupId);
            });
        } catch (e) {
            runInAction(() => {
                this.saveLoading = false;
            });
        }
    }

    // 获取详情
    @action.bound
    public async queryDetail(type: number) {
        try {
            const params = {
                id: this.apiId
            };
            const requestApi = type === 1 ?
                nsMockManageKoasApiManageAutoDocQueryDetailGet :
                nsMockManageKoasApiManageHttpApiQueryDetailGet;
            const result = await requestApi.remote(params);
            runInAction(() => {
                this.initBaseInfo(result?.baseInfo, type);
                result?.request &&
                    this.requestParamsM.init(result?.request, this.moduleId, this.type === 'copy' ? 0 : this.apiId);
                result?.response &&
                    this.responseParamsM.init(result?.response, this.moduleId, this.type === 'copy' ? 0 : this.apiId);
            });
        } catch (e) {
        }
    }

    // 获取分组下的关联分支
    @action
    protected async queryAllBranchList() {
        try {
            const params = {
                moduleId: this.moduleId
            };
            const result = await nsMockManageKoasApiManageModuleQueryAllBranchListGet.remote(params);
            runInAction(() => {
                this.branchList = result.branchList;
            });
        } catch (e) {
        }
    }

    // 获取分组列表
    @Bind
    protected async queryModuleGroupList() {
        try {
            const params = {
                moduleId: this.moduleId,
            };
            const result = await nsMockManageKoasApiManageGroupQueryModuleGroupListGet.remote(params);
            runInAction(() => {
                this.groupList = result.groupList;
            });
        } catch (e) {
        }
    }

    // 获取头像
    @action
    protected async batchGetUser(usernames: string[]) {
        if (!usernames?.length) {
            return;
        }
        try {
            const params = {
                usernames: usernames.join(',')
            };
            const result = await nsMockPermissionBatchGetUserGet.remote(params);
            runInAction(() => {
                this.batchGetUserCallback(result?.list || []);
            });
        } catch (e) {
        }
    }

    @action.bound
    public onChangeTeamIds(teamIds: string[]) {
        this.teamIds = teamIds;
    }

    // 获取关联team
    @Bind
    private async queryTeamsByApi() {
        try {
            const result = await nsMockManageKoasApiManageHttpApiQueryTeamsByApiGet.remote({apiId: this.apiId});
            runInAction(() => {
                this.teamIds = result?.result.map(item => item.taskId);
                if (this.teamIds.length) {
                    this.teamsSelectM.queryTeams(this.teamIds.join(','));
                }
            });
        } catch {
        }
    }
}
