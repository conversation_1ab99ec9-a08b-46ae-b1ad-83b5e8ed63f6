import { ModuleDetailModel } from '../../ModuleDetailModel';
import { nsMockManageKoasApiManageHttpApiQueryAllTeamsGet } from '@/remote';

export interface ITeamItem extends nsMockManageKoasApiManageHttpApiQueryAllTeamsGet.ITeamItem {}

export interface IOwner {
    username: string;
    photo?: string;
    name: string;
}

export enum reqDataEnum {
    HEADER = 'header',
    BODY = 'body',
    FORM = 'form',
    QUERY = 'query',
    PATH = 'path'
}

interface ISelectOptions {
    label?: string;
    value: string;
}

export const methodOptions: ISelectOptions[] = [
    {
        value: 'GET',
    },
    {
        value: 'POST',
    },
    {
        value: 'PUT',
    },
    {
        value: 'DELETE',
    },
    {
        value: 'PATCH',
    }
];

export const protocolOptions: ISelectOptions[] = [
    {
        value: 'HTTP',
    }
];

export interface IProps {
    moduleDetailM?: ModuleDetailModel;
}
