.rightApi {
  margin: 0 20px;
  height: 100%;

  .apiListTopWrap {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 16px 0;

    .filter {
      margin-right: 10px;
    }

    .keyword {
      width: 300px;
      margin-right: 10px;
    }
  }

  .copyBtn {
    margin-left: 4px;
  }

  .deleteBtn {
    margin: 0 4px;
  }

  .focusBtn {
    background-color: #ffa114;
    border: 1px solid #eb9008;
    color: #ffffff;
  }

  .apiName,
  .apiPath {
    word-break: break-word;

    .method {
      background-color: rgba(50, 125, 255, .08);
      // opacity: 0.08;
      color: #327dff;
      margin-right: 8px;
      border-radius: 4px;
      padding: 0 4px;
    }
  }

  .ignore {
    font-size: 12px;
    margin: 4px 0 0 4px;
  }

  .noIgnore {
    font-size: 12px;
    margin-top: 4px;
    color: #898a8c;
  }

  .redColor {
    color: #ff4d4f;
  }

  .greenColor {
    color: #31bf30;
  }

  .detail {
    margin-left: 4px;
    font-size: 12px;
  }

  .rightApiOperate {
    position: absolute;
    right: 20px;
    top: 75px;
  }

  .createBtn {
    margin-left: 8px;
  }

  .empty {
    padding-top: 140px;

    :global {

      .ant-empty-description {
        opacity: .25;
      }
    }
  }
}