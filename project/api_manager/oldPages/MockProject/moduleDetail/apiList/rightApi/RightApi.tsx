import { AView } from 'libs';
import React from 'react';
import { RightApiModel } from './RightApiModel';
import { inject, observer } from 'mobx-react';
import { Input, Radio, Button, Table, Modal, Empty, Tooltip, Tag } from 'antd';
import css from './RightApi.less';
import { apiStatusEnum } from '@/business/toolFun/ToolFun';
import Bind from 'lodash-decorators/bind';
import { bindObserver } from '@libs/mvvm';
import { KdevIconFont } from '@/business/commonComponents/KdevIconFont';
import { SearchEmptyIcon } from '@/business/commonIcon';
import { GlobalSearch } from '@/business/apiGlobalSearch/GlobalSearch';
import { RightApiDetail } from '@/pages/moduleDetail/apiList/rightApiDetail/RightApiDetail';
import { EditApi } from '@/pages/moduleDetail/apiList/editApi/EditApi';
import { AddApiToFolder } from '@/business/addApiToFolder/AddApiToFolder';
import { CurlImportApi } from './curlImportApi/CurlImportApi';
import { ApiDiffBranch } from '@/business/httpApiComponents/apiDiffBranch/ApiDiffBranch';
import { StandardInfo } from '@/business/apiDetail/standardInfo/StandardInfo';
import { ERouter } from 'CONFIG';
import { ApiStatus } from '@/business/apiStatus/ApiStatus';
import { IProps, showTypeEnum } from './configure';
import { priorityColor } from '@/business/apiDetail/httpApiBaseInfo/configure';

const { Search } = Input;

const Search_keyword = bindObserver(Search, 'keyword');
const RadioGroup_filter = bindObserver(Radio.Group, 'filter');

@inject('moduleDetailM')
@observer
export class RightApi extends AView<RightApiModel, IProps> {

    @Bind
    private renderApiName(record): React.ReactNode {
        return (
            <a
                className={css.apiName}
                onClick={() => this.model.onChangeShowType('view', record.apiId)}
            >
                {record.apiName}
            </a>
        );
    }

    @Bind
    private renderStatus(record): React.ReactNode {
        return (
            <Tag
                className={css.tag}
                color={apiStatusEnum[record.status].color}
                onClick={this.model.onOpenApiStatus.bind(this.model, record)}
            >
                {record.statusDesc}
            </Tag>
        );
    }

    @Bind
    private renderApiPath(reocrd): React.ReactNode {
        return (
            <div className={css.apiPath}>
                <span className={css.method}>{reocrd.apiMethod}</span>
                {reocrd.apiUrl}
            </div>
        );
    }

    @Bind
    private renderIsDiffTitle(): React.ReactNode {
        return (
            <span>
                一致性校验
                <Tooltip title="API文档与最新编译的一个分支进行对比，校验文档与代码是否一致">
                    <KdevIconFont id="#iconquestion" />
                </Tooltip>
            </span>
        );
    }

    @Bind
    private ignoreConfirm(docId: number): void {
        Modal.confirm({
            content: '是否要忽略该文档与最新代码的一致性？',
            onOk: () => this.model.ignoredConsistency(docId)
        });
    }

    @Bind
    private renderIsDiff(record): React.ReactNode {
        if (record.isDiff) {
            return (
                <>
                    <Tooltip title={'点击查看详情'}>
                        <a
                            className={record.isDiff !== 2 ? css.redColor : css.greenColor}
                            href={`${ERouter.API_MOCK_VERSIONCOMPARISON}?artificailId=${record.apiId}`}
                            target="_blank"
                        >
                            {record.isDiff !== 2 ? '未通过' : '通过'}
                        </a>
                    </Tooltip>
                    {
                        record.isDiff === 1 &&
                        <a className={css.ignore} onClick={() => this.ignoreConfirm(record.apiId)}>
                            忽略
                        </a>
                    }
                    {
                        record.isDiff === 3 &&
                        <span className={css.noIgnore}>（已忽略）</span>
                    }
                </>
            );
        }
        return '-';
    }

    @Bind
    private renderIsStandard(record): React.ReactNode {
        return (
            <>
                <span style={{ color: record.isStandard === 2 ? '#ff4d4f' : '#31bf30' }}>
                    {record.isStandard === 2 ? '未通过' : '通过'}
                </span>
                {
                    record.isStandard === 2 &&
                    <a
                        className={css.detail}
                        onClick={() => this.model.onOpenStandardInfo(record.apiId)}
                    >
                        详情
                    </a>
                }
            </>
        );
    }

    @Bind
    protected columns(model): Array<any> {
        const columns = [
            {
                title: 'API名称',
                // dataIndex: 'apiName',
                key: 'apiName',
                width: 150,
                render: this.renderApiName
            },
            {
                title: '状态',
                // dataIndex: 'apiName',
                key: 'status',
                align: 'center',
                width: 100,
                render: this.renderStatus
            },
            {
                title: 'PATH',
                // dataIndex: 'apiUrl',
                key: 'apiUrl',
                width: 220,
                render: this.renderApiPath
            },
            {
                title: '关联分支',
                dataIndex: 'apiBranch',
                key: 'apiBranch',
                width: 120,
            },
            {
                title: '优先级',
                dataIndex: 'priority',
                key: 'priority',
                width: 90,
                render: text => <span style={{color: priorityColor[text]}}>{text}</span>
            },
            {
                title: '负责人',
                // dataIndex: 'createUser',
                key: 'createUser',
                width: 120,
                render: record => record.owner || record.createUser
            },
            {
                title: this.renderIsDiffTitle,
                // dataIndex: 'isTheSame',
                key: 'isDiff',
                align: 'center',
                width: 130,
                render: this.renderIsDiff
            },
            {
                title: '规范性校验',
                // dataIndex: 'isStandard',
                width: 100,
                render: this.renderIsStandard
            },
            {
                title: '操作',
                // dataIndex: 'operation',
                key: 'operation',
                align: 'right',
                width: 210,
                fixed: 'right',
                render: record => {
                    return <>
                        <Tooltip title={'编辑'}>
                            <Button
                                icon={<KdevIconFont id={'#iconxingzhuangjiehe'} />}
                                onClick={() => model.onChangeShowType('edit', record.apiId)}
                            />
                        </Tooltip>
                        <Tooltip title={'复制'}>
                            <Button
                                icon={<KdevIconFont id={'#iconcopy'} />}
                                onClick={() => model.onChangeShowType('copy', record.apiId)}
                                className={css.copyBtn}
                            />
                        </Tooltip>
                        <Tooltip title={'删除'}>
                            <Button
                                icon={<KdevIconFont id={'#iconyanse'} />}
                                className={css.deleteBtn}
                                onClick={() => this.onDeleteApi(record)}
                            />
                        </Tooltip>
                        <Button
                            className={record.focus && css.focusBtn}
                            icon={<KdevIconFont id={'#iconguanzhu'} />}
                            onClick={() => model.attention(record)}
                        />
                        <Tooltip title={'添加到API集合'}>
                            <Button
                                icon={<KdevIconFont id={'#iconAddfolder'} />}
                                className={css.deleteBtn}
                                onClick={() => model.onOpenAddApiToFolderModal(record.apiId)}
                            />
                        </Tooltip>
                    </>;
                }
            }
        ];
        return columns;
    }

    // 确认是否删除模块
    @Bind
    public onDeleteApi(record) {
        Modal.confirm({
            content: '删除后将无法恢复，确认删除接口？',
            onOk: () => this.model.delete(record.apiId)
        });
    }

    @Bind
    protected renderApiListTop(model): React.ReactNode {
        return <div className={css.apiListTopWrap}>
            <div>
                <RadioGroup_filter model={model} className={css.filter} onChange={model.onSearchApiList}>
                    <Radio.Button value={1}>全部</Radio.Button>
                    <Radio.Button value={2}>我创建的</Radio.Button>
                    <Radio.Button value={4}>我参与的</Radio.Button>
                    <Radio.Button value={3}>我关注的</Radio.Button>
                </RadioGroup_filter>
                搜索：
                <Search_keyword
                    model={model}
                    className={css.keyword}
                    placeholder={'支持模块名/API名称/路径/创建人搜索'}
                    onSearch={model.onSearchApiList}
                    onClick={model.openGlobalSearch}
                    readOnly={true}
                />
            </div>
        </div>;
    }

    @Bind
    protected renderApi(model): React.ReactNode {
        if (model.showType === showTypeEnum.VIEW) {
            return <RightApiDetail model={model.rightApiDetailM} />;
        }
        if (model.showType === showTypeEnum.NEW || model.showType === showTypeEnum.EDIT
            || model.showType === showTypeEnum.COPY || model.showType === showTypeEnum.AUTO_COPY) {
            return <EditApi model={model.editApiM} />;
        }
        return (
            <>
                {this.renderApiListTop(model)}
                {
                    model.total
                        ? <Table
                            columns={this.columns(model)}
                            loading={model.apiListLoading}
                            dataSource={model.apiList}
                            rowKey={'apiId'}
                            bordered
                            pagination={{
                                showTotal: total => `共 ${total} 条`,
                                current: model.currentPage,
                                pageSize: model.pageSize,
                                showSizeChanger: true,
                                total: model.total,
                                onChange: model.onChangePageInfo,
                            }}
                            scroll={{
                                y: model.tableScrollY
                            }}
                        />
                        : <Empty
                            className={css.empty}
                            image={<SearchEmptyIcon />}
                            description={'暂无数据～'}
                        />
                }
            </>
        );
    }

    @Bind
    private renderCreateApiBtn(): React.ReactNode {
        const model = this.model;
        if (model.showType === 'view' || model.showType === '') {
            return (
                <div className={css.rightApiOperate}>
                    <CurlImportApi onSaveCallback={model.onSaveCurlCallback} groupId={model.groupId} />
                    <Tooltip title={'请选择左侧分组， 如果没有请新建'} placement={'topLeft'}>
                        <Button
                            type={'primary'}
                            className={css.createBtn}
                            // icon={ <PlusOutlined /> }
                            onClick={() => model.onChangeShowType('new', 0)}
                            disabled={!model.groupId}
                        >
                            新建API
                        </Button>
                    </Tooltip>
                </div>
            );
        }
    }

    public render(): React.ReactNode {
        const model = this.model;
        return <div className={css.rightApi}>
            {this.renderCreateApiBtn()}
            {this.renderApi(model)}
            <GlobalSearch model={model.globalSearchM} />
            <AddApiToFolder model={model.addApiToFolderM} />
            <ApiDiffBranch model={model.apiDiffBranchM} />
            <StandardInfo model={model.standardInfoM} />
            <ApiStatus model={model.apiStatusM} />
        </div>;
    }
}
