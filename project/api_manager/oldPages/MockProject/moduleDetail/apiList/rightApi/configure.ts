import { ModuleDetailModel } from '../../ModuleDetailModel';
// export interface IQuery {
//     showType: string;
// }

export interface IProps {
    moduleDetailM?: ModuleDetailModel;
}

// src/@/pages/moduleDetail/ModuleDetail.tsx
export enum showTypeEnum {
    NEW = 'new', // 新建
    EDIT = 'edit', // 编辑
    COPY = 'copy', // 复制
    AUTO_COPY = 'autoCopy', // 复制自动文档
    VIEW = 'view', // 详情
    NONE = '' // 不展示
}
