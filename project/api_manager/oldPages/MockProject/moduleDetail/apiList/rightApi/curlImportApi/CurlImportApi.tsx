import React from 'react';
import { observer } from 'mobx-react';
import { action, observable, runInAction } from 'mobx';
import { Button, Modal, Input, Tooltip } from 'antd';
import { Bind } from 'lodash-decorators';
import { nsMockManageKoasApiManageHttpApiParseCurl2ApiPost } from '@/remote';
import { ErrorText, ErrorTextConfig } from '@/business/commonComponents';

const { TextArea } = Input;

interface IProps {
    groupId?: number;
    onSaveCallback?(result, curl: string): void;
}

@observer
export class CurlImportApi extends React.Component<IProps> {
    @observable private visible: boolean = false;
    @observable private curl: string = '';
    @observable private loading: boolean = false;

    @action.bound
    private onCloseCurlModal(): void {
        this.visible = false;
    }

    @action.bound
    private onChangeCurl(e): void {
        this.curl = e.target.value;
    }

    @Bind
    private async parseCurl2Api(): Promise<void> {
        runInAction(() => this.loading = true);
        try {
            const params = {
                curl: this.curl,
                groupId: this.props.groupId
            };
            const result = await nsMockManageKoasApiManageHttpApiParseCurl2ApiPost.remote(params);
            this.props.onSaveCallback && this.props.onSaveCallback(result, this.curl);
            this.onCloseCurlModal();
            runInAction(() => this.loading = false);
        } catch {
            runInAction(() => this.loading = false);
        }
    }

    @Bind
    private renderCurlTitle(): React.ReactNode {
        return (
            <span>请输入cURL {ErrorText(ErrorTextConfig.IMPORT_CURL_TIP)}</span>
        );
    }

    @Bind
    private renderCurlModal(): React.ReactNode {
        return (
            <Modal
                title={this.renderCurlTitle()}
                visible={this.visible}
                onCancel={this.onCloseCurlModal}
                destroyOnClose
                onOk={this.parseCurl2Api}
                okButtonProps={{
                    loading: this.loading
                }}
            >
                <TextArea rows={10} placeholder="请输入cURL" onChange={this.onChangeCurl} />
            </Modal>
        );
    }

    @action.bound
    private onOpenCurlModal(): void {
        this.visible = true;
    }

    public render(): React.ReactNode {
        return (
            <React.Fragment>
                <Tooltip title={'填写curl命令生成API文档'} placement={'topLeft'}>
                    <Button
                        type="primary"
                        onClick={this.onOpenCurlModal}
                        disabled={!this.props.groupId}
                    >
                        导入API
                    </Button>
                </Tooltip>
                {this.renderCurlModal()}
            </React.Fragment>
        );
    }
}
