import { AViewModel } from 'libs';
import { observable, action, runInAction } from 'mobx';
import {
    nsMockManageKoasApiManageHttpApiQueryApiPageListPost,
    nsMockManageKoasApiManageHttpApiDeletePost,
    nsMockManageKoasApiManageHttpApiAttentionPost, nsMockManageKoasApiManageHttpApiIgnoredConsistencyPost
} from '@/remote';
import { message } from 'antd';
import { GlobalSearchM } from 'oldPages/MockProject/component/apiGlobalSearch/GlobalSearchM';
import { departmentCascader } from '@/business/global';
import { RightApiDetailModel } from '@/pages/moduleDetail/apiList/rightApiDetail/RightApiDetailModel';
import { EditApiModel } from '@/pages/moduleDetail/apiList/editApi/EditApiModel';
import { pushKey, getKey0OrKey1 } from '@/index.config/tools';
import { AddApiToFolderM } from '@/business/addApiToFolder/AddApiToFolderM';
import { ApiDiffBranchM } from '@/business/httpApiComponents/apiDiffBranch/ApiDiffBranchM';
import { Bind } from 'lodash-decorators';
import { StandardInfoM } from '@/business/apiDetail/standardInfo/StandardInfoM';
import { ApiStatusModel } from '@/business/apiStatus/ApiStatusModel';
import * as QS from 'query-string';
import { showTypeEnum } from './configure';

interface IParentThis {
    moduleId: number;
    groupSelectKey: string[];
    showType: string;
    keyWord: string;
    isSearchApi: boolean;
    setSelectedKeys(selectedKeys: string[]): void;
    createDeleteApiDoc(groupId: number): void;
    queryAllGroupList(): void;
    // queryAllApiList(nodeData: INodeData): void;
    setApiStatus(groupId: number, apiId: number, status: number): void;
}

export class RightApiModel extends AViewModel {
    @observable public departmentId: number = departmentCascader.getDepartmentId();
    @observable protected moduleId: number = 0;

    @observable public showType: string = showTypeEnum.NONE;
    @observable public branchName: string = '最新分支';
    @observable public groupId: number = 0;
    @observable public filter: number = 1;
    @observable public keyword: string = '';
    private teamIdsStr: string = '';
    @observable public currentPage: number = 1;
    @observable public pageSize: number = 10;
    @observable public apiListLoading: boolean = false;
    @observable public apiList: nsMockManageKoasApiManageHttpApiQueryApiPageListPost.IApiList[] = [];
    @observable public total: number = 0;
    @observable public tableScrollY: number = 200;

    private parentThis: IParentThis | null = null;

    public globalSearchM = new GlobalSearchM();
    public rightApiDetailM = new RightApiDetailModel();
    public editApiM = new EditApiModel();
    public addApiToFolderM = new AddApiToFolderM();
    public apiDiffBranchM = new ApiDiffBranchM();
    public standardInfoM = new StandardInfoM();
    public apiStatusM = new ApiStatusModel();

    // 获取url参数
    @action.bound
    public initParams(): void {
        const urlParams: object = QS.parse(location.search);
        this.showType = urlParams['showType'] || showTypeEnum.NONE;
        this.moduleId = Number(urlParams['moduleId']) || this.moduleId;
        this.teamIdsStr = urlParams['teamIdsStr'] || '';
        if (urlParams['branchName'] === '' || urlParams['branchName']) {
            this.branchName = urlParams['branchName'];
        }
        const selectedKey: string = urlParams['groupSelectKey'] || '';
        this.groupId = getKey0OrKey1(selectedKey)[0] || this.groupId;
        let apiId: number = getKey0OrKey1(selectedKey)[1] || 0;
        if (this.showType === showTypeEnum.AUTO_COPY && urlParams['apiId']) {
            apiId = urlParams['apiId'];
        }
        if (selectedKey) {
            this.init(apiId);
        }
    }

    @action.bound
    public setParentThis(parentThis): void {
        this.parentThis = parentThis;
        this.rightApiDetailM.setApiListApiStatus = parentThis.setApiStatus;
    }

    @action
    public init(apiId: number) {
        this.onChangeShowType(this.showType, apiId);
        this.tableScrollY = document.documentElement.clientHeight - 340;
    }

    @action.bound
    public initData(): void {
        this.showType = showTypeEnum.NONE;
        this.groupId = 0;
        this.apiList = [];
        this.total = 0;
        pushKey({
            showType: this.showType,
            groupSelectKey: ''
        });
    }

    // 修改表格列表以及左侧树列表的API状态
    @Bind
    private setApiStatus(status: number, statusDesc: string, apiId: number): void {
        this.queryApiPageList();
        this.parentThis && this.parentThis.setApiStatus(this.groupId, apiId, status);
    }

    // 打开修改状态弹框
    public onOpenApiStatus(record): void {
        this.apiStatusM.init(record.apiId, record.status);
        this.apiStatusM.onSaveCallback = this.setApiStatus;
    }

    @Bind
    public onOpenApiDiffBranch(docId: number): void {
        this.apiDiffBranchM.init(docId);
    }

    @action.bound
    public onOpenStandardInfo(docId: number): void {
        this.standardInfoM.init(docId, 2);
    }

    @action.bound
    public openGlobalSearch(): void {
        this.globalSearchM.init(this.departmentId);
    }

    @action.bound
    public onChangeShowType(showType: string, apiId: number): void {
        pushKey({ showType });
        switch (showType) {
            case showTypeEnum.NONE:
                this.showApiList(apiId, this.groupId);
                break;
            case showTypeEnum.VIEW:
                this.viewApi(apiId);
                break;
            case showTypeEnum.NEW:
                this.newApi();
                break;
            case showTypeEnum.EDIT:
                this.editApi(apiId);
                break;
            case showTypeEnum.COPY:
                this.copyApi(apiId);
                break;
            case showTypeEnum.AUTO_COPY:
                this.autoCopyApi(apiId);
                break;
        }
    }

    @action.bound
    private showApiList(apiId: number, groupId: number): void {
        this.showType = showTypeEnum.NONE;
        this.parentThis && this.parentThis.setSelectedKeys([`${groupId}`]);
        this.onSearchApiList();
        pushKey({
            showType: this.showType,
            groupSelectKey: `${groupId}`
        });
    }

    @action.bound
    private viewApi(apiId: number): void {
        this.showType = showTypeEnum.VIEW;
        const selectedKey = `${this.groupId}-${apiId}`;
        pushKey({
            showType: this.showType,
            groupSelectKey: selectedKey
        });
        this.parentThis && this.parentThis.setSelectedKeys([selectedKey]);
        this.rightApiDetailM.initParams();
        this.rightApiDetailM.initLoading();
        this.rightApiDetailM.viewApiCallback = this.viewApiCallback;
    }

    @action.bound
    private viewApiCallback(type: string, apiId: number, groupId: number): void {
        if (type === '') {
            this.showApiList(apiId, groupId);
        }
        if (type === 'edit') {
            this.editApi(apiId);
        }
        if (type === 'copy') {
            this.copyApi(apiId);
        }
        if (type === 'delete') {
            this.deleteApiDoc(apiId);
        }
    }

    @action.bound
    private editApi(apiId: number): void {
        this.showType = showTypeEnum.EDIT;
        this.parentThis && this.parentThis.setSelectedKeys([`${this.groupId}-${apiId}`]);
        this.editApiM.initLoading(apiId, this.groupId, this.moduleId);
        this.editApiM.onSaveCancelCallBack = this.editSaveCancelCallBack;
        pushKey({
            showType: this.showType,
            groupSelectKey: `${this.groupId}-${apiId}`
        });
    }

    @action.bound
    private newApi(): void {
        this.showType = showTypeEnum.NEW;
        this.parentThis && this.parentThis.setSelectedKeys([`${this.groupId}`]);
        this.editApiM.initLoading(0, this.groupId, this.moduleId);
        this.editApiM.onSaveCancelCallBack = this.editSaveCancelCallBack;
        pushKey({
            showType: this.showType,
            groupSelectKey: `${this.groupId}`
        });
    }

    @action.bound
    private copyApi(apiId: number): void {
        this.showType = showTypeEnum.COPY;
        this.parentThis && this.parentThis.setSelectedKeys([`${this.groupId}`]);
        this.editApiM.initLoading(apiId, this.groupId, this.moduleId, 2);
        this.editApiM.onSaveCancelCallBack = this.editSaveCancelCallBack;
        pushKey({
            showType: this.showType,
            groupSelectKey: `${this.groupId}`
        });
    }

    @action.bound
    private autoCopyApi(apiId: number): void {
        this.showType = showTypeEnum.AUTO_COPY;
        this.parentThis && this.parentThis.setSelectedKeys([`${this.groupId}`]);
        this.editApiM.initLoading(apiId, this.groupId, this.moduleId, 1);
        this.editApiM.onSaveCancelCallBack = this.editSaveCancelCallBack;
        pushKey({
            showType: this.showType,
            groupSelectKey: `${this.groupId}`
        });
    }

    @action.bound
    private editSaveCancelCallBack(apiId: number, type: string, groupId: number): void {
        this.groupId = groupId;
        if (type === 'new' || type === 'copy') {
            if (this.parentThis && this.parentThis.keyWord) {
                this.parentThis.queryAllGroupList();
            } else if (this.parentThis) {
                this.parentThis.setSelectedKeys([`${groupId}-${apiId}`]);
                this.parentThis.createDeleteApiDoc(this.groupId);
            }
            this.onChangeShowType(showTypeEnum.VIEW, apiId);
        }
        if (type === 'cancel') {
            this.onChangeShowType(apiId ? showTypeEnum.VIEW : showTypeEnum.NONE, apiId);
        }
        if (type === 'edit') {
            if (this.parentThis) {
                this.parentThis.queryAllGroupList();
            }
            // else if (this.parentThis) {
            //     this.parentThis.queryAllApiList({
            //         id: groupId,
            //         key: `${groupId}`
            //     });
            //     this.parentThis.createDeleteApiDoc(getKey0OrKey1(this.parentThis.groupSelectKey)[0]);
            // }
            this.onChangeShowType(showTypeEnum.VIEW, apiId);
        }
    }

    // 删除doc文档之后操作
    @action.bound
    private deleteApiDoc(apiId: number): void {
        if (this.parentThis && this.parentThis.keyWord) {
            this.parentThis.queryAllGroupList();
        } else if (this.parentThis) {
            this.parentThis.createDeleteApiDoc(this.groupId);
        }
        this.showApiList(apiId, this.groupId);
    }

    // 刷新接口列表
    @action.bound
    public onSearchApiList() {
        this.currentPage = 1;
        const keyWord: string = this.parentThis ? this.parentThis.keyWord : '';
        this.queryApiPageList(keyWord);
    }

    // 打开将doc文档添加到集合弹框
    @action.bound
    public onOpenAddApiToFolderModal(docId: number): void {
        this.addApiToFolderM.init(this.departmentId, docId);
    }

    // 分页
    @action.bound
    public onChangePageInfo(currentPage: number, pageSize?: number) {
        this.currentPage = currentPage;
        this.pageSize = pageSize || this.pageSize;
        this.queryApiPageList();
    }

    // curl导入保存之后操作
    @action.bound
    public onSaveCurlCallback(result, curl: string): void {
        this.newApi();
        this.editApiM.setCurl(curl);
        result?.baseInfo && this.editApiM.initBaseInfo(result?.baseInfo, 1);
        result?.request && this.editApiM.requestParamsM.init(result?.request, this.moduleId, 0);
        result?.request && this.editApiM.responseParamsM.init(result?.response, this.moduleId, 0);
    }

    // 分页获取API列表
    @action.bound
    public async queryApiPageList(keyWord: string = '') {
        this.apiListLoading = true;
        try {
            const params = {
                groupId: this.groupId,
                filter: this.filter,
                branchName: this.branchName,
                keyword: keyWord || this.keyword,
                currentPage: this.currentPage,
                pageSize: this.pageSize,
                teamIds: this.teamIdsStr ? this.teamIdsStr.split(',') : []
            };
            const result = await nsMockManageKoasApiManageHttpApiQueryApiPageListPost.remote(params);
            runInAction(() => {
                this.apiListLoading = false;
                this.apiList = result.list;
                this.total = result.total;
            });
        } catch (e) {
            runInAction(() => {
                this.apiListLoading = false;
            });
        }
    }

    // 删除API
    @action
    public async delete(apiId: number) {
        try {
            const params = {
                id: apiId,
            };
            await nsMockManageKoasApiManageHttpApiDeletePost.remote(params);
            runInAction(() => {
                message.success('删除成功');
                this.deleteApiDoc(apiId);
            });
        } catch (e) {
        }
    }

    // 关注、取消关注模块
    @action.bound
    public async attention(record) {
        try {
            const params = {
                apiId: record.apiId,
                action: record.focus ? 'unfocus' : 'focus'
            };
            await nsMockManageKoasApiManageHttpApiAttentionPost.remote(params);
            runInAction(() => {
                message.success(record.focus ? '取消关注成功' : '关注成功');
                this.queryApiPageList();
            });
        } catch (e) {
        }
    }

    // 忽略
    @Bind
    public async ignoredConsistency(docId: number): Promise<void> {
        try {
            await nsMockManageKoasApiManageHttpApiIgnoredConsistencyPost.remote({ docId });
            this.queryApiPageList();
            message.success('已忽略');
        } catch {
        }
    }
}
