import { AView } from 'libs';
import React from 'react';
import { RightApiDetailModel } from './RightApiDetailModel';
import { apiStatusEnum } from '@/business/toolFun/ToolFun';
import { inject, observer } from 'mobx-react';
import {
    Button, <PERSON>u, Divider, Tooltip, Skeleton, Dropdown, Modal, Tag, Tabs, Space
} from 'antd';
import Bind from 'lodash-decorators/bind';
import css from './RightApiDetail.less';
import { LeftOutlined } from '@ant-design/icons';
import { ApiMock } from './apiMock/ApiMock';
import { ApiTest } from './apiTest/ApiTest';
import { KdevIconFont } from '@/business/commonComponents/KdevIconFont';
import { VersionRecord } from '@/business/versionRecord/VersionRecord';
import { ApiStatus } from '@/business/apiStatus/ApiStatus';
import { QRCode } from 'oldPages/OrganizeSpace/QRCode/QRCode';
import { CommentT } from '@/business/apiDetail/comment/Comment';
import { ApiBaseInfo } from '@/business/apiDetail/httpApiBaseInfo/ApiBaseInfo';
import { HttpApiRequestParams } from '@/business/apiDetail/httpApiRequestParams/HttpApiRequestParams';
import { HttpApiResponseParams } from '@/business/apiDetail/httpApiResponseParams/HttpApiResponseParams';
import { ruleTypeEnum, IProps, apiActiveTypeEnum } from './configure';
import { AddApiToFolder } from '@/business/addApiToFolder/AddApiToFolder';
import { AutoGenerateCode } from '@/business/httpApiComponents/AutoGenerateCode';
import { ApiStream } from '@/business/httpApiComponents/apiStream/ApiStream';

const { TabPane } = Tabs;

@inject('apiListM', 'moduleDetailM')
@observer
export class RightApiDetail extends AView<RightApiDetailModel, IProps> {
    @Bind
    public componentWillUnmount(): void {
        this.model.initData();
    }

    @Bind
    public componentWillMount(): void {
        this.model.setParentModel(this.props);
    }

    @Bind
    protected renderApiMockOperate(): React.ReactNode {
        const model = this.model;
        if (model.apiActiveKey === apiActiveTypeEnum.DETAIL) {
            return this.renderApiDetailOperate();
        }
        if (model.apiActiveKey === apiActiveTypeEnum.MOCK) {
            return (
                <div className={css.apiMockOperate}>
                    <Button
                        icon={<KdevIconFont id={'#iconerweima'} />}
                        onClick={model.onOpenQRCode}
                    />
                    <Button
                        className={css.margLeft8px}
                        loading={model.ruleSwitchLoading}
                        onClick={model.ruleSwitch}
                    >
                        {model.ruleType === ruleTypeEnum.CLIENT ? '停止客户端 mock' : '开启客户端 mock'}
                    </Button>
                    <QRCode model={model.QRCodeM} />
                </div>
            );
        }
    }

    @Bind
    protected renderApiDetailOperate(): React.ReactNode {
        const model = this.model;
        const overlay: React.ReactElement = (
            <Menu>
                <Menu.Item onClick={model.onCopyApiDoc}>复制</Menu.Item>
                <Menu.Item onClick={this.onDeleteApi}>删除</Menu.Item>
                <Menu.Item onClick={model.onOpenAddApiToFolderModal}>添加到集合</Menu.Item>
            </Menu>
        );
        return (
            <div className={css.apiMockOperate}>
                <Tooltip title={'编辑'}>
                    <Button
                        icon={<KdevIconFont id={'#iconxingzhuangjiehe'} />}
                        onClick={model.onChangeEditApi}
                    />
                </Tooltip>
                <Tooltip title={'版本记录'}>
                    <Button
                        icon={<KdevIconFont id={'#iconlishibanben'} />}
                        className={css.margLeft8px}
                        onClick={model.onOpenVersionRecord}
                    />
                </Tooltip>
                <Tooltip title={model.focus ? '取消关注' : '关注'}>
                    <Button
                        className={model.focus ? css.focusBtn : css.margLeft8px}
                        icon={<KdevIconFont id={'#iconguanzhu'} />}
                        onClick={model.attention}
                    />
                </Tooltip>
                <Dropdown
                    placement={'bottomRight'}
                    overlay={overlay}
                >
                    <Button
                        icon={<KdevIconFont id={'#icongengduo'} />}
                        className={css.margLeft8px}
                    />
                </Dropdown>
            </div>
        );
    }

    // 确认是否删除模块
    @Bind
    public onDeleteApi() {
        Modal.confirm({
            content: '删除后将无法恢复，确认删除接口？',
            onOk: () => this.model.delete()
        });
    }

    private renderApiName(): React.ReactNode {
        const model = this.model;
        if (!model.queryDetailLoading && model.apiName) {
            return (
                <Space>
                    <span className={css.apiName}>
                        {model.apiName}
                    </span>
                    {
                        model.statusDesc &&
                        <Tooltip title="点击修改API状态">
                            <Tag
                                color={apiStatusEnum[model.status].color}
                                onClick={model.onOpenApiStatus}
                            >
                                {model.statusDesc}
                            </Tag>
                        </Tooltip>
                    }
                    <span className={css.lastUpdateTime}>更新时间：{model.lastUpdateTime}</span>
                </Space>
            );
        }
    }

    @Bind
    protected renderApiOrMockTabs(): React.ReactNode {
        const model = this.model;
        return (
            <div className={css.apiDetailTop}>
                <Button
                    icon={<LeftOutlined />}
                    className={css.backBtn}
                    type={'link'}
                    onClick={model.onCloseApiDetail}
                />
                <Divider type={'vertical'} className={css.dividerVertical} />
                {this.renderApiName()}
                {
                    model.apiActiveKey === apiActiveTypeEnum.DETAIL &&
                    <AutoGenerateCode
                        docId={model.apiId}
                        className={css.dropDown}
                    />
                }
            </div>
        );
    }

    @Bind
    protected renderDetail(): React.ReactNode {
        const model = this.model;
        if (model.queryDetailLoading) {
            return <Skeleton active />;
        }
        return <div className={css.apiDetail}>
            <ApiBaseInfo model={model.apiBaseInfoM} isShowBranch />
            <HttpApiRequestParams model={model.httpApiRequestParamsM} />
            <HttpApiResponseParams model={model.httpApiResponseParamsM} />
            {
                model.commentM &&
                <CommentT model={model.commentM} />
            }
        </div>;
    }

    @Bind
    protected renderCon(): React.ReactNode {
        const model = this.model;
        return (
            <Tabs
                activeKey={model.apiActiveKey}
                onChange={model.onChangeType}
                tabBarExtraContent={this.renderApiMockOperate()}
                className={css.apiTabs}
            >
                <TabPane tab={'API详情'} key={apiActiveTypeEnum.DETAIL}>
                    {this.renderDetail()}
                </TabPane>
                <TabPane tab={'Mock'} key={apiActiveTypeEnum.MOCK}>
                    <ApiMock model={model.apiMockM} />
                </TabPane>
                <TabPane tab={'测试'} key={apiActiveTypeEnum.TEST}>
                    <ApiTest model={model.apiTestM} />
                </TabPane>
                <TabPane tab={'API上下游'} key={apiActiveTypeEnum.STREAM}>
                    {
                        model.apiStreamM &&
                        <ApiStream model={model.apiStreamM} />
                    }
                </TabPane>
            </Tabs>
        );
    }

    public render(): React.ReactNode {
        const model = this.model;
        return (
            <div className={css.rightApiDetailWrap}>
                {this.renderApiOrMockTabs()}
                {this.renderCon()}
                <VersionRecord model={model.versionRecordM} />
                <ApiStatus model={model.apiStatusM} />
                <AddApiToFolder model={model.addApiToFolderM} />
            </div>
        );
    }
}
