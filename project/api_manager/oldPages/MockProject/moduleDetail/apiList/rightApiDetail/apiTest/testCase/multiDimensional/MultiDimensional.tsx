import { <PERSON>View } from 'libs';
import React from 'react';
import { MultiDimensionalM } from './MultiDimensionalM';
import { observer } from 'mobx-react';
import { Input, Button, Table, Select, Checkbox, Modal, Tooltip, DatePicker } from 'antd';
import css from './MultiDimensional.less';
import { PlusCircleOutlined } from '@ant-design/icons';
import { Bind } from 'lodash-decorators';
import { KdevIconFont } from '@/business/commonComponents/KdevIconFont';
import { DataStructureModal } from '@/business/editJsonTable/dataStructureModal/DataStructureModal';
import { IMultiDimensionalPorps } from '../configure';
import moment from 'moment';

interface IOptions {
    label: string;
    value: number;
}

const contentCheckTypes: IOptions[] = [
    { label: '等于', value: 0 },
    { label: '大于', value: 1 },
    { label: '小于', value: 2 },
    { label: '不等于', value: 3 },
];

const typeOptions = [
    { value: 'object' },
    { value: 'array' },
    { value: 'number' },
    { value: 'string' },
    { value: 'boolean' }
];

@observer
export class MultiDimensional extends AView<MultiDimensionalM, IMultiDimensionalPorps> {

    @Bind
    public componentDidMount(): void {
        if (this.props.tableTitleKey) {
            this.model.getTableScrollX(this.props.tableTitleKey, this.column);
        }
    }

    private column: any[] = [
        {
            title: '名称',
            key: 'name',
            width: 300,
            fixed: 'left',
            render: this.renderName
        },
        {
            title: '类型',
            key: 'type',
            width: 120,
            render: this.renderType
        },
        // {
        //     title: '是否必填',
        //     key: 'required',
        //     width: 90,
        //     render: this.renderRequired
        // },
        {
            title: '内容校验',
            key: 'contentCheckType',
            width: 100,
            render: this.renderContentCheckType
        },
        {
            title: this.props.valueTitle || '参数值',
            key: 'value',
            width: 120,
            render: this.renderValue
        },
        {
            title: '操作',
            key: 'operate',
            width: 140,
            fixed: 'right',
            render: this.renderOperate
        }
    ];

    @Bind
    public columns(): any[] {
        const props = this.props;
        if (props.tableTitleKey) {
            const column = this.column.filter(item => props.tableTitleKey?.includes(item.key));
            return column;
        }
        return this.column;
    }

    @Bind
    protected renderName(record): React.ReactNode {
        let disabledName: boolean = false;
        if (record.key === '0' && this.model.rootName) {
            disabledName = true;
        }
        return (
            <Input
                value={record.name}
                onChange={(e) => {
                    this.model.onChangeInputVal(e, record, 'name');
                }}
                disabled={this.model.disabledName(record) || disabledName || this.props?.disabled?.name}
            />
        );
    }

    @Bind
    protected renderType(record): React.ReactNode {
        return (
            <Select
                value={record.type}
                className={css.type}
                options={typeOptions}
                onSelect={(val) => this.model.onSelectVal(val, record, 'type')}
                disabled={this.props?.disabled?.type}
            />
        );
    }

    @Bind
    protected renderRequired(record): React.ReactNode {
        return (
            <Checkbox
                checked={record.required}
                onChange={(e) => {
                    this.model.onChangeCheckboxChecked(e, record);
                }}
                disabled={this.props.disabled?.required}
            >是</Checkbox>
        );
    }

    @Bind
    private renderValue(record): React.ReactNode {
        const isDisabled: boolean = record.type === 'array' || record.type === 'object'
            || Boolean(this.props?.disabled?.value);
        const dynamicDataValueOptions = this.props?.caseParamTypeList || [];
        const isShowDataConstructor = Boolean(dynamicDataValueOptions.length);
        return (
            <Input.Group
                className={isShowDataConstructor ? css.inputGroupSelect : css.valueWrap}
                compact
            >
                {
                    isShowDataConstructor &&
                    <Select
                        options={dynamicDataValueOptions}
                        value={record.dynamicDataValue || 0}
                        onSelect={(val, option) => this.model.onSelectVal(option, record, 'dynamicDataValue')}
                        disabled={isDisabled}
                        dropdownMatchSelectWidth={false}
                    />
                }
                {
                    record.dynamicDataType === 2
                        ? <DatePicker
                            value={moment(record.value)}
                            onChange={(date, dateString: string) => this.model.onChangeDate(dateString, record)}
                            allowClear={false}
                            className={css.width100}
                        />
                        : <Input
                            value={record.value}
                            disabled={isDisabled || record.dynamicDataType === 1}
                            onChange={e => {
                                this.model.onChangeInputVal(e, record, 'value');
                            }}
                        />
                }
            </Input.Group>
        );
    }

    @Bind
    private renderContentCheckType(record): React.ReactNode {
        return (
            <Select
                placeholder="请选择"
                value={record.contentCheckType || 0}
                options={contentCheckTypes}
                className={css.contentCheckType}
                disabled={record.type === 'object' || record.type === 'array' || this.props?.disabled?.contentCheckType}
                onSelect={(val) => this.model.onSelectVal(val, record, 'contentCheckType')}
            />
        );
    }

    @Bind
    protected renderDescription(record): React.ReactNode {
        return (
            <Input
                value={record.description}
                onChange={(e) => {
                    this.model.onChangeInputVal(e, record, 'description');
                }}
                disabled={this.props?.disabled?.description}
            />
        );
    }

    @Bind
    protected renderOperate(record): React.ReactNode {
        const model = this.model;
        return (
            <>
                <Button
                    icon={<KdevIconFont id={'#iconyanse'} />}
                    className={css.deleteBtn}
                    onClick={() => this.onDeleteRow(record)}
                    disabled={this.props?.disabled?.operate}
                />
                {
                    (record.type === 'object' || record.type === 'array') &&
                    <Tooltip title={'添加子节点'}>
                        <Button
                            icon={<KdevIconFont id={'#icontianjiaziziduan'} />}
                            className={css.addChildRowBtn}
                            onClick={() => model.onAddRow(record)}
                            disabled={this.props?.disabled?.operate}
                        />
                    </Tooltip>
                }
                <Tooltip title={'选择已有数据结构'}>
                    <Button
                        icon={<KdevIconFont id={'#iconadd-circle'} />}
                        onClick={() => model.onOpenReferenceDataModal(record)}
                        disabled={this.props?.disabled?.operate}
                    />
                </Tooltip>
            </>
        );
    }

    @Bind
    protected onDeleteRow(record) {
        if (record.children && record.children.length) {
            Modal.confirm({
                content: record.name + '该参数下有子集删除将不可恢复，确认删除？',
                onOk: () => this.model.onDeleteRow(record)
            });
        } else {
            this.model.onDeleteRow(record);
        }
    }

    @Bind
    protected renderAddRowBtn(): React.ReactNode {
        const model = this.model;
        if (model.list.length < 1) {
            return (
                <Button
                    icon={<PlusCircleOutlined />}
                    type={'dashed'}
                    className={css.addRowBtn}
                    onClick={() => model.onAddRow(null)}
                    disabled={this.props?.disabled?.ruleType}
                >添加一行</Button>
            );
        }
    }

    public render(): React.ReactNode {
        const model = this.model;
        return (
            <div className={css.multiDimensionalWrap}>
                <Table
                    className={css.table}
                    columns={this.columns()}
                    dataSource={model.list}
                    bordered
                    pagination={false}
                    rowKey={'key'}
                    defaultExpandAllRows
                    expandable={{
                        expandedRowKeys: model.expandedRowKeys,
                        onExpand: model.onExpandRowKeys
                    }}
                />
                <div>
                    {this.renderAddRowBtn()}
                    {this.props.tableBottom}
                </div>
                <DataStructureModal model={model.dataStructureModalModel} />
            </div>
        );
    }
}
