import { <PERSON>View } from 'libs';
import React from 'react';
import { observer } from 'mobx-react';
import { Bind } from 'lodash-decorators';
import { RequestParams } from '../requestParams/RequestParams';
import { ResponseParams } from '../responseParams/ResponseParams';
import { CaseDetailM } from './CaseDetailM';
import css from './CaseDetail.less';

@observer
export class CaseDetail extends AView<CaseDetailM> {

    @Bind
    private renderRequestParams(): React.ReactNode {
        const model = this.model;
        if (model.showRequest) {
            return (
                <>
                    <div className={css.baseInfoRow}>
                        <span>请求参数</span>
                    </div>
                    <RequestParams model={model.requestParamsM} />
                </>
            );
        }
    }

    @Bind
    private renderResponseParams(): React.ReactNode {
        const model = this.model;
        if (model.showResponse) {
            return (
                <>
                    <div className={css.baseInfoRow}>
                        <span>返回参数</span>
                        <span className={css.ruleType}>{model.ruleType === 2 ? '数据结构校验' : '返回值校验'}</span>
                    </div>
                    <ResponseParams model={model.responseParamsM} />
                </>
            );
        }
    }

    public render(): React.ReactNode {
        const model = this.model;
        return (
            <div className={css.caseDetailWrap}>
                <div className={css.baseInfoRow}><span>测试时间：</span>{model.caseExecuteTime}</div>
                {this.renderRequestParams()}
                {this.renderResponseParams()}
            </div>
        );
    }
}
