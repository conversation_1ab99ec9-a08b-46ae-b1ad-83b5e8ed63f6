import { <PERSON><PERSON>iew } from 'libs';
import React from 'react';
import { observer } from 'mobx-react';
import { bindObserver } from '@libs/mvvm';
import { Bind } from 'lodash-decorators';
import css from './TestCase.less';
import { Modal, Input, Radio, Select, Button, Popover, Checkbox, Space } from 'antd';
import {
    responseTypeOptions, resDataTypeOptions
} from '@/pages/moduleDetail/apiList/rightApiDetail/apiTest/testCase/configure';
import { TestCaseM } from './TestCaseM';
import { MultiDimensional } from './multiDimensional/MultiDimensional';
import { OneDimensional } from './oneDimensional/OneDimensional';
import { KdevIconFont, ErrorText, ErrorTextConfig, QuestionTips, questionTipsConfig } from '@/business/commonComponents';
import { JsonImport } from '@/business/jsonImportModal/JsonImport';
import { AceDrag } from '@/business/aceDrag/AceDrag';
import { RequestTypeRadio } from '@/business/httpApiComponents/requestTypeRadio/RequestTypeRadio';

const Input_caseName = bindObserver(Input, 'caseName');
const Input_caseDesc = bindObserver(Input, 'caseDesc');
const RadioGroup_responseType = bindObserver(Radio.Group, 'responseType');
const Select_resDataType = bindObserver(Select, 'resDataType');
const Select_ruleType = bindObserver(Select, 'ruleType');

const ruleTypeOptions = [
    { label: '返回值校验', value: 1 },
    { label: '数据结构校验', value: 2 }
];

@observer
export class TestCase extends AView<TestCaseM> {
    private popDom: React.RefObject<HTMLDivElement> = React.createRef();

    private tableTitleKey: string[] = ['name', 'type', 'required', 'value', 'operate'];
    private bodyTableTitleKey: string[] = ['name', 'type', 'required', 'value', 'contentCheckType', 'operate'];

    @Bind
    private confirmClearParams(type: 'res' | 'req'): void {
        Modal.confirm({
            content: '确认要清空数据吗？',
            onOk: () => this.model.onClearParams(type)
        });
    }

    @Bind
    private renderReqResOperate(type: 'req' | 'res'): React.ReactNode {
        const model = this.model;
        let importJsonBtnVisible = false;
        if (type === 'res' && model.resDataType === 1 && model.responseType === 2) {
            importJsonBtnVisible = true;
        }
        if (type === 'req' && model.reqDataType === 'body' && model.requestType === 2) {
            importJsonBtnVisible = true;
        }
        return (
            <div>
                <Button
                    // disabled={model.ruleType === 2 && type === 'res'}
                    onClick={() => this.confirmClearParams(type)}
                >
                    清空参数
                </Button>
                {
                    importJsonBtnVisible &&
                    <Button
                        type={'primary'}
                        // disabled={model.ruleType === 2 && type === 'res'}
                        className={css.jsonImportBtn}
                        onClick={() => model.onOpenJsonImportModal(type === 'req' ? model.reqDataType : '')}
                    >
                        JSON导入
                    </Button>
                }
            </div>
        );
    }

    @Bind
    private renderValueTitle(): React.ReactNode {
        const title: React.ReactNode = (
            <div className={css.valueTitleTip}>{questionTipsConfig.TEST_CASE_VALUE_CHECK_RULE}</div>
        );
        return (
            <div ref={this.popDom}>参数值 {QuestionTips({title, popDom: this.popDom})}</div>
        );
    }

    @Bind
    private renderRequestTable(): React.ReactNode {
        const model = this.model;
        if (model.requestType === 1) {
            return (
                <OneDimensional
                    model={model.reqHeaderOneDimensionalM}
                    valueTitle={this.renderValueTitle()}
                    tableTitleKey={this.tableTitleKey}
                    caseParamTypeList={model.caseParamTypeList}
                />
            );
        }
        if (model.requestType === 2) {
            if (model.reqDataType === 'body') {
                return (
                    <MultiDimensional
                        model={model.reqBodyMultiDimensionalM}
                        valueTitle={this.renderValueTitle()}
                        tableTitleKey={this.tableTitleKey}
                        caseParamTypeList={model.caseParamTypeList}
                    />
                );
            }
            if (model.reqDataType === 'form') {
                return (
                    <OneDimensional
                        model={model.reqFormOneDimensionalM}
                        valueTitle={this.renderValueTitle()}
                        tableTitleKey={this.tableTitleKey}
                        caseParamTypeList={model.caseParamTypeList}
                    />
                );
            }
        }
        if (model.requestType === 3) {
            return (
                <OneDimensional
                    model={model.reqQueryOneDimensionalM}
                    valueTitle={this.renderValueTitle()}
                    tableTitleKey={this.tableTitleKey}
                    caseParamTypeList={model.caseParamTypeList}
                />
            );
        }
        if (model.requestType === 4) {
            return (
                <OneDimensional
                    model={model.reqPathOneDimensionalM}
                    valueTitle={this.renderValueTitle()}
                    tableTitleKey={this.tableTitleKey}
                    caseParamTypeList={model.caseParamTypeList}
                />
            );
        }
    }

    @Bind
    private pythonScriptTip(): React.ReactNode {
        if (this.model.resDataType === 2) {
            const url: string = 'https://docs.corp.kuaishou.com/d/home/<USER>';
            const content = (
                <a target={'_blank'} href={url}>
                    点击获取现成代码, 拿来就用
                </a>
            );
            return (
                <>
                    <Popover content={content}>
                        <a target={'_blank'} href={url}>
                            <KdevIconFont id={'#iconquestion'} className={css.tipsIcon} />
                        </a>
                    </Popover>
                    <Button
                        type="primary"
                        onClick={() => this.model.onChangePythonTemplate(0)}
                        className={css.pythonTemplateBtn}
                    >
                        常规data模板
                    </Button>
                    <Button
                        type="primary"
                        onClick={() => this.model.onChangePythonTemplate(1)}
                        className={css.pythonTemplateBtn}
                    >
                        字符串data模板
                    </Button>
                </>
            );
        }
    }

    @Bind
    private renderRuleTypeSelect(): React.ReactNode {
        return (
            <span className={css.ruleTypeSelectWrap}>
                校验方式：
                <Select_ruleType
                    model={this.model}
                    options={ruleTypeOptions}
                    className={css.ruleTypeSelect}
                />
                {
                    ErrorText(this.model.ruleType === 1
                        ? ErrorTextConfig.RETURN_VALUE_CHECK
                        : ErrorTextConfig.RETURN_DATA_STRUCTURE_CHECK)
                }
            </span>
        );
    }

    @Bind
    private renderResponseType(): React.ReactNode {
        const model = this.model;
        return (
            <div className={css.responseTypeWrap}>
                <div>
                    <RadioGroup_responseType
                        model={model}
                        className={css.responseTypeRadio}
                        options={responseTypeOptions}
                        optionType={'button'}
                    />
                    {
                        model.responseType === 2 &&
                        <>
                            数据类型
                            <Select_resDataType
                                model={model}
                                className={css.resDataTypeSelect}
                                options={resDataTypeOptions}
                            // disabled={model.ruleType === 2}
                            />
                            {this.pythonScriptTip()}
                        </>
                    }
                    {this.renderRuleTypeSelect()}
                </div>
                {this.renderReqResOperate('res')}
            </div>
        );
    }

    @Bind
    private renderResponseTable(): React.ReactNode {
        const model = this.model;
        if (model.responseType === 2) {
            if (model.resDataType === 1) {
                return (
                    <MultiDimensional
                        model={model.resBodyMultiDimensionalM}
                        valueTitle={'预期结果'}
                        tableTitleKey={this.bodyTableTitleKey}
                        disabled={{
                            // required: true,
                            // ruleType: model.ruleType === 2,
                            // name: model.ruleType === 2,
                            value: model.ruleType === 2,
                            // type: model.ruleType === 2,
                            // description: model.ruleType === 2,
                            // operate: model.ruleType === 2,
                            contentCheckType: model.ruleType === 2
                        }}
                    />
                );
            }
            if (model.resDataType === 2) {
                return <AceDrag
                    theme={'tomorrow'}
                    mode={'python'}
                    value={model.resPythonScript}
                    onChange={model.onChangePythonScript}
                />;
            }
        }
        if (model.responseType === 1) {
            return (
                <OneDimensional
                    model={model.resHeaderOneDimensionalM}
                    valueTitle={'预期结果'}
                    tableTitleKey={this.bodyTableTitleKey}
                    disabled={{
                        // required: true,
                        // ruleType: model.ruleType === 2,
                        // name: model.ruleType === 2,
                        value: model.ruleType === 2,
                        // type: model.ruleType === 2,
                        // description: model.ruleType === 2,
                        // operate: model.ruleType === 2,
                        contentCheckType: model.ruleType === 2
                    }}
                />
            );
        }
    }

    @Bind
    private renderFooter(): React.ReactNode {
        const model = this.model;
        return (
            <div className={css.footer}>
                <Space>
                    <Checkbox
                        checked={Boolean(model.onlyAllowCreatorUpdate)}
                        onChange={model.onChangeOnlyAllowCreatorUpdate}
                    >
                        仅维护者可修改测试用例
                    </Checkbox>
                    <Checkbox
                        checked={model.sendKimWhenUpdate}
                        onChange={model.onChangeSendKimWhenUpdate}
                    >
                        给研发参与人发送kim通知
                    </Checkbox>
                </Space>
                <span>
                    <Button onClick={model.onCloseTestCaseModal}>取消</Button>
                    <Button
                        type="primary"
                        loading={model.loading}
                        onClick={model.onSaveTestCase}
                    >
                        确定
                    </Button>
                </span>
            </div>
        );
    }

    public render(): React.ReactNode {
        const model = this.model;
        return (
            <Modal
                className={css.testCaseModal}
                visible={model.visible}
                width={'80%'}
                title={model.type === 'edit' ? '编辑用例' : '新建用例'}
                onCancel={model.onCloseTestCaseModal}
                maskClosable={false}
                footer={this.renderFooter()}
            >
                <div className={css.testCaseModalBody}>
                    <div className={css.labelRequired}>用例名称</div>
                    <Input_caseName model={model} placeholder={'请填写用例名称'} />
                    <div className={css.label}>用例描述</div>
                    <Input_caseDesc model={model} placeholder={'请填写用例描述'} />
                    <div className={css.label}>请求参数</div>
                    <RequestTypeRadio
                        requestTypeProps={{
                            value: model.requestType,
                            onChange: model.onChangeRequestType
                        }}
                        reqDataTypeProps={{
                            value: model.reqDataType,
                            onChange: model.onChangeReqDataType
                        }}
                        rigthSlot={this.renderReqResOperate('req')}
                    />
                    {this.renderRequestTable()}
                    <div className={css.label}>返回参数</div>
                    {this.renderResponseType()}
                    {this.renderResponseTable()}
                </div>
                <JsonImport model={model.jsonImportM} />
                {/* <SendKimMsg model={model.sendKimMsgM} /> */}
            </Modal>
        );
    }
}
