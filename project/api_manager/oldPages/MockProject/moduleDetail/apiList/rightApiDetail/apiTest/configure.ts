export const testCaseStatus = {
    0: {
        color: '',
        statusDesc: '未测试'
    },
    1: {
        color: '#ff4d4f',
        statusDesc: '未通过'
    },
    2: {
        color: '#31bf30',
        statusDesc: '通过'
    },
    3: {
        color: '#ff4d4f',
        statusDesc: '未通过（已忽略）'
    },
    4: {
        color: '#ff4d4f',
        statusDesc: '失败'
    }
};

interface ILabelValue {
    label: string;
    value: string | number;
}

export const reportResult: ILabelValue[] = [
    {
        label: '测试用例数',
        value: 'totalQuantity'
    },
    {
        label: '测试通过率',
        value: 'percent'
    },
    {
        label: '执行用例数',
        value: 'execQuantity'
    },
    {
        label: '通过用例数',
        value: 'passQuantity'
    },
    {
        label: '未通过用例数',
        value: 'unPassQuantity'
    }
];

export const testCaseTypeOptions: ILabelValue[] = [
    {
        label: '测试用例',
        value: 0
    },
    {
        label: '测试报告',
        value: 1
    }
];

export enum testCaseType {
    TEST_CASE = 0,
    TEST_REPORT
}
