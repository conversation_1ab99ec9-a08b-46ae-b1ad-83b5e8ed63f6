.testCaseModal {
  .testCaseModalBody{
    max-height: calc(100vh - 350px);
    overflow: auto;

    .valueTitleTip{
      white-space: pre-line;
    }
  }
  .label {
    margin: 24px 0 8px;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  
  .labelRequired {
    margin: 24px 0 8px;
  }

  .labelRequired:before {
    content: '* ';
    color: #ff4d4f;
  }

  .requestTypeWrap, .responseTypeWrap {
    margin-bottom: 16px;
    display: flex;
    justify-content: space-between;

    .requestTypeRadio, .responseTypeRadio {
      margin-right: 16px;
    }

    .reqDataTypeSelect {
      margin-left: 8px;
      width: 120px;
    }

    .resDataTypeSelect{
      margin-left: 8px;
      width: 140px;
    }

    .ruleTypeSelectWrap{
      margin-left: 16px;
      
      .ruleTypeSelect {
        width: 126px;
        margin-right: 4px;
      }
    }

    .pythonTemplateBtn{
      margin-left: 8px;
    }
  }

  .jsonImportBtn {
    margin-left: 8px;
  }

  .tipsIcon {
    font-size: 16px;
    margin-left: 4px;
  }

  .footer{
    display: flex;
    justify-content: space-between;
  }

  :global {
    .ant-modal-body {
      padding-top: 0;
    }
  }
}
