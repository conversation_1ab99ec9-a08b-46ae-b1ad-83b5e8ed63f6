import { AView } from 'libs';
import React from 'react';
import { observer } from 'mobx-react';
import { bindObserver } from '@libs/mvvm';
import { Bind } from 'lodash-decorators';
import css from './TestConfiguration.less';
import { PlusCircleOutlined } from '@ant-design/icons';
import { Modal, Input, Table, Button, Radio, AutoComplete } from 'antd';
import { TestConfigurationM } from './TestConfigurationM';
import { KdevIconFont } from '@/business/commonComponents/KdevIconFont';
import { settings } from './configure';

const Input_address = bindObserver(Input, 'address');

@observer
export class TestConfiguration extends AView<TestConfigurationM> {

    @Bind
    protected renderSettingName(record): React.ReactNode {
        return (
            <Input
                value={record.name}
                onChange={(e) => {
                    this.model.onSettingChangeInputVal(e, 'name');
                }}
            />
        );
    }

    @Bind
    private renderSettingValue(record): React.ReactNode {
        return (
            <Input
                value={record.value}
                onChange={e => {
                    this.model.onSettingChangeInputVal(e, 'value');
                }}
            />
        );
    }

    @Bind
    protected renderName(record): React.ReactNode {
        const headerKeyOptions = [
            { value: 'Cookie' },
            { value: 'trace-context' }
        ];
        return (
            <AutoComplete
                className={css.name}
                options={headerKeyOptions}
                value={record.name}
                onChange={(val: string) => {
                    this.model.onChangeName(val, record);
                }}
            />
        );
        // return (
        //     <Input
        //         value={record.name}
        //         onChange={(e) => {
        //             this.model.onChangeInputVal(e, record, 'name');
        //         }}
        //     />
        // );
    }

    @Bind
    private renderValue(record): React.ReactNode {
        return (
            <Input
                value={record.value}
                onChange={e => {
                    this.model.onChangeInputVal(e, record, 'value');
                }}
            />
        );
    }

    @Bind
    protected renderAddRowBtn(): React.ReactNode {
        const model = this.model;
        return (
            <Button
                icon={<PlusCircleOutlined />}
                type={'dashed'}
                className={css.addRowBtn}
                onClick={model.onAddRow}
            >添加一行</Button>
        );
    }

    @Bind
    protected renderOpreate(text, row, i): React.ReactNode {
        return (
            <Button
                icon={<KdevIconFont id={'#iconyanse'} />}
                className={css.deleteBtn}
                onClick={() => this.model.onDeleteRow(i)}
            />
        );
    }

    @Bind
    private renderRequestHeaderTable(): React.ReactNode {
        const model = this.model;
        return <>
            <Table
                className={css.table}
                columns={[
                    {
                        title: '名称',
                        key: 'name',
                        width: 140,
                        render: this.renderName
                    },
                    {
                        title: '值',
                        key: 'value',
                        render: this.renderValue
                    },
                    {
                        title: '操作',
                        key: 'opreate',
                        width: 100,
                        fixed: 'right',
                        render: this.renderOpreate
                    }
                ]}
                dataSource={model.headers}
                bordered
                pagination={false}
                rowKey={'key'}
                defaultExpandAllRows
            />
            {this.renderAddRowBtn()}
        </>;
    }

    @Bind
    private renderCredentialsTable(): React.ReactNode {
        const model = this.model;
        return model.radioValue !== '0' && <Table
            className={css.table}
            columns={[
                {
                    title: '名称',
                    key: 'name',
                    width: 140,
                    render: this.renderSettingName
                },
                {
                    title: '值',
                    key: 'value',
                    render: this.renderSettingValue
                },
            ]}
            dataSource={model.setting[model.radioValue]}
            bordered
            pagination={false}
            rowKey={'key'}
            defaultExpandAllRows
        />;
    }

    public render(): React.ReactNode {
        const model = this.model;
        return (
            <Modal
                className={css.testCaseModal}
                visible={model.visible}
                width={1000}
                title={'测试配置'}
                onCancel={model.onCloseTestConfigurationModal}
                onOk={model.onOKTestConfigurationModal}
            >
                <div className={css['url-label']}>请求URL<span className={css['tips']}>（请填写完整的请求URL）</span></div>
                <Input_address model={model} placeholder={'请填写完整的请求URL'} />
                <div className={css['url-label']}>
                    <p>请选择接口所属类型</p>
                    <Radio.Group className={css.radio} onChange={model.onRadioChange} value={model.radioValue}>
                        {
                            settings.map(item => {
                                return <Radio key={item.type} value={item.type}>{item.name}</Radio>;
                            })
                        }
                    </Radio.Group>
                    {this.renderCredentialsTable()}
                </div>
                <div className={css['header-label']}>
                    请求头
                    <Button
                        type="dashed"
                        size="small"
                        onClick={model.onAddAP}
                        disabled={model.headers.filter(item => item.name === 'trace-context').length > 0}>添加泳道</Button>
                </div>
                {this.renderRequestHeaderTable()}
            </Modal>
        );
    }
}
