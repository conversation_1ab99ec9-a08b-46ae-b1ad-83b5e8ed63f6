import { AViewModel } from '@libs/mvvm';
import { observable, action } from 'mobx';
import { ViewJsonTableM } from '../viewJsonTable/ViewJsonTableM';
import { reqDataEnum } from '@/pages/moduleDetail/apiList/editApi/enum';
import { formatKey1, JSONbigStringify } from '@/index.config/tools';

interface IParamsList {
    in?: string;
    key: string;
    name: string;
    example?: object;
    description: string;
    required: boolean;
    type: string;
    value: string;
    children?: IParamsList[];
}

interface IRequest {
    parameters: any[];
}

export class RequestParamsM extends AViewModel {
    @observable public headerList: IParamsList[] = [];
    @observable public bodyList: IParamsList[] = [];
    @observable public reqBodyExample: string = '';
    @observable public formList: IParamsList[] = [];
    @observable public queryList: IParamsList[] = [];
    @observable public pathList: IParamsList[] = [];

    public headerViewJsonTableM = new ViewJsonTableM();
    public bodyViewJsonTableM = new ViewJsonTableM();
    public formViewJsonTableM = new ViewJsonTableM();
    public queryViewJsonTableM = new ViewJsonTableM();
    public pathViewJsonTableM = new ViewJsonTableM();

    @action.bound
    public setData(request: IRequest): void {
        this.initData();
        request?.parameters && this.formatParameters(request.parameters);
        // this.setFields();
    }

    @action.bound
    public initData(): void {
        this.headerList = [];
        this.bodyList = [];
        this.formList = [];
        this.queryList = [];
        this.pathList = [];
        this.headerViewJsonTableM.initData();
        this.bodyViewJsonTableM.initData();
        this.formViewJsonTableM.initData();
        this.queryViewJsonTableM.initData();
        this.pathViewJsonTableM.initData();
    }

    @action.bound
    public isExpandAllKeys(boo: boolean): void {
        this.bodyViewJsonTableM.onExpandAllKeys(boo);
    }

    // 处理parameters参数
    @action.bound
    private formatParameters(data: IParamsList[]) {
        const headerList: IParamsList[] = [];
        const bodyList: IParamsList[] = [];
        const formList: IParamsList[] = [];
        const queryList: IParamsList[] = [];
        const pathList: IParamsList[] = [];
        data.forEach(item => {
            switch (item.in) {
                case reqDataEnum.HEADER:
                    headerList.push(item);
                    break;
                case reqDataEnum.BODY:
                    bodyList.push(item);
                    break;
                case reqDataEnum.FORM:
                    formList.push(item);
                    break;
                case reqDataEnum.PATH:
                    pathList.push(item);
                    break;
                case reqDataEnum.QUERY:
                    queryList.push(item);
                    break;
            }
        });
        if (headerList.length > 0) {
            this.headerList = formatKey1(headerList)[0];
            this.headerViewJsonTableM.setListAndExpanedKeys(this.headerList);
        }
        if (bodyList.length > 0) {
            const [list, expandedKey] = formatKey1(bodyList);
            this.bodyList = list;
            this.bodyViewJsonTableM.setListAndExpanedKeys(this.bodyList, expandedKey);
            this.reqBodyExample =
                this.bodyList[0]?.example && JSONbigStringify(this.bodyList[0]?.example) || '';
        }
        if (formList.length > 0) {
            this.formList = formatKey1(formList)[0];
            this.formViewJsonTableM.setListAndExpanedKeys(this.formList);
        }
        if (queryList.length > 0) {
            this.queryList = formatKey1(queryList)[0];
            this.queryViewJsonTableM.setListAndExpanedKeys(this.queryList);
        }
        if (pathList.length > 0) {
            this.pathList = formatKey1(pathList)[0];
            this.pathViewJsonTableM.setListAndExpanedKeys(this.pathList);
        }
    }
}
