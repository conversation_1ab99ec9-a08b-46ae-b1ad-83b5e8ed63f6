import { nsMockManageKoasApiManageTestCaseQueryCaseParamTypeGet } from '@/remote';

export interface ICaseParamTypeItem extends nsMockManageKoasApiManageTestCaseQueryCaseParamTypeGet.IItem { }

interface IRadioOptions {
    label: string;
    value: number;
}

interface ISelectOptions {
    label?: string;
    value: string | number;
}

export const responseTypeOptions: IRadioOptions[] = [
    {
        label: '返回头部',
        value: 1,
    },
    {
        label: '返回结果',
        value: 2,
    }
];

export const resDataTypeOptions: ISelectOptions[] = [
    {
        label: '静态 json',
        value: 1,
    },
    {
        label: 'Python2.7 脚本',
        value: 2,
    }
];

export enum reqDataEnum {
    HEADER = 'header',
    BODY = 'body',
    FORM = 'form',
    QUERY = 'query',
    PATH = 'path'
}

const python2ScriptDemo2: string =
    'def on_response_test(headers, body):\n' +
    '   """\n' +
    '   Python 脚本需要实现这个函数, 名字和参数都是固定的. 在这里完成业务数据的判断.\n' +
    '   教程: API 准入测试 Python 脚本速成\n' +
    '   :param headers: json 结构\n' +
    '   :param body: json 结构\n' +
    '   :return: 不返回或者返回 `None` 则代表校验通过. 如果与预期不符, 就返回说明性的内容, 平台会在测试详情中展示.\n' +
    '   """\n' +
    '\n' +
    '   # data 长度校验\n' +
    '   if body["data"] < 10:\n' +
    '       return u"data 数据量不足"\n' +
    '\n' +
    '   # data 内容校验\n' +
    '   for item in body["data"]:\n' +
    '       name = ite"联调测试":\n' +
    '       return u"name 错误: %s" % name';

const python2ScriptDemo3: string =
    'def on_response_test(headers, body):\n' +
    '   """\n' +
    '   Python 脚本需要实现这个函数, 名字和参数都是固定的. 在这里完成业务数据的判断.\n' +
    '   教程: API 准入测试 Python 脚本速成\n' +
    '   :param headers: json 结构\n' +
    '   :param body: json 结构\n' +
    '   :return: 不返回或者返回 `None` 则代表校验通过. 如果与预期不符, 就返回说明性的内容, 平台会在测试详情中展示.\n' +
    '   """\n' +
    '\n' +
    '   # 对付 String 类型的数据, 可以通过 `json.loads` 解析成 json 后再处理\n' +
    '   json_body_data = json.loads(body["data"])\n' +
    '\n' +
    '   # data 长度校验\n' +
    '   if json_body_data < 10:\n' +
    '       return u"data 数据量不足"\n' +
    '\n' +
    '   # data 内容校验\n' +
    '   for item in json_body_data:\n' +
    '       name = item["name"]\n' +
    '       if name != u"联调测试":\n' +
    '           return u"name 错误: %s" % name';

export const python2ScriptTemplate: string[] = [
    python2ScriptDemo2, python2ScriptDemo3
];

export interface IReqList {
    in?: string;
    key?: string;
    name: string;
    example?: object;
    value?: string;
    description: string;
    required: boolean;
    type: string;
    children?: IReqList[];
}

export interface IResponse {
    name?: string;
    type?: string;
    required?: boolean;
    description: string;
    example?: object;
    headers: any;
    body: any;
}

export interface IDynamicResponsePython {
    script: string;
}

export interface IOneDimensionalProps {
    valueTitle?: string | React.ReactNode;
    tableTitleKey?: string[];
    disabled?: {
        [key: string]: boolean;
    };
    caseParamTypeList?: ICaseParamTypeItem[];
}

export interface IMultiDimensionalPorps {
    tableBottom?: React.ReactElement | React.ReactNode;
    valueTitle?: string | React.ReactNode;
    tableTitleKey?: string[];
    disabled?: {
        [key: string]: boolean;
    };
    caseParamTypeList?: ICaseParamTypeItem[];
}
