import { AViewModel } from 'libs';
import { action, observable, runInAction } from 'mobx';
import { MultiDimensionalM } from './multiDimensional/MultiDimensionalM';
import { OneDimensionalM } from './oneDimensional/OneDimensionalM';
import { Bind } from 'lodash-decorators';
import {
    nsMockManageKoasApiManageTestCaseCreatePost, nsMockManageKoasApiManageTestCaseUpdatePost,
    nsMockManageKoasApiManageTestCaseDetailGet, nsMockManageKoasApiManageHttpApiQueryDetailGet,
    nsMockManageKoasApiManageTestCaseQueryCaseParamTypeGet
} from '@/remote';
import { message } from 'antd';
import { formatKey1, JSONbigStringify, JSONbigParse } from '@/index.config/tools';
import { reqDataEnum, python2ScriptTemplate, ICaseParamTypeItem, IReqList, IResponse, IDynamicResponsePython } from './configure';
import { JsonImportM } from '@/business/jsonImportModal/JsonImportM';

export class TestCaseM extends AViewModel {
    @observable public visible: boolean = false;
    @observable public loading: boolean = false;
    @observable public caseId: number = 0;
    @observable public docId: number = 0;
    @observable public type: string = 'new'; // new|edit|copy
    private fullUrl: string = '';
    @observable public caseName: string = '';
    @observable public caseDesc: string = '';
    @observable public requestType: number = 2;
    @observable public reqDataType: string = 'body';
    @observable public ruleType: number = 1; // 1:校验value值 2:只校验字段是否存在与类型
    @observable public responseType: number = 2;
    @observable public resDataType: number = 1;
    @observable private pythonTemplateIndex: number = 0;
    @observable public resPythonScript: string = python2ScriptTemplate[this.pythonTemplateIndex];
    @observable public response: any;
    private moduleId: number = 0;
    private reqBodyExample: string = '';
    private resBodyExample: string = '';

    @observable public onlyAllowCreatorUpdate: number = 0; // 是否仅允许维护人修改测试用例 0:否 1:是
    @observable public sendKimWhenUpdate: boolean = true;
    private developerList: string[] = [];
    private remarkWhenUpdate: string = '';
    @observable public caseParamTypeList: ICaseParamTypeItem[] = [];

    public onSaveCallback?(): void;

    public reqHeaderOneDimensionalM = new OneDimensionalM();
    public reqBodyMultiDimensionalM = new MultiDimensionalM();
    public reqFormOneDimensionalM = new OneDimensionalM(reqDataEnum.FORM);
    public reqQueryOneDimensionalM = new OneDimensionalM();
    public reqPathOneDimensionalM = new OneDimensionalM();
    public resBodyMultiDimensionalM = new MultiDimensionalM();
    public resHeaderOneDimensionalM = new OneDimensionalM();
    public jsonImportM = new JsonImportM();

    @action.bound
    public init(type: string, docId: number, caseId?: number, fullUrl: string = ''): void {
        this.visible = true;
        this.docId = docId;
        this.caseId = caseId || this.caseId;
        this.type = type;
        this.fullUrl = fullUrl;
        if (this.type === 'new' && !fullUrl) {
            this.queryApiDetail();
        } else if (this.caseId) {
            this.detail();
        }
        this.queryCaseParamType();
    }

    // 关闭测试用例弹框
    @action.bound
    public onCloseTestCaseModal(): void {
        this.visible = false;
        this.initData();
    }

    // 初始化参数
    @action
    private initData(): void {
        this.type = 'new';
        this.docId = 0;
        this.caseId = 0;
        this.caseName = '';
        this.caseDesc = '';
        this.requestType = 2;
        this.reqDataType = 'body';
        this.ruleType = 1;
        this.responseType = 2;
        this.resDataType = 1;
        this.response = null;
        this.pythonTemplateIndex = 0;
        this.resPythonScript = python2ScriptTemplate[this.pythonTemplateIndex];
        this.moduleId = 0;
        this.remarkWhenUpdate = '';
        this.sendKimWhenUpdate = true;
        this.developerList = [];
        this.reqHeaderOneDimensionalM.initData();
        this.reqBodyMultiDimensionalM.initData();
        this.reqFormOneDimensionalM.initData();
        this.reqQueryOneDimensionalM.initData();
        this.reqPathOneDimensionalM.initData();
        this.resHeaderOneDimensionalM.initData();
        this.resBodyMultiDimensionalM.initData();
    }

    @action.bound
    public onOpenJsonImportModal(type: string): void {
        this.visible = false;
        this.jsonImportM.init(type, type ? this.reqBodyExample : this.resBodyExample, this.docId);
        this.jsonImportM.onSaveJsonCallBack = this.onSaveJsonCallBack;
        this.jsonImportM.onCloseModalCallBack = this.onCloseModalCallBack;
    }

    @action.bound
    public onChangeRuleType(e): void {
        this.ruleType = e.target.checked ? 2 : 1;
        this.resDataType = 1;
    }

    @action.bound
    public onChangePythonScript(resPythonScript: string): void {
        this.resPythonScript = resPythonScript;
    }

    @action.bound
    public onChangePythonTemplate(pythonTemplateIndex: number): void {
        this.onChangePythonScript(python2ScriptTemplate[pythonTemplateIndex]);
    }

    @action.bound
    private onCloseModalCallBack(): void {
        this.visible = true;
    }

    @action.bound
    private onSaveJsonCallBack(type: string, data, expamle: string) {
        this.visible = true;
        if (type) {
            this.initRequest(data);
            this.reqBodyExample = expamle;
        } else {
            this.formatResBody(data);
            this.resBodyExample = expamle;
        }
    }

    @action.bound
    public onChangeRequestType(e): void {
        this.requestType = e.target.value;
    }

    @action.bound
    public onChangeReqDataType(reqDataType): void {
        this.reqDataType = reqDataType;
    }

    @action
    public onClearParams(type: 'res' | 'req'): void {
        if (type === 'req') {
            if (this.requestType === 1) {
                this.reqHeaderOneDimensionalM.initData();
            }
            if (this.requestType === 2) {
                if (this.reqDataType === 'body') {
                    this.reqBodyMultiDimensionalM.initData();
                }
                if (this.reqDataType === 'form') {
                    this.reqFormOneDimensionalM.initData();
                }
            }
            if (this.requestType === 3) {
                this.reqQueryOneDimensionalM.initData();
            }
            if (this.requestType === 4) {
                this.reqPathOneDimensionalM.initData();
            }
        }
        if (type === 'res') {
            if (this.responseType === 1) {
                this.resHeaderOneDimensionalM.initData();
            }
            if (this.responseType === 2) {
                if (this.resDataType === 1) {
                    this.resBodyMultiDimensionalM.initData();
                }
                if (this.resDataType === 2) {
                    this.resPythonScript = '';
                }
            }
        }
    }

    @action.bound
    public onChangeOnlyAllowCreatorUpdate(e): void {
        this.onlyAllowCreatorUpdate = e.target.checked ? 1 : 0;
    }

    // 给研发参与人发送通知
    @action.bound
    public onChangeSendKimWhenUpdate(e): void {
        this.sendKimWhenUpdate = e.target.checked;
    }

    @action.bound
    public onSaveTestCase() {
        if (this.reqBodyMultiDimensionalM.checkParams()) {
            return;
        }
        if (this.resBodyMultiDimensionalM.checkParams()) {
            return;
        }
        if (!this.caseName) {
            message.warn('请填写测试用例名称！');
            return;
        }
        this.createOrUpdate();
    }

    @action
    private getParams() {
        const requestHeaderList = this.reqHeaderOneDimensionalM.getList().map(item => {
            item['in'] = 'header';
            return item;
        });
        let requestBodyList: IReqList[];
        if (this.reqDataType === 'form') {
            requestBodyList = this.reqFormOneDimensionalM.getList().map(item => {
                item['in'] = 'form';
                return item;
            });
        } else {
            requestBodyList = this.reqBodyMultiDimensionalM.getList().map(item => {
                item['in'] = 'body';
                item['example'] = JSONbigParse(this.reqBodyExample);
                return item;
            });
        }
        const requestQueryList = this.reqQueryOneDimensionalM.getList().map(item => {
            item['in'] = 'query';
            return item;
        });
        const requestPathList = this.reqPathOneDimensionalM.getList().map(item => {
            item['in'] = 'path';
            return item;
        });
        const parameters = [
            ...requestHeaderList,
            ...requestBodyList,
            ...requestQueryList,
            ...requestPathList
        ];
        const responseBodyList = this.resBodyMultiDimensionalM.getList();
        let response: IResponse | null = null;
        if (this.resDataType === 1) {
            const headers = {};
            this.resHeaderOneDimensionalM.getList().forEach(item => {
                headers[item.name] = {
                    type: item['type'],
                    // example: item['example'],
                    required: item['required'],
                    description: item['description']
                };
            });
            const body = responseBodyList.length ? {
                name: responseBodyList[0]?.name || '',
                type: responseBodyList[0]?.type || '',
                required: responseBodyList[0]?.required || false,
                model: responseBodyList[0]?.children || [],
                example: JSONbigParse(this.resBodyExample),
            } : null;
            response = {
                description: '',
                headers,
                body,
            };
        }
        let dynamicResponsePython: IDynamicResponsePython | null = null;
        if (this.resDataType === 2) {
            dynamicResponsePython = {
                script: this.resPythonScript
            };
        }
        const params = {
            docId: this.docId,
            id: this.caseId,
            name: this.caseName,
            description: this.caseDesc,
            request: {
                parameters
            },
            response,
            dynamicResponsePython,
            responseType: this.resDataType,
            ruleType: this.ruleType,
            onlyAllowCreatorUpdate: this.onlyAllowCreatorUpdate,
            sendKimWhenUpdate: this.sendKimWhenUpdate,
            developerList: this.developerList,
            remarkWhenUpdate: this.remarkWhenUpdate
        };
        if (this.type === 'new' && this.fullUrl) {
            params['fullUrl'] = this.fullUrl;
        }
        return params;
    }

    @Bind
    private async createOrUpdate() {
        try {
            const params = this.getParams();
            const apiRequest = this.type === 'edit'
                ? nsMockManageKoasApiManageTestCaseUpdatePost
                : nsMockManageKoasApiManageTestCaseCreatePost;
            runInAction(() => this.loading = true);
            await apiRequest.remote(params);
            message.success(this.type === 'edit' ? '编辑测试用例成功～' : '新建用例成功～');
            runInAction(() => this.loading = false);
            this.onCloseTestCaseModal();
            this.onSaveCallback && this.onSaveCallback();
        } catch (e) {
            runInAction(() => this.loading = false);
        }
    }

    @action
    private initReqResBody(moduleId: number): void {
        this.moduleId = moduleId;
        this.reqBodyMultiDimensionalM.init('RequestRoot', this.moduleId);
        this.resBodyMultiDimensionalM.init('ResponseRoot', this.moduleId);
    }

    @action
    private formatDetail(detail): void {
        this.caseName = this.type === 'copy' ? `${detail.name}-副本` : detail.name;
        this.caseDesc = detail.description;
        this.ruleType = detail?.ruleType || 1;
        this.resDataType = detail.responseType || 1;
        this.resPythonScript = detail?.dynamicResponsePython?.script || '';
        this.onlyAllowCreatorUpdate = detail?.onlyAllowCreatorUpdate;
        this.developerList = detail?.developerList || [];
        this.sendKimWhenUpdate = detail?.sendKimWhenUpdate;
        this.initReqResBody(detail?.moduleId);
    }

    @Bind
    private async detail() {
        try {
            const params = {
                caseId: this.caseId
            };
            const result = await nsMockManageKoasApiManageTestCaseDetailGet.remote(params);
            result && this.formatDetail(result);
            result?.request && result?.request?.parameters && this.initRequest(result?.request?.parameters);
            result?.response && this.initResponse(result?.response);
        } catch (e) {
        }
    }

    @action.bound
    private initReqType(bodyList, formList, queryList, pathList, headerList): void {
        if (bodyList.length || formList.length) {
            this.requestType = 2;
            return;
        }
        if (queryList.length) {
            this.requestType = 3;
            return;
        }
        if (pathList.length) {
            this.requestType = 4;
            return;
        }
        if (headerList.length) {
            this.requestType = 1;
            return;
        }
    }

    @action.bound
    public initRequest(parameters: IReqList[]): void {
        const headerList: IReqList[] = [];
        const bodyList: IReqList[] = [];
        const formList: IReqList[] = [];
        const queryList: IReqList[] = [];
        const pathList: IReqList[] = [];
        parameters.forEach(item => {
            switch (item.in) {
                case reqDataEnum.HEADER:
                    headerList.push(item);
                    break;
                case reqDataEnum.BODY:
                    this.reqDataType = 'body';
                    bodyList.push(item);
                    break;
                case reqDataEnum.FORM:
                    this.reqDataType = 'form';
                    formList.push(item);
                    break;
                case reqDataEnum.PATH:
                    pathList.push(item);
                    break;
                case reqDataEnum.QUERY:
                    queryList.push(item);
                    break;
            }
        });
        this.initReqType(bodyList, formList, queryList, pathList, headerList);
        if (headerList.length > 0) {
            const list = formatKey1(headerList)[0];
            this.reqHeaderOneDimensionalM.setList(list);
        }
        if (bodyList.length > 0) {
            const [list, expandedKey] = formatKey1(bodyList);
            this.reqBodyMultiDimensionalM.setListAndExpanedKeys(list as any, expandedKey);
            this.reqBodyExample =
                list[0]?.example && JSONbigStringify(list[0]?.example) || '';
        }
        if (formList.length > 0) {
            const list = formatKey1(formList)[0];
            this.reqFormOneDimensionalM.setList(list);
        }
        if (queryList.length > 0) {
            const list = formatKey1(queryList)[0];
            this.reqQueryOneDimensionalM.setList(list);
        }
        if (pathList.length > 0) {
            const list = formatKey1(pathList)[0];
            this.reqPathOneDimensionalM.setList(list);
        }
    }

    // 处理返回参数
    @action.bound
    public initResponse(response) {
        if (response) {
            const headers = response?.headers || {};
            const body = response?.body || {};
            const example = body?.example || '';
            this.resBodyExample = JSONbigStringify(example) || '';
            this.formatResHeaderList(headers);
            this.formatResBody(body);
        }
    }

    // 处理返回header参数
    @action
    private formatResHeaderList(headerObj) {
        const headerList: Array<any> = [];
        const headerKeys = Object.keys(headerObj);
        headerKeys.length && headerKeys.map(item => {
            headerList.push({
                name: item,
                type: headerObj[item]?.type || '',
                description: headerObj[item]?.description || '',
                // example: headerObj[item]?.example || ''
            });
        });
        const list = formatKey1(headerList)[0];
        this.resHeaderOneDimensionalM.setList(list);
    }

    // 处理返回body参数
    @action
    private formatResBody(body): void {
        if (body.model && body.model.length) {
            body['children'] = body.model;
        }
        if (body['name'] || body['model']) {
            const [list, keys] = formatKey1([body]);
            const newKeys = keys.filter(item => item.split('-').length < 3).splice(0, 10);
            this.resBodyMultiDimensionalM.setListAndExpanedKeys(list, newKeys);
        }
    }

    @Bind
    private async queryApiDetail() {
        try {
            const params = {
                id: this.docId
            };
            const result = await nsMockManageKoasApiManageHttpApiQueryDetailGet.remote(params);
            result?.baseInfo && this.initReqResBody(result?.baseInfo?.moduleId);
            result?.request && result?.request?.parameters && this.initRequest(result.request.parameters);
            this.response = result?.response || null;
            result?.response && this.initResponse(result?.response);
        } catch (e) {
        }
    }

    // 获取测试用例生成参数类型
    @Bind
    private async queryCaseParamType() {
        try {
            const result = await nsMockManageKoasApiManageTestCaseQueryCaseParamTypeGet.remote({docId: this.docId});
            runInAction(() => {
                this.caseParamTypeList = result?.results;
            });
        } catch {
        }
    }
}
