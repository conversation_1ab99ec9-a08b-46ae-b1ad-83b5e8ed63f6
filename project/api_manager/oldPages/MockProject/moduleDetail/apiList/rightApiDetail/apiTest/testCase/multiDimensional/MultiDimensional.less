.multiDimensionalWrap {
  .table {
    .valueWrap {
      :global {

        .ant-input:last-child,
        .ant-input-group-addon:first-child {
          border-radius: 4px;
        }
      }
    }

    .inputGroupSelect {
      display: flex;

      :global {
        *:first-child {
          border-top-left-radius: 4px !important;
          border-bottom-left-radius: 4px !important;
        }

        *:last-child {
          border-top-right-radius: 4px;
          border-bottom-right-radius: 4px;
        }
      }
    }

    .width100 {
      width: 100%;
    }

    .type,
    .contentCheckType {
      width: 94px;
    }

    .deleteBtn {
      margin-right: 8px;
    }

    .addChildRowBtn {
      margin-right: 8px;
    }

    :global {
      .ant-table .ant-table-container .ant-table-tbody .ant-table-row .ant-table-cell:nth-child(1) {
        display: flex;
        align-items: center;
        height: 100%;
      }
    }
  }

  .addRowBtn {
    margin-top: 16px;
    margin-right: 16px;
  }
}