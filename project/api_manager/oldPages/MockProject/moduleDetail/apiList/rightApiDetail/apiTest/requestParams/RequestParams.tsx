import React from 'react';
import { observer } from 'mobx-react';
import { AView } from 'libs';
import { RequestParamsM } from './RequestParamsM';
import css from './RequestParams.less';
import { Bind } from 'lodash-decorators';
import { KdevTitle } from '@/business/commonComponents/commonTitle/CommonTitle';
import { ViewJsonTable } from '../viewJsonTable/ViewJsonTable';

@observer
export class RequestParams extends AView<RequestParamsM> {
    protected tableTitleKey: any[] = ['name', 'value', 'type'];

    @Bind
    private renderKdevTitleExtra(type: string): React.ReactNode {
        const model = this.model;
        if (type === 'JSON') {
            return (
                <span className={css.kdevTitleExtra}>
                    <span className={css.dataType}>{type}</span>
                    <span>
                        <a onClick={() => model.isExpandAllKeys(true)}>全部展开</a>/
                        <a onClick={() => model.isExpandAllKeys(false)}>全部收起</a>
                    </span>
                </span>
            );
        }
        if (type === 'FORM') {
            return <span className={css.dataType}>{type}</span>;
        }
    }

    @Bind
    private renderHeaderParams(): React.ReactNode {
        const model = this.model;
        if (model.headerList.length) {
            return (
                <>
                    <KdevTitle
                        text={'Header参数'}
                        size={'small'}
                        className={css.kdevTitle}
                    />
                    <ViewJsonTable
                        className={css.viewJsonTable}
                        model={model.headerViewJsonTableM}
                        valueTitle={'参数值'}
                        tableTitleKey={this.tableTitleKey}
                    />
                </>
            );
        }
    }

    @Bind
    private renderBodyParams(): React.ReactNode {
        const model = this.model;
        if (model.bodyList.length) {
            return (
                <>
                    <KdevTitle
                        text={'Body参数'}
                        size={'small'}
                        className={css.kdevTitle}
                        extra={this.renderKdevTitleExtra('JSON')}
                    />
                    <ViewJsonTable
                        className={css.viewJsonTable}
                        model={model.bodyViewJsonTableM}
                        valueTitle={'参数值'}
                        tableTitleKey={this.tableTitleKey}
                    />
                </>
            );
        }
    }

    @Bind
    private renderFormParams(): React.ReactNode {
        const model = this.model;
        if (model.formList.length) {
            return (
                <>
                    <KdevTitle
                        text={'Body参数'}
                        size={'small'}
                        className={css.kdevTitle}
                        extra={this.renderKdevTitleExtra('FORM')}
                    />
                    <ViewJsonTable
                        className={css.viewJsonTable}
                        model={model.formViewJsonTableM}
                        valueTitle={'参数值'}
                        tableTitleKey={this.tableTitleKey}
                    />
                </>
            );
        }
    }

    @Bind
    private renderQueryParams(): React.ReactNode {
        const model = this.model;
        if (model.queryList.length) {
            return (
                <>
                    <KdevTitle
                        text={'Query参数'}
                        size={'small'}
                        className={css.kdevTitle}
                    />
                    <ViewJsonTable
                        className={css.viewJsonTable}
                        model={model.queryViewJsonTableM}
                        valueTitle={'参数值'}
                        tableTitleKey={this.tableTitleKey}
                    />
                </>
            );
        }
    }

    @Bind
    private renderPathParams(): React.ReactNode {
        const model = this.model;
        if (model.pathList.length) {
            return (
                <>
                    <KdevTitle
                        text={'Path参数'}
                        size={'small'}
                        className={css.kdevTitle}
                    />
                    <ViewJsonTable
                        className={css.viewJsonTable}
                        model={model.pathViewJsonTableM}
                        valueTitle={'参数值'}
                        tableTitleKey={this.tableTitleKey}
                    />
                </>
            );
        }
    }

    public render(): React.ReactNode {
        const model = this.model;
        return (
            <div className={css.requestParamsWrap}>
                {this.renderHeaderParams()}
                {this.renderBodyParams()}
                {this.renderFormParams()}
                {this.renderQueryParams()}
                {this.renderPathParams()}
            </div>
        );
    }
}
