import { AView } from 'libs';
import React from 'react';
import { ApiTestM } from './ApiTestM';
import { observer } from 'mobx-react';
import { Button, Radio, Tooltip, Modal, Switch, Table, Space, Tag, Typography, Collapse } from 'antd';
import Bind from 'lodash-decorators/bind';
import css from './ApiTest.less';
import { TestCase } from '@/pages/moduleDetail/apiList/rightApiDetail/apiTest/testCase/TestCase';
import { TestConfiguration } from '@/pages/moduleDetail/apiList/rightApiDetail/apiTest/testConfiguration/TestConfiguration';
import { TestResult } from './testResult/TestResult';
import { testCaseStatus, reportResult, testCaseTypeOptions, testCaseType } from './configure';
import { CaseDetail } from './testReportCaseDetail/CaseDetail';
import { CaseDetailM } from './testReportCaseDetail/CaseDetailM';
import { CurlImportCase } from './CurlImportCase';

const { Title, Text } = Typography;
const { Panel } = Collapse;

@observer
export class ApiTest extends AView<ApiTestM> {

    @Bind
    protected titleRender(nodeData): React.ReactNode {
        const model = this.model;
        return <div className={css.nodeDataTitle}>
            <div className={css.nodeDataTitleName}>
                <Tooltip title={nodeData.name} placement={'topLeft'}>
                    <span>{nodeData.name}</span>
                </Tooltip>
            </div>
            <div className={css.nodeDataOperation}>
                <Tooltip title={nodeData.default ? '默认数据表示默认被使用的数据' : ''}>
                    <Switch
                        checked={nodeData.default}
                        checkedChildren={'默认'}
                        onChange={e => {
                            // model.setDefault(nodeData);
                        }}
                    />
                </Tooltip>
            </div>
        </div>;
    }

    @Bind
    protected onOpenDeleteConfirm(id) {
        Modal.confirm({
            title: '确认删除测试用例？',
            onOk: () => this.model.deleteTestCaseData(id)
        });
    }

    @Bind
    protected onOpenRejectConfirm() {
        Modal.confirm({
            title: '确认联调驳回？',
            onOk: () => this.model.rejectTestCase()
        });
    }

    @Bind
    private renderTestCasesOperate(): React.ReactNode {
        const model = this.model;
        if (model.radioValue === testCaseType.TEST_CASE) {
            return (
                <Space>
                    <Button onClick={() => model.onOpenTestCaseModal('new')} type="primary">新建用例</Button>
                    <CurlImportCase onSaveCallback={model.onSaveCallbackCurlImport} docId={model.apiId} />
                    <Button onClick={model.doAllOfTestCases} type="primary">批量测试</Button>
                    <Button onClick={model.onOpenTestConfigurationModal} type="primary">测试配置</Button>
                    <Button
                        disabled={model.status !== 10}
                        onClick={model.passSelfTest}
                        type="primary"
                    >
                        自测通过
                    </Button>
                </Space>
            );
        }
    }

    @Bind
    private renderOperations(): React.ReactNode {
        const model = this.model;
        return (
            <div className={css.apiTestOperations}>
                <Radio.Group
                    value={model.radioValue}
                    options={testCaseTypeOptions}
                    optionType="button"
                    onChange={model.onRadioChange}
                />
                {this.renderTestCasesOperate()}
            </div>
        );
    }

    @Bind
    private renderTestStatus(record): React.ReactNode {
        // 执行状态：  0-未测试  1-未通过 2-通过 3-忽略 4-失败
        const model = this.model;
        return (
            <Space size="small">
                <span style={{ color: testCaseStatus[record.status]?.color }}>
                    {record.statusDesc}
                </span>
                {
                    record.status !== 0 &&
                    <a href="#" onClick={() => model.onOpenTestResult(record, 'koasCaseId')}>查看结果</a>
                }
            </Space>
        );
    }

    @Bind
    private renderCaseOperate(record): React.ReactNode {
        const model = this.model;
        return (
            <Space>
                <Button
                    type="link"
                    size="small"
                    onClick={model.doTestCase.bind(this, record.id, record.name)}
                >测试</Button>
                <Button
                    type="link"
                    size="small"
                    onClick={() => model.onOpenTestCaseModal('edit', record)}
                    disabled={!record.allowUpdate}
                >
                    编辑
                </Button>
                <Button
                    type="link"
                    size="small"
                    onClick={() => model.getKoasCaseSingleResultForBug(record.id)}
                >
                    创建缺陷
                </Button>
                <Button
                    type="link"
                    size="small"
                    onClick={() => model.onOpenTestCaseModal('copy', record)}
                >复制</Button>
                <Button
                    type="link"
                    size="small"
                    onClick={this.onOpenDeleteConfirm.bind(this, record.id)}
                >删除</Button>
            </Space>
        );
    }

    @Bind
    private renderCaseName(record): React.ReactNode {
        const model = this.model;
        return (
            <Space size="small">
                <a onClick={() => model.onOpenTestResult(record, 'caseId')}>{record.name}</a>
                {record.status === 3 && <Tag color="#a0a0a0">忽略</Tag>}
            </Space>
        );
    }

    @Bind
    protected renderTable(): React.ReactNode {
        const model = this.model;

        const columns = [
            {
                title: '用例名称',
                // dataIndex: 'name',
                key: 'name',
                render: this.renderCaseName
            },
            {
                title: '维护人',
                dataIndex: 'maintainer',
                key: 'maintainer',
            },
            {
                title: '测试时间',
                dataIndex: 'lastUpdateTime',
                key: 'lastUpdateTime',
            },
            {
                title: '测试结果',
                // dataIndex: 'statusDesc',
                key: 'statusDesc',
                render: this.renderTestStatus
            },
            {
                title: '操作',
                // dataIndex: 'action',
                width: 186,
                key: 'action',
                render: this.renderCaseOperate
            },
        ];
        return <Table
            bordered
            dataSource={model.testCaseList}
            columns={columns}
            rowKey={'id'}
        />;
    }

    @Bind
    private renderTestReport(item): React.ReactNode {
        const caseDetailM = new CaseDetailM();
        const header = (
            <div className={css.caseDetailHeader} onClick={() => caseDetailM.init(item.id)}>
                <span>
                    {item.name}
                    <span className={css.caseStatus} style={{ color: testCaseStatus[item.status].color }}>
                        {testCaseStatus[item.status].statusDesc}
                    </span>
                </span>
                <span>查看详情</span>
            </div>
        );
        return (
            <Panel header={header} key={item.id} showArrow={false}>
                <CaseDetail model={caseDetailM} />
            </Panel>
        );
    }

    @Bind
    private renderTestCaseReuslt(): React.ReactNode {
        const { testCaseReport = {} } = this.model;
        return (
            <div className={css.apiTestResults}>
                {reportResult.map(item => {
                    return (
                        <div className={css.apiTestResult} key={item.value}>
                            <Title
                                level={4}
                                type={item.value === 'percent' ? 'danger' : undefined}
                            >
                                {testCaseReport[item.value]}
                            </Title>
                            {item.label}
                        </div>
                    );
                })}
            </div>
        );
    }

    @Bind
    protected renderReport(): React.ReactNode {
        const { testCaseReport = {}, status } = this.model;
        return <>
            <Space direction="vertical" className={css.apiTestReport}>
                <div className={css.apiTestResultBlock}>
                    <Text className={css.apiTestResultTitle}>测试结果</Text>
                    <Button
                        disabled={status === 10 ? true : false}
                        type="primary" onClick={this.onOpenRejectConfirm}
                    >联调驳回</Button>
                </div>
                {/* <div className={css.apiTestResults}> */}
                {this.renderTestCaseReuslt()}
                {/* </div> */}
                {testCaseReport.detail && testCaseReport.detail.length ? <>
                    <Text className={css.apiTestResultDT}>测试详情</Text>
                    <Collapse>
                        {testCaseReport.detail.map(item => this.renderTestReport(item))}
                    </Collapse>
                </> : <></>}
            </Space>
        </>;
    }

    public render(): React.ReactNode {
        const model = this.model;
        return (
            <div className={css.apiTestWrap}>
                {this.renderOperations()}
                {model.radioValue === 0 ? this.renderTable() : this.renderReport()}
                <TestCase model={model.testCaseM} />
                <TestConfiguration model={model.testConfigurationM} />
                <TestResult model={model.testResultM} />
            </div>
        );
    }
}
