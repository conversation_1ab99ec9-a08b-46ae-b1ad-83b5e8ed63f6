.oneDimensionalWrap {
  .table {
    .type {
      width: 94px;
    }

    .valueWrap {
      :global {

        .ant-input:last-child,
        .ant-input-group-addon:first-child {
          border-radius: 4px;
        }
      }
    }

    .width100 {
      width: 100%;
    }

    .inputGroupSelect {
      display: flex;

      :global {
        *:first-child {
          border-top-left-radius: 4px !important;
          border-bottom-left-radius: 4px !important;
        }

        *:last-child {
          border-top-right-radius: 4px;
          border-bottom-right-radius: 4px;
        }
      }
    }

    .deleteBtn {
      margin-right: 8px;
    }

    .uploadBox {
      display: flex;

      .fileBox {
        display: flex;
        justify-items: center;
        border: 1px solid #d9d9d9;
        border-right: 0;
        padding-left: 2px;
        border-radius: 4px 0 0 4px;
        background-color: #fafafa;
        width: calc(100% - 32px);

        .fileName {
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
          flex: 1;
          line-height: 30px;
        }

        .closeIcon {
          line-height: 36px;
          height: 30px;
          padding: 0 2px;
          cursor: pointer;
        }
      }

      :global {
        .ant-tag {
          margin-right: 0;
          line-height: 30px;
        }
      }
    }

    :global {
      .ant-table .ant-table-container .ant-table-tbody .ant-table-row .ant-table-cell:nth-child(1) {
        display: flex;
        align-items: center;
        height: 100%;
      }
    }
  }

  .addRowBtn {
    margin-top: 16px;
  }
}