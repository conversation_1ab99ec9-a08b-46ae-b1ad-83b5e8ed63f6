import { AViewModel } from 'libs';
import { action, observable } from 'mobx';
import { Bind } from 'lodash-decorators';
import {
    nsMockManageKoasApiManageTestCaseGetKoasCaseSingleResultGet
} from '@/remote';
import { RequestParamsM } from '../requestParams/RequestParamsM';
import { ResponseParamsM } from '../responseParams/ResponseParamsM';
import moment from 'moment';

export class CaseDetailM extends AViewModel {
    @observable public caseId: number = 0;
    @observable public caseExecuteTime: string = '';
    @observable public showRequest: boolean = true;
    @observable public showResponse: boolean = true;
    @observable public ruleType: number = 1;

    public requestParamsM = new RequestParamsM();
    public responseParamsM = new ResponseParamsM();

    @action.bound
    public init(caseId: number): void {
        if (this.caseId !== caseId) {
            this.caseId = caseId;
            this.getKoasCaseSingleResult();
        }
    }

    @action
    private formatDetail(detail): void {
        this.ruleType = detail?.ruleType || 1;
        this.caseExecuteTime = moment(detail.testTime).format('YYYY-MM-DD HH:mm:ss');
    }

    @action
    private initRequest(request): void {
        this.requestParamsM.setData(request);
        if (!(request?.parameters && request.parameters.length)) {
            this.showRequest = false;
        }
    }

    @action
    private initResponse(response, ruleType: number): void {
        this.responseParamsM.setData(response, ruleType);
        if (Object.keys(response?.body).length < 1 && Object.keys(response?.header).length < 1 ) {
            this.showResponse = false;
        }
    }

    @Bind
    private async getKoasCaseSingleResult() {
        try {
            const params = {
                koasCaseId: this.caseId
            };
            const result = await nsMockManageKoasApiManageTestCaseGetKoasCaseSingleResultGet.remote(params);
            result && this.formatDetail(result);
            result?.request && this.initRequest(result?.request);
            result?.response && this.initResponse(result?.response, result?.ruleType);
        } catch (e) {
        }
    }
}
