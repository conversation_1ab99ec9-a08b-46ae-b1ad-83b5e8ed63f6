import { AView } from 'libs';
import React from 'react';
import { OneDimensionalM } from './OneDimensionalM';
import { observer } from 'mobx-react';
import { Input, Button, Table, Select, Checkbox, Upload, Tooltip, DatePicker } from 'antd';
import css from './OneDimensional.less';
import { PlusCircleOutlined, UploadOutlined, CloseOutlined } from '@ant-design/icons';
import Bind from 'lodash-decorators/bind';
import { KdevIconFont } from '@/business/commonComponents';
import { IOneDimensionalProps } from '../configure';
import moment from 'moment';

const { Option } = Select;

@observer
export class OneDimensional extends AView<OneDimensionalM, IOneDimensionalProps> {

    @Bind
    public componentDidMount(): void {
        if (this.props.tableTitleKey) {
            this.model.getTableScrollX(this.props.tableTitleKey, this.column);
        }
    }

    @Bind
    public componentWillReceiveProps(nextProps): void {
        this.model = nextProps.model;
    }

    private column: any[] = [
        {
            title: '名称',
            key: 'name',
            width: 300,
            fixed: 'left',
            render: this.renderName
        },
        {
            title: '类型',
            key: 'type',
            width: 120,
            render: this.renderType
        },
        // {
        //     title: '是否必填',
        //     key: 'required',
        //     width: 100,
        //     render: this.renderRequired
        // },
        {
            title: this.props.valueTitle || '参数值',
            key: 'value',
            width: 120,
            render: this.renderValue
        },
        {
            title: '备注',
            key: 'description',
            width: 200,
            render: this.renderDescription
        },
        {
            title: '操作',
            key: 'operate',
            width: 80,
            fixed: 'right',
            render: this.renderOperate
        }
    ];

    @Bind
    public columns(): any[] {
        const props = this.props;
        if (props.tableTitleKey) {
            const column = this.column.filter(item => props.tableTitleKey?.includes(item.key));
            return column;
        }
        return this.column;
    }

    @Bind
    protected renderName(record): React.ReactNode {
        return (
            <Input
                value={record.name}
                disabled={this.props?.disabled?.name}
                onChange={(e) => {
                    this.model.onChangeInputVal(e, record, 'name');
                }}
            />
        );
    }

    @Bind
    protected renderType(record): React.ReactNode {
        return (
            <Select
                value={record.type}
                className={css.type}
                disabled={this.props?.disabled?.type}
                onSelect={(val) => this.model.onSelectVal(val, record, 'type')}
            >
                <Option value={'number'}>number</Option>
                <Option value={'string'}>string</Option>
                <Option value={'boolean'}>boolean</Option>
                {
                    this.model.type === 'form'
                    && <Option value={'file'}>file</Option>
                }
            </Select>
        );
    }

    @Bind
    protected renderRequired(record): React.ReactNode {
        return (
            <Checkbox
                checked={record.required}
                onChange={(e) => {
                    this.model.onChangeCheckboxChecked(e, record);
                }}
                disabled={this.props.disabled?.required}
            >是</Checkbox>
        );
    }

    @Bind
    private renderValue(record): React.ReactNode {
        const model = this.model;
        const dynamicDataValueOptions = this.props?.caseParamTypeList || [];
        const isShowDataConstructor = Boolean(dynamicDataValueOptions.length);
        if (record.type === 'file') {
            return (
                <div className={css.uploadBox}>
                    {
                        record.value &&
                        <div className={css.fileBox}>
                            <Tooltip title={record.value.split('|')[0]}>
                                <span className={css.fileName}>{record.value.split('|')[0]}</span>
                            </Tooltip>
                            <CloseOutlined
                                className={css.closeIcon}
                                onClick={(e) => {
                                    e.stopPropagation();
                                    model.removeFile(record);
                                }}
                            />
                        </div>
                    }
                    <Upload
                        beforeUpload={model.beforeUpload}
                        customRequest={(requestConfig) => model.uploadFile(requestConfig, record)}
                        showUploadList={false}
                    >
                        <Button icon={<UploadOutlined />} />
                    </Upload>
                </div>
            );
        }
        return (
            <Input.Group className={isShowDataConstructor ? css.inputGroupSelect : css.valueWrap} compact>
                {
                    isShowDataConstructor &&
                    <Select
                        options={dynamicDataValueOptions}
                        value={record.dynamicDataValue || 0}
                        onSelect={(val, option) => this.model.onSelectVal(option, record, 'dynamicDataValue')}
                        disabled={this.props.disabled?.value}
                        dropdownMatchSelectWidth={false}
                    />
                }
                {
                    record.dynamicDataType === 2
                        ? <DatePicker
                            value={moment(record.value)}
                            onChange={(date, dateString: string) => this.model.onChangeDate(dateString, record)}
                            allowClear={false}
                            className={css.width100}
                        />
                        : <Input
                            value={record.value}
                            disabled={this.props.disabled?.value || record.dynamicDataType === 1}
                            onChange={e => {
                                this.model.onChangeInputVal(e, record, 'value');
                            }}
                        />
                }
            </Input.Group>
        );
    }

    @Bind
    protected renderDescription(record): React.ReactNode {
        return (
            <Input
                value={record.description}
                disabled={this.props?.disabled?.description}
                onChange={(e) => {
                    this.model.onChangeInputVal(e, record, 'description');
                }}
            />
        );
    }

    @Bind
    protected renderOperate(record): React.ReactNode {
        return (
            <Button
                icon={<KdevIconFont id={'#iconyanse'} />}
                className={css.deleteBtn}
                disabled={this.props?.disabled?.operate}
                onClick={() => this.model.onDeleteRow(record.key)}
            />
        );
    }

    @Bind
    protected renderAddRowBtn(): React.ReactNode {
        const model = this.model;
        return (
            <Button
                icon={<PlusCircleOutlined />}
                type={'dashed'}
                className={css.addRowBtn}
                onClick={model.onAddRow}
                disabled={this.props?.disabled?.ruleType}
            >添加一行</Button>
        );
    }

    public render(): React.ReactNode {
        const model = this.model;
        return (
            <div
                className={css.oneDimensionalWrap}
            >
                <Table
                    className={css.table}
                    columns={this.columns()}
                    dataSource={model.list}
                    bordered
                    pagination={false}
                    rowKey={'key'}
                    defaultExpandAllRows
                />
                {this.renderAddRowBtn()}
            </div>
        );
    }
}
