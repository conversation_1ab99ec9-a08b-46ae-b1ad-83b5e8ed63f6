.viewJsonTable {
    .paramsResultSame {
        margin-left: 4px;
    }

    .name {
        padding: 8px 0;
    }

    .realValue {
        word-break: break-all;
    }

    .operateTitle{
        display: flex;
        align-items: center;
        justify-content: space-between;
    }

    .indentBorder {
        :global {
            .ant-table-row-indent {
                height: 100%;
                border-right: 1px solid #d9d9d9;
            }
        }
    }

    :global {
        .ant-table .ant-table-container .ant-table-tbody .ant-table-cell {
            word-break: break-all;
        }

        .ant-table .ant-table-container .ant-table-tbody .ant-table-row>td:nth-of-type(1) {
            padding: 0 12px !important;
        }

        .ant-table-row-indent + .ant-table-row-expand-icon{
            margin-top: 10px;
        }
    }
}