import { AViewModel } from 'libs';
import { action, observable, runInAction } from 'mobx';
import { formatKey1 } from '@/index.config/tools';
import {
    nsMockManageKoasApiManageTestCaseUpdateHeaderPost, nsMockManageKoasApiManageTestCaseGetHeaderGet
} from '@/remote';
import { settings } from './configure';
import { Bind } from 'lodash-decorators';

interface IHeader {
    address: string;
    docId: number;
    headerData: {
        headers: {
            [key: string]: string;
        },
        property: {
            [key: string]: string;
        }
    };
}

interface ISetting {
    [key: string]: {
        [key: string]: string;
    };
}

export class TestConfigurationM extends AViewModel {
    @observable public visible: boolean = false;
    @observable public docId: number = 0;
    @observable public type: string = 'new'; // new|edit|copy
    @observable public address: string = '';
    @observable public sid: string = '';
    @observable public radioValue: string = settings[0].type;
    @observable public setting: any = {
        1: [],
        100: []
    };
    @observable public property: any = {};
    @observable public headers: Array<{ [key: string]: string; }> = [];
    @observable public headerFromOutside: Partial<IHeader> = {};

    @action.bound
    private init(headerFromOutside: Partial<IHeader>): void {
        this.headerFromOutside = headerFromOutside;
        this.address = headerFromOutside.address || '';
        this.docId = headerFromOutside.docId || 0;
        this.radioValue = '0';
        headerFromOutside.headerData = headerFromOutside.headerData || {
            headers: {},
            property: {},
        };
        headerFromOutside.headerData.headers = headerFromOutside.headerData.headers || {};
        headerFromOutside.headerData.property = headerFromOutside.headerData.property || {};
        const headers = headerFromOutside.headerData.headers;
        const keysOfheaders = Object.keys(headers);
        if (keysOfheaders.length) {
            this.headers = [];
            for (const key of keysOfheaders) {
                this.headers.push({
                    key: key + headers[key],
                    name: key,
                    value: headers[key],
                });
            }
        } else {
            this.headers = [];
        }
        const property = headerFromOutside.headerData.property;
        this.property = property;
        this.initSettings(property);
    }

    @action.bound
    private initSettings(property) {
        if (property && property.type) {
            this.setting = {
                1: [],
                100: []
            };
            this.radioValue = property.type;
            for (const key of Object.keys(property)) {
                if (key !== 'sid' && key !== 'type') {
                    this.setting[property.type].push({
                        key: key,
                        name: key,
                        value: property[key],
                    });
                }
            }
            if (!this.setting[1].length) {
                this.setting[1].push({
                    key: '1',
                    name: 'phone',
                    value: '',
                });
            }
            if (!this.setting[100].length) {
                this.setting[100].push({
                    key: '100',
                    name: 'phone',
                    value: '',
                });
            }
        } else {
            this.property = {};
            this.setting = {
                1: [{
                    key: '1',
                    name: 'phone',
                    value: '',
                }],
                100: [{
                    key: '100',
                    name: 'phone',
                    value: '',
                }]
            };
        }
    }

    @action.bound
    public show(docId: number): void {
        this.docId = docId;
        this.visible = true;
        this.getHeader();
    }

    @action.bound
    public onRadioChange(e) {
        const value: string = e.target.value;
        this.radioValue = value;
    }

    // 关闭测试用例弹框
    @action.bound
    public async onOKTestConfigurationModal() {
        try {
            const params = {
                docId: this.docId,
                address: this.address,
                headerData: {
                    headers: this.headers.reduce((accumulator, currentItem) => {
                        if (currentItem.name === '') {
                            return accumulator;
                        }
                        accumulator[currentItem.name] = currentItem.value;
                        return accumulator;
                    }, {}),
                    property: {
                        sid: this.radioValue === '1' ? 'kuaishou.api' : '',
                        ...this.radioValue === '1' && this.setting[1].length && this.setting[1][0].name ?
                            { [this.setting[1][0].name]: this.setting[1][0].value, type: 1} :
                            null,
                        ...this.radioValue === '100' && this.setting[100].length && this.setting[100][0].name ?
                            { [this.setting[100][0].name]: this.setting[100][0].value, type: 100} :
                            null,
                        ...this.radioValue === '0' && { type: 0 }
                    }
                } as any
            };
            await nsMockManageKoasApiManageTestCaseUpdateHeaderPost.remote(params);
            runInAction(() => {
                this.property = params.headerData.property;
                this.headerFromOutside = params;
                this.visible = false;
            });
        } catch (e) {
        }
    }

    // 关闭测试配置弹框
    @action.bound
    public onCloseTestConfigurationModal(): void {
        this.visible = false;
        this.initSettings(this.property);
    }

    @action.bound
    protected redRawTable() {
        this.headers = [...this.headers];
    }

    // 输入Input值
    @action.bound
    public onSettingChangeInputVal(e, keyName: string) {
        this.setting[this.radioValue][0][keyName] = e.target.value;
        this.setting = Object.assign({}, this.setting);
    }

    // 输入Input值
    @action.bound
    public onChangeInputVal(e, record, keyName: string) {
        record[keyName] = e.target.value;
        this.redRawTable();
    }

    @action.bound
    public onChangeName(val: string, record): void {
        record['name'] = val;
        this.redRawTable();
    }

    // 添加一行
    @action.bound
    public onAddRow() {
        const obj = {
            key: `${this.headers.length}`,
            name: '',
            value: '',
        };
        this.headers.push(obj);
        this.redRawTable();
    }

    // 添加泳道
    @action.bound
    public onAddAP() {
        const obj = {
            key: `${this.headers.length}`,
            name: 'trace-context',
            value: '{"laneId": "请在这里填写泳道"}',
        };
        this.headers.push(obj);
        this.redRawTable();
        setTimeout(() => {
            const input: HTMLInputElement | null = document.querySelector(`input[value='{"laneId": "请在这里填写泳道"}']`);
            input!.focus();
            input!.setSelectionRange(12, 20);
        });
    }

    // 删除当前行
    @action.bound
    public onDeleteRow(key: number) {
        this.headers.splice(key, 1);
        this.headers = formatKey1(this.headers)[0];
        this.redRawTable();
    }

    // 获取测试配置
    @Bind
    private async getHeader(): Promise<void> {
        try {
            const params = {
                docId: this.docId,
            };
            const result = await nsMockManageKoasApiManageTestCaseGetHeaderGet.remote(params);
            this.init(result);
        } catch (e) {
        }
    }
}
