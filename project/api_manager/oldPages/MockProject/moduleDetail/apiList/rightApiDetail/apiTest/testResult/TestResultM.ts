import { AViewModel } from 'libs';
import { action, observable, runInAction } from 'mobx';
import { Bind } from 'lodash-decorators';
import {
    nsMockManageKoasApiManageTestCaseDetailGet, nsMockManageKoasApiManageTestCaseCaseStateChangePost,
    nsMockManageKoasApiManageTestCaseDoTestCaseGet, nsMockManageKoasApiManageTestCaseGetKoasCaseSingleResultGet
} from '@/remote';
import { RequestParamsM } from '../requestParams/RequestParamsM';
import { ResponseParamsM } from '../responseParams/ResponseParamsM';
import { message } from 'antd';
import { JSONbigStringify } from '@/index.config/tools';

export class TestResultM extends AViewModel {
    // tslint:disable-next-line: no-empty
    protected getAllTestCaseList: () => void = () => {};
    @observable public visible: boolean = false;
    @observable public caseId: number = 0;
    @observable public caseName: string = '';
    @observable public caseDesc: string = '';
    @observable public status: number = 0;
    @observable public remark: string = '';
    @observable public caseExecuteTime: string = '';
    @observable public showRequest: boolean = true;
    @observable public showResponse: boolean = true;
    @observable public doTestCaseLoading: boolean = false;
    @observable public reqBodyExample: string = '';
    @observable public resBodyExample: string = '';
    @observable public resDataType: number = 1;
    @observable public resPythonScript: string = '';
    @observable public ruleType: number = 1;
    @observable public curl: string = '';

    @observable public type: 'caseId' | 'koasCaseId' = 'caseId';

    public requestParamsM = new RequestParamsM();
    public responseParamsM = new ResponseParamsM();

    @action.bound
    public init(caseId: number, getAllTestCaseList: () => void, type: 'caseId' | 'koasCaseId'): void {
        this.visible = true;
        this.caseId = caseId;
        this.type = type;
        this.detail();
        this.getAllTestCaseList = getAllTestCaseList;
    }

    // 关闭测试用例弹框
    @action.bound
    public onCloseTestConfigurationModal(): void {
        this.visible = false;
        this.initData();
    }

    // 初始化参数
    @action
    public initData(): void {
        this.caseName = '';
        this.caseDesc = '';
        this.status = 0;
        this.reqBodyExample = '';
        this.resBodyExample = '';
        this.ruleType = 1;
        this.resDataType = 1;
        this.resPythonScript = '';
        this.curl = '';
        this.requestParamsM.initData();
        this.responseParamsM.initData();
    }

    @action
    private formatDetail(detail): void {
        this.caseName = detail.name;
        this.caseDesc = detail.description;
        this.status = detail.status;
        this.remark = detail.remark;
        this.caseExecuteTime = detail.caseExecuteTime;
        this.ruleType = detail?.ruleType || 1;
        this.curl = detail?.curl || '';
        if (detail.responseType) {
            this.resDataType = detail.responseType;
            this.resPythonScript = detail?.dynamicResponsePython?.script || '';
        }
    }

    @action
    private initRequest(request): void {
        this.requestParamsM.setData(request);
        if (!(request?.parameters && request.parameters.length)) {
            this.showRequest = false;
        } else {
            this.getReqBodyExample(request?.parameters);
        }
    }

    @action.bound
    private getReqBodyExample(parameters): void {
        if (parameters.length) {
            parameters.forEach(item => {
                if (item.in === 'body') {
                    this.reqBodyExample = JSONbigStringify(item.example) || '';
                }
            });
        }
    }

    @action
    private initResponse(response, ruleType: number): void {
        this.responseParamsM.setData(response, ruleType);
        const headersKeysLength: number = response?.headers && Object.keys(response?.headers)?.length || 0;
        const bodyKeysLength: number = Object.keys(response?.body)?.length || 0;
        if (bodyKeysLength < 1 && headersKeysLength < 1 ) {
            this.showResponse = false;
        } else {
            this.resBodyExample = JSONbigStringify(response.body?.example) || '';
        }
    }

    @Bind
    private async detail() {
        try {
            let result: any = {
                request: null,
                response: null
            };
            if (this.type === 'caseId') {
                const params = {
                    caseId: this.caseId
                };
                result = await nsMockManageKoasApiManageTestCaseDetailGet.remote(params);
            }
            if (this.type === 'koasCaseId') {
                const params = {
                    koasCaseId: this.caseId
                };
                result = await nsMockManageKoasApiManageTestCaseGetKoasCaseSingleResultGet.remote(params);
            }
            result && this.formatDetail(result);
            result?.request && this.initRequest(result?.request);
            result?.response && this.initResponse(result?.response, result?.ruleType);
        } catch (e) {
        }
    }

    @Bind
    public async ignoreCase() {
        try {
            const params = {
                caseId: this.caseId,
                state: 3
            };
            await nsMockManageKoasApiManageTestCaseCaseStateChangePost.remote(params);
            message.success('测试用例已忽略～');
            this.detail();
            this.getAllTestCaseList();
        } catch {
        }
    }

    // 执行测试数据
    @Bind
    public async doTestCase() {
        try {
            runInAction(() => {
                this.doTestCaseLoading = true;
            });
            const parmas = {
                id: this.caseId,
             };
            const result = await nsMockManageKoasApiManageTestCaseDoTestCaseGet.remote(parmas);
            runInAction(() => {
                this.doTestCaseLoading = false;
                this.detail();
                if (result.caseStatus === 1 || result.caseStatus === 2) {
                    message.success(result?.caseStatusDesc);
                } else {
                    message.error(result?.caseStatusDesc);
                }
            });
        } catch (e) {
            runInAction(() => {
                this.doTestCaseLoading = false;
            });
        }
    }
}
