export enum Tips {
    reqTips = '请求参数',
    resTips = '返回参数',
    result = '忽略可能会影响联调，请谨慎选择'
}

export const testCaseStatus = {
    0: {
        color: '',
        statusDesc: '未测试'
    },
    1: {
        color: '#ff4d4f',
        statusDesc: '未通过'
    },
    2: {
        color: '#31bf30',
        statusDesc: '通过'
    },
    3: {
        color: '#ff4d4f',
        statusDesc: '未通过（已忽略）'
    },
    4: {
        color: '#ff4d4f',
        statusDesc: '失败'
    }
};
