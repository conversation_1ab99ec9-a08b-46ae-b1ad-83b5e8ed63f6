import { AViewModel } from 'libs';
import { observable, action } from 'mobx';
import { formatKey1 } from '@/index.config/tools';
import { message } from 'antd';
import { DataStructureModalModel } from '@/business/editJsonTable/dataStructureModal/DataStructureModalModel';
import moment from 'moment';

interface IList {
    key: string;
    name: string;
    type: string;
    required: boolean;
    description: string;
    children?: IList[];
}

const dynamicDataValueEnum = {
    1: '$timestamp',
    2: '$random'
};

export class MultiDimensionalM extends AViewModel {
    @observable public rootName: string = ''; // 'RequestRoot'｜'ResponseRoot'｜''
    @observable public list: IList[] = [];
    @observable public expandedRowKeys: string[] = [];
    @observable public checkName: string = '';
    @observable public moduleId: number = 0;
    @observable public tableScrollX: number = 1300;

    public dataStructureModalModel = new DataStructureModalModel();

    @action.bound
    public init(rootName: string, moduleId: number) {
        this.rootName = rootName;
        this.moduleId = moduleId;
    }

    @action.bound
    public setListAndExpanedKeys(list?: IList[], expandedRowKeys?: string[]): void {
        this.list = list || this.list;
        this.expandedRowKeys = expandedRowKeys || this.expandedRowKeys;
    }

    // 初始化数据
    @action.bound
    public initData() {
        this.list = [];
        this.expandedRowKeys = [];
        this.checkName = '';
        this.tableScrollX = 1300;
    }

    @action.bound
    public getTableScrollX(tableTitleKey: string[], column: any[]): void {
        this.tableScrollX = 0;
        column.forEach(item => {
            if (tableTitleKey.includes(item.key)) {
                this.tableScrollX += item.width;
            }
        });
    }

    // 禁用名称
    @action.bound
    public disabledName(record) {
        const list = record?.key?.split('-');
        const key = record?.key || '';
        if (list.length > 1 && key) {
            const index = key.lastIndexOf('-');
            const parentKey = key.slice(0, index);
            const heap: any = [
                {
                    children: this.list
                }
            ];
            while (heap.length > 0) {
                const temp = heap.shift();
                if (parentKey === temp['key'] && temp.type === 'array') {
                    return true;
                } else if (temp.children && temp.children.length) {
                    temp.children.forEach(item => {
                        heap.push(item);
                    });
                }
            }
        }
    }

    // 添加一行
    @action.bound
    public onAddRow(record) {
        if (record) {
            const len = record?.children?.length || 0;
            const obj = {
                key: `${record.key}-${len}`,
                name: record.type === 'array' ? `${record.name}[${len}]` : '',
                type: 'object',
                required: true,
                value: '',
                description: '',
                contentCheckType: 0,
                dynamicDataValue: 0,
            };
            if (record.children && record.children.length) {
                record.children.push(obj);
            } else {
                record.children = [obj];
            }
            this.onExpandRowKeys(true, record);
        } else {
            const obj = {
                key: '0',
                name: this.rootName,
                type: 'object',
                required: true,
                value: '',
                description: '',
                contentCheckType: 0,
                dynamicDataValue: 0,
            };
            this.list.push(obj);
        }
        this.redRawTable();
    }

    // 删除当前行
    @action.bound
    public onDeleteRow(record) {
        const key: string = record.key;
        this.list = formatKey1([...this.deleteRow(this.list, key)])[0];
    }

    @action.bound
    protected deleteRow(list, key: string, parentItme: any = null) {
        const index = list.findIndex(item => item.key === key);
        if (index > -1) {
            list.splice(index, 1);
            if (parentItme && parentItme?.type === 'array') {
                list.map((item, idx) => item.name = `${parentItme.name}[${idx}]`);
            }
            return list;
        } else {
            list.map(item => {
                if (item.children && item.children.length) {
                    this.deleteRow(item.children, key, item);
                }
            });
            return list;
        }
    }

    @action.bound
    protected redRawTable() {
        this.list = [...this.list];
    }

    @action.bound
    public onChangeInputNumVal(val, record, keyName: string): void {
        record[keyName] = val;
        this.redRawTable();
    }

    // 输入Input值
    @action.bound
    public onChangeInputVal(e, record, keyName: string) {
        record[keyName] = e.target.value;
        if (record.type === 'array' && keyName === 'name') {
            this.changeArrItemName(record, e.target.value);
        }
        this.redRawTable();
    }

    @action.bound
    public onChangeDate(dateString: string, record) {
        record['value'] = dateString;
        this.redRawTable();
    }

    // 数组类型name设置
    @action.bound
    protected changeArrItemName(record, parentName) {
        if (record.type === 'array' && record.children && record.children.length) {
            record.children.map((item, index) => {
                item.name = `${parentName}[${index}]`;
                this.changeArrItemName(item, item.name);
            });
        }
    }

    // 选择类型
    @action.bound
    public onSelectVal(val, record, keyName: string) {
        if (keyName === 'dynamicDataValue') {
            record[keyName] = val.value;
            record['dynamicDataType'] = val.type;
            record['dynamicExtraInput'] = val.input;
            if (val.type === 2) {
                record['value'] = moment(new Date()).format('YYYY-MM-DD');
            } else {
                record['value'] = val.symbol || record['value'];
            }
        } else {
            record[keyName] = val;
        }
        if (keyName === 'type') {
            delete record.children;
            if (val === 'array') {
                this.onAddRow(record);
            }
            if (val === 'array' || val === 'object') {
                record.value = '';
                record.contentCheckType = 0;
                record.dynamicDataValue = 0;
            }
        }
        this.redRawTable();
    }

    // 是否必选
    @action.bound
    public onChangeCheckboxChecked(e, record) {
        record.required = e.target.checked;
        this.redRawTable();
    }

    // 控制展开行
    @action.bound
    public onExpandRowKeys(expanded: boolean, record) {
        if (expanded) {
            if (!this.expandedRowKeys.includes(record.key)) {
                this.expandedRowKeys.push(record.key);
                this.expandedRowKeys = [...this.expandedRowKeys];
            }
        } else {
            this.expandedRowKeys = this.expandedRowKeys.filter(item => item !== record.key);
        }
    }

    // 打开引用数据弹框
    @action.bound
    public onOpenReferenceDataModal(record) {
        this.dataStructureModalModel.initLoading(this.moduleId);
        this.dataStructureModalModel.onSaveSelectDataStructureCallback =
            (list) => this.saveSelectDataStructureCallback(list, record);
    }

    @action.bound
    public saveSelectDataStructureCallback(list, record) {
        const newList: IList[] = this.spliceRow(this.list, record, list);
        const [bodyList, keys] = formatKey1(newList);
        this.list = bodyList;
        this.expandedRowKeys = keys;
        this.redRawTable();
    }

    // 替换数据
    @action.bound
    public spliceRow(list, record, spliceList): IList[] {
        const index = list.findIndex(item => item.key === record.key);
        if (index > -1) {
            list.splice(index, 1, ...spliceList);
        } else {
            list.map(item => {
                if (item.children && item.children.length) {
                    this.spliceRow(item.children, record, spliceList);
                }
            });
        }
        return list;
    }

    // 校验必填参数
    @action.bound
    public checkParams() {
        const num = this.list.length === 1 ? this.checkList(this.list[0]) : 0;
        if (num === 1) {
            message.warn('参数名称不能为空');
            return true;
        }
        if (num === 2) {
            message.warn('同级参数有重复名称' + this.checkName);
            return true;
        }
        if (num === 3) {
            message.warn(this.checkName + '参数取值范围必须数组格式，如：[1,2,3]');
            return true;
        }
        return false;
    }

    // 校验参数值
    @action.bound
    public checkList(obj) {
        const heap = [obj];
        while (heap.length > 0) {
            const temp = heap.shift();
            if (temp.name === '') {
                return 1;
            } else if (temp.children && temp.children.length) {
                const boo = temp.children.some(item => {
                    if (item.name) {
                        const arr = temp.children.filter(it => it.name === item.name);
                        if (arr.length > 1) {
                            this.checkName = item.name;
                            return true;
                        }
                    }
                });
                if (boo) {
                    return 2;
                } else {
                    temp.children.forEach(item => {
                        heap.push(item);
                    });
                }
            } else if (temp.valueScope) {
                if (!temp.valueScope.match(/\[.*?\]/g)) {
                    this.checkName = temp.name;
                    return 3;
                }
            }
        }
    }

    @action.bound
    public getList() {
        return this.list;
    }

    @action.bound
    public onExpandAllKeys(boo?: boolean): void {
        if (boo) {
            this.expandedRowKeys = formatKey1(this.list)[1];
        } else {
            this.expandedRowKeys = [];
        }
    }

}
