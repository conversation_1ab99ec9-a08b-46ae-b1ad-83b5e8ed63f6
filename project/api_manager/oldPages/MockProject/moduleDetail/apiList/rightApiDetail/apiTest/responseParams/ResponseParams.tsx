import React from 'react';
import { observer } from 'mobx-react';
import { AView } from 'libs';
import { ResponseParamsM } from './ResponseParamsM';
import css from './ResponseParams.less';
import { Bind } from 'lodash-decorators';
import { KdevTitle } from '@/business/commonComponents/commonTitle/CommonTitle';
import { ViewJsonTable } from '../viewJsonTable/ViewJsonTable';

@observer
export class ResponseParams extends AView<ResponseParamsM> {

    @Bind
    private renderHeaderParams(): React.ReactNode {
        const model = this.model;
        if (model.headerList.length) {
            return (
                <>
                    <KdevTitle
                        text={ 'Header参数' }
                        size={ 'small' }
                        className={ css.kdevTitle }
                    />
                    <ViewJsonTable
                        className={ css.viewJsonTable }
                        model={ model.headerViewJsonTableM }
                        valueTitle={'预期结果'}
                        tableTitleKey={this.model.getTableTitleKey}
                    />
                </>
            );
        }
    }

    @Bind
    private renderBodyParams(): React.ReactNode {
        const model = this.model;
        if (model.bodyList.length) {
            return (
                <>
                    <KdevTitle
                        text={ 'Body参数' }
                        size={ 'small' }
                        className={ css.kdevTitle }
                    />
                    <ViewJsonTable
                        className={ css.viewJsonTable }
                        model={ model.bodyViewJsonTableM }
                        valueTitle={'预期结果'}
                        tableTitleKey={this.model.getTableTitleKey}
                    />
                </>
            );
        }
    }

    public render(): React.ReactNode {
        const model = this.model;
        return (
            <div className={ css.responseParamsWrap }>
                { this.renderHeaderParams() }
                { this.renderBodyParams() }
            </div>
        );
    }
}
