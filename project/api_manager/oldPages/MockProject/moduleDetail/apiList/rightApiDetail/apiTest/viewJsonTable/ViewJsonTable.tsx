import { AView } from 'libs';
import React from 'react';
import { observer } from 'mobx-react';
import { ViewJsonTableM } from './ViewJsonTableM';
import { Table, Tag } from 'antd';
import Bind from 'lodash-decorators/bind';
import { KdevIconFont } from '@/business/commonComponents/KdevIconFont';
import css from './ViewJsonTable.less';

interface IProps {
    className?: string;
    valueTitle?: string;
    checkColumnKey?: string;
    tableTitleKey?: string[];
    enableResize?: boolean;
}

const contentCheckTypes = {
    0: '等于',
    1: '大于',
    2: '小于',
    3: '不等于'
};

const dynamicDataValues = {
    1: '时间戳',
    2: '随机数',
    3: 'MD5'
};

@observer
export class ViewJsonTable extends AView<ViewJsonTableM, IProps> {

    @Bind
    public componentDidMount(): void {
        this.model.onChangeTableLoading(true);
    }

    @Bind
    private renderName(record): React.ReactNode {
        return (
            <div className={css.name}>
                {record.name}
            </div>
        );
    }

    @Bind
    private renderContentCheckType(record): React.ReactNode {
        return (
            <div>
                {contentCheckTypes[record.contentCheckType] || '等于'}
            </div>
        );
    }

    @Bind
    private renderValue(record): React.ReactNode {
        if (this.model.type === 'form' && record.type === 'file' && record.value) {
            return (
                <a href={`${record.value.split('|')[1]}`}>{record.value.split('|')[0]}</a>
            );
        }
        if (record.type === 'boolean') {
            return (
                <span>
                    {Boolean(record.dynamicDataValue) && <Tag>{dynamicDataValues[record.dynamicDataValue]}</Tag>}
                    {`${record.value}`}
                </span>
            );
        }
        return record.value;
    }

    @Bind
    private renderRealValue(record): React.ReactNode {
        const sameType: string = typeof record.same;
        return (
            <span className={css.realValue}>
                {record.realValue}
                {
                    sameType === 'boolean' &&
                    <KdevIconFont
                        className={css.paramsResultSame}
                        id={record.same ? '#iconchenggong' : '#iconshibai'}
                        style={{ color: record.same ? '#31bf30' : '#ff4d4f' }}
                    />
                }
            </span>
        );
    }

    @Bind
    public columns(): any[] {
        const props = this.props;
        let column: any[] = [
            {
                title: '名称',
                // dataIndex: 'name',
                key: 'name',
                width: 300,
                render: this.renderName
            },
            {
                title: '类型',
                dataIndex: 'type',
                key: 'type',
                width: 100,
            },
            // {
            //     title: '是否必填',
            //     dataIndex: 'required',
            //     key: 'required',
            //     width: 100,
            //     render: text => text ? '是' : '否'
            // },
            {
                title: '内容校验',
                key: 'contentCheckType',
                width: 100,
                render: this.renderContentCheckType
            },
            {
                title: props.valueTitle || '参数值',
                // dataIndex: 'value',
                key: 'value',
                width: 100,
                disabled: true,
                render: this.renderValue
            },
            {
                title: '实际结果',
                key: 'realValue',
                width: 100,
                render: this.renderRealValue
            },
            {
                title: '备注',
                dataIndex: 'description',
                width: 100,
                key: 'description',
            },
            {
                title: '原因',
                dataIndex: 'desc',
                width: 100,
                key: 'desc',
            }
        ];
        if (props.tableTitleKey) {
            column = column.filter(item => props.tableTitleKey?.includes(item.key));
        }
        return column;
    }

    // 添加行className
    @Bind
    private rowClassName(record): string {
        if (record.children) {
            return '';
        }
        return css.indentBorder;
    }

    public render(): React.ReactNode {
        const model = this.model;
        return (
            <div>
                {
                    model.tableLoading &&
                    <Table
                        columns={this.columns()}
                        dataSource={model.list}
                        bordered
                        pagination={false}
                        rowKey={'key'}
                        defaultExpandAllRows
                        rowClassName={this.rowClassName}
                        expandable={{
                            expandedRowKeys: model.expandedRowKeys,
                            onExpand: model.onExpandRowKeys
                        }}
                        className={`${this.props.className} ${css.viewJsonTable}`}
                    />
                }
            </div>
        );
    }
}
