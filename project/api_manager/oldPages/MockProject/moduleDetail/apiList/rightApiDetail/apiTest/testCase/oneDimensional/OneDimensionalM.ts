import { AViewModel } from 'libs';
import { observable, action, runInAction } from 'mobx';
import { formatKey1 } from '@/index.config/tools';
import { message } from 'antd';
import { nsMockManageKoasApiManageHttpApiUploadFormFilePost, nsMockManageKoasApiManageHttpApiDeleteFormFilePost } from '@/remote';
import { Bind } from 'lodash-decorators';
import moment from 'moment';

interface IList {
    key: string;
    name: string;
    type: string;
    required: boolean;
    description: string;
}

export class OneDimensionalM extends AViewModel {
    @observable public rootName: string = ''; // RequestRoot｜ResponseRoot
    @observable public list: IList[] = [];
    @observable public expandedRowKeys: string[] = [];
    @observable public checkName: string = '';
    @observable public tableScrollX: number = 1300;
    @observable public type: string = '';

    constructor(query: string = '') {
        super();
        this.init(query);
    }

    @action
    public init(type: string) {
        this.type = type;
    }

    // 初始化数据
    @action.bound
    public initData() {
        this.rootName = '';
        this.list = [];
        this.expandedRowKeys = [];
        this.checkName = '';
    }

    @action.bound
    public getTableScrollX(tableTitleKey: string[], column: any[]): void {
        this.tableScrollX = 0;
        column.forEach(item => {
            if (tableTitleKey.includes(item.key)) {
                this.tableScrollX += item.width;
            }
        });
    }

    // 上传文件限制
    @action.bound
    public beforeUpload(file) {
        if (file.size > 1024 * 1000 * 100) {
            message.warn('您选择的上传的文件过大，请选择100M以内文件！');
            return false;
        }
        return true;
    }

    @action.bound
    public setList(list): void {
        this.list = list;
    }

    // 添加一行
    @action.bound
    public onAddRow() {
        const obj = {
            key: `${this.list.length}`,
            name: this.rootName,
            type: 'string',
            required: true,
            value: '',
            description: '',
            dynamicDataValue: 0
        };
        this.list.push(obj);
        this.redRawTable();
    }

    // 删除当前行
    @action.bound
    public onDeleteRow(key: string) {
        this.list.splice(Number(key), 1);
        this.list = formatKey1(this.list)[0];
        this.redRawTable();
    }

    @action.bound
    protected redRawTable() {
        this.list = [...this.list];
    }

    // 输入Input值
    @action.bound
    public onChangeInputVal(e, record, keyName: string) {
        record[keyName] = e.target.value;
        this.redRawTable();
    }

    @action.bound
    public onChangeDate(dateString: string, record) {
        record['value'] = dateString;
        this.redRawTable();
    }

    @action.bound
    public onChangeInputNumVal(val, record, keyName: string): void {
        record[keyName] = val;
        this.redRawTable();
    }

    // 选择类型
    @action.bound
    public onSelectVal(val, record, keyName: string) {
        if (keyName === 'dynamicDataValue') {
            record[keyName] = val.value;
            record['dynamicDataType'] = val.type;
            record['dynamicExtraInput'] = val.input;
            if (val.type === 2) {
                record['value'] = moment(new Date()).format('YYYY-MM-DD');
            } else {
                record['value'] = val.symbol || record['value'];
            }
        } else {
            record[keyName] = val;
        }
        this.redRawTable();
    }

    // 是否必选
    @action.bound
    public onChangeCheckboxChecked(e, record) {
        record.required = e.target.checked;
        this.redRawTable();
    }

    // 校验必填参数
    @action.bound
    public checkParams() {
        const obj = { children: this.list };
        const num = this.checkList(obj);
        if (num === 1) {
            message.warn('参数名称不能为空');
            return true;
        }
        if (num === 2) {
            message.warn('同级参数有重复名称' + this.checkName);
            return true;
        }
        if (num === 3) {
            message.warn(this.checkName + '参数取值范围必须数组格式，如：[1,2,3]');
            return true;
        }
        return false;
    }

    // 校验参数值
    @action.bound
    public checkList(obj) {
        const heap = [obj];
        while (heap.length > 0) {
            const temp = heap.shift();
            if (temp.name === '') {
                return 1;
            } else if (temp.children && temp.children.length) {
                const boo = temp.children.some(item => {
                    if (item.name) {
                        const arr = temp.children.filter(it => it.name === item.name);
                        if (arr.length > 1) {
                            this.checkName = item.name;
                            return true;
                        }
                    }
                });
                if (boo) {
                    return 2;
                } else {
                    temp.children.forEach(item => {
                        heap.push(item);
                    });
                }
            } else if (temp.valueScope) {
                if (!temp.valueScope.match(/\[.*?\]/g)) {
                    this.checkName = temp.name;
                    return 3;
                }
            }
        }
    }

    @action.bound
    public getList(): IList[] {
        return this.list;
    }

    @Bind
    public async uploadFile(requestConfig, record): Promise<void> {
        try {
            const params = new FormData();
            params.append('file', requestConfig.file);
            const result = await nsMockManageKoasApiManageHttpApiUploadFormFilePost.remote(params);
            record.value = `${requestConfig.file.name}|${result?.downUrl}`;
            this.redRawTable();
        } catch {
        }
    }

    @Bind
    public async removeFile(record): Promise<void> {
        try {
            const params = {
                downUrl: record.value.split('|')[1]
            };
            await nsMockManageKoasApiManageHttpApiDeleteFormFilePost.remote(params);
            record.value = '';
            this.redRawTable();
        } catch {
        }
    }

}
