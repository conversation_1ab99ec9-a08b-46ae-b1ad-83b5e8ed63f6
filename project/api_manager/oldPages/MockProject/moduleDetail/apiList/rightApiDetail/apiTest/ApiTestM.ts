import { AViewModel } from 'libs';
import { observable, action, runInAction, } from 'mobx';
import {
    nsMockManageKoasApiManageTestCaseGetTestCaseListGet,
    nsMockManageKoasApiManageTestCaseGetTestCaseReportGet,
    nsMockManageKoasApiManageTestCaseDoTestCaseGet,
    nsMockManageKoasApiManageTestCaseDoAllOfTestCasesGet,
    nsMockManageKoasApiManageTestCaseDeleteGet,
    nsMockManageKoasApiManageHttpApiApiStateChangePost,
    nsMockManageKoasApiManageTestCaseGetKoasCaseSingleResultForBugGet
} from '@/remote';
import { Modal, message } from 'antd';
import { TestCaseM } from '@/pages/moduleDetail/apiList/rightApiDetail/apiTest/testCase/TestCaseM';
import { TestConfigurationM } from '@/pages/moduleDetail/apiList/rightApiDetail/apiTest/testConfiguration/TestConfigurationM';
import { TestResultM } from './testResult/TestResultM';
import { Bind } from 'lodash-decorators';
import * as QS from 'qs';

interface ITestCaseList {
    id: number;
    docId: number;
    name: string;
    desc: string;
    lastUpdateTime: string;
    status: number;
    statusDesc: string;
    maintainer: string;
}

interface ITestCaseReport {
    docId: number;
    totalQuantity: number;
    percent: string;
    executeQuantity: number;
    passQuantity: number;
    unPassQuantity: number;
    detail: Array<{
        id: number;
        name: string;
        status: number;
        statusDesc: string;
        caseExecuteTime: string;
    }>;
}

export class ApiTestM extends AViewModel {
    private isLoading = false;
    @observable public apiId: number = 0;
    @observable public radioValue: 0 | 1 = 0;
    @observable public status = 0;
    @observable public testCaseList: ITestCaseList[] = [];
    @observable public testCaseReport: Partial<ITestCaseReport> = {};
    @observable public selectedKeys: number[] = [];
    public onSaveCallback?(status: number, statusDesc: string): void;

    public testCaseM = new TestCaseM();
    public testConfigurationM = new TestConfigurationM();
    public testResultM = new TestResultM();

    @action.bound
    public initLoading(apiId: number, status: number, onSaveCallback) {
        this.changeStatus(status);
        this.apiId = apiId;
        this.getAllTestCaseList();
        if (this.radioValue === 1) {
            this.getTestCaseReport();
        }
        this.onSaveCallback = onSaveCallback;
    }

    @action.bound
    public onSaveCallbackCurlImport(result) {
        this.onOpenTestCaseModal('new', { id: 0 }, result?.baseInfo?.fullUrl || '');
        result?.request?.parameters && this.testCaseM.initRequest(result?.request?.parameters);
        result?.response && this.testCaseM.initResponse(result?.response || {});
    }

    @action.bound
    public onOpenTestCaseModal(type: string, record?, fullUrl: string = ''): void {
        this.testCaseM.init(type, this.apiId, record?.id || 0, fullUrl);
        this.testCaseM.onSaveCallback = this.getAllTestCaseList;
    }

    @action.bound
    public onOpenTestConfigurationModal(): void {
        this.testConfigurationM.show(this.apiId);
    }

    @action.bound
    public onOpenTestResult(record, type: 'caseId' | 'koasCaseId'): void {
        this.testResultM.init(record.id, this.getAllTestCaseList, type);
    }

    // API状态改变
    @action.bound
    public changeStatus(status: number): void {
        this.status = status;
    }

    // 测试用例、测试报告切换
    @action.bound
    public onRadioChange(e) {
        this.radioValue = e.target.value;
        if (this.radioValue === 1) {
            this.getTestCaseReport();
        } else {
            this.getAllTestCaseList();
        }
    }

    // 获取测试用例列表
    @action.bound
    public async getAllTestCaseList() {
        try {
            const params = {
                docId: this.apiId,
            };
            const result = await nsMockManageKoasApiManageTestCaseGetTestCaseListGet.remote(params);
            runInAction(() => {
                this.testCaseList = [];
                result?.rowList.forEach((item, index) => {
                    const obj = {
                        ...item,
                        key: item.id,
                    };
                    this.testCaseList.push(obj);
                });
            });
        } catch (e) {
        }
    }

    // 获取测试用例列表
    @action.bound
    public async getTestCaseReport() {
        try {
            this.testCaseReport = {};
            const params = {
                docId: this.apiId,
            };
            const result = await nsMockManageKoasApiManageTestCaseGetTestCaseReportGet.remote(params);
            runInAction(() => {
                if (result) {
                    this.testCaseReport = result;
                }
            });
        } catch (e) {
        }
    }

    // 删除test case数据
    @action.bound
    public async deleteTestCaseData(id) {
        if (this.isLoading) {
            return;
        }
        try {
            this.isLoading = true;
            const parmas = {
                id
            };
            await nsMockManageKoasApiManageTestCaseDeleteGet.remote(parmas);
            runInAction(() => {
                this.isLoading = false;
                message.success('删除成功～');
                this.getAllTestCaseList();
            });
        } catch (e) {
            runInAction(() => {
                this.isLoading = false;
            });
        }
    }

    // 驳回
    @action.bound
    public async rejectTestCase() {
        if (this.isLoading) {
            return;
        }
        try {
            this.isLoading = true;
            const parmas = {
                docId: this.apiId,
                nextState: 30
            };
            await nsMockManageKoasApiManageHttpApiApiStateChangePost.remote(parmas);
            runInAction(() => {
                this.onSaveCallback && this.onSaveCallback(30, '已驳回');
                this.isLoading = false;
                message.success('驳回成功～');
            });
        } catch (e) {
            runInAction(() => {
                this.isLoading = false;
            });
        }
    }

    // 执行测试数据
    @action.bound
    public async doTestCase(id) {
        if (this.isLoading) {
            return;
        }
        try {
            this.isLoading = true;
            const parmas = {
                id,
            };
            const result = await nsMockManageKoasApiManageTestCaseDoTestCaseGet.remote(parmas);
            runInAction(() => {
                if (!result.bconfigIsAdd) {
                    Modal.confirm({
                        title: '请先填写「测试配置」',
                        onOk: async () => {
                            this.onOpenTestConfigurationModal();
                        },
                        okText: '测试配置',
                        cancelText: '取消',
                    });
                } else {
                    this.getAllTestCaseList();
                    if (result.caseStatus === 1 || result.caseStatus === 2) {
                        message.success(result?.caseStatusDesc);
                    } else {
                        message.error(result?.caseStatusDesc);
                    }
                }
                this.isLoading = false;
            });
        } catch (e) {
            runInAction(() => {
                this.isLoading = false;
            });
        }
    }

    // 批量测试数据
    @action.bound
    public async doAllOfTestCases() {
        if (!this.testCaseList || !this.testCaseList.length) {
            message.error('还没有测试用例，请先新建用例～');
            return;
        }
        if (this.isLoading) {
            return;
        }
        this.isLoading = true;
        try {
            const parmas = {
                docId: this.apiId,
            };
            const result = await nsMockManageKoasApiManageTestCaseDoAllOfTestCasesGet.remote(parmas);
            runInAction(() => {
                if (!result.bconfigIsAdd) {
                    Modal.confirm({
                        title: '请先填写「测试配置」',
                        onOk: async () => {
                            this.onOpenTestConfigurationModal();
                        },
                        okText: '测试配置',
                        cancelText: '取消',
                    });
                } else {
                    this.getAllTestCaseList();
                    message.success('执行测试成功～');
                }
                this.isLoading = false;

            });
        } catch (e) {
            runInAction(() => {
                this.isLoading = false;
            });
        }
    }

    // 自测通过
    @action.bound
    public async passSelfTest() {
        if (!this.testCaseList || !this.testCaseList.length) {
            Modal.confirm({
                title: '请先创建测试用例，并测试通过～',
                onOk: async () => {
                    this.onOpenTestCaseModal('new');
                },
                okText: '新建用例',
                cancelText: '取消',
            });
            return;
        }
        const testCase = this.testCaseList.find(item => {
            return item.status === 1 || item.status === 4;
        });
        if (testCase) {
            Modal.confirm({
                title: '自测未通过，全部用例执行通过或忽略后，才能自测通过～',
                okText: '知道了',
                cancelText: '取消',
            });
            return;
        }
        if (this.isLoading) {
            return;
        }
        Modal.confirm({
            title: '确认自测通过？通过后API状态将变为【联调中】状态',
            onOk: async () => {
                try {
                    this.isLoading = true;
                    const parmas = {
                        docId: this.apiId,
                        nextState: 20
                    };
                    await nsMockManageKoasApiManageHttpApiApiStateChangePost.remote(parmas);
                    runInAction(() => {
                        this.onSaveCallback && this.onSaveCallback(20, '联调中');
                        this.changeStatus(20);
                        this.isLoading = false;
                        this.getAllTestCaseList();
                    });
                } catch (e) {
                    runInAction(() => {
                        this.isLoading = false;
                    });
                }
            },
        });
    }

    @action.bound
    public async getKoasCaseSingleResultForBug(koasCaseId: number): Promise<void> {
        try {
            const result = await nsMockManageKoasApiManageTestCaseGetKoasCaseSingleResultForBugGet
                .remote({ koasCaseId });
            this.createBugTeam(result);
        } catch {
        }
    }

    @Bind
    private createBugTeam(result): void {
        const urlHost: string = 'https://team.corp.kuaishou.com';
        let str: string = `用例标题\n   ${result.name}\n`;
        if (result.request) {
            str = `${str}\n请求参数：\n${result.request}\n`;
        }
        if (result.response) {
            str = `${str}\n返回数据：\n${result.response}\n`;
        }
        if (result.curl) {
            str = `${str}\nCURL：\n${result.curl}\n`;
        }
        str = `${str}\n测试用例：\n${location.href}`;
        const queryObject = {
            bizId: 'KDEV_API_ID',
            bizName: 'KDEV_API',
            taskInfo: {
                title: `${result.name}`,
                description: str
            }
        };
        window.open(`${urlHost}/open/sdk/create-task?${QS.stringify(queryObject)}`);
    }
}
