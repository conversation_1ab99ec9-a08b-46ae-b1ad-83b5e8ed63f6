.apiTestWrap {
  height: calc(100% - 104px);
  .apiTestOperations {
    display: flex;
    justify-content: space-between;
    margin-bottom: 16px;
  }
  .caseDetailHeader {
    display: flex;
    align-items: center;
    justify-content: space-between;
    .caseStatus {
      padding-left: 10px;
    }
  }
  .apiTestReport {
    // width: 100%;
    display: flex;
    :global {
      .ant-collapse > .ant-collapse-item > .ant-collapse-header {
        padding-left: 16px;
      }
    }
  }
  .apiTestResultTitle {
    font-size: 16px;
  }
  .apiTestResultDT {
    font-size: 16px;
    padding-bottom: 8px;
    display: inline-block;
  }
  .apiTestResultBlock {
    display: flex;
    align-items: flex-end;
    justify-content: space-between;
  }
  .apiTestResults {
    margin-bottom: 10px;
    // width: 120%;
    display: flex;
    justify-content: space-between;
    .apiTestResult {
      padding: 24px 0;
      text-align: center;
      background-color: #f9f9f9;
      flex: 1;
      margin-left: 24px;
    }
    .apiTestResult:nth-of-type(1) {
      margin-left: 0;
    }
  }
}