.testResultModal {
    .testResultModalBody {
        max-height: calc(100vh - 350px);
        overflow: auto;
    }

    .baseInfoRow {
        margin-bottom: 8px;
        // display: flex;

        .ignoreBtn{
            margin: 0 8px;
        }

        .dataType {
            display: inline-block;
            background-color: rgba(50, 125, 255, .08);
            border-radius: 4px;
            margin-left: 16px;
            color: #327dff;
            padding: 0 4px;
        }

        .ruleType {
            float: right;
        }
    }

    .example{
        margin-bottom: 32px;
    }

    .tipsIcon {
        font-size: 16px;
        margin-left: 4px;
    }

    // .redColor {
    //     color: #ff4d4f;
    // }

    // .greenColor {
    //     color: #31bf30;
    // }

    // .blueColor {
    //     color: #19b2ff;
    // }
    // :global {
    //   .ant-modal-body {
    //     padding-top: 0;
    //   }
    // }
}
  