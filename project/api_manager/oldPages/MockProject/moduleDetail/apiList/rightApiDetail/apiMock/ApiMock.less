.apiMockWrap {
  height: 100%;

  .apiMock {
    display: flex;
    height: 100%;

    .apiMockListWrap{
      height: 100%;
      overflow: auto;
    }

    .apiMockList {
      width: 220px;
      margin: 12px 12px 0 -24px;

      .nodeDataTitle {
        display: flex;
        align-items: center;
        justify-content: space-between;
        width: 100%;

        .nodeDataTitleName {
          word-break: normal;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
          width: 100%;
        }

        .nodeDataOperation {
          display: flex;
          align-items: center;
          padding: 4px 0;
        }
      }
    }

    .apiMockData {
      padding: 16px 24px;
      border: 1px solid #d9d9d9;
      border-radius: 4px;
      flex: 1;
      height: 100%;
      overflow: auto;
    }
  }

  .empty {
    padding-top: 140px;

    :global {
      .ant-empty-description {
        opacity: .25;
      }
    }
  }

  :global {
    .ant-tree .ant-tree-treenode {
      overflow: hidden;
      width: 100%;
    }

    .ant-tree.ant-tree-block-node .ant-tree-list-holder-inner .ant-tree-node-content-wrapper {
      width: calc(100% - 36px);
    }
  }
}
