.rightApiDetailWrap {
  height: 100%;
  padding: 16px 0 24px;
  overflow: hidden;

  .margLeft8px {
    margin-left: 8px;
  }

  .apiDetail {
    overflow: auto;
    height: 100%;
  }

  .apiDetailTop {
    line-height: 32px;

    .backBtn {
      color: #252626;
    }

    .dividerVertical {
      height: 20px;
    }

    .apiName {
      font-size: 16px;
      font-weight: bold;
    }

    .lastUpdateTime{
      color: #898a8c;
      font-size: 12px;
    }

    .dropDown, .proxyConfigBtn{
      float: right;
    }
  }

  .apiTabs {
    height: 100%;
    .apiMockOperate {
      .focusBtn {
        .margLeft8px();
        background-color: #ffa114;
        border: 1px solid #eb9008;
        color: #ffffff;
      }

      .versionRecordBtn {
        margin: 0 8px;
      }
    }

    :global {
      .ant-tabs-content {
        padding: 16px 0 32px;
      }

      .ant-tabs-content-holder, .ant-tabs-content {
        height: 100%;
        overflow: auto;
      }
    }
  }
}
