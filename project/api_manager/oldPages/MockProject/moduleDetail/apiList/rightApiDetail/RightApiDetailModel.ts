import { AViewModel } from 'libs';
import { observable, action, runInAction, } from 'mobx';
import {
    nsMockManageKoasApiManageHttpApiQueryDetailGet,
    nsGenerateCodePost, nsMockManageKoasApiManageHttpApiGenerateCodePost, nsMockManageKoasApiManageMockRuleSwitchPost,
    nsMockManageKoasApiManageSceneQuerySwitchInfoGet, nsMockManageKoasApiManageHttpApiDeletePost,
    nsMockManageKoasApiManageHttpApiAttentionPost
} from '@/remote';
import { ApiMockM } from './apiMock/ApiMockM';
import { ApiTestM } from './apiTest/ApiTestM';
import { VersionRecordModel } from '@/business/versionRecord/VersionRecordModel';
import { ApiStatusModel } from '@/business/apiStatus/ApiStatusModel';
import { QRCodeM } from 'oldPages/OrganizeSpace/QRCode/QRCodeM';
import { CommentM } from '@/business/apiDetail/comment/CommentM';
import { departmentCascader } from '@/business/global';
import { ApiBaseInfoM } from '@/business/apiDetail/httpApiBaseInfo/ApiBaseInfoM';
import { HttpApiRequestParamsM } from '@/business/apiDetail/httpApiRequestParams/HttpApiRequestParamsM';
import { HttpApiResponseParamsM } from 'oldPages/HttpApiDetail/component/httpApiResponseParams/HttpApiResponseParamsM';
import { message } from 'antd';
import { AddApiToFolderM } from '@/business/addApiToFolder/AddApiToFolderM';
import { Bind } from 'lodash-decorators';
import { ruleTypeEnum, IProps, apiActiveTypeEnum } from './configure';
import { ApiStreamM } from '@/business/httpApiComponents/apiStream/ApiStreamM';
import { ApiListM } from '../ApiListM';
import { getKey0OrKey1, pushKey } from '@/index.config/tools';
import { ModuleDetailModel } from '../../ModuleDetailModel';
import * as QS from 'query-string';

interface IUrlParams {
    languageType: string;
    language: string;
}

export class RightApiDetailModel extends AViewModel {
    @observable public departmentId: number = departmentCascader.getDepartmentId();
    @observable public apiId: number = 0;
    @observable public apiActiveKey: string = apiActiveTypeEnum.DETAIL;
    @observable public ruleType: number = ruleTypeEnum.WEB; // 2：匹配 1：设备
    @observable public ruleSwitchLoading: boolean = false;
    @observable public QRCodeUrl: string = '';
    @observable public apiName: string = '';
    @observable public status: number = 0;
    @observable public statusDesc: string = '';
    @observable public lastUpdateTime: string = '';
    @observable public groupId: number = 0;
    @observable public focus: boolean = false;
    @observable public queryDetailLoading: boolean = false;

    @observable public reqDataType: string = '';

    @observable public sceneId: number = 0;

    public onCloseApiDetailCallBack?(groupId: number): void;
    public viewApiCallback?(type: string, apiId: number, groupId: number): void;
    public setApiListApiStatus?(groupId: number, apiId: number, apiStatus: number): void;
    private apiListM?: ApiListM;
    private moduleDetailM?: ModuleDetailModel;

    public apiMockM = new ApiMockM();
    public apiTestM = new ApiTestM();
    public versionRecordM = new VersionRecordModel();
    public apiStatusM = new ApiStatusModel();
    public QRCodeM = new QRCodeM();
    public commentM: null | CommentM = null;
    public apiBaseInfoM = new ApiBaseInfoM();
    public httpApiRequestParamsM = new HttpApiRequestParamsM();
    public httpApiResponseParamsM = new HttpApiResponseParamsM();
    public addApiToFolderM = new AddApiToFolderM();

    public apiStreamM?: ApiStreamM;

    @action.bound
    public initParams() {
        const urlParams: object = QS.parse(location.search);
        this.apiActiveKey = urlParams['apiActiveKey'] || this.apiActiveKey;
        this.apiId = getKey0OrKey1(urlParams['groupSelectKey'] || '')[1] || this.apiId;
        this.groupId = getKey0OrKey1(urlParams['groupSelectKey'] || '')[1] || this.groupId;
        this.apiActiveKey = urlParams['apiActiveKey'] || this.apiActiveKey;
    }

    @action.bound
    public setParentModel(props: IProps): void {
        this.apiListM = props.apiListM;
        this.moduleDetailM = props.moduleDetailM;
    }

    @action.bound
    public initLoading() {
        this.initData();
        this.queryDetail();
        this.apiMockM.initLoading(this.apiId);
        this.commentM = new CommentM({ apiId: this.apiId });
        this.onChangeType(this.apiActiveKey);
    }

    @action.bound
    public initData() {
        // this.apiActiveKey = apiActiveTypeEnum.DETAIL;
        this.ruleType = ruleTypeEnum.WEB;
        this.QRCodeUrl = '';
        this.reqDataType = '';
        this.apiBaseInfoM.initData();
        this.httpApiRequestParamsM.initData();
        this.httpApiResponseParamsM.initData();
    }

    // 返回api列表页
    @action.bound
    public onCloseApiDetail() {
        this.viewApiCallback && this.viewApiCallback('', 0, this.groupId);
    }

    // 切换编辑态
    @action.bound
    public onChangeEditApi() {
        // 调用父级api
        this.viewApiCallback && this.viewApiCallback('edit', this.apiId, this.groupId);
    }

    // 复制
    @action.bound
    public onCopyApiDoc() {
        // 调用父级api
        this.viewApiCallback && this.viewApiCallback('copy', this.apiId, this.groupId);
    }

    // 删除
    @action.bound
    public deleteApiDoc() {
        this.viewApiCallback && this.viewApiCallback('delete', this.apiId, this.groupId);
    }

    // 获取要编辑的代码类型
    @action.bound
    public onChangeCode(e) {
        const urlParams: IUrlParams = {
            languageType: e.keyPath[1], // clients | servers
            language: e.keyPath[0]
        };
        this.getGenerateCodeParams(urlParams);
    }

    // 打开API status记录
    @action.bound
    public onOpenApiStatus() {
        this.apiStatusM.init(this.apiId, this.status);
        this.apiStatusM.onSaveCallback = this.setApiStatus;
    }

    // 打开版本记录
    @action.bound
    public onOpenVersionRecord() {
        this.versionRecordM.initLoading(this.apiId);
        this.versionRecordM.coverCallback = this.queryDetail;
    }

    // 打开绑定设备二维码弹框
    @action.bound
    public onOpenQRCode(): void {
        this.QRCodeM.init(this.apiId, this.sceneId);
        this.QRCodeM.onCloseQRCodeCallBack = this.onCloseQRCodeCallBack;
    }

    @action.bound
    public onOpenAddApiToFolderModal(): void {
        this.addApiToFolderM.init(this.departmentId, this.apiId);
    }

    @action.bound
    public onCloseQRCodeCallBack(): void {
        this.querySwitchInfo();
        this.apiMockM.getAllMockDataList();
    }

    @action.bound
    public onChangeType(apiActiveKey: string): void {
        if (apiActiveKey) {
            this.apiActiveKey = apiActiveKey;
            pushKey({
                apiActiveKey: apiActiveKey
            });
        }
        this.initChildrenModel();
    }

    @action
    private initChildrenModel() {
        switch (this.apiActiveKey) {
            case apiActiveTypeEnum.MOCK:
                if (this.ruleType === ruleTypeEnum.CLIENT) {
                    this.querySwitchInfo();
                }
                break;
            case apiActiveTypeEnum.STREAM:
                if (!this.apiStreamM) {
                    this.apiStreamM = new ApiStreamM(this.apiId);
                } else {
                    this.apiStreamM.initLoading(this.apiId);
                }
                break;
        }
    }

    @action.bound
    private initBaseInfo(baseInfo): void {
        this.apiName = baseInfo?.name;
        this.lastUpdateTime = baseInfo?.lastUpdateTime;
        this.statusDesc = baseInfo?.statusDesc;
        this.status = baseInfo?.status;
        this.ruleType = baseInfo?.ruleType || ruleTypeEnum.WEB;
        this.focus = baseInfo?.focus;
        this.groupId = baseInfo?.groupId;
        this.apiBaseInfoM.setBaseInfo(baseInfo);
        this.apiBaseInfoM.changeBranchCallback = this.changeBranchCallback;
    }

    // 刷新左侧树及详情
    @action.bound
    private changeBranchCallback(branchName: string, docId: number): void {
        if (this.apiListM?.groupSelectKey) {
            pushKey({
                branchName,
                groupSelectKey: `${getKey0OrKey1(this.apiListM?.groupSelectKey)[0]}-${docId}`
            });
            this.moduleDetailM?.setBranch(branchName);
            this.apiListM.initParams();
            this.apiListM.initLoading();
            this.apiListM.rightApiModel.initParams();
        }
    }

    @action.bound
    private setApiStatus(status: number, statusDesc: string): void {
        this.status = status;
        this.statusDesc = statusDesc;
        this.apiTestM.changeStatus(status);
        this.setApiListApiStatus && this.setApiListApiStatus(this.groupId, this.apiId, status);
    }

    // 获取详情
    @action.bound
    public async queryDetail() {
        this.queryDetailLoading = true;
        try {
            const params = {
                id: this.apiId
            };
            const result = await nsMockManageKoasApiManageHttpApiQueryDetailGet.remote(params);
            runInAction(() => {
                result?.baseInfo && this.initBaseInfo(result?.baseInfo);
                result?.request && this.httpApiRequestParamsM.setData(result.request);
                result?.response && this.httpApiResponseParamsM.setData(result.response);
                if (this.ruleType === ruleTypeEnum.CLIENT && this.apiActiveKey === apiActiveTypeEnum.MOCK) {
                    this.querySwitchInfo();
                }
                this.queryDetailLoading = false;
                this.apiTestM.initLoading(this.apiId, result?.baseInfo?.status || 0, this.setApiStatus);
            });
        } catch (e) {
            runInAction(() => {
                this.queryDetailLoading = false;
            });
        }
    }

    // 获取生成代码参数
    @action.bound
    public async getGenerateCodeParams(urlParams: IUrlParams) {
        try {
            const params = {
                id: this.apiId
            };
            const result = await nsMockManageKoasApiManageHttpApiGenerateCodePost.remote(params);
            runInAction(() => {
                this.generateCode(urlParams, result);
            });
        } catch (e) {
        }
    }

    // 一键生成代码
    @action.bound
    public async generateCode(urlParams: IUrlParams, spec) {
        try {
            const parmas = {
                spec
            };
            const result = await nsGenerateCodePost.remote(urlParams, parmas);
            runInAction(() => {
                window.open(`https://generator.swagger.io/api/gen/download/${result.code}`);
            });
        } catch (e) {
        }
    }

    // 返回规则切换
    @Bind
    public async ruleSwitch() {
        runInAction(() => this.ruleSwitchLoading = true);
        const ruleType: number = this.ruleType === ruleTypeEnum.CLIENT ? ruleTypeEnum.WEB : ruleTypeEnum.CLIENT;
        try {
            const params = {
                docId: this.apiId,
                status: ruleType
            };
            const result = await nsMockManageKoasApiManageMockRuleSwitchPost.remote(params);
            runInAction(() => {
                this.ruleSwitchLoading = false;
                if (result?.code !== 0 && ruleType === ruleTypeEnum.CLIENT) {
                    this.onOpenQRCode();
                } else {
                    this.ruleType = ruleType;
                    this.apiMockM.getAllMockDataList();
                }
            });
        } catch (e) {
            runInAction(() => {
                this.ruleSwitchLoading = false;
            });
        }
    }

    @action.bound
    public async querySwitchInfo() {
        try {
            const params = {
                docId: this.apiId,
                departmentId: this.departmentId
            };
            const result = await nsMockManageKoasApiManageSceneQuerySwitchInfoGet.remote(params);
            runInAction(() => {
                this.sceneId = result?.sceneId;
                this.ruleType = result?.ruleType;
                if (result?.code !== 0) {
                    this.onOpenQRCode();
                }
                this.apiMockM.mockDataM.setScene({ sceneId: this.sceneId, sceneName: result?.sceneName });
            });
        } catch (e) {
        }
    }

    // 删除API
    @Bind
    public async delete() {
        try {
            const params = {
                id: this.apiId,
            };
            await nsMockManageKoasApiManageHttpApiDeletePost.remote(params);
            message.success('删除成功');
            this.deleteApiDoc();
        } catch (e) {
        }
    }

    // 关注、取消关注API
    @Bind
    public async attention() {
        try {
            const params = {
                apiId: this.apiId,
                action: this.focus ? 'unfocus' : 'focus'
            };
            await nsMockManageKoasApiManageHttpApiAttentionPost.remote(params);
            runInAction(() => {
                this.focus = !this.focus;
                message.success(this.focus ? '关注成功' : '取消关注成功');
            });
        } catch (e) {
        }
    }
}
