import { AView } from 'libs';
import React from 'react';
import { ApiListM } from './ApiListM';
import { inject, observer, Provider } from 'mobx-react';
import { Input, Button, Tree, Dropdown, Menu, Modal, Skeleton, Empty, Tag, Tooltip, Radio } from 'antd';
import css from './ApiList.less';
import SplitPane from 'react-split-pane';
import Pane from 'react-split-pane/lib/Pane';
import Bind from 'lodash-decorators/bind';
import { bindObserver } from '@libs/mvvm';
import { FolderFilled, EllipsisOutlined, DoubleLeftOutlined, DoubleRightOutlined } from '@ant-design/icons';
import { SearchEmptyIcon } from '@/business/commonIcon';
import { RightApi } from './rightApi/RightApi';
import { apiStatusEnum } from '@/business/toolFun/ToolFun';
import classNames from 'classnames';
import { IProps } from './configure';
import { CreateGroup } from './createGroup/CreateGroup';

const { Search } = Input;

const Search_keyWord = bindObserver(Search, 'keyWord');

@inject('moduleDetailM')
@observer
export class ApiList extends AView<ApiListM, IProps> {
    private domRef: React.RefObject<HTMLDivElement> = React.createRef();

    @Bind
    protected onDeleteGroup(nodeData) {
        Modal.confirm({
            title: '确认删除该分组吗？',
            content: '删除后将无法恢复',
            onOk: () => this.model.delete(nodeData)
        });
    }

    @Bind
    public componentWillMount(): void {
        this.model.changeModuleDetailM(this.props.moduleDetailM);
        this.model.onResizeTreeHeight();
        window.addEventListener('resize', this.model.onResizeTreeHeight);
    }

    @Bind
    public componentWillUnmount() {
        window.removeEventListener('resize', this.model.onResizeTreeHeight);
    }

    @Bind
    private renderGroupEditOperate(nodeData): React.ReactNode {
        const model = this.model;
        if (nodeData.type === 'folder') {
            return (
                <Dropdown
                    placement={'bottomRight'}
                    overlay={
                        <Menu>
                            <Menu.Item
                                onClick={e => {
                                    e.domEvent.stopPropagation();
                                    model.onOpenCreateGroupModal(nodeData);
                                }}
                            >编辑</Menu.Item>
                            <Menu.Item
                                onClick={e => {
                                    e.domEvent.stopPropagation();
                                    this.onDeleteGroup(nodeData);
                                }}
                                disabled={!nodeData.canDelete}
                            >删除</Menu.Item>
                        </Menu>
                    }>
                    <Button
                        icon={<EllipsisOutlined />}
                        type={'link'}
                        // size={ 'small' }
                        className={css.groupMoreOperation}
                    />
                </Dropdown>
            );
        }
    }

    @Bind
    protected renderTreeTitle(nodeData): React.ReactNode {
        return <div className={css.groupTitle}>
            {
                nodeData.type !== 'folder' && nodeData.statusDesc &&
                <Tag
                    className={css.tag}
                    color={apiStatusEnum[nodeData.status].color}>{nodeData.statusDesc}
                </Tag>
            }
            <Tooltip title={nodeData.title} placement="right">
                <div className={css.groupName}>
                    {
                        nodeData.type === 'folder' &&
                        <FolderFilled className={css.folderIcon} />
                    }
                    {nodeData.title}
                </div>
            </Tooltip>
            {this.renderGroupEditOperate(nodeData)}
        </div>;
    }

    @Bind
    protected renderGroupList(): React.ReactNode {
        const model = this.model;
        if (model.loading) {
            return <Skeleton />;
        } else {
            if (model.groupList && model.groupList.length) {
                const treeConfig: object = {
                    blockNode: true,
                    draggable: true,
                    className: css.groupTree,
                    treeData: model.groupList,
                    selectedKeys: model.groupSelectKey,
                    expandedKeys: model.groupExpandKey,
                    autoExpandParent: true,
                    titleRender: this.renderTreeTitle,
                    onSelect: model.onSelectKey,
                    onExpand: model.onExpandGroupKey,
                    onDrop: model.onDragApiToGroup,
                    height: model.treeScrollHeight
                };
                return (
                    <Tree {...treeConfig} />
                );
            } else {
                return <Empty
                    className={css.empty}
                    image={<SearchEmptyIcon />}
                    description={'还未创建分组，快去添加吧～'}
                />;
            }
        }
    }

    @Bind
    protected renderLeftGroup(): React.ReactNode {
        const model = this.model;
        return <Pane minSize={model.leftPaneMinSize} size={model.leftPaneSize}>
            <div className={css.leftGroupWrap} hidden={Boolean(model.showLeftPane)}>
                <div className={css.leftGroupTop}>
                    <Search_keyWord model={model} placeholder={'搜索分组/API'} onSearch={model.onSearchGroupList} />
                    <Button
                        type={'primary'}
                        className={css.createGroupBtn}
                        // onClick={() => model.openCreateEditModal(null)}
                        onClick={() => model.onOpenCreateGroupModal()}
                    >
                        新建
                    </Button>
                </div>
                {this.renderGroupList()}
            </div>
        </Pane>;
    }

    @Bind
    private renderExpanedBtn(): React.ReactNode {
        const model = this.model;
        return (
            <Tooltip
                title={Boolean(model.showLeftPane) ? '点击展开分组' : '点击收起分组'}
                getPopupContainer={() => {
                    return this.domRef.current || document.body;
                }}
                visible={model.expandIconVisible}
                placement={Boolean(model.showLeftPane) ? 'right' : 'left'}
            >
                <div
                    className={
                        Boolean(model.showLeftPane)
                            ? classNames(css.expandIcon, css.rightExpandIcon)
                            : css.expandIcon
                    }
                    onClick={model.onOpenCloseLeftPane}
                    onMouseEnter={() => model.enterLeaveExpandIcon(true)}
                    onMouseLeave={() => model.enterLeaveExpandIcon(false)}
                >
                    {Boolean(model.showLeftPane) ? <DoubleRightOutlined /> : <DoubleLeftOutlined />}
                </div>
            </Tooltip>
        );
    }

    public render(): React.ReactNode {
        const model = this.model;
        return (
            <Provider apiListM={model}>
                <div className={css.apiListWrap}>
                    <SplitPane onChange={model.onChangeSplitPane}>
                        {this.renderLeftGroup()}
                        <Pane minSize={'991px'}>
                            <div className={css.rightPane} ref={this.domRef}>
                                {this.renderExpanedBtn()}
                            </div>
                            <RightApi model={this.model.rightApiModel} />
                        </Pane>
                    </SplitPane>
                </div>
                <CreateGroup model={this.model.createGroupM} />
            </Provider>
        );
    }
}
