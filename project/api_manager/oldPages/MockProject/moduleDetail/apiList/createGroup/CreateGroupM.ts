import { AViewModel } from 'libs';
import { observable, action } from 'mobx';
import {
    nsMockManageKoasApiManageGroupCreatePost,
    nsMockManageKoasApiManageGroupEditPost
} from '@/remote';
import { message } from 'antd';
import { Bind } from 'lodash-decorators';

export class CreateGroupM extends AViewModel {
    @observable public visible: boolean = false;
    private moduleId: number = 0;
    @observable public groupId: number = 0;
    @observable public groupName: string = '';

    public saveGroupCallback?(type: 'new' | 'edit', groupId: number, groupName: string): void;

    // 打开新建/编辑分组弹框
    @action.bound
    public onOpenCreateGroupModal(moduleId: number, groupId: number = 0, groupName: string = ''): void {
        this.visible = true;
        this.moduleId = moduleId;
        this.groupId = groupId;
        this.groupName = groupName;
    }

    // 关闭弹框
    @action.bound
    public closeCreateGroupModal(): void {
        this.initData();
        this.visible = false;
    }

    // 初始化数据
    @action
    private initData(): void {
        this.moduleId = 0;
        this.groupId = 0;
        this.groupName = '';
    }

    // 保存分组
    @action.bound
    public onSaveGroup(): void {
        if (!this.groupName) {
            message.warn('请填写分组名称～');
            return;
        }
        this.groupId ? this.edit() : this.create();
    }

    // 新建分组
    @Bind
    private async create() {
        try {
            const parmas = {
                moduleId: this.moduleId,
                groupName: this.groupName
            };
            const result = await nsMockManageKoasApiManageGroupCreatePost.remote(parmas);
            message.success('新建成功～');
            result.id && this.saveGroupCallback && this.saveGroupCallback('new', result.id, this.groupName);
            this.closeCreateGroupModal();
        } catch (e) {
        }
    }

    // 编辑分组
    @Bind
    private async edit() {
        try {
            const parmas = {
                moduleId: this.moduleId,
                groupId: this.groupId,
                groupName: this.groupName
            };
            await nsMockManageKoasApiManageGroupEditPost.remote(parmas);
            message.success('修改成功～');
            this.saveGroupCallback && this.saveGroupCallback('edit', this.groupId, this.groupName);
            this.closeCreateGroupModal();
        } catch (e) {
        }
    }
}
