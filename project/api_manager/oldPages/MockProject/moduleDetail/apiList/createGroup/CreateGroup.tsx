import { <PERSON><PERSON>ie<PERSON> } from 'libs';
import React from 'react';
import { CreateGroupM } from './CreateGroupM';
import { observer } from 'mobx-react';
import { Input, Modal } from 'antd';
import css from './CreateGroup.less';
import { bindObserver } from '@libs/mvvm';

const Input_groupName = bindObserver(Input, 'groupName');

@observer
export class CreateGroup extends AView<CreateGroupM> {
    public render(): React.ReactNode {
        const model = this.model;
        return (
            <Modal
                title={ model.groupId ? '编辑分组' : '新建分组' }
                visible={ model.visible }
                className={ css.createGroupModal }
                onCancel={ model.closeCreateGroupModal }
                onOk={ model.onSaveGroup }
                maskClosable={ false }
                destroyOnClose
            >
                <div className={ css.requiredLabel }>分组名称</div>
                <Input_groupName
                    model={ model }
                    placeholder={ '请输入分组名称' }
                    autoFocus
                    onPressEnter={ model.onSaveGroup }
                />
            </Modal>
        );
    }
}
