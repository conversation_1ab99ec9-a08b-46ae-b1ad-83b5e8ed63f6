import { AViewModel } from 'libs';
import { observable, action, runInAction } from 'mobx';
import {
    nsMockManageKoasApiManageGroupQueryAllGroupListPost,
    nsMockManageKoasApiManageHttpApiQueryAllApiListGet,
    nsMockManageKoasApiManageGroupDeletePost,
    nsMockManageKoasApiManageHttpApiDragToModifyGroupPost
} from '@/remote';
import { message } from 'antd';
import * as QS from 'query-string';
import { Bind, Debounce } from 'lodash-decorators';
import { RightApiModel } from './rightApi/RightApiModel';
import { pushKey, getKey0OrKey1 } from '@/index.config/tools';
import { apiStatusEnum } from '@/business/toolFun/ToolFun';
import { API_MGR_PANE_SIZE_KEYS, getLoaclStoragePaneSize, setLoaclStoragePaneSize } from '@/index.config/setLocalStorageFun';
import { IQuery } from './configure';
import { ModuleDetailModel } from '../ModuleDetailModel';
import { CreateGroupM } from './createGroup/CreateGroupM';

export class ApiListM extends AViewModel {
    @observable protected moduleId: number = 0;
    // src/@/pages/moduleDetail/apiList/rightApi/RightApiModel.ts
    @observable public keyWord: string = '';
    private teamIdsStr: string = '';
    @observable public groupList: Array<any> = [];
    @observable public groupSelectKey: string[] = [];
    @observable public groupExpandKey: string[] = [];
    @observable public loading: boolean = false;
    @observable public leftPaneSize: string = '246px';
    @observable public leftPaneMinSize: string = '246px';
    @observable public showLeftPane: string = '';
    @observable public expandIconVisible: boolean = false;

    // tree组件高度
    @observable public treeScrollHeight: number = 0;

    private moduleDetailM: ModuleDetailModel | undefined;
    public rightApiModel = new RightApiModel();
    public createGroupM = new CreateGroupM();

    @action.bound
    public changeModuleDetailM(moduleDetailM?: ModuleDetailModel): void {
        this.moduleDetailM = moduleDetailM;
        if (moduleDetailM) {
            this.initParams({ moduleId: moduleDetailM.moduleId });
            this.initLoading();
        }
        this.rightApiModel.setParentThis(this);
    }

    @action.bound
    public onResizeTreeHeight(): void {
        this.treeScrollHeight = document.body.clientHeight - 260;
    }

    // 获取url参数
    @action.bound
    public initParams(query?: IQuery): void {
        const urlParams: object = QS.parse(location.search);
        this.groupSelectKey = urlParams['groupSelectKey'] ? [urlParams['groupSelectKey']] : [];
        this.moduleId = Number(urlParams['moduleId']) || this.moduleId;
        this.teamIdsStr = urlParams['teamIdsStr'] || '';
    }

    // 初始化加载
    @action.bound
    public initLoading() {
        this.initLeftPaneSize();
        this.groupExpandKey = [];
        this.groupList = [];
        this.queryAllGroupList();
        this.rightApiModel.initParams();
    }

    // 打开关闭左侧Pane
    @action.bound
    public onOpenCloseLeftPane(): void {
        if (this.showLeftPane) {
            this.leftPaneSize = this.showLeftPane;
            this.showLeftPane = '';
            this.leftPaneMinSize = '246px';
        } else {
            this.showLeftPane = this.leftPaneSize;
            this.leftPaneSize = '0';
            this.leftPaneMinSize = '0';
        }
        this.enterLeaveExpandIcon(false);
    }

    // 移入移除展开Pane icon
    @action
    public enterLeaveExpandIcon(expandIconVisible: boolean): void {
        this.expandIconVisible = expandIconVisible;
    }

    // 手动点击选中节点
    @action.bound
    public onSelectKey(selectedKeys: any[], e): void {
        if (e.selected) {
            let showType: string = '';
            if (/-/.test(selectedKeys[0])) {
                showType = 'view';
            } else {
                if (!this.groupExpandKey.includes(selectedKeys[0])) {
                    this.onExpandGroupKey([...this.groupExpandKey, ...selectedKeys]);
                }
            }
            this.selectGroupKey(selectedKeys, showType);
        }
    }

    // src/@/pages/moduleDetail/apiList/rightApi/RightApiModel.ts
    // 修改API状态
    @action.bound
    protected setApiStatus(groupId: number, apiId: number, apiStatus: number): void {
        this.groupList.forEach(item => {
            if (item.id === groupId) {
                item.children && item.children.map(it => {
                    if (it.id === apiId) {
                        it.status = apiStatus;
                        it.statusDesc = apiStatusEnum[apiStatus].statusDesc;
                    }
                });
            }
        });
        this.repaintTree();
    }

    // src/@/pages/moduleDetail/apiList/rightApi/RightApiModel.ts
    @action.bound
    protected setSelectedKeys(selectedKeys: string[]): void {
        this.groupSelectKey = selectedKeys;
    }

    // src/@/pages/moduleDetail/apiList/rightApi/RightApiModel.ts
    @action.bound
    protected createDeleteApiDoc(groupId: number): void {
        this.groupList.forEach(item => {
            if (item.id === groupId) {
                this.queryAllApiList(item);
            }
        });
    }

    // 选中节点
    @action.bound
    private selectGroupKey(selectedKeys: string[], showType?: string): void {
        this.groupSelectKey = selectedKeys;
        const [groupId, apiId] = getKey0OrKey1(this.groupSelectKey);
        this.pushKey(showType);
        this.rightApiModel.initParams();
    }

    // url保存参数
    @action.bound
    protected pushKey(showType?: string) {
        const params = {
            groupSelectKey: this.groupSelectKey[0] || '',
        };
        if (showType || showType === '') {
            params['showType'] = showType;
        }
        pushKey(params);
    }

    // 展开节点
    @action.bound
    public onExpandGroupKey(expandedKeys: any[]) {
        this.groupExpandKey = expandedKeys;
    }

    // 搜索分组
    @action.bound
    public onSearchGroupList() {
        this.groupSelectKey = [];
        this.groupExpandKey = [];
        this.rightApiModel.initData();
        this.queryAllGroupList();
    }

    // pane组件size受控
    @action.bound
    public onChangeSplitPane(paneSize) {
        if (paneSize[0] !== this.leftPaneSize) {
            this.leftPaneSize = paneSize[0];
            this.setLeftPaneSize();
        }
    }

    // 初始化左侧Pane宽度
    @action
    private initLeftPaneSize(): void {
        localStorage.removeItem('apiListPaneSize');
        const leftPaneSize = getLoaclStoragePaneSize()[API_MGR_PANE_SIZE_KEYS.MODULE_DETAIL_API_TREE] || '';
        if (leftPaneSize && leftPaneSize.substring(leftPaneSize.length - 2) === 'px') {
            this.leftPaneSize = leftPaneSize;
        }
        setLoaclStoragePaneSize(API_MGR_PANE_SIZE_KEYS.MODULE_DETAIL_API_TREE, this.leftPaneSize);
    }

    // 记录上次宽度
    @Debounce(300)
    @action
    protected setLeftPaneSize() {
        setLoaclStoragePaneSize(API_MGR_PANE_SIZE_KEYS.MODULE_DETAIL_API_TREE, this.leftPaneSize);
    }

    // 重绘树
    @action.bound
    public repaintTree() {
        this.groupList = [...this.groupList];
        this.groupExpandKey = [...this.groupExpandKey];
    }

    // 打开新建编辑分组弹框
    @action.bound
    public onOpenCreateGroupModal(nodeData?): void {
        if (nodeData) {
            this.createGroupM.onOpenCreateGroupModal(this.moduleId, nodeData.id, nodeData.groupName);
        } else {
            this.createGroupM.onOpenCreateGroupModal(this.moduleId);
        }
        this.createGroupM.saveGroupCallback = this.saveGroupCallback;
    }

    // 保存分组后操作
    @action.bound
    private saveGroupCallback(type: 'new' | 'edit', groupId: number, groupName: string): void {
        if (type === 'new') {
            this.groupList.unshift({
                key: groupId.toString(),
                id: groupId,
                title: groupName,
                type: 'folder',
                canDelete: true,
                groupName,
                children: []
            });
        }
        if (type === 'edit') {
            this.groupList.map(item => {
                if (item.id === groupId) {
                    item.groupName = groupName;
                    item.title = groupName;
                }
            });
        }
        this.repaintTree();
    }

    // 删除分组
    @action.bound
    private deleteGroup(nodeData) {
        const index = this.groupList.findIndex(item => item.id === nodeData.id);
        if (index > -1) {
            this.groupList.splice(index, 1);
            if (this.groupList.length && nodeData.key === this.groupSelectKey[0]) {
                this.selectGroupKey([this.groupList[0].key]);
            }
            this.repaintTree();
        }
    }

    // 初始化分组
    @action.bound
    private initGroupList(groupList): void {
        const expandesKeys: string[] = [];
        this.groupList = groupList.map(item => {
            if (item.children) {
                item.children = item.children.map(it => {
                    it['groupId'] = item.id;
                    it['type'] = 'file';
                    it['title'] = it.apiName;
                    return it;
                });
            }
            item['type'] = 'folder';
            item['title'] = item.groupName;
            expandesKeys.push(item['key']);
            return item;
        });
        // 若url上没有groupSelectKey则设置默认值
        if (!this.groupSelectKey.length && this.groupList.length) {
            this.selectGroupKey([expandesKeys[0]]);
        }
        this.onExpandGroupKey(expandesKeys);
    }

    // 初始化API
    @action.bound
    private initApiList(nodeData, apiList): void {
        this.groupList.map(item => {
            if (nodeData.key === item.key) {
                item.canDelete = !apiList.length;
                item.children = apiList?.map(it => {
                    it['groupId'] = nodeData.id;
                    it['type'] = 'file';
                    it['title'] = it.apiName;
                    delete it.leaf;
                    return it;
                });
                if (!this.groupExpandKey.includes(nodeData.key)) {
                    this.groupExpandKey.push(nodeData.key);
                }
            }
        });
        this.repaintTree();
    }

    // src/@/pages/moduleDetail/apiList/rightApi/RightApiModel.ts
    // 获取分组列表
    @action.bound
    protected async queryAllGroupList() {
        this.loading = true;
        try {
            const params = {
                keyWord: this.keyWord,
                moduleId: this.moduleId,
                branchName: this.moduleDetailM?.branchName,
                teamIds: this.teamIdsStr ? this.teamIdsStr.split(',') : []
            };
            const result = await nsMockManageKoasApiManageGroupQueryAllGroupListPost.remote(params);
            runInAction(() => {
                this.loading = false;
                result.groupList && this.initGroupList(result.groupList);
            });
        } catch (e) {
            runInAction(() => {
                this.loading = false;
            });
        }
    }

    // src/@/pages/moduleDetail/apiList/rightApi/RightApiModel.ts
    // 获取分组下的api
    @Bind
    public async queryAllApiList(nodeData) {
        try {
            const parmas = {
                groupId: nodeData.id,
                branchName: this.moduleDetailM?.branchName
            };
            const result = await nsMockManageKoasApiManageHttpApiQueryAllApiListGet.remote(parmas);
            result?.apiList && this.initApiList(nodeData, result?.apiList);
        } catch (e) {
            this.initApiList(nodeData, []);
        }
    }

    // 删除分组
    @Bind
    public async delete(nodeData) {
        try {
            const parmas = {
                moduleId: this.moduleId,
                groupId: nodeData.id
            };
            await nsMockManageKoasApiManageGroupDeletePost.remote(parmas);
            message.success('删除成功～');
            this.deleteGroup(nodeData);
        } catch (e) {
        }
    }

    // 开始拖拽api
    @action.bound
    public onDragApiToGroup(info): void {
        if (!info.dropToGap && info.node.type === 'folder' && info.dragNode.type === 'file') {
            const docId: number = info.dragNode.id;
            const groupId: number = info.node.id;
            const newSelectKey: string = `${groupId}-${docId}`;
            if (newSelectKey !== info.dragNodesKeys[0]) {
                this.dragToModifyGroup(docId, groupId);
            }
        }
    }

    // 拖拽API到某个分组
    @Bind
    private async dragToModifyGroup(docId: number, groupId: number): Promise<void> {
        try {
            runInAction(() => this.loading = true);
            const params = {
                docId,
                groupId
            };
            await nsMockManageKoasApiManageHttpApiDragToModifyGroupPost.remote(params);
            runInAction(() => {
                this.groupSelectKey = [`${groupId}-${docId}`];
            });
            this.pushKey('view');
            this.initLoading();
        } catch {
            runInAction(() => this.loading = false);
        }
    }
}
