.moduleDetailWrap {
  background-color: #ffffff;

  .breadcrumb {
    padding: 16px;
    display: flex;
    align-items: center;
    border-bottom: 1px solid #ebedf0;

    .backBtn {
      font-size: 28px;
      // margin-right: 8px;
    }

    .forwardIcon,
    .verticalBarIcon {
      font-size: 24px;
    }
  }

  .moduleDetailTabs {
    //margin: 0 20px;
    height: calc(100% - 65.5px);

    .mockProxyConfigsTab {
      display: flex;
      align-items: center;
    }

    :global {
      .ant-tabs-tab {
        padding: 16px 0 12px;
      }

      .ant-tabs-nav {
        margin-bottom: 0;
      }

      .ant-tabs-content-holder,
      .ant-tabs-content {
        height: 100%;
      }

      .ant-tabs-nav-list {
        margin: 0 20px;
      }
    }
  }

  .operate {
    margin-right: 20px;
    align-items: center;

    .focusBtn {
      background-color: #ffa114;
      border: 1px solid #eb9008;
      color: #ffffff;
    }

    .branchSelect {
      min-width: 100px;
      margin-right: 180px;
    }

    .teams {
      width: 300px;
      margin-top: 6px;
    }
  }
}