import APageModel from '@/pages/APageModel';
import { action, observable, runInAction } from 'mobx';
import {
    nsMockManageKoasApiManageModuleAttentionPost,
    nsMockManageKoasApiManageProjectQueryAllModuleListGet,
    nsMockManageKoasApiManageModuleGetHttModuleArBranchGet,
    nsMockManageKoasApiManageModuleQueryDepartmentAndProjectByIdGet
} from '@/remote';
import { message } from 'antd';
import { ModuleOverviewModel } from './moduleOverview/ModuleOverviewModel';
import { CreateModuleModalModel } from 'oldPages/HttpModuleMgr/component/createModuleModal/CreateModuleModalModel';
import { DataStructureModel } from './dataStructure/DataStructureModel';
import { ApiListM } from './apiList/ApiListM';
import { MockProxyConfigsM } from 'oldPages/OrganizeSpace/component/mockProxyConfigs/MockProxyConfigsM';
import { activeKeyEnum, IQuery } from './configure';
import { Bind } from 'lodash-decorators';
import { pushKey } from '@/index.config/tools';
import { departmentCascader } from '@/business/global/departmentCascader';
import { TeamsSelectM } from '@/business/httpApiComponents/teamsSelect/TeamsSelectM';

export class ModuleDetailModel extends APageModel<IQuery> {
    @observable public projectId: number = 0;
    @observable public projectName: string = '';
    private departmentIds: number[] = [];
    @observable public departmentFullName: string = '';
    @observable public moduleId: number = 0;
    @observable public moduleList: nsMockManageKoasApiManageProjectQueryAllModuleListGet.IModuleList[] = [];
    @observable public activeKey: string = activeKeyEnum.TWO;
    // src/@/pages/moduleDetail/apiList/ApiListM.ts
    @observable public branchName: string = '最新分支';
    @observable public branchNameList: string[] = [];
    @observable public teamIds: string[] = [];
    private teamIdsStr: string = '';

    public createModuleModalModel = new CreateModuleModalModel();
    public moduleOverviewM: any;
    public dataStructureM: any;
    public apiListM: ApiListM | undefined;
    public mockProxyConfigsM: any;
    public teamsSelectM: TeamsSelectM = new TeamsSelectM();

    protected getQueryFields(): Array<keyof IQuery> {
        return [
            'projectId',
            'moduleId',
            'activeKey',
            'branchName',
            'teamIdsStr'
        ];
    }

    constructor(query: IQuery) {
        super(query);
        this.initByQueryFields(query);
        this.init();
    }

    @action
    private init() {
        this.projectId = Number(this.projectId) || 0;
        this.moduleId = Number(this.moduleId) || 0;
        this.teamIds = this.teamIdsStr && this.teamIdsStr.split(',') || this.teamIds;
        this.changeActiveKey(this.activeKey);
        this.queryAllModuleList();
        this.getHttModuleArBranch();
        this.queryDepartmentAndProjectById();
    }

    // 切换tabs
    @action.bound
    public changeActiveKey(activeKey: string) {
        switch (activeKey) {
            case activeKeyEnum.ONE:
                if (!this.moduleOverviewM) {
                    this.moduleOverviewM = new ModuleOverviewModel({ moduleId: this.moduleId });
                }
                break;
            case activeKeyEnum.TWO:
                if (!this.apiListM) {
                    this.apiListM = new ApiListM({ moduleId: this.moduleId });
                }
                break;
            case activeKeyEnum.THREE:
                if (!this.dataStructureM) {
                    this.dataStructureM = new DataStructureModel({ moduleId: this.moduleId });
                }
                break;
            case activeKeyEnum.FOUR:
                if (!this.mockProxyConfigsM) {
                    this.mockProxyConfigsM = new MockProxyConfigsM(this.moduleId);
                }
                break;
        }
        this.activeKey = activeKey;
        pushKey({ activeKey: this.activeKey });
    }

    // 打开关联模块弹框
    @action.bound
    public onOpenCreateModuleModal() {
        this.createModuleModalModel.initLoading(1, this.projectId, this.moduleId);
        this.createModuleModalModel.onCloseCallback = this.moduleOverviewModelLoading;
    }

    // 获取模块概况
    @action.bound
    public moduleOverviewModelLoading() {
        this.dataStructureM.initLoading(this.moduleId);
    }

    // 切换模块
    @action.bound
    public changeModuleId() {
        pushKey({
            moduleId: this.moduleId
        });
        if (this.moduleOverviewM) {
            this.moduleOverviewM.initLoading(this.moduleId);
        }
        if (this.apiListM) {
            this.apiListM.initParams();
            this.apiListM.initLoading();
        }
        if (this.dataStructureM) {
            this.dataStructureM.initLoading(this.moduleId);
        }
        if (this.mockProxyConfigsM) {
            this.mockProxyConfigsM.initLoading(this.moduleId);
        }
    }

    // 获取模块列表
    @action
    protected async queryAllModuleList() {
        try {
            const params = {
                projectId: this.projectId,
            };
            const result = await nsMockManageKoasApiManageProjectQueryAllModuleListGet.remote(params);
            runInAction(() => {
                this.moduleList = result?.moduleList || [];
            });
        } catch (e) {
        }
    }

    // 关注、取消关注模块
    @Bind
    public async attention() {
        try {
            const params = {
                moduleId: this.moduleId,
                action: this.moduleOverviewM.focus ? 'unfocus' : 'focus'
            };
            await nsMockManageKoasApiManageModuleAttentionPost.remote(params);
            message.success(this.moduleOverviewM.focus ? '取消关注成功' : '关注成功');
            this.moduleOverviewM.initLoading(this.moduleId);
        } catch (e) {
        }
    }

    // 切换分支
    @action.bound
    public onSelectBranch(branchName: string): void {
        this.branchName = branchName;
        pushKey({
            showType: '',
            branchName,
            groupSelectKey: ''
        });
        if (this.apiListM) {
            this.apiListM.initParams({ branchName });
            this.apiListM.initLoading();
        }
    }

    // 筛选team
    @action.bound
    public onChangeTeamIds(teamIds: string[], teamSelectedRows) {
        this.teamIds = teamIds;
        this.setTeamSelectedRows(teamSelectedRows);
        const teamIdsStr = teamIds.join(',');
        pushKey({
            showType: '',
            groupSelectKey: '',
            teamIdsStr,
        });
        if (this.apiListM) {
            this.apiListM.initParams();
            this.apiListM.initLoading();
        }
    }

    @action
    private async setTeamSelectedRows(rows) {
        const teamSelectRows = rows.map(item => {
            return {
                taskId: item.key,
                title: item.label
            };
        });
        this.teamsSelectM.setSelectedRows(teamSelectRows);
    }

    // src/@/pages/moduleDetail/apiList/rightApiDetail/RightApiDetailModel.ts
    @action.bound
    public setBranch(branchName: string): void {
        this.branchName = branchName;
    }

    // 获取分支列表
    @Bind
    private async getHttModuleArBranch(): Promise<void> {
        try {
            const params = { moduleId: this.moduleId };
            const result = await nsMockManageKoasApiManageModuleGetHttModuleArBranchGet.remote(params);
            runInAction(() => {
                this.branchNameList = result?.branchNameList || [];
            });
        } catch { }
    }

    // 获取部门项目信息
    @Bind
    private async queryDepartmentAndProjectById(): Promise<void> {
        try {
            const params = {
                projectId: this.projectId
            };
            const result = await nsMockManageKoasApiManageModuleQueryDepartmentAndProjectByIdGet.remote(params);
            runInAction(() => {
                this.departmentIds = result?.departmentIds || [];
                this.departmentFullName = result?.departmentNames?.join('/') || '';
                this.projectName = result?.projectName || '';
            });
        } catch { }
    }

    // 设置部门
    @action.bound
    public setDepartment() {
        if (this.departmentIds.length && this.departmentFullName) {
            departmentCascader.setDepartment(this.departmentIds, this.departmentFullName);
        }
    }
}
