import { <PERSON><PERSON>ie<PERSON> } from 'libs';
import React from 'react';
import { DataStructureModel } from './DataStructureModel';
import { observer } from 'mobx-react';
import { Table, Button, Input, Modal } from 'antd';
import css from './DataStructure.less';
import Bind from 'lodash-decorators/bind';
import { KdevIconFont } from '@/business/commonComponents/KdevIconFont';
import { CreateDataStructure } from './createDataStructure/CreateDataStructure';
import moment from 'moment';
import { bindObserver } from '@libs/mvvm';
import { DataStructureDetail } from './dataStructureDetail/DataStructureDetail';

const { Search } = Input;

const Search_modelName = bindObserver(Search, 'modelName');

@observer
export class DataStructure extends AView<DataStructureModel> {

    @Bind
    public dataStructureColumns(model): any[] {
        const columns = [
            {
                title: '名称',
                // dataIndex: 'fullClassName',
                key: 'fullClassName',
                render: record => {
                    return <a onClick={ () => model.onOpenDataStructureDetail(record.id) }>{ record.fullClassName }</a>;
                }
            },
            {
                title: '描述',
                dataIndex: 'description',
                key: 'description',
            },
            {
                title: '类型',
                dataIndex: '',
                key: '',
                render: record => 'json'
            },
            {
                title: '创建者',
                dataIndex: 'createUser',
                key: 'createUser',
            },
            {
                title: '最后更新时间',
                dataIndex: 'createTime',
                key: 'createTime',
                render: text => moment(text).format('YYYY-MM-DD HH:mm:ss')
            },
            {
                title: '操作',
                // dataIndex: 'operation',
                key: 'operation',
                align: 'right',
                width: 100,
                render: record => {
                    return <>
                        <Button
                            icon={ <KdevIconFont id={ '#iconxingzhuangjiehe' } /> }
                            onClick={ () => model.onOpenCreateDataStructure(record.id) }
                        />
                        <Button
                            icon={ <KdevIconFont id={ '#iconyanse' } /> }
                            className={ css.deleteBtn }
                            onClick={ () => this.onDeleteModel(record) }
                        />
                    </>;
                }
            }
        ];
        return columns;
    }

    // 确认是否删除模块
    @Bind
    public onDeleteModel(record) {
        Modal.confirm({
            content: '删除后将无法恢复，确认删除该数据结构？',
            onOk: () => this.model.deleteById(record.id)
        });
    }

    public render(): React.ReactNode {
        const model = this.model;
        if (model.createEditVisible) {
            return <CreateDataStructure model={ model.createDataStructureModel } />;
        } else {
            return (
                <div className={ css.dataStructureWrap }>
                    {/*<div>*/}
                    {/*    数据结构*/}
                    {/*    <span className={ css.dataStructureTitTooltip }>*/}
                    {/*        （您可以将API文档中相同的部分保存为数据结构，API文档通过引用数据结构可以减少编写文档的工作量）*/}
                    {/*    </span>*/}
                    {/*</div>*/}
                    <div className={ css.dataStructureTop }>
                        <Search_modelName
                            className={ css.modelName }
                            model={ model }
                            placeholder={ '支持数据结构名搜索' }
                            onSearch={ () => model.onChangePageInfo(1) }
                        />
                        <Button
                            type={ 'primary' }
                            onClick={ () => model.onOpenCreateDataStructure(0) }
                        >
                            新建数据结构
                        </Button>
                    </div>
                    <Table
                        className={ css.dataStructureListTable }
                        columns={ this.dataStructureColumns(model) }
                        bordered
                        dataSource={ model.modelList }
                        rowKey={ 'id' }
                        pagination={ {
                            showTotal: total => `共 ${ total } 条`,
                            current: model.pageIndex,
                            // pageSize: model.pageSize,
                            showSizeChanger: false,
                            total: model.total,
                            onChange: model.onChangePageInfo,
                        } }
                    />
                    <DataStructureDetail model={ model.dataStructureDetailM } />
                </div>
            );
        }
    }
}
