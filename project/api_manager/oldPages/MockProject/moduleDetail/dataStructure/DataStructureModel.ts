import { AViewModel } from 'libs';
import { observable, action, runInAction } from 'mobx';
import {
    nsMockManageKoasApiManageModelQueryByModuleIdGet, nsMockManageKoasApiManageModelDeleteByIdGet
} from '@/remote';
import { CreateDataStructureModel } from './createDataStructure/CreateDataStructureModel';
import { message } from 'antd';
import { DataStructureDetailM } from './dataStructureDetail/DataStructureDetailM';

export class DataStructureModel extends AViewModel {
    @observable protected moduleId: number = 0;
    @observable public createEditVisible: boolean = false;
    @observable public modelList: any[] = [];
    @observable public pageIndex: number = 1;
    @observable public modelName: string = '';
    @observable public total: number = 0;

    public createDataStructureModel = new CreateDataStructureModel();
    public dataStructureDetailM = new DataStructureDetailM();

    constructor(query) {
        super(query);
        this.initLoading(query.moduleId);
    }

    @action
    public initLoading(moduleId: number) {
        this.moduleId = moduleId;
        this.queryByModuleId();
    }

    // 打开新建数据结构弹框
    @action.bound
    public onOpenCreateDataStructure(id: number) {
        this.createEditVisible = true;
        this.createDataStructureModel.initLoading(id, this.moduleId);
        this.createDataStructureModel.createDataStructureCallback = this.onCreateDataStructureCallback;
    }

    @action.bound
    public onOpenDataStructureDetail(id: number) {
        this.dataStructureDetailM.init(id);
    }

    // 取消新建编辑数据结构回调
    @action.bound
    public onCreateDataStructureCallback(type: string) {
        if (type === 'save') {
            this.queryByModuleId();
        }
        this.createEditVisible = false;
    }

    @action.bound
    public onChangePageInfo(pageIndex) {
        this.pageIndex = pageIndex;
        this.queryByModuleId();
    }

    // 获取数据结构列表
    @action.bound
    protected async queryByModuleId() {
        try {
            const params = {
                moduleId: this.moduleId,
                modelName: this.modelName,
                pageIndex: this.pageIndex
            };
            const result = await nsMockManageKoasApiManageModelQueryByModuleIdGet.remote(params);
            runInAction(() => {
                this.modelList = result.modelList;
                this.total = result.total;
            });
        } catch (e) {
        }
    }

    // 删除数据结构
    @action.bound
    public async deleteById(id) {
        try {
            const params = {
                id
            };
            await nsMockManageKoasApiManageModelDeleteByIdGet.remote(params);
            runInAction(() => {
                message.success('删除成功～');
                this.onChangePageInfo(1);
            });
        } catch (e) {
        }
    }
}
