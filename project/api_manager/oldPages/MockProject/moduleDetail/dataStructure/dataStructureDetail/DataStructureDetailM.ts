import { AViewModel } from 'libs';
import { observable, action, runInAction } from 'mobx';
import { formatKey1, JSONbigStringify } from '@/index.config/tools';
import { nsMockManageKoasApiManageModelQueryByIdGet } from '@/remote';
import { ViewJsonTableM } from '@/business/apiDetail/viewJsonTable/ViewJsonTableM';

export class DataStructureDetailM extends AViewModel {
    @observable public visible: boolean = false;
    // @observable protected moduleId: number = 0;
    @observable public id: number = 0;
    @observable public dataModelName: string = '';
    @observable public dataModelDesc: string = '';
    @observable public dataModelList: nsMockManageKoasApiManageModelQueryByIdGet.IChildren[] = [];
    @observable public expandedRowKeys: string[] = [];
    @observable public dataModelExample: string = '';

    public viewJsonTableM = new ViewJsonTableM();

    @action.bound
    public init(id: number): void {
        this.visible = true;
        if (this.id === id) {
            return;
        }
        this.id = id;
        this.queryById();
    }

    @action.bound
    public onClose(): void {
        this.visible = false;
    }

    // 初始化数据
    @action.bound
    public initData() {
        // this.moduleId = 0;
        this.id = 0;
    }

    // 关闭查看数据结构
    @action.bound
    public onCloseCreateDataStructure() {
        this.initData();
    }

    // 获取数据结构详情
    @action.bound
    protected async queryById() {
        try {
            const params = {
                id: this.id
            };
            const result = await nsMockManageKoasApiManageModelQueryByIdGet.remote(params);
            runInAction(() => {
                this.dataModelName = result?.name || '';
                this.dataModelDesc = result.description;
                const [list, keys] = formatKey1(result?.children);
                this.dataModelList = list;
                this.expandedRowKeys = keys;
                this.viewJsonTableM.setListAndExpanedKeys(list as any, keys);
                this.dataModelExample = JSONbigStringify(result?.example || '');
            });
        } catch (e) {
        }
    }
}
