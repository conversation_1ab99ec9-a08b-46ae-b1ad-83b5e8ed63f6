import { AVie<PERSON> } from 'libs';
import React from 'react';
import { DataStructureDetailM } from './DataStructureDetailM';
import { observer } from 'mobx-react';
import { Descriptions, Drawer } from 'antd';
import css from './DataStructureDetail.less';
import Bind from 'lodash-decorators/bind';
import { ViewJsonTable } from '@/business/apiDetail/viewJsonTable/ViewJsonTable';
import { KdevTitle, AsyncAceEditor } from '@/business/commonComponents';
import { KEYS_TABLE } from '@/business/commonComponents/ResizableTable';

@observer
export class DataStructureDetail extends AView<DataStructureDetailM> {
    protected tableTitleKey: any[] = ['value', 'name', 'type', 'required', 'description'];

    @Bind
    private renderDataModelBaseInfo(): React.ReactNode {
        const model = this.model;
        return (
            <div className={css.dataModelBaseInfo}>
                <Descriptions column={2}>
                    <Descriptions.Item label={'名称'}>{model.dataModelName}</Descriptions.Item>
                    <Descriptions.Item label={'描述'}>{model.dataModelDesc}</Descriptions.Item>
                </Descriptions>
            </div>
        );
    }

    public render(): React.ReactNode {
        const model = this.model;
        return (
            <Drawer
                open={model.visible}
                className={css.dataStructureDetailDrawer}
                width={800}
                title={'详情'}
                onClose={model.onClose}
            >
                <KdevTitle text={'基本信息'} />
                {this.renderDataModelBaseInfo()}
                <KdevTitle text={'数据结构'} />
                <ViewJsonTable
                    model={model.viewJsonTableM}
                    className={css.viewJsonTable}
                    tableTitleKey={this.tableTitleKey}
                    unifiedId={KEYS_TABLE.DATASTRUCTURE_DETAIL_KEYS_TABLE}
                />
                <KdevTitle text={'示例'} />
                <AsyncAceEditor
                    width={'100%'}
                    height={'400px'}
                    showPrintMargin={false}
                    className={css.aceEditor}
                    value={model.dataModelExample}
                    readOnly
                />
            </Drawer>
        );
    }
}
