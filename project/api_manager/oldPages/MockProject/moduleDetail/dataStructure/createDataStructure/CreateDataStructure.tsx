import { AView } from 'libs';
import React from 'react';
import { CreateDataStructureModel } from './CreateDataStructureModel';
import { observer } from 'mobx-react';
import { Input, Button, Radio, Modal } from 'antd';
import { bindObserver } from '@libs/mvvm';
import css from './CreateDataStructure.less';
import Bind from 'lodash-decorators/bind';
import { MultiDimensional } from '@/business/editJsonTable/multiDimensional/MultiDimensional';

const Input_name = bindObserver(Input, 'name');
const Input_description = bindObserver(Input, 'description');
const RadioGroup_type = bindObserver(Radio.Group, 'type');

@observer
export class CreateDataStructure extends AView<CreateDataStructureModel> {
    private tableTitleKey: string[] = ['name', 'type', 'required', 'value', 'operate'];

    @Bind
    protected cancelSave() {
        if (this.model.isLeave) {
            Modal.confirm({
                content: '您所编辑的内容还未保存，确认要退出页面吗？',
                okText: '留下',
                cancelText: '退出',
                onCancel: () => this.model.onCloseCreateDataStructure('cancel')
            });
        } else {
            this.model.onCloseCreateDataStructure('cancel');
        }
    }

    public render(): React.ReactNode {
        const model = this.model;
        return (
            <div
                className={ css.createDataStructureWrap }
            >
                <div className={ css.fromRow }>
                    <div className={ css.fromRowLeft }>
                        <div className={ css.rowLabel }>名称 <span className={ css.rowLabelRequired }>*</span></div>
                        <Input_name model={ model } placeholder={ '请输入名称' } />
                    </div>
                    <div className={ css.fromRowRight }>
                        <div className={ css.rowLabel }>描述</div>
                        <Input_description model={ model } placeholder={ '请输入' } />
                    </div>
                </div>
                <RadioGroup_type model={ model } className={ css.fromRowType }>
                    <Radio value={ 'form' }>form</Radio>
                    <Radio value={ 'json' }>json</Radio>
                    <Radio value={ 'xml' }>xml</Radio>
                    <Radio value={ 'raw' }>raw</Radio>
                </RadioGroup_type>
                <MultiDimensional
                    model={model.multiDimensionalM}
                    tableTitleKey={this.tableTitleKey}
                />
                <div className={ css.createDataStructureBottom }>
                    <Button
                        className={ css.cancelBtn }
                        onClick={ this.cancelSave }
                    >取消</Button>
                    <Button
                        type={ 'primary' }
                        onClick={ model.onSave }
                    >保存</Button>
                </div>
            </div>
        );
    }
}
