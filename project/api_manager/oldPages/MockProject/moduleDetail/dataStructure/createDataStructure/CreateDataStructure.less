.createDataStructureWrap {
  margin: 0 20px;
  overflow: auto;
  height: 100%;

  .fromRow {
    display: flex;
    width: 100%;
    margin-bottom: 24px;

    .fromRowLeft {
      width: calc(50% - 24px);
    }

    .fromRowRight {
      width: calc(50% - 24px);
      margin-left: 48px;
    }
  }

  .fromRowType {
    margin: 24px 0 8px 0;
    width: calc(50% - 24px);
    display: none;
  }

  .addRowBtn {
    background-color: #f5f7fa;
    color: #327dff;
    margin-top: 16px;
  }

  .rowLabel {
    color: #898a8c;
    margin: 24px 0 8px 0;

    .rowLabelRequired {
      color: #ff4d4f;
    }
  }

  .createDataStructureBottom {
    margin: 20px 0;
    text-align: right;

    .cancelBtn {
      margin-right: 8px;
    }
  }

  :global {
    .ant-modal-body {
      padding-top: 0;
    }
  }
}
