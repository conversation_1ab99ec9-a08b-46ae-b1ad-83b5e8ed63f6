import { AViewModel } from 'libs';
import { observable, action, runInAction } from 'mobx';
import { formatKey } from '@/index.config/tools';
import {
    nsMockManageKoasApiManageModelCreatePost, nsMockManageKoasApiManageModelQueryByIdGet,
    nsMockManageKoasApiManageModelUpdatePost
} from '@/remote';
import { message } from 'antd';
import { MultiDimensionalM } from '@/business/editJsonTable/multiDimensional/MultiDimensionalM';

export class CreateDataStructureModel extends AViewModel {
    @observable protected moduleId: number = 0;
    @observable public id: number = 0;
    @observable public name: string = '';
    @observable public description: string = '';
    @observable public type: string = 'json';
    @observable public isLeave: boolean = false;

    public createDataStructureCallback?(type: string): void;

    public multiDimensionalM = new MultiDimensionalM();

    @action.bound
    public initLoading(id: number, moduleId: number) {
        this.id = id;
        this.moduleId = moduleId;
        if (this.id) {
            this.queryById();
        }
        this.multiDimensionalM.init('', this.moduleId);
    }

    // 初始化数据
    @action.bound
    public initData() {
        this.moduleId = 0;
        this.id = 0;
        this.name = '';
        this.description = '';
        this.isLeave = false;
        this.multiDimensionalM.initData();
    }

    /**
     * 关闭新建编辑数据结构
     * @param type(cancel|save)
     */
    @action.bound
    public onCloseCreateDataStructure(type: string) {
        this.initData();
        this.createDataStructureCallback && this.createDataStructureCallback(type);
    }

    // 保存数据结构
    @action.bound
    public onSave() {
        // 保存逻辑
        if (this.id) {
            // 修改逻辑
            this.update();
        } else {
            this.create();
        }
    }

    // 校验必填参数
    @action.bound
    public checkParams() {
        if (!this.name) {
            message.warn('请填写数据结构名称～');
            return true;
        }
        if (this.multiDimensionalM.checkParams()) {
            return true;
        }
        return false;
    }

    // 新建数据结构
    @action.bound
    protected async create() {
        if (this.checkParams()) {
            return;
        }
        try {
            const params = {
                name: this.name,
                description: this.description,
                moduleId: this.moduleId,
                children: this.multiDimensionalM.getList() as any
            };
            const result = await nsMockManageKoasApiManageModelCreatePost.remote(params);
            runInAction(() => {
                message.success('新建数据结构成功～');
                this.onCloseCreateDataStructure('save');
            });
        } catch (e) {
        }
    }

    // 新建数据结构
    @action.bound
    protected async update() {
        if (this.checkParams()) {
            return;
        }
        try {
            const params = {
                id: this.id,
                name: this.name,
                description: this.description,
                moduleId: this.moduleId,
                children: this.multiDimensionalM.getList() as any
            };
            const result = await nsMockManageKoasApiManageModelUpdatePost.remote(params);
            runInAction(() => {
                message.success('修改数据结构成功～');
                this.onCloseCreateDataStructure('save');
            });
        } catch (e) {
        }
    }

    // 获取数据结构详情
    @action.bound
    protected async queryById() {
        try {
            const params = {
                id: this.id
            };
            const result = await nsMockManageKoasApiManageModelQueryByIdGet.remote(params);
            runInAction(() => {
                this.name = result.name;
                this.description = result.description;
                const [list, keys] = formatKey(result.children);
                const newKeys = keys.filter(item => item.split('-').length < 3).splice(0, 10);
                this.multiDimensionalM.setListAndExpanedKeys(list, newKeys);
            });
        } catch (e) {
        }
    }
}
