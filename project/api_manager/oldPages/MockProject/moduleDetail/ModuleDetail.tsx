import React from 'react';
import APage from '@/pages/APage';
import { observer, Provider } from 'mobx-react';
import { ModuleDetailModel } from './ModuleDetailModel';
import css from './ModuleDetail.less';
import Bind from 'lodash-decorators/bind';
import { Button, Select, Tabs, Space, Breadcrumb } from 'antd';
import { ModuleOverview } from './moduleOverview/ModuleOverview';
import { bindObserver } from '@libs/mvvm';
import { CreateModuleModal } from '@/business/createModuleModal/CreateModuleModal';
import { ApiList } from './apiList/ApiList';
import { KdevIconFont, QuestionTips, questionTipsConfig } from '@/business/commonComponents';
import { DataStructure } from './dataStructure/DataStructure';
import { EditJsonStore } from '@/business/editJsonTable/EditJsonStore';
import { MockProxyConfigs } from '@/business/httpApiComponents/mockProxyConfigs/MockProxyConfigs';
import { activeKeyEnum, IQuery } from './configure';
import { showTypeEnum } from './apiList/rightApi/configure';
import { ERouter } from 'CONFIG';
import { TeamsSelect } from 'oldPages/MockProject/component/teamsSelect/TeamsSelect';

const { Option } = Select;
const { TabPane } = Tabs;

const Select_moduleId = bindObserver(Select, 'moduleId');

@observer
export default class ModuleDetail extends APage<IQuery, ModuleDetailModel> {
    protected editJsonStore = new EditJsonStore();
    protected createModel(): ModuleDetailModel {
        return new ModuleDetailModel(this.query);
    }

    @Bind
    private renderModule(): React.ReactChild {
        const model = this.model;
        return (
            <Select_moduleId model={model} bordered={false} onChange={model.changeModuleId}>
                {
                    model.moduleList.map(item => (
                        <Option value={item.id} key={item.id}>{item.moduleName}</Option>
                    ))
                }
            </Select_moduleId>
        );
    }

    @Bind
    private renderBreadcrumb(): React.ReactChild {
        const model = this.model;
        const moduleMgrUrl = `${ERouter.API_MOCK_PROJECT_MODULEMGR}?projectId=${model.projectId}`;
        return (
            <Breadcrumb separator="" className={css.breadcrumb}>
                <Breadcrumb.Item href={moduleMgrUrl}>
                    <KdevIconFont id="#iconyemian-fanhui1" className={css.backBtn} />
                </Breadcrumb.Item>
                <Breadcrumb.Separator>
                    <KdevIconFont id="#iconvertical-bar" className={css.verticalBarIcon} />
                </Breadcrumb.Separator>
                <Breadcrumb.Item href={`${ERouter.API_MOCK_ORGANIZESPACE_PROJECTMGR}`} onClick={model.setDepartment}>
                    {model.departmentFullName}
                </Breadcrumb.Item>
                <Breadcrumb.Separator>
                    <KdevIconFont id="#iconqianjin1" className={css.forwardIcon} />
                </Breadcrumb.Separator>
                <Breadcrumb.Item href={moduleMgrUrl}>{model.projectName}</Breadcrumb.Item>
                <Breadcrumb.Separator>
                    <KdevIconFont id="#iconqianjin1" className={css.forwardIcon} />
                </Breadcrumb.Separator>
                <Breadcrumb.Item>{this.renderModule()}</Breadcrumb.Item>
            </Breadcrumb>
        );
    }

    @Bind
    protected renderOperateBtn(): React.ReactNode {
        const model = this.model;
        const showType: string = model.apiListM?.rightApiModel.showType || '';
        if (model.activeKey === activeKeyEnum.ONE) {
            return (
                <Space className={css.operate}>
                    <Button
                        icon={<KdevIconFont id={'#iconxingzhuangjiehe'} />}
                        onClick={model.onOpenCreateModuleModal}
                    />
                    <Button
                        icon={<KdevIconFont id={'#iconguanzhu'} />}
                        className={model.moduleOverviewM.focus && css.focusBtn}
                        onClick={model.attention}
                    />
                </Space>
            );
        }
        if (model.activeKey === activeKeyEnum.TWO
            && (showType === showTypeEnum.VIEW || showType === showTypeEnum.NONE)) {
            return (
                <Space className={css.operate}>
                    <TeamsSelect
                        model={model.teamsSelectM}
                        className={css.teams}
                        placeholder="请选择team进行筛选"
                        value={model.teamIds}
                        onChange={model.onChangeTeamIds}
                    />
                    <Select
                        className={css.branchSelect}
                        value={model.branchName}
                        placeholder="请选择分支"
                        onSelect={model.onSelectBranch}
                        dropdownMatchSelectWidth={false}
                        showSearch
                        optionFilterProp="children"
                    >
                        {
                            this.model.branchNameList.map(item => (
                                <Option key={item} value={item}>{item || '未关联分支'}</Option>
                            ))
                        }
                    </Select>
                </Space>
            );
        }
    }

    @Bind
    private renderMockProxyConfigsTab(): React.ReactNode {
        return (
            <span className={css.mockProxyConfigsTab}>
                代理配置 {QuestionTips({ title: questionTipsConfig.MOCK_PROXY_CONFIG_DESC })}
            </span>
        );
    }

    public renderContent(): React.ReactNode {
        const model = this.model;
        return (
            <Provider editJsonStore={this.editJsonStore} moduleDetailM={model}>
                <div className={css.moduleDetailWrap}>
                    {this.renderBreadcrumb()}
                    <Tabs
                        className={css.moduleDetailTabs}
                        tabBarExtraContent={this.renderOperateBtn()}
                        activeKey={model.activeKey}
                        onChange={model.changeActiveKey}
                    >
                        <TabPane tab="模块概况" key={activeKeyEnum.ONE}>
                            <ModuleOverview model={model.moduleOverviewM} />
                        </TabPane>
                        <TabPane tab="API列表" key={activeKeyEnum.TWO}>
                            {
                                model.apiListM &&
                                <ApiList model={model.apiListM} />
                            }
                        </TabPane>
                        <TabPane tab="数据结构" key={activeKeyEnum.THREE}>
                            <DataStructure model={model.dataStructureM} />
                        </TabPane>
                        <TabPane tab={this.renderMockProxyConfigsTab()} key={activeKeyEnum.FOUR}>
                            <MockProxyConfigs model={model.mockProxyConfigsM} />
                        </TabPane>
                    </Tabs>
                </div>
                <CreateModuleModal model={model.createModuleModalModel} />
            </Provider>
        );
    }
}
