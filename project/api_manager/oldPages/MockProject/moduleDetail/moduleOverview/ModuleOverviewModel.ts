import { AViewModel } from 'libs';
import { observable, action, runInAction, } from 'mobx';
import {
    nsMockManageKoasApiManageModuleDetailGet, nsMockPermissionBatchGetUserGet,
    nsMockManageKoasApiManageModuleQuerySwitchGet, nsMockManageKoasApiManageModuleChangeSwitchPost
} from '@/remote';
import { Bind } from 'lodash-decorators';
import { message, Modal } from 'antd';

interface ICooperList {
    username: string;
    name: string;
    photo?: string;
    avatarUrl: string;
}

interface IModuleSwitch extends nsMockManageKoasApiManageModuleQuerySwitchGet.IModuleSwitch { }

export class ModuleOverviewModel extends AViewModel {
    @observable protected moduleId: number = 0;
    @observable public lastUpdateTime: string = '';
    @observable public name: string = '';
    @observable public moduleName: string = '';
    @observable public createUser: string = '';
    @observable public cooperList: ICooperList[] = [];
    @observable public gitUrl: string = '';
    @observable public basePath: string = '';
    @observable public mock: string = '';
    @observable public desc: string = '';
    @observable public branchs: string[] = [];
    @observable public focus: boolean = false;
    // public createMaintainerModel = new CreateMaintainerModel();
    @observable public moduleSwitches: IModuleSwitch[] = [];
    @observable public switchLoading: boolean = false;

    constructor(query) {
        super(query);
        this.initLoading(query.moduleId);
    }

    @action.bound
    public initLoading(moduleId: number) {
        this.moduleId = moduleId;
        this.detail();
        this.querySwitch();
    }

    // 获取详情
    @action
    protected async detail() {
        try {
            const params = {
                moduleId: this.moduleId
            };
            const result = await nsMockManageKoasApiManageModuleDetailGet.remote(params);
            runInAction(() => {
                this.name = result.name;
                // this.moduleType = result.moduleType;
                this.moduleName = result.moduleName;
                this.gitUrl = result.gitUrl;
                this.basePath = result.basePath;
                this.mock = result.mock;
                this.desc = result.desc;
                this.branchs = result.branchs;
                this.lastUpdateTime = result.lastUpdateTime;
                this.createUser = result.createUser;
                this.focus = result.focus;
                // this.batchGetUser([result.createUser], 'createUser');
                this.batchGetUser(result.cooperUsers, 'cooperList');
                // this.dropList();
            });
        } catch (e) {
        }
    }

    // 获取头像
    @action
    protected async batchGetUser(usernames: string[], str: string) {
        if (!usernames?.length) {
            return;
        }
        try {
            const params = {
                usernames: usernames.join(',')
            };
            const result = await nsMockPermissionBatchGetUserGet.remote(params);
            runInAction(() => {
                this[str] = result?.list.map(item => {
                    return {
                        name: item.name,
                        username: item.username,
                        avatarUrl: item?.photo || ''
                    };
                }) || [];
            });
        } catch (e) {
        }
    }

    @Bind
    private async querySwitch() {
        try {
            const result = await nsMockManageKoasApiManageModuleQuerySwitchGet.remote({ moduleId: this.moduleId });
            runInAction(() => this.moduleSwitches = result?.moduleSwitches || []);
        } catch { }
    }

    @Bind
    public async onChangeSwitch(moduleSwitch: IModuleSwitch, checked: boolean) {
        Modal.confirm({
            title: `是否确认${checked ? '开启' : '关闭'}？`,
            content: `${moduleSwitch.desc}`,
            okButtonProps: { loading: this.switchLoading },
            onOk: () => this.changeSwitch(moduleSwitch, checked)
        });
    }

    @Bind
    private async changeSwitch(moduleSwitch: IModuleSwitch, checked: boolean) {
        runInAction(() => this.switchLoading = true);
        try {
            const params = {
                moduleId: this.moduleId,
                key: moduleSwitch.key,
                value: checked ? 'true' : 'false',
                desc: moduleSwitch.desc,
                display: moduleSwitch.display,
                name: moduleSwitch.name
            };
            await nsMockManageKoasApiManageModuleChangeSwitchPost.remote(params);
            message.success('修改成功～');
            runInAction(() => {
                this.switchLoading = false;
                moduleSwitch.value = checked ? 'true' : 'false';
                this.moduleSwitches = [...this.moduleSwitches];
            });
        } catch {
            runInAction(() => this.switchLoading = false);
        }
    }
}
