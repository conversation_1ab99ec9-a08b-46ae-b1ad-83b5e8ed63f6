import { AView } from 'libs';
import React from 'react';
import { ModuleOverviewModel } from './ModuleOverviewModel';
import { observer } from 'mobx-react';
import { Input, Select, Collapse, List, Row, Col, Switch } from 'antd';
import css from './ModuleOverview.less';
import { BranchesOutlined } from '@ant-design/icons';
import { AvatarList } from '@/business/commonComponents/AvatarList/AvatarList';
import { Bind } from 'lodash-decorators';

const { TextArea } = Input;
const { Option } = Select;
const { Panel } = Collapse;

@observer
export class ModuleOverview extends AView<ModuleOverviewModel> {

    @Bind
    private renderModuleSwitches() {
        const model = this.model;
        return model.moduleSwitches.map(item => {
            if (item.display) {
                return (
                    <Row className={css.moduleOverviewRow} key={item.key}>
                        <Col span={24}>
                            <span className={css.moduleOverviewLabel}>{item.name}：</span>
                            <Switch
                                checkedChildren="开启"
                                unCheckedChildren="关闭"
                                checked={item.value === 'true'}
                                onChange={(checked) => model.onChangeSwitch(item, checked)}
                            />
                        </Col>
                    </Row>
                );
            }
        });
    }

    public render(): React.ReactNode {
        const model = this.model;
        return (
            <div className={css.moduleOverviewWrap}>
                <div className={css.moduleOverviewRow}>
                    <span className={css.moduleOverviewLabel}>模块更新时间：</span>
                    {model.lastUpdateTime}
                </div>
                <div className={css.moduleOverviewRow}>
                    <div className={css.moduleOverviewRowLeft}>
                        <span className={css.moduleOverviewLabel}>模块名称：</span>
                        {model.name}
                    </div>
                    <div>
                        <span className={css.moduleOverviewLabel}>模块地址：</span>
                        {model.moduleName}
                    </div>
                </div>
                <div className={css.moduleOverviewRow}>
                    <span className={css.moduleOverviewLabel}>git地址：</span>
                    {model.gitUrl}
                </div>
                <div className={css.moduleOverviewRow}>
                    <div className={css.moduleOverviewRowLeft}>
                        <span className={css.moduleOverviewLabel}>模块创建人：</span>
                        {model.createUser}
                        {/*<AvatarList reviewers={ model.createUser } />*/}
                    </div>
                    <div>
                        <span className={css.moduleOverviewLabel}>模块参与人：</span>
                        <AvatarList reviewers={model.cooperList} />
                    </div>
                </div>
                <div className={css.moduleOverviewRow}>
                    <span className={css.moduleOverviewLabel}>API基本路径：</span>
                    {model.basePath}
                </div>
                <div className={css.moduleOverviewRow}>
                    <span className={css.moduleOverviewLabel}>后端服务地址：</span>
                    {model.mock}
                </div>
                <div className={css.moduleOverviewRow}>
                    <span className={css.moduleOverviewLabel}>描述：</span>
                    {model.desc}
                </div>
                {this.renderModuleSwitches()}
                <div className={css.moduleOverviewRow}>
                    <span className={css.moduleOverviewLabel}>关联分支：</span>
                    <List
                        header={'分支名称'}
                        dataSource={model.branchs}
                        renderItem={item => (
                            <List.Item><BranchesOutlined /> {item}</List.Item>
                        )}
                        bordered
                        className={css.branchsList}
                    />
                </div>
            </div>
        );
    }
}
