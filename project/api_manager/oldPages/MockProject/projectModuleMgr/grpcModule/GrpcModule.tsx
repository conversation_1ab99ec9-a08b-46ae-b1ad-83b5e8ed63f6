import { AView } from 'libs';
import React from 'react';
import { GrpcModuleM } from './GrpcModuleM';
import { observer } from 'mobx-react';
import { Button, Table, Tooltip, Modal } from 'antd';
import { Bind } from 'lodash-decorators';
import css from './GrpcModule.less';
import { KdevIconFont } from '@/business/commonComponents';
import moment from 'moment';
import { IModuleDetail } from './configure';

@observer
export class GrpcModule extends AView<GrpcModuleM> {

    @Bind
    private renderName(record): React.ReactChild {
        return (
            <a onClick={() => this.model.onOpenModuleDetail(record.id)}>{record.moduleName}</a>
        );
    }

    @Bind
    private renderModuleName(moduleName: string): React.ReactChild {
        return <div className={css.moduleName}>{moduleName}</div>;
    }

    // 确认是否移除模块
    @Bind
    private onDeleteModule(moduleId: number) {
        Modal.confirm({
            content: '删除后将无法恢复，确认删除模块？',
            onOk: () => this.model.moveOutProject(moduleId)
        });
    }

    @Bind
    private renderOperate(record: IModuleDetail): React.ReactNode {
        return (
            <Tooltip
                placement={'topRight'}
                title={'移除模块'}
            >
                <Button
                    icon={<KdevIconFont id={'#iconyanse'} />}
                    onClick={() => this.onDeleteModule(record.id)}
                />
            </Tooltip>
        );
    }

    @Bind
    private columns(): any[] {
        const columns = [
            {
                title: '模块名称',
                // dataIndex: 'moduleName',
                key: 'name',
                render: this.renderName
            },
            {
                title: '模块',
                dataIndex: 'moduleName',
                key: 'moduleName',
                render: this.renderModuleName
            },
            {
                title: '创建人',
                dataIndex: 'createUser',
                key: 'createUser',
            },
            {
                title: '最后更新时间',
                dataIndex: 'updateTime',
                key: 'updateTime',
                render: text => moment(text).format('YYYY-MM-DD HH:mm:ss')
            },
            {
                title: '操作',
                key: 'operate',
                align: 'right',
                render: this.renderOperate
            }
        ];
        return columns;
    }

    @Bind
    private renderModuleTable(): React.ReactNode {
        const model = this.model;
        return (
            <Table
                columns={this.columns()}
                dataSource={model.moduleList}
                rowKey="id"
                loading={model.loading}
                bordered
                className={css.moduleTable}
                pagination={{
                    // size: 'small'
                }}
            />
        );
    }

    public render(): React.ReactNode {
        return (
            <div className={css.grpcModule}>
                {this.renderModuleTable()}
            </div>
        );
    }
}
