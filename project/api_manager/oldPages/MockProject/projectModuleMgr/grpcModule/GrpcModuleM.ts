import { AViewModel } from 'libs';
import { observable, action, runInAction } from 'mobx';
import {
    nsMockManageApiManageRpcGetModulesByProjectIdPost,
    nsMockManageApiManageRpcMoveOutProjectPost
} from '@/remote';
import { message } from 'antd';
import { Bind } from 'lodash-decorators';
import { router } from '@libs/mvvm';
import { ERouter } from 'CONFIG';
import { departmentCascader } from '@/business/global';
import { IModuleDetail } from './configure';

export class GrpcModuleM extends AViewModel {
    @observable public projectId: number = 0;
    @observable public moduleList: IModuleDetail[] = [];
    @observable public loading: boolean = false;

    constructor(projectId: number) {
        super();
        this.setProjectId(projectId);
    }

    // 设置下项目ID，进入该页面必须设置项目ID
    @action
    private setProjectId(projectId: number): void {
        this.projectId = projectId;
        this.getModulesByProjectId();
    }

    // 获取rpc模块列表
    @Bind
    private async getModulesByProjectId(): Promise<void> {
        runInAction(() => this.loading = true);
        try {
            const params = {
                projectId: this.projectId
            };
            const result = await nsMockManageApiManageRpcGetModulesByProjectIdPost.remote(params);
            runInAction(() => {
                this.moduleList = result?.list;
                this.loading = false;
            });
        } catch {
            runInAction(() => this.loading = false);
        }
    }

    // 移除项目
    @Bind
    public async moveOutProject(moduleId: number): Promise<void> {
        try {
            const params = {
                projectId: this.projectId,
                moduleId
            };
            await nsMockManageApiManageRpcMoveOutProjectPost.remote(params);
            message.success('移除成功～');
            this.getModulesByProjectId();
        } catch {}
    }

    // 打开rpc模块管理
    @Bind
    public onOpenModuleDetail(moduleId: number): void {
        router.push(ERouter.API_MOCK_PROJECT_GRPC_MODULE, {projectId: this.projectId, moduleId});
    }
}
