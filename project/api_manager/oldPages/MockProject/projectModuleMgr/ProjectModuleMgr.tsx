import React from 'react';
import APage from '@/pages/APage';
import { observer } from 'mobx-react';
import { IQuery, ProjectModuleMgrM } from './ProjectModuleMgrM';
import css from './ProjectModuleMgr.less';
import Bind from 'lodash-decorators/bind';
import { <PERSON><PERSON>, Divider, Tabs, Breadcrumb } from 'antd';
import { LeftOutlined, RightOutlined } from '@ant-design/icons';
import { HttpModule } from './httpModule/HttpModule';
import { GrpcModule } from './grpcModule/GrpcModule';
import { moduleActiveKeyEnum } from './configure';
import { KdevIconFont } from '@/business/commonComponents';
import { ERouter } from 'CONFIG';

const { TabPane } = Tabs;

@observer
export default class ProjectModuleMgr extends APage<IQuery, ProjectModuleMgrM> {
    protected createModel(): ProjectModuleMgrM {
        return new ProjectModuleMgrM(this.query);
    }

    @Bind
    protected renderProjectName(): React.ReactNode {
        const model = this.model;
        const projectMgrUrl = `${ERouter.API_MOCK_ORGANIZESPACE_PROJECTMGR}`;
        return (
            <Breadcrumb separator="" className={css.breadcrumb}>
                <Breadcrumb.Item href={projectMgrUrl} onClick={model.setDepartment}>
                    <KdevIconFont id="#iconyemian-fanhui1" className={css.backBtn} />
                </Breadcrumb.Item>
                <Breadcrumb.Separator>
                    <KdevIconFont id="#iconvertical-bar" className={css.verticalBarIcon} />
                </Breadcrumb.Separator>
                <Breadcrumb.Item href={projectMgrUrl} onClick={model.setDepartment}>
                    {model.departmentFullName}
                </Breadcrumb.Item>
                <Breadcrumb.Separator>
                    <KdevIconFont id="#iconqianjin1" className={css.forwardIcon} />
                </Breadcrumb.Separator>
                <Breadcrumb.Item>{model.projectName}</Breadcrumb.Item>
            </Breadcrumb>
        );
    }

    @Bind
    private renderModuleTabs(): React.ReactChild {
        const model = this.model;
        return (
            <Tabs activeKey={model.activeKey} onChange={model.onChangeActiveKey} className={css.tabs}>
                <TabPane tab="HTTP模块" key={moduleActiveKeyEnum.HTTP}>
                    {
                        model.httpModuleM &&
                        <HttpModule model={model.httpModuleM} />
                    }
                </TabPane>
                <TabPane tab="GRPC模块" key={moduleActiveKeyEnum.GRPC}>
                    {
                        model.grpcModuleM &&
                        <GrpcModule model={model.grpcModuleM} />
                    }
                </TabPane>
            </Tabs>
        );
    }

    public renderContent(): React.ReactNode {
        return (
            <div className={css.projectModuleMgr}>
                {this.renderProjectName()}
                {this.renderModuleTabs()}
            </div>
        );
    }
}
