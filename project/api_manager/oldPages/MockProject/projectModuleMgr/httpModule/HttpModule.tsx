import { A<PERSON>iew } from 'libs';
import React from 'react';
import { HttpModuleM } from './HttpModuleM';
import { observer } from 'mobx-react';
import { bindObserver } from '@libs/mvvm';
import { Radio, Input, Button, Table, Empty, Tooltip, Space, Modal } from 'antd';
import { Bind } from 'lodash-decorators';
import css from './HttpModule.less';
import { filterOptions } from './configure';
import { SearchEmptyIcon } from '@/business/commonIcon';
import { KdevIconFont } from '@/business/commonComponents';
import { GlobalSearch } from '@/business/apiGlobalSearch/GlobalSearch';
import { CreateModuleModal } from '@/business/createModuleModal/CreateModuleModal';

const RadioGroup_filter = bindObserver(Radio.Group, 'filter');
const Input_key = bindObserver(Input, 'key');

@observer
export class HttpModule extends AView<HttpModuleM> {

    @Bind
    private renderCreateBtn(): React.ReactChild {
        const model = this.model;
        return (
            <Button
                type={'primary'}
                className={css.createModuleBtn}
                onClick={() => model.onOpenCreateModuleModal()}
            >
                新建模块
            </Button>
        );
    }

    @Bind
    private renderSearchParams(): React.ReactNode {
        const model = this.model;
        return (
            <div className={css.searchParams}>
                <span>
                    <RadioGroup_filter
                        model={model}
                        optionType="button"
                        options={filterOptions}
                        onChange={() => model.onChangePageInfo()}
                        className={css.filter}
                    />
                    搜索：
                    <Input_key
                        model={model}
                        className={css.key}
                        placeholder={'支持模块名/API名称/路径/创建人搜索'}
                        onClick={model.openGlobalSearch}
                        readOnly={true}
                    />
                </span>
                {this.renderCreateBtn()}
            </div>
        );
    }

    @Bind
    private renderName(record): React.ReactChild {
        return (
            <a
                onClick={() => this.model.onOpenModuleDetail(record.moduleId)}
                className={css.name}
            >
                {record.name}
            </a>
        );
    }

    @Bind
    private renderModuleName(moduleName: string): React.ReactChild {
        return <div className={css.moduleName}>{moduleName}</div>;
    }

    // 确认是否删除模块
    @Bind
    private onDeleteModule(record) {
        Modal.confirm({
            content: '删除后将无法恢复，确认删除模块？',
            onOk: () => this.model.delete(record.moduleId)
        });
    }

    @Bind
    private renderOperate(record): React.ReactNode {
        const model = this.model;
        return (
            <Space>
                <Button
                    icon={<KdevIconFont id={'#iconxingzhuangjiehe'} />}
                    onClick={() => model.onOpenCreateModuleModal(record.moduleId)}
                />
                <Tooltip
                    placement={'topRight'}
                    title={
                        record.canDelete
                            ? '删除模块'
                            : '包含API不能删除，如想删除，请先删除API'
                    }
                >
                    <Button
                        icon={<KdevIconFont id={'#iconyanse'} />}
                        onClick={() => this.onDeleteModule(record)}
                        disabled={!record.canDelete}
                    />
                </Tooltip>
                <Button
                    className={record.focus && css.focusBtn}
                    icon={<KdevIconFont id={'#iconguanzhu'} />}
                    onClick={() => model.attention(record)}
                />
                <Tooltip title={'查看API耗时及错误信息'}>
                    <Button
                        icon={<KdevIconFont id="#iconduliang" />}
                        onClick={() => model.onOpenApiStatistics(record)}
                    />
                </Tooltip>
            </Space>
        );
    }

    @Bind
    private columns(): any[] {
        const columns = [
            {
                title: '模块名称',
                // dataIndex: 'name',
                key: 'name',
                width: 240,
                render: this.renderName
            },
            {
                title: '模块',
                dataIndex: 'moduleName',
                key: 'moduleName',
                width: 240,
                render: this.renderModuleName
            },
            {
                title: '创建人',
                dataIndex: 'createUser',
                key: 'createUser',
                width: 112
            },
            {
                title: '参与人',
                dataIndex: 'cooperUser',
                key: 'cooperUser',
                width: 112,
                render: text => text?.join('、') || ''
            },
            {
                title: '描述',
                dataIndex: 'moduleDesc',
                key: 'moduleDesc',
                width: 150
            },
            {
                title: '最后更新时间',
                dataIndex: 'lastUpdateTime',
                key: 'lastUpdateTime',
                width: 180
            },
            {
                title: '操作',
                // dataIndex: 'operation',
                key: 'operation',
                fixed: 'right',
                width: 180,
                render: this.renderOperate
            }
        ];
        return columns;
    }

    @Bind
    private renderModuleTable(): React.ReactNode {
        const model = this.model;
        return (
            <Table
                columns={this.columns()}
                dataSource={model.moduleList}
                rowKey="moduleId"
                loading={model.loading}
                pagination={{
                    // size: 'small',
                    showTotal: total => `共 ${total} 条`,
                    current: model.currentPage,
                    pageSize: model.pageSize,
                    showSizeChanger: true,
                    total: model.total,
                    onChange: model.onChangePageInfo,
                }}
                scroll={{
                    x: 1034
                }}
                bordered
                className={css.moduleTable}
            />
        );
    }

    // @Bind
    // private renderEmpty(): React.ReactNode {
    //     const model = this.model;
    //     if (!model.total) {
    //         return (
    //             <Empty
    //                 className={css.empty}
    //                 image={<SearchEmptyIcon />}
    //                 description={'还未关联模块，快去关联吧～'}
    //             >
    //                 {this.renderCreateBtn()}
    //             </Empty>
    //         );
    //     }
    // }

    public render(): React.ReactNode {
        return (
            <div className={css.httpModule}>
                {this.renderSearchParams()}
                {this.renderModuleTable()}
                {/* {this.renderEmpty()} */}
                <CreateModuleModal model={this.model.createModuleModalM} />
                <GlobalSearch model={this.model.globalSearchM} />
            </div>
        );
    }
}
