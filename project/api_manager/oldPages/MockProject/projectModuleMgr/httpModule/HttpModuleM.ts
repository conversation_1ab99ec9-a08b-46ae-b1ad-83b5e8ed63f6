import { AViewModel } from 'libs';
import { observable, action, runInAction, } from 'mobx';
import {
    nsMockManageKoasApiManageModuleQueryModulePageListPost,
    nsMockManageKoasApiManageModuleAttentionPost,
    nsMockManageKoasApiManageModuleDeletePost
} from '@/remote';
import { message } from 'antd';
import { Bind } from 'lodash-decorators';
import { filterEnum } from './configure';
import { router } from '@libs/mvvm';
import { ERouter } from 'CONFIG';
import { GlobalSearchM } from 'oldPages/MockProject/component/apiGlobalSearch/GlobalSearchM';
import { departmentCascader } from '@/business/global';
import { CreateModuleModalModel } from 'oldPages/HttpModuleMgr/component/createModuleModal/CreateModuleModalModel';

export class HttpModuleM extends AViewModel {
    private projectId: number = 0;
    @observable public filter: number = filterEnum.ALL;
    @observable public key: string = '';

    @observable public currentPage: number = 1;
    @observable public pageSize: number = 20;
    @observable public total: number = 0;
    @observable public loading: boolean = false;

    @observable public moduleList: nsMockManageKoasApiManageModuleQueryModulePageListPost.IModuleList[] = [];

    private departmentId: number = departmentCascader.getDepartmentId();
    public globalSearchM: GlobalSearchM = new GlobalSearchM();
    public createModuleModalM: CreateModuleModalModel = new CreateModuleModalModel();

    constructor(projectId: number) {
        super(projectId);
        this.setProjectId(projectId);
    }

    // 设置projectId
    @action
    private setProjectId(projectId: number): void {
        this.projectId = projectId;
        this.onChangePageInfo();
    }

    // 获取http模块列表
    @Bind
    private async queryModulePageList() {
        runInAction(() => this.loading = true);
        try {
            const params = {
                projectId: this.projectId,
                key: this.key,
                filter: this.filter,
                pageSize: this.pageSize,
                currentPage: this.currentPage,
            };
            const result = await nsMockManageKoasApiManageModuleQueryModulePageListPost.remote(params);
            runInAction(() => {
                this.moduleList = result?.list || [];
                this.total = result?.total || 0;
                this.loading = false;
            });
        } catch (e) {
            runInAction(() => this.loading = false);
        }
    }

    // 分页
    @action.bound
    public onChangePageInfo(currentPage: number = 1, pageSize?: number): void {
        this.currentPage = currentPage;
        if (pageSize) {
            this.pageSize = pageSize;
        }
        this.queryModulePageList();
    }

    // 打开模块详情
    @action.bound
    public onOpenModuleDetail(moduleId: number) {
        router.push(ERouter.API_MOCK_PROJECT_MODULEDETAIL, {
            projectId: this.projectId,
            moduleId
        });
    }

    // 打开关联模块弹框
    @action.bound
    public onOpenCreateModuleModal(moduleId: number = 0) {
        const type: 0 | 1 = moduleId ? 1 : 0;
        this.createModuleModalM.initLoading(type, this.projectId, moduleId);
        this.createModuleModalM.onCloseCallback = this.createModuleCallback;
    }

    @action.bound
    private createModuleCallback(type: 0 | 1): void {
        this.onChangePageInfo(type ? this.currentPage : 1);
    }

    // 删除模块
    @Bind
    public async delete(moduleId: number) {
        try {
            const params = {
                moduleId,
                projectId: this.projectId
            };
            await nsMockManageKoasApiManageModuleDeletePost.remote(params);
            message.success('删除成功');
            this.onChangePageInfo();
        } catch (e) {
        }
    }

    // 关注、取消关注模块
    @Bind
    public async attention(record) {
        try {
            const params = {
                moduleId: record.moduleId,
                action: record.focus ? 'unfocus' : 'focus'
            };
            await nsMockManageKoasApiManageModuleAttentionPost.remote(params);
            message.success(record.focus ? '取消关注成功' : '关注成功');
            this.onChangePageInfo(this.currentPage);
        } catch (e) {
        }
    }

    // 查看API耗时及错误信息
    @action.bound
    public onOpenApiStatistics(record): void {
        router.push(ERouter.API_MOCK_MODULEMGR_APISTATISTICS, {
            projectId: this.projectId,
            moduleId: record.moduleId,
            moduleName: record.moduleName
        });
    }

    // 打开全局搜索
    @action.bound
    public openGlobalSearch(): void {
        this.globalSearchM.init(this.departmentId);
    }
}
