import APageModel from '@/pages/APageModel';
import { action, observable, runInAction } from 'mobx';
import { HttpModuleM } from './httpModule/HttpModuleM';
import { GrpcModuleM } from './grpcModule/GrpcModuleM';
import { moduleActiveKeyEnum } from './configure';
import { Bind } from 'lodash-decorators';
import {
    nsMockManageKoasApiManageModuleQueryDepartmentAndProjectByIdGet
} from '@/remote';
import { pushKey } from '@/index.config/tools';
import { departmentCascader } from '@/business/global/departmentCascader';

export interface IQuery {
    projectId: number;
    activeKey: string;
}

export class ProjectModuleMgrM extends APageModel<IQuery> {
    @observable public activeKey: string = moduleActiveKeyEnum.HTTP;
    private projectId: number = 0;
    private departmentIds: number[] = [];
    @observable public departmentFullName: string = '';
    @observable public projectName: string = '';

    public httpModuleM?: HttpModuleM;
    public grpcModuleM?: GrpcModuleM;

    protected getQueryFields(): Array<keyof IQuery> {
        return [
            'projectId',
            'activeKey'
        ];
    }

    constructor(query: IQuery) {
        super(query);
        this.initByQueryFields(query);
        this.init();
    }

    @action
    private init() {
        this.projectId = Number(this.projectId) || 0;
        this.queryDepartmentAndProjectById();
        this.onChangeActiveKey(this.activeKey);
    }

    // 切换模块tabs
    @action.bound
    public onChangeActiveKey(activeKey: string): void {
        this.activeKey = activeKey;
        switch (activeKey) {
            case moduleActiveKeyEnum.HTTP:
                if (!this.httpModuleM) {
                    this.httpModuleM = new HttpModuleM(this.projectId);
                }
                break;
            case moduleActiveKeyEnum.GRPC:
                if (!this.grpcModuleM) {
                    this.grpcModuleM = new GrpcModuleM(this.projectId);
                }
                break;
        }
        pushKey({activeKey});
    }

    // 获取部门项目信息
    @Bind
    private async queryDepartmentAndProjectById(): Promise<void> {
        try {
            const params = {
                projectId: this.projectId
            };
            const result = await nsMockManageKoasApiManageModuleQueryDepartmentAndProjectByIdGet.remote(params);
            runInAction(() => {
                this.departmentIds = result?.departmentIds || [];
                this.departmentFullName = result?.departmentNames?.join('/') || '';
                this.projectName = result?.projectName || '';
            });
        } catch {}
    }

    // 设置部门
    @action.bound
    public setDepartment() {
        if (this.departmentIds.length && this.departmentFullName) {
            departmentCascader.setDepartment(this.departmentIds, this.departmentFullName);
        }
    }
}
