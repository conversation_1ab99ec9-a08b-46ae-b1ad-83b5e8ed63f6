import React from 'react';
import { changeStatusEnum, changeStatusDesc } from '../../src/pages/versionComparison/configure';
import css from './VersionComparison.less';

interface IProps {
    checkedValues: number[];
    paramsNums: number[];
    onChange?(checkedValues: number[]): void;
}

interface IBtnProps {
    checkedValue: number;
    isChecked: boolean;
    cssClassName: string;
    paramsNum: number;
    onHighlight(checkedValue): void;
}

function ChangedStatusBtn(btnProps: IBtnProps) {
    const { checkedValue, isChecked, cssClassName, paramsNum, onHighlight } = btnProps;
    return (
        <span
            className={
                isChecked
                    ? `${cssClassName} ${css.boxShadow10}`
                    : cssClassName
            }
            onClick={(e) => {
                e.stopPropagation();
                onHighlight(checkedValue);
            }}
        >
            {`${changeStatusDesc[checkedValue]} ${paramsNum || 0} 个`}
        </span>
    );
}

export function ChangedStatusBtnGroup(props: IProps) {
    const { checkedValues, paramsNums, onChange } = props;
    const onHighlight = (checkedValue: number) => {
        let values: number[] = [];
        if (checkedValues.includes(checkedValue)) {
            values = checkedValues.filter(item => item !== checkedValue);
        } else {
            values = [...checkedValues, ...[checkedValue]];
        }
        onChange && onChange(values);
    };

    return (
        <div className={css.changedStatusBtnGroup}>
            <ChangedStatusBtn
                checkedValue={changeStatusEnum.ADD_PARAMS}
                isChecked={checkedValues.includes(changeStatusEnum.ADD_PARAMS)}
                cssClassName={css.addBtn}
                paramsNum={paramsNums[0]}
                onHighlight={onHighlight}
            />
            <ChangedStatusBtn
                checkedValue={changeStatusEnum.DELETE_PARAMS}
                isChecked={checkedValues.includes(changeStatusEnum.DELETE_PARAMS)}
                cssClassName={css.deleteBtn}
                paramsNum={paramsNums[1]}
                onHighlight={onHighlight}
            />
            {/* <ChangedStatusBtn
                checkedValue={changeStatusEnum.UPDATE_PARAMS}
                isChecked={checkedValues.includes(changeStatusEnum.UPDATE_PARAMS)}
                cssClassName={css.updateBtn}
                paramsNum={paramsNums[2]}
                onHighlight={onHighlight}
            /> */}
        </div>
    );
}
