import { AView } from 'libs';
import React from 'react';
import { observer } from 'mobx-react';
import { Bind } from 'lodash-decorators';
import { VersionBaseInfoM } from './VersionBaseInfoM';
import css from './VersionBaseInfo.less';
import { Collapse, Descriptions} from 'antd';
import { AvatarList } from '@/business/commonComponents/AvatarList/AvatarList';
import { apiStatusEnum } from '@/business/toolFun/ToolFun';

const { Panel } = Collapse;

@observer
export class VersionBaseInfo extends AView<VersionBaseInfoM> {

    public render() {
        const model = this.model;
        const gitUrl: string = `https://${model.gitUrl}/${model.branchName}`;
        return (
            <Descriptions column={2} className={css.baseInfoContent}>
                <Descriptions.Item label={'API名称'}>
                    <a onClick={model.openApiDetail}>{model.name}</a>
                </Descriptions.Item>
                <Descriptions.Item label={'PATH'}>
                    <span className={css.method}>{model.method}</span>{model.path}
                </Descriptions.Item>
                <Descriptions.Item label={'负责人'}>
                    <AvatarList reviewers={model.owners} />
                </Descriptions.Item>
                <Descriptions.Item label={'参与人'}>
                    <AvatarList reviewers={model.coopers} />
                </Descriptions.Item>
                <Descriptions.Item label={'分组'}>
                    <a onClick={model.openGroupDetail}>{model.groupName}</a>
                </Descriptions.Item>
                <Descriptions.Item label={'关联分支'}>
                    <a href={gitUrl} target={'_blank'}>{model.branchName}</a>
                </Descriptions.Item>
            </Descriptions>
        );
    }
}
