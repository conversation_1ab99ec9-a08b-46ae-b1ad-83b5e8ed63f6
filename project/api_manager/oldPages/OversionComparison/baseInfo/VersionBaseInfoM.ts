import { AViewModel } from 'libs';
import { observable, action, runInAction } from 'mobx';
import {
    nsMockManageKoasApiManageDiffGetDiffVOByChoiceForArGet, nsMockPermissionBatchGetUserGet
} from '@/remote';
import { Bind } from 'lodash-decorators';
import { ERouter } from 'CONFIG';
import { departmentCascader } from '@/business/global';

interface IUser {
    name: string;
    username: string;
    avatarUrl: string;
}

export class VersionBaseInfoM extends AViewModel {
    @observable public name: string = '';
    @observable public method: string = '';
    @observable public path: string = '';
    private owner: string = '';
    @observable public owners: IUser[] = [];
    private cooperList: string[] = [];
    @observable public coopers: IUser[] = [];
    @observable public groupName: string = '';
    @observable public branchName: string = '';
    public gitUrl: string = '';
    private departmentFullId: number[] = [];
    private departmentFullName: string = '';
    private groupId: number = 0;
    private moduleId: number = 0;
    private moduleName: string = '';
    private projectId: number = 0;
    private projectName: string = '';
    private currentDocId: number = 0;
    private apiType: number = 1;

    @action.bound
    public init(baseInfo: nsMockManageKoasApiManageDiffGetDiffVOByChoiceForArGet.IBaseInfo, apiType: number) {
        this.initData();
        this.apiType = apiType;
        this.name = baseInfo.name;
        this.method = baseInfo.method;
        this.path = baseInfo.path;
        this.groupName = baseInfo.groupName;
        this.branchName = baseInfo.branchName;
        this.gitUrl = baseInfo.gitUrl;
        this.departmentFullId = baseInfo.departmentFullId;
        this.departmentFullName = baseInfo.departmentFullName;
        this.groupId = baseInfo.groupId;
        this.moduleId = baseInfo.moduleId;
        this.moduleName = baseInfo.moduleName;
        this.projectId = baseInfo.projectId;
        this.projectName = baseInfo.projectName;
        this.currentDocId = baseInfo.currentDocId;
        this.owner = baseInfo.owner || '';
        this.cooperList = baseInfo?.cooperList || [];
        let usernames: string = baseInfo?.owner || '';
        if (this.cooperList.length) {
            if (usernames && !this.cooperList.includes(usernames)) {
                usernames = `${usernames},${this.cooperList.join(',')}`;
            } else {
                usernames = this.cooperList.join(',');
            }
        }
        this.batchGetUser(usernames);
    }

    @action.bound
    private initData(): void {
        this.apiType = 1;
        this.name = '';
        this.method = '';
        this.path = '';
        this.groupName = '';
        this.branchName = '';
        this.gitUrl = '';
        this.departmentFullId = [];
        this.departmentFullName = '';
        this.groupId = 0;
        this.moduleId = 0;
        this.moduleName = '';
        this.projectId = 0;
        this.projectName = '';
        this.currentDocId = 0;
        this.owner = '';
        this.owners = [];
        this.cooperList = [];
        this.coopers = [];
     }

     @Bind
     public openApiDetail(): void {
        departmentCascader.setDepartment(this.departmentFullId, this.departmentFullName);
        const apiDetailUrl: string = `${location.origin}${ERouter.API_MOCK_PROJECT_MODULEDETAIL}?groupSelectKey=${this.groupId}-${this.currentDocId}&moduleId=${this.moduleId}&projectId=${this.projectId}&showType=view`;
        const autoApiDetailUrl: string = `${location.origin}${ERouter.API_MOCK_HTTP_API_DETAIL}?apiId=${this.currentDocId}&moduleId=${this.moduleId}&moduleName=${this.moduleName}&branchName=${this.branchName}`;
        window.open(this.apiType === 1 ? apiDetailUrl : autoApiDetailUrl);
     }

     @Bind
     public openGroupDetail(): void {
        departmentCascader.setDepartment(this.departmentFullId, this.departmentFullName);
        const groupUrl: string = `${location.origin}${ERouter.API_MOCK_PROJECT_MODULEDETAIL}?groupSelectKey=${this.groupId}&moduleId=${this.moduleId}&projectId=${this.projectId}&showType=`;
        window.open(groupUrl);
     }

    // 获取头像
    @Bind
    private async batchGetUser(usernames: string) {
        if (!usernames) {
            return;
        }
        try {
            const params = {
                usernames: usernames
            };
            const result = await nsMockPermissionBatchGetUserGet.remote(params);
            runInAction(() => {
                result?.list.forEach(item => {
                    const it: IUser = {
                        name: item.name,
                        username: item.username,
                        avatarUrl: item?.photo || ''
                    };
                    if (this.cooperList.includes(it.username)) {
                        this.coopers = [...this.coopers, ...[it]];
                    }
                    if (this.owner === it.username) {
                        this.owners = [it];
                    }
                });
            });
        } catch (e) {
        }
    }
}
