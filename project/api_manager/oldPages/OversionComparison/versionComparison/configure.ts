interface IVersionIsDiffDesc {
    color: string;
    desc: string;
    coverTip: string;
}

export const versionIsDiffDesc: IVersionIsDiffDesc[] = [
    {
        color: '#33cc33',
        desc: '一致性校验通过',
        coverTip: '版本无变化，不支持一键覆盖'
    },
    {
        color: '#ff0000',
        desc: '一致性校验未通过，通过代码解析的API与人工编辑的API内容不一致，请修改代码或API文档，保证代码与API文档的一致性',
        coverTip: '一键将代码解析API覆盖到人工编辑API'
    }
];

export enum changeStatusEnum {
    UNUPDATE_PARAMS = 0,
    ADD_PARAMS = 1,
    DELETE_PARAMS = 2,
    UPDATE_PARAMS = 3
}

export const changeStatusDesc = {
    0: '未变更参数',
    1: '新增参数',
    2: '删除参数',
    3: '变更参数'
};

export interface IReqResParams {
    changedStatus: number;
    name: string;
    type: string;
    required: boolean;
}

export enum treeTypeEnum {
    LEFT_TREE = 'leftTree',
    RIGHT_TREE = 'rightTree'
}

export interface IParamsInfo {
    addParamsNum: number;
    deleteParamsNum: number;
    updateParamsNum: number;
}
