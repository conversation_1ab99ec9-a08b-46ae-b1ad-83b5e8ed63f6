.versionComparisonWrap {
  background-color: #FFFFFF;
  overflow: hidden;

  @backGrey: #f0f2f5;
  @borderColor: #D8DFE6;
  @blueColor: #327DFF;
  @addBackColor: rgba(54, 207, 162, 0.08);
  @deleteBackColor: rgba(255, 73, 64, .08);
  @updateBackColor: rgba(255, 161, 20, .08);

  .versionComparisonTop {
    padding: 0 24px;
    height: 49px;
    border-bottom: 1px solid @borderColor;
    font-size: 16px;
    font-weight: 700;
    background-color: @backGrey;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .versionComparisonTopTip::before {
      content: '：';
      color: #000000d9;
    }

    .versionComparisonTopOperate {
      display: flex;
      align-items: center;

      .kimOncall {
        margin-right: 18px;
      }
    }
  }

  .versionComparisonTitle {
    display: flex;
    // padding: 0 24px;
    height: 44px;
    background-color: @backGrey;
    border-bottom: 1px solid @borderColor;

    >div {
      width: 50%;
      line-height: 44px;

      .versionLabel() {
        padding: 4px 8px;
        margin-right: 8px;
        margin-left: 24px;
        border-radius: 4px;
      }

      .baseVersionLabel {
        .versionLabel();
        background: rgba(25, 178, 255, .08);
        color: #19b2ff;
      }

      .currentVersionLabel {
        .versionLabel();
        background: @deleteBackColor;
        color: #ff4940;
      }

      .version {
        font-size: 14px;
        font-weight: 600;
      }

      .updateCon {
        font-size: 12px;
        color: #7f7f7f;
        margin-left: 16px;
      }
    }

    >div:nth-of-type(1) {
      border-right: 1px solid @borderColor;
    }
  }

  .versionComparisonContent {
    margin: 24px;
    overflow: auto;
    height: calc(100% - 142px);

    .td() {
      background-color: transparent;
    }

    .addRowClassName {
      background-color: @addBackColor;

      td {
        background-color: @addBackColor;
      }
    }

    .updateRowClassName {
      background-color: @updateBackColor;

      td {
        background-color: @updateBackColor;
      }
    }

    .deleteRowClassName {
      background-color: @deleteBackColor;

      td {
        background-color: @deleteBackColor;
        text-decoration: line-through;
      }
    }
  }

  .versionComparisonCollapse {
    margin-bottom: 24px;
    border: 1px solid #d9d9d9;
    border-radius: 4px;

    .collapsePanel {
      display: flex;
      justify-content: space-between;
      margin: 8px 0 24px;

      .leftTable {
        width: 50%;
        margin-right: 24px;
      }

      .rightTable {
        width: 50%;
        margin-left: 24px;
      }

      // .indentBorder {
      //   :global {
      //     .ant-table-row-indent {
      //       height: 100%;
      //       border-right: 1px solid #d9d9d9;
      //     }
      //   }
      // }

      // :global {
      //   .ant-table .ant-table-container .ant-table-tbody .ant-table-cell {
      //     word-break: break-all;
      //   }

      //   .ant-table .ant-table-container .ant-table-tbody .ant-table-row>td:nth-of-type(1) {
      //     padding: 0 12px !important;
      //   }

      //   .ant-table-row-indent+.ant-table-row-expand-icon {
      //     margin-top: 10px;
      //   }

      //   .ant-table-row-indent {
      //     height: 100%;
      //     border-right: 1px solid #d9d9d9;
      //   }
      // }
    }

    :global {
      .ant-collapse-item>.ant-collapse-content>.ant-collapse-content-box {
        padding: 0 24px;
      }

      .ant-collapse-item>.ant-collapse-header {
        padding: 24px 24px 24px 40px;
      }
    }
  }
}

.changedStatusBtnGroup{
  .btn() {
    margin-left: 8px;
    padding: 4px 8px;
    cursor: pointer;
    border-radius: 4px;
  }

  .addBtn {
    .btn();
    color: #36CFA2;
    background-color: rgba(54, 207, 162, .08);
    margin-left: 0;
  }

  .updateBtn {
    .btn();
    color: #FFA114;
    background-color: rgba(255, 73, 64, .08);
  }

  .deleteBtn {
    .btn();
    color: #FF4940;
    background-color: rgba(255, 161, 20, .08);
  }

  .boxShadow10 {
    box-shadow: 0 0 10px;
  }
}