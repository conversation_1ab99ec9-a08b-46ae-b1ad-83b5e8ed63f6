import APageModel from '@/pages/APageModel';
import { action, computed, observable, runInAction } from 'mobx';
import {
    nsMockManageKoasApiManageDiffGetDiffVOByAutomicIdGet, nsMockManageKoasApiManageDiffGetDiffVOByArGet,
    nsMockManageKoasApiManageDiffGetDiffVOByChoiceForAutoGet,
    nsMockManageKoasApiManageDiffGetDiffVOByChoiceForArGet
} from '@/remote';
import { Bind } from 'lodash-decorators';
import { VersionBaseInfoM } from './baseInfo/VersionBaseInfoM';
import { ConfirmCoverM } from './confirmCover/ConfirmCoverM';
import { IParamsInfo } from './versionComparison/configure';

export interface IQuery {
}

export class VersionComparisonM extends APageModel<IQuery> {
    @observable public artificailId: number = 0;
    @observable public automicId: number = 0;
    @observable public baseVersionId: string = '';
    @observable public currentVersionId: string = '';
    @observable public type: number = 0; // 1：手动保存 2：代码生成 0：其他
    private currentDocId: number = 0;
    @observable public leftTree: any = {};
    @observable public rightTree: any = {};
    @observable public reqParamsInfo: IParamsInfo = {} as IParamsInfo;
    @observable public resParamsInfo: IParamsInfo = {} as IParamsInfo;
    @observable public requestStatus: number[] = [1, 2, 3];
    @observable public responseStatus: number[] = [1, 2, 3];

    public versionBaseInfoM = new VersionBaseInfoM();
    public confirmCoverM = new ConfirmCoverM();

    @computed
    public get isDiff(): number {
        const reqParamsInfo = this.reqParamsInfo;
        const resParamsInfo = this.resParamsInfo;
        if (reqParamsInfo.addParamsNum || reqParamsInfo.deleteParamsNum || reqParamsInfo.updateParamsNum
            || resParamsInfo.addParamsNum || resParamsInfo.deleteParamsNum || resParamsInfo.updateParamsNum) {
            return 1;
        }
        return 0;
    }

    protected getQueryFields(): any {
        return [
            'artificailId',
            'automicId',
            'baseVersionId',
            'currentVersionId',
            'type'
        ];
    }

    constructor(query: IQuery) {
        super(query);
        this.initByQueryFields(query);
        this.init();
    }

    @action
    private init() {
        this.artificailId = Number(this.artificailId);
        this.automicId = Number(this.automicId);
        this.type = Number(this.type);
        if (this.artificailId) {
            this.getDiffVOByAr();
        }
        if (this.automicId) {
            this.getDiffVOByAutomicId();
        }
        if (this.type === 1) {
            this.getDiffVOByChoiceForAr();
        }
        if (this.type === 2) {
            this.getDiffVOByChoiceForAuto();
        }
    }

    @Bind
    public onOpenConfirmCover(): void {
        this.confirmCoverM.init(this.currentDocId, {
            reqParamsInfo: this.reqParamsInfo,
            resParamsInfo: this.resParamsInfo
        });
    }

    @action.bound
    public onChangeStatus(statusType: string, checkedValues: number[]): void {
        this[statusType] = checkedValues;
    }

    @action
    protected getResult(result) {
        let apiType: number = 1;
        if (this.automicId || this.type === 2) {
            apiType = 2;
        }
        result?.baseInfo && this.versionBaseInfoM.init(result?.baseInfo, apiType);
        this.currentDocId = result?.baseInfo?.currentDocId || 0;
        this.leftTree = result?.leftTree;
        this.rightTree = result?.rightTree;
        this.reqParamsInfo = this.rightTree?.request?.paramsInfo || {};
        this.resParamsInfo = this.rightTree?.response?.paramsInfo || {};
    }

    // 根据人工文档ID查询自动解析文档与人工编辑文档参数变化详情
    @action.bound
    public async getDiffVOByAr() {
        try {
            const params = {
                artificailId: this.artificailId
            };
            const result = await nsMockManageKoasApiManageDiffGetDiffVOByArGet.remote(params);
            runInAction(() => {
                this.getResult(result);
            });
        } catch (e) {
        }
    }

    // 根据自动解析文档ID查询自动解析文档与人工编辑文档的参数变化
    @action.bound
    public async getDiffVOByAutomicId() {
        try {
            const params = {
                automicId: this.automicId
            };
            const result = await nsMockManageKoasApiManageDiffGetDiffVOByAutomicIdGet.remote(params);
            runInAction(() => {
                this.getResult(result);
            });
        } catch (e) {
        }
    }

    // 查询两个人工版本之间的变更参数变化详情
    @action.bound
    public async getDiffVOByChoiceForAr() {
        try {
            const params = {
                baseVersionId: this.baseVersionId,
                currentVersionId: this.currentVersionId
            };
            const result = await nsMockManageKoasApiManageDiffGetDiffVOByChoiceForArGet.remote(params);
            runInAction(() => {
                this.getResult(result);
            });
        } catch (e) {
        }
    }

    // 查询两个自动版本之间的变更参数变化详情
    @action.bound
    public async getDiffVOByChoiceForAuto() {
        try {
            const params = {
                baseVersionId: this.baseVersionId,
                currentVersionId: this.currentVersionId
            };
            const result = await nsMockManageKoasApiManageDiffGetDiffVOByChoiceForAutoGet.remote(params);
            runInAction(() => {
                this.getResult(result);
            });
        } catch (e) {
        }
    }

}
