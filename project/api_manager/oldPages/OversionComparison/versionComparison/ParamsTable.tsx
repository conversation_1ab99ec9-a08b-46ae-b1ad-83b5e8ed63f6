import { AView } from 'libs';
import React from 'react';
import { observer } from 'mobx-react';
import { Bind } from 'lodash-decorators';
import { VersionComparisonM } from '../VersionComparisonM';
import css from './VersionComparison.less';
import { Table } from 'antd';
import { changeStatusEnum, IReqResParams } from './configure';

interface IProps {
    dataSource: IReqResParams[];
    className: string;
    type: 'request' | 'response';
    rowClassName?: any;
    defaultExpandedRowKeys?: string[];
}

@observer
export class ParamsTable extends AView<VersionComparisonM, IProps> {

    @Bind
    private columns(): any[] {
        const column = [
            {
                title: '参数',
                dataIndex: 'name',
                key: 'name'
            },
            {
                title: '类型',
                dataIndex: 'type',
                key: 'type',
                width: 120
            }
        ];
        return column;
    }

    @Bind
    protected rowClassName(record, type: string): string {
        const model = this.model;
        if (record.changedStatus === changeStatusEnum.ADD_PARAMS
            && model[`${type}Status`].includes(changeStatusEnum.ADD_PARAMS)) {
            return css.addRowClassName;
        }
        if (record.changedStatus === changeStatusEnum.DELETE_PARAMS
            && model[`${type}Status`].includes(changeStatusEnum.DELETE_PARAMS)) {
            return css.deleteRowClassName;
        }
        if (record.changedStatus === changeStatusEnum.UPDATE_PARAMS
            && model[`${type}Status`].includes(changeStatusEnum.UPDATE_PARAMS)) {
            return css.updateRowClassName;
        }
        return '';
    }

    public render() {
        const model = this.model;
        return (
            <Table
                columns={this.columns()}
                bordered
                rowClassName={record => this.rowClassName(record, this.props.type)}
                rowKey={'key'}
                pagination={false}
                defaultExpandAllRows
                {...this.props}
            />
        );
    }
}
