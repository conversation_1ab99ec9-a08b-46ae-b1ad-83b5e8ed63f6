import React from 'react';
import APage from '@/pages/APage';
import { observer } from 'mobx-react';
import { IQuery, VersionComparisonM } from './VersionComparisonM';
import css from './VersionComparison.less';
import { Bind } from 'lodash-decorators';
import { <PERSON><PERSON>, Tooltip, Collapse } from 'antd';
import { versionIsDiffDesc, treeTypeEnum } from '../../src/pages/versionComparison/configure';
import { KdevIconFont } from '@/business/commonComponents/KdevIconFont';
import { KdevTitle } from '@/business/commonComponents/commonTitle/CommonTitle';
import { ParamsTable } from '../../src/pages/versionComparison/ParamsTable';
import { VersionBaseInfo } from './baseInfo/VersionBaseInfo';
import { ConfirmCover } from './confirmCover/ConfirmCover';
import { ChangedStatusBtnGroup } from './ChangedStatusBtn';
import { <PERSON><PERSON><PERSON>all } from 'oldPages/OversionComparison/kimOncall/KimOncall';
import { team } from '@/business/global';
import { formatKey1 } from '@/index.config/tools';
import { eachNode } from '@libs/utils';
import { toJS } from 'mobx';

const { Panel } = Collapse;

@observer
export default class VersionComparison extends APage<IQuery, VersionComparisonM> {
    protected createModel(): VersionComparisonM {
        return new VersionComparisonM(this.query);
    }

    @Bind
    private renderVersionTop(): React.ReactNode {
        const model = this.model;
        return (
            <div className={css.versionComparisonTop}>
                <span>
                    API版本对比
                    {
                        model.artificailId > 0 &&
                        <span
                            className={css.versionComparisonTopTip}
                            style={{ color: versionIsDiffDesc[model.isDiff]?.color }}
                        >
                            {versionIsDiffDesc[model.isDiff]?.desc}
                        </span>
                    }
                </span>
                <div className={css.versionComparisonTopOperate}>
                    <KimOncall userName={team.getUserInfo().userName} className={css.kimOncall} />
                    {
                        model.artificailId > 0 &&
                        <Tooltip title={versionIsDiffDesc[model.isDiff]?.coverTip} placement="bottomRight">
                            <span>
                                <Button
                                    type="primary"
                                    disabled={!model.isDiff}
                                    onClick={model.onOpenConfirmCover}
                                >
                                    一键覆盖API文档
                                </Button>
                                <KdevIconFont id="#iconquestion" />
                            </span>
                        </Tooltip>
                    }
                </div>
            </div>
        );
    }

    @Bind
    private renderVersionTitle(treeType: string): React.ReactNode {
        const { version, updateUser, undateTime } = this.model[treeType];
        const title: string = treeType === treeTypeEnum.LEFT_TREE ? '基准版本' : '对比版本';
        return (
            <div>
                <span className={css.baseVersionLabel}>{title}</span>
                <span className={css.version}>{version}</span>
                <span className={css.updateCon}>
                    {`更新人：${updateUser || ''} ｜ 更新时间： ${undateTime || ''}`}
                </span>
            </div>
        );
    }

    @Bind
    private renderBaseInfo(): React.ReactNode {
        return (
            <Collapse className={css.versionComparisonCollapse} defaultActiveKey={1} ghost>
                <Panel header={'API基本信息'} key={1}>
                    <VersionBaseInfo model={this.model.versionBaseInfoM} />
                </Panel>
            </Collapse>
        );
    }

    @Bind
    private renderOperate(type: string): React.ReactNode {
        const model = this.model;
        let paramsInfo: any = {};
        if (type === 'request') {
            paramsInfo = model.reqParamsInfo;
        }
        if (type === 'response') {
            paramsInfo = model.resParamsInfo;
        }
        return (
            <ChangedStatusBtnGroup
                paramsNums={[paramsInfo.addParamsNum, paramsInfo.deleteParamsNum, paramsInfo.updateParamsNum]}
                checkedValues={model[`${type}Status`]}
                onChange={(checkedValues) => {
                    model.onChangeStatus(`${type}Status`, checkedValues);
                }}
            />
        );
    }

    @Bind
    private getExpandedKeys(key: string): string[] {
        const keyList = key.split('-');
        const keys: string[] = [];
        while (keyList.length > 1) {
            keyList.pop();
            keys.push(keyList.join('-'));
        }
        return keys;
    }

    @Bind
    private renderParamsTable(title: string, dataSource: any[], type: 'request' | 'response'): React.ReactNode {
        const model = this.model;
        // const leftDataSource: any[] = formatKey1(dataSource[0])[0];
        const rightDataSource: any[] = formatKey1(dataSource[1])[0];
        const expandedKeys: string[] = [];
        eachNode(rightDataSource, item => {
            if (item.changedStatus) {
                const keys = this.getExpandedKeys(item.key);
                keys.forEach(e => {
                    if (!expandedKeys.includes(e)) {
                        expandedKeys.push(e);
                    }
                });
            }
            return false;
        });
        return (
            <>
                <KdevTitle
                    text={title}
                    size={'small'}
                // extra={this.renderKdevTitleExtra('FORM')}
                />
                <div className={css.collapsePanel}>
                    <ParamsTable
                        dataSource={dataSource[0]}
                        className={css.leftTable}
                        type={type}
                        model={model}
                        rowClassName={false}
                    // defaultExpandedRowKeys={expandedKeys}
                    />
                    <ParamsTable
                        dataSource={rightDataSource}
                        className={css.rightTable}
                        type={type}
                        model={model}
                        defaultExpandedRowKeys={expandedKeys}
                    />
                </div>
            </>
        );
    }

    @Bind
    private renderRequestParams(): React.ReactNode {
        const model = this.model;
        const leftRequestData = model.leftTree?.request?.requestData || {};
        const rightRequestData = model.rightTree?.request?.requestData || {};
        const leftHeaderSet = leftRequestData.headerSet || [];
        let leftBodyParam = leftRequestData.bodyParam
            ? [JSON.parse(JSON.stringify(leftRequestData.bodyParam))]
            : [];
        const leftFormSet = leftRequestData.formSet || [];
        if (leftFormSet.length > 0) {
            leftBodyParam = leftFormSet;
        }
        const leftQuerySet = leftRequestData.querySet || [];
        const leftPathSet = leftRequestData.pathSet || [];
        const rightHeaderSet = rightRequestData.headerSet || [];
        let rightBodyParam = rightRequestData.bodyParam
            ? [JSON.parse(JSON.stringify(rightRequestData.bodyParam))]
            : [];
        const rightFormSet = rightRequestData.formSet || [];
        if (rightFormSet.length > 0) {
            rightBodyParam = rightFormSet;
        }
        const rightQuerySet = rightRequestData.querySet || [];
        const rightPathSet = rightRequestData.pathSet || [];
        return (
            <Collapse className={css.versionComparisonCollapse} defaultActiveKey={1} ghost>
                <Panel header={'请求参数'} key={1} extra={this.renderOperate('request')}>
                    {
                        (leftHeaderSet.length > 0 || rightHeaderSet.length > 0) &&
                        model.type === 1 &&
                        this.renderParamsTable('Header参数', [leftHeaderSet, rightHeaderSet], 'request')
                    }
                    {
                        (leftBodyParam.length > 0 || rightBodyParam.length > 0) &&
                        this.renderParamsTable('Body参数', [leftBodyParam, rightBodyParam], 'request')
                    }
                    {
                        (leftQuerySet.length > 0 || rightQuerySet.length > 0) &&
                        this.renderParamsTable('Query参数', [leftQuerySet, rightQuerySet], 'request')}
                    {
                        (leftPathSet.length > 0 || rightPathSet.length > 0) &&
                        this.renderParamsTable('Path参数', [leftPathSet, rightPathSet], 'request')
                    }
                </Panel>
            </Collapse>
        );
    }

    @Bind
    private renderResponseParams(): React.ReactNode {
        const model = this.model;
        const leftResponseData = model.leftTree?.response?.responseData || {};
        const rightResponseData = model.rightTree?.response?.responseData || {};
        const leftHeaderSet = leftResponseData.headerSet || [];
        const leftBodyParam = leftResponseData.bodyParam
            ? [JSON.parse(JSON.stringify(leftResponseData.bodyParam))]
            : [];
        const rightHeaderSet = rightResponseData.headerSet || [];
        const rightBodyParam = rightResponseData.bodyParam
            ? [JSON.parse(JSON.stringify(rightResponseData.bodyParam))]
            : [];
        return (
            <Collapse className={css.versionComparisonCollapse} defaultActiveKey={1} ghost>
                <Panel header={'返回参数'} key={1} extra={this.renderOperate('response')}>
                    {
                        (leftHeaderSet.length > 0 || rightHeaderSet.length > 0) &&
                        model.type === 1 &&
                        this.renderParamsTable('Header参数', [leftHeaderSet, rightHeaderSet], 'response')
                    }
                    {
                        (leftBodyParam.length > 0 || rightBodyParam.length > 0) &&
                        this.renderParamsTable('Body参数', [leftBodyParam, rightBodyParam], 'response')
                    }
                </Panel>
            </Collapse>
        );
    }

    public renderContent(): React.ReactNode {
        const model = this.model;
        return (
            <div className={css.versionComparisonWrap}>
                {this.renderVersionTop()}
                <ConfirmCover model={model.confirmCoverM} />
                <div className={css.versionComparisonTitle}>
                    {this.renderVersionTitle(treeTypeEnum.LEFT_TREE)}
                    {this.renderVersionTitle(treeTypeEnum.RIGHT_TREE)}
                </div>
                <div className={css.versionComparisonContent}>
                    {this.renderBaseInfo()}
                    {this.renderRequestParams()}
                    {this.renderResponseParams()}
                </div>
            </div>
        );
    }
}
