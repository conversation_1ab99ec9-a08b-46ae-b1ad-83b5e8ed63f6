import { AViewModel } from 'libs';
import { observable, action, runInAction } from 'mobx';
import { nsMockManageKoasApiManageDiffOneClickCoverageArPost } from '@/remote';
import { Bind } from 'lodash-decorators';
import { message } from 'antd';

export class ConfirmCoverM extends AViewModel {
    @observable public visible: boolean = false;
    // @observable public checkedValues: number[] = [1, 2, 3];
    @observable public reqParamsInfo: number[] = [];
    @observable public resParamsInfo: number[] = [];
    @observable public reqChecked: boolean = true;
    @observable public resChecked: boolean = true;
    @observable public baseDocId: number = 0;
    @observable public loading: boolean = false;

    @action.bound
    public init(baseDocId: number, paramsInfo): void {
        this.baseDocId = baseDocId;
        this.visible = true;
        const { reqParamsInfo, resParamsInfo } = paramsInfo;
        this.reqParamsInfo = [reqParamsInfo.addParamsNum, reqParamsInfo.deleteParamsNum, reqParamsInfo.updateParamsNum];
        this.resParamsInfo = [resParamsInfo.addParamsNum, resParamsInfo.deleteParamsNum, resParamsInfo.updateParamsNum];
    }

    // @action.bound
    // public onChangeCheckedValues(checkedValues: number[]): void {
    //     this.checkedValues = checkedValues;
    // }

    @action.bound
    public onCloseConfirmCover(): void {
        this.visible = false;
        this.reqParamsInfo = [];
        this.resParamsInfo = [];
    }

    @action.bound
    public onChangeReqResChecked(key: string, value: boolean): void {
        this[key] = value;
    }

    // 一键覆盖
    @Bind
    public async oneClickCoverageAr(): Promise<void> {
        runInAction(() => this.loading = true);
        try {
            let type: number = 0;
            if (this.reqChecked && !this.resChecked) {
                type = 1;
            }
            if (!this.reqChecked && this.resChecked) {
                type = 2;
            }
            const params = {
                baseDocId: this.baseDocId,
                type
            };
            await nsMockManageKoasApiManageDiffOneClickCoverageArPost.remote(params);
            message.success('版本覆盖成功， 请查看最新API文档');
            this.onCloseConfirmCover();
            runInAction(() => this.loading = false);
        } catch {
            runInAction(() => this.loading = false);
        }
    }
}
