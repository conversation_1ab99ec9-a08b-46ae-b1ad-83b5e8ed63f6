import { AView } from 'libs';
import React from 'react';
import { observer } from 'mobx-react';
// import { Bind } from 'lodash-decorators';
import { ConfirmCoverM } from './ConfirmCoverM';
import css from './ConfirmCover.less';
import { Checkbox, Modal } from 'antd';
import { ChangedStatusBtnGroup } from '../ChangedStatusBtn';

@observer
export class ConfirmCover extends AView<ConfirmCoverM> {

    public render() {
        const model = this.model;
        return (
            <Modal
                title="选择同步内容并确认"
                visible={model.visible}
                className={css.confirmCoverModal}
                onCancel={model.onCloseConfirmCover}
                onOk={model.oneClickCoverageAr}
                okButtonProps={{
                    disabled: !model.reqChecked && !model.resChecked,
                    loading: model.loading
                }}
            >
                <Checkbox
                    checked={model.reqChecked}
                    className={css.reqResCheckbox}
                    onChange={(e) => model.onChangeReqResChecked('reqChecked', e.target.checked)}
                >
                    请求参数
                </Checkbox>
                <ChangedStatusBtnGroup
                    checkedValues={[1, 2, 3]}
                    paramsNums={model.reqParamsInfo}
                    // onChange={model.onChangeCheckedValues}
                />
                <Checkbox
                    checked={model.resChecked}
                    className={css.reqResCheckbox}
                    onChange={(e) => model.onChangeReqResChecked('resChecked', e.target.checked)}
                >
                    返回参数
                </Checkbox>
                <ChangedStatusBtnGroup
                    checkedValues={[1, 2, 3]}
                    paramsNums={model.resParamsInfo}
                    // onChange={model.onChangeCheckedValues}
                />
            </Modal>
        );
    }
}
