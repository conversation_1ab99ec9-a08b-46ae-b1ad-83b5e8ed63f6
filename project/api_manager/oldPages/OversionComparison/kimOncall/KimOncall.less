.oncallList{
    width: 160px;
    :global{
      .ant-list-item-meta{
        align-items: center;
      }
      .ant-list-item-meta-title{
        margin-bottom: 0;
      }
    }
  }
  .oncallPopContent{
    .noInfo {
      padding: 12px 16px;
      width: 122px;
      text-align: center;
      background-clip: padding-box;
      background: rgb(255, 255, 255);
      border-radius: 2px;
      outline: none;
      -webkit-box-shadow: 0 3px 6px -4px rgba(0, 0, 0, 0.12), 0 6px 16px 0 rgba(0, 0, 0, 0.08), 0 9px 28px 8px rgba(0, 0, 0, 0.05);
      box-shadow: 0 3px 6px -4px rgba(0, 0, 0, 0.12), 0 6px 16px 0 rgba(0, 0, 0, 0.08), 0 9px 28px 8px rgba(0, 0, 0, 0.05);
    }
  
    .oncallItemContent{
      margin-left: 8px;
      vertical-align: middle;
    }
  
    :global{
      .ant-dropdown-menu-item, .ant-dropdown-menu-submenu-title{
        padding: 12px;
      }
      .ant-dropdown-menu-item-active {
        background: #F5F7FA;
      }
    }
  }
  
  