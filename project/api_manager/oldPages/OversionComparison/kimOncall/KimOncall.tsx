import * as React from 'react';
import { RealDOM } from '@libs/utils';
import { Avatar, Menu, Dropdown } from 'antd';
import css from './KimOncall.less';
import classnames from 'classnames';
import { Bind } from 'lodash-decorators';
import { inviteUserIntoGroup, nsOpenApiInviteUser } from '@/remote';

interface IProps {
    userName: string;
    className?: string;
    style?: React.CSSProperties;
}

interface IOncallList {
    groupId?: string;
    avatar?: string;
    userName?: string;
    name?: string;
}

export class KimOncall extends React.Component<IProps, any> {
    public domKimOncall = new RealDOM<HTMLElement>();
    private oncallList: IOncallList[] = [
        {
            groupId: '2217918',
            avatar: 'https://cdnfile.corp.kuaishou.com/kc/files/a/QA-SERVEREE-FE-IN/menuIcon/qrcode.png'
        },
        {
            userName: 'liufei13',
            name: '刘飞',
            avatar: 'https://static.yximgs.com/udata/pkg/KS-IS-AVATAR-PRODUCTION/49283/0927D26D0DBC9645A81154F2E77C04F9_compressed_100'
        },
        {
            userName: 'xuning',
            name: '徐宁',
            avatar: 'https://static.yximgs.com/udata/pkg/KS-IS-AVATAR-PRODUCTION/20561/D162242DCBF3450F9E8C798D58DF660E_compressed_100'
        },
        {
            userName: 'yinwenjing03',
            name: '尹文静',
            avatar: 'https://static.yximgs.com/udata/pkg/KS-IS-AVATAR-PRODUCTION/35438/C168D6531FC2D2E583120D6135FAD698_compressed_100'
        }
    ];

    @Bind
    private onOpenOncall(item): void {
        if (item.groupId) {
            // window.open(`kim://thread?type=4&id=${item.groupId}`);
            inviteUserIntoGroup(item.groupId, this.props.userName);
            // this.inviteUserIntoGroup(item.groupId, this.props.userName);
        } else {
            window.open(`kim://username?username=${ item.userName }`, '_self');
        }
    }

    private renderOncallUser(item): React.ReactNode {
        return (
            <Menu.Item
                key={ item.groupId || item.userName }
                icon={ <Avatar size={ 20 } src={ item.avatar } /> }
                onClick={ () => this.onOpenOncall(item) }
            >
                <span className={ css.oncallItemContent }>
                    { item.groupId ? '进入用户群' : item.name }
                </span>
            </Menu.Item>
        );
    }

    public renderOncallList(): React.ReactElement {
        return this.oncallList && this.oncallList.length > 0
            ? <Menu>{ this.oncallList.map(item => this.renderOncallUser(item)) }</Menu>
            : <div className={ css.noInfo }>暂无数据</div>;
    }

    public render(): React.ReactElement {
        return (
            <div
                style={ { ...this.props.style } }
                className={ classnames(this.props.className) }
                ref={ this.domKimOncall.setDom }>
                <Dropdown
                    overlay={ this.renderOncallList() }
                    placement="bottomCenter"
                    getPopupContainer={ () => this.domKimOncall.dom || document.body }
                    arrow
                    overlayClassName={ css.oncallPopContent }
                >
                    <img src={ 'https://cdnfile.corp.kuaishou.com/kc/files/a/QA-SERVEREE-FE-IN/kim.png' } style={ {
                        height: '16px',
                        cursor: 'pointer'
                    } } />
                </Dropdown>
            </div>
        );
    }
}
