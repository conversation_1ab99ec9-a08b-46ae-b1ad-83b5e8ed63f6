import React from 'react';
import APage from '@/pages/APage';
import { SceneMgrM, IQuery } from './SceneMgrM';
import { observer } from 'mobx-react';
import css from './SceneMgr.less';
import { Input, Radio, Button, Table, Tooltip, Modal } from 'antd';
import Bind from 'lodash-decorators/bind';
import { bindObserver } from '@libs/mvvm';
import { KdevIconFont } from '@/business/commonComponents/KdevIconFont';
import { statusMap } from './enum';
import { CreateScene } from './createSceneModal/CreateScene';
import { QRCode } from 'oldPages/OrganizeSpace/QRCode/QRCode';

const { Search } = Input;

const Input_key = bindObserver(Search, 'key');
const RadioGroup_filter = bindObserver(Radio.Group, 'filter');

@observer
export default class SceneMgr extends APage<IQuery, SceneMgrM> {
    protected createModel(): SceneMgrM {
        return new SceneMgrM(this.query);
    }

    @Bind
    public columns(): any[] {
        const model = this.model;
        const columns = [
            {
                title: '场景名称',
                // dataIndex: 'name',
                key: 'name',
                render: record => <a onClick={() => model.onOpenAScene(record)}>{record.name}</a>
            },
            {
                title: '创建人',
                dataIndex: 'createUser',
                key: 'createUser',
                width: 150,
            },
            {
                title: '描述',
                dataIndex: 'desc',
                key: 'desc',
            },
            {
                title: '状态',
                // dataIndex: 'status',
                key: 'status',
                width: 120,
                render: record => this.renderColumnsStatus(record)
            },
            {
                title: '操作',
                // dataIndex: 'operation',
                key: 'operation',
                align: 'right',
                width: 120,
                render: record => this.renderOperation(record)
            }
        ];
        return columns;
    }

    @Bind()
    private renderColumnsStatus(record): React.ReactNode {
        const model = this.model;
        const status = record.status;
        const text = statusMap(status)?.text;
        const icon = statusMap(status)?.icon;
        const color = statusMap(status)?.color;
        const clickBtnEve = () => {
            !status ? model.startScene(record.sceneId) : model.stopScene(record.sceneId);
        };
        return (
            <div
                className={css.columnsStatusBox}
                onClick={clickBtnEve}
            >
                <KdevIconFont
                    id={icon}
                    className={css.statusIcon}
                    style={{ color }}
                />
                {text}
            </div>
        );
    }

    @Bind
    private renderOperation(record): React.ReactNode {
        const model = this.model;
        return (
            <div className={css.operation}>
                <Tooltip title={'编辑场景'}>
                    <Button
                        icon={<KdevIconFont id={'#iconxingzhuangjiehe'} />}
                        onClick={() => model.onOpenCreateScene(record)}
                    />
                </Tooltip>
                <Tooltip
                    placement={'topRight'}
                    title={record.canDelete ? '删除场景' : '包含API不能删除，如想删除，请先删除API'}
                >
                    <Button
                        icon={<KdevIconFont id={'#iconyanse'} />}
                        onClick={() => this.onDeleteScene(record)}
                        disabled={!record.canDelete}
                    />
                </Tooltip>
            </div>
        );
    }

    @Bind
    private onDeleteScene(record): void {
        Modal.confirm({
            content: '删除后将无法恢复，确认删除场景？',
            onOk: () => this.model.deleteScene(record.sceneId)
        });
    }

    @Bind
    protected onChangeFilter(): void {
        this.push();
        this.model.onChangePageInfo(1);
    }

    @Bind
    private renderCreateBtn(): React.ReactNode {
        return (
            <Button
                type={'primary'}
                className={css.createBtn}
                onClick={() => this.model.onOpenCreateScene(null)}
            >
                新建场景
            </Button>
        );
    }

    @Bind
    private renderTop(): React.ReactNode {
        const model = this.model;
        return (
            <div className={css.top}>
                <RadioGroup_filter
                    className={css.filter}
                    model={model}
                    onChange={this.onChangeFilter}>
                    <Radio.Button value={1}>全部场景</Radio.Button>
                    <Radio.Button value={2}>我的场景</Radio.Button>
                </RadioGroup_filter>
                搜索：
                <Input_key
                    model={model}
                    className={css.key}
                    placeholder={'请输入关键字'}
                    onSearch={() => model.onChangePageInfo(1)}
                />
                {this.renderCreateBtn()}
                <Tooltip title={'App 重启后需要点击右侧二维码重新绑定'}>
                    <KdevIconFont id={'#iconquestion'} className={css.codeTooltip} />
                </Tooltip>
                <Tooltip title={'扫码绑定设备'}>
                    <Button
                        icon={<KdevIconFont id={'#iconerweima'} />}
                        onClick={() => model.onOpenQRCode(0)}
                        className={css.QRcodeBtn}
                    />
                </Tooltip>
            </div>
        );
    }

    @Bind
    private renderTable(): React.ReactNode {
        const model = this.model;
        return (
            <Table
                columns={this.columns()}
                dataSource={model.sceneList}
                rowKey={'sceneId'}
                loading={model.sceneListLoading}
                pagination={{
                    // size: 'small',
                    pageSize: model.pageSize,
                    current: model.currentPage,
                    showSizeChanger: true,
                    total: model.total,
                    showTotal: total => `共 ${total} 条`,
                    onChange: model.onChangePageInfo,
                }}
                bordered
                className={css.sceneList}
            />
        );
    }

    public renderContent(): React.ReactNode {
        const model = this.model;
        return (
            <div className={css.sceneMgrWrap}>
                {this.renderTop()}
                {this.renderTable()}
                <CreateScene model={model.createSceneM} />
                <QRCode model={model.QRCodeM} />
            </div>
        );
    }
}
