import APageModel from '@/pages/APageModel';
import { action, observable, runInAction } from 'mobx';
import { departmentCascader } from '@/business/global';
import { router } from '@libs/mvvm';
import { ERouter } from 'CONFIG';
import {
    nsMockManageKoasApiManageSceneQueryScenePageListPost, nsMockManageKoasApiManageSceneQueryMineScenePageListPost,
    nsMockManageKoasApiManageSceneStartScenePost, nsMockManageKoasApiManageSceneDeleteScenePost,
    nsMockManageKoasApiManageSceneStopScenePost, nsMockManageKoasApiManageSceneExistActiveSceneGet
} from '@/remote';
import { message } from 'antd';
import { CreateSceneM } from './createSceneModal/CreateSceneM';
import { QRCodeM } from 'oldPages/OrganizeSpace/QRCode/QRCodeM';
import { Bind } from 'lodash-decorators';

export interface IQuery {
}

interface ISceneList {
    id: number;
    name: string;
    desc: string;
    createUser: string;
    focus: boolean;
    status: number;
}

export class SceneMgrM extends APageModel<IQuery> {
    @observable public departmentId: number = departmentCascader.getDepartmentId();
    @observable public key: string = '';
    @observable public filter: number = 2; // 1：全部 2：我的
    @observable public currentPage: number = 1;
    @observable public pageSize: number = 10;
    @observable public total: number = 0;
    @observable public sceneList: ISceneList[] = [];
    @observable public sceneListLoading: boolean = false;

    public createSceneM: any;
    public QRCodeM = new QRCodeM();

    protected getQueryFields(): any {
        return ['filter'];
    }

    constructor(query: IQuery) {
        super(query);
        this.initByQueryFields(query);
        this.init();
    }

    @action.bound
    protected init(): void {
        this.filter = Number(this.filter);
        this.createSceneM = new CreateSceneM({ departmentId: this.departmentId });
        this.onChangePageInfo(1);
    }

    @action.bound
    public onOpenCreateScene(record): void {
        this.createSceneM.init(record);
        this.createSceneM.onSaveCallBack = this.onChangePageInfo;
    }

    @action.bound
    public onOpenAScene(record): void {
        router.push(ERouter.API_MOCK_ORGANIZESPACE_ASCENE, { id: record.sceneId, sceneName: record.name });
    }

    // 打开绑定设备二维码弹框
    @action.bound
    public onOpenQRCode(sceneId: number): void {
        this.QRCodeM.init(0, sceneId);
        this.QRCodeM.existActiveScene = this.existActiveScene;
        this.QRCodeM.onCloseQRCodeCallBack = this.onCloseQRCodeCallBack;
    }

    @action.bound
    public onCloseQRCodeCallBack(): void {
        this.onChangePageInfo(1);
    }

    // 分页
    @action.bound
    public onChangePageInfo(currentPage: number, pageSize?: number): void {
        this.currentPage = currentPage;
        this.pageSize = pageSize || this.pageSize;
        if (this.filter === 1) {
            this.queryScenePageList();
        }
        if (this.filter === 2) {
            this.queryMineScenePageList();
        }
    }

    // 获取全部场景列表
    @action.bound
    protected async queryScenePageList() {
        try {
            const params = {
                departmentId: this.departmentId,
                key: this.key,
                // filter: this.filter
                currentPage: this.currentPage,
                pageSize: this.pageSize
            };
            const result = await nsMockManageKoasApiManageSceneQueryScenePageListPost.remote(params);
            runInAction(() => {
                this.sceneList = result?.list || [];
                this.total = result?.total;
            });
        } catch (e) {
        }
    }

    // 获取我的场景列表
    @action.bound
    protected async queryMineScenePageList() {
        try {
            const params = {
                departmentId: this.departmentId,
                key: this.key,
                currentPage: this.currentPage,
                pageSize: this.pageSize
            };
            const result = await nsMockManageKoasApiManageSceneQueryMineScenePageListPost.remote(params);
            runInAction(() => {
                this.sceneList = result?.list || [];
                this.total = result?.total;
            });
        } catch (e) {
        }
    }

    // 启动场景
    @action.bound
    public async startScene(id: number) {
        try {
            const parmas = {
                id
            };
            const result = await nsMockManageKoasApiManageSceneStartScenePost.remote(parmas);
            runInAction(() => {
                if (result?.code !== 0) {
                    this.onOpenQRCode(id);
                }
                this.onChangePageInfo(this.currentPage);
            });
        } catch (e) {
        }
    }

    @action.bound
    public async stopScene(id: number) {
        try {
            const parmas = {
                id
            };
            await nsMockManageKoasApiManageSceneStopScenePost.remote(parmas);
            runInAction(() => {
                this.onChangePageInfo(this.currentPage);
            });
        } catch (e) {
        }
    }

    // 删除场景
    @action.bound
    public async deleteScene(id: number) {
        try {
            const params = {
                id
            };
            await nsMockManageKoasApiManageSceneDeleteScenePost.remote(params);
            runInAction(() => {
                message.success('删除成功～');
                this.onChangePageInfo(1);
            });
        } catch (e) {
        }
    }

    @Bind
    private async existActiveScene() {
        try {
            const result = await nsMockManageKoasApiManageSceneExistActiveSceneGet.remote();
            if (!result.result) {
                message.warn('暂无启用场景，如想使用请先开启一个场景');
            }
        } catch (e) {
        }
    }
}
