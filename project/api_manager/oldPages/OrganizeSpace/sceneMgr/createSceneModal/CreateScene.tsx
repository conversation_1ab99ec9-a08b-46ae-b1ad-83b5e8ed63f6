import { <PERSON>View } from 'libs';
import React from 'react';
import { CreateSceneM } from './CreateSceneM';
import { observer } from 'mobx-react';
import { Modal, Input } from 'antd';
import css from './CreateScene.less';
import { bindObserver } from '@libs/mvvm';

const { TextArea } = Input;

const Input_name = bindObserver(Input, 'name');
const TextArea_desc = bindObserver(TextArea, 'desc');

@observer
export class CreateScene extends AView<CreateSceneM> {

    public render(): React.ReactNode {
        const model = this.model;
        return (
            <Modal
                className={ css.createSceneModal }
                visible={ model.visible }
                title={ model.id ? '编辑场景' : '新建场景' }
                onCancel={ model.onCloseCreateScene }
                onOk={ model.checkParams }
                confirmLoading={ model.saveLoading }
            >
                <div className={ css.formLabel }>场景名称 <span className={ css.required }>*</span></div>
                <Input_name model={ model } placeholder={ '请输入场景名称' } />
                <div className={ css.formLabel }>描述信息</div>
                <TextArea_desc model={ model } placeholder={ '请输入描述信息' } />
            </Modal>
        );
    }
}
