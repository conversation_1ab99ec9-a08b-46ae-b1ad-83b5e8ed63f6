import { AViewModel } from 'libs';
import { observable, action, runInAction } from 'mobx';
import { message } from 'antd';
import {
    nsMockManageKoasApiManageSceneCreateScenePost, nsMockManageKoasApiManageSceneUpdateScenePost
} from '@/remote';

export class CreateSceneM extends AViewModel {
    @observable public departmentId: number = 0;
    @observable public visible: boolean = false;
    @observable public id: number = 0;
    @observable public name: string = '';
    @observable public desc: string = '';
    @observable public saveLoading: boolean = false;

    public onSaveCallBack?(currentPage?: number): void;

    constructor(query) {
        super();
        this.departmentId = query.departmentId;
    }

    @action.bound
    public init(record?): void {
        if (record) {
            this.id = record.sceneId;
            this.name = record.name;
            this.desc = record.desc;
        }
        this.visible = true;
    }

    @action.bound
    public onCloseCreateScene(): void {
        this.id = 0;
        this.name = '';
        this.desc = '';
        // this.departmentId = 0;
        this.visible = false;
    }

    @action.bound
    public checkParams() {
        if (!this.name) {
            message.warn('场景名称不能为空～');
            return;
        }
        this.id ? this.updateScene() : this.createScene();
    }

    @action.bound
    protected async createScene() {
        this.saveLoading = true;
        try {
            const params = {
                departmentId: this.departmentId,
                name: this.name,
                desc: this.desc
            };
            await nsMockManageKoasApiManageSceneCreateScenePost.remote(params);
            runInAction(() => {
                message.success('新建成功～');
                this.onCloseCreateScene();
                this.saveLoading = false;
                this.onSaveCallBack && this.onSaveCallBack(1);
            });
        } catch (e) {
            runInAction(() => {
                this.saveLoading = false;
            });
        }
    }

    @action.bound
    protected async updateScene() {
        this.saveLoading = true;
        try {
            const params = {
                id: this.id,
                name: this.name,
                desc: this.desc
            };
            await nsMockManageKoasApiManageSceneUpdateScenePost.remote(params);
            runInAction(() => {
                message.success('修改成功～');
                this.onCloseCreateScene();
                this.saveLoading = false;
                this.onSaveCallBack && this.onSaveCallBack();
            });
        } catch (e) {
            runInAction(() => {
                this.saveLoading = false;
            });
        }
    }
}
