.sceneMgrWrap {
  background-color: #fff;
  padding: 24px;
  overflow: auto;

  .top {
    margin-bottom: 24px;

    .filter {
      margin-right: 24px;
    }

    .key {
      width: 276px;
    }

    .createBtn {
      float: right;
    }

    .codeTooltip{
      float: right;
      height: 32px;
      font-size: 20px;
      margin: 6px 16px 0 0;
    }

    .QRcodeBtn {
      float: right;
      margin-right: 4px;
    }
  }

  .sceneList {
    .columnsStatusBox {
      display: flex;
      align-items: center;
      cursor: pointer;

      .statusIcon {
        font-size: 20px;
        margin-right: 4px;
      }
    }

    .operation {
      button {
        margin-right: 8px;
      }

      .focusBtn {
        background-color: #ffa114;
        border: 1px solid #eb9008;
        color: #ffffff;
      }
    }
  }

  .empty {
    padding-top: 140px;

    :global {
      .ant-empty-description {
        opacity: .25;
      }
    }
  }
}
