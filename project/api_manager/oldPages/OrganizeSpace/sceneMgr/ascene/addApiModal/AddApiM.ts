import { AViewModel } from 'libs';
import { observable, action, runInAction } from 'mobx';
import { Bind } from 'lodash-decorators';
import { message } from 'antd';
import {
    nsMockManageKoasApiManageSceneAddApiPost, nsMockManageKoasApiManageSceneTempQueryGet,
    nsMockManageKoasApiManageProjectQueryProjectListPost, nsMockManageKoasApiManageModuleQueryModulePageListPost,
    nsMockManageKoasApiManageFolderQueryFolderListGet, nsMockManageKoasApiManageFolderQueryDocListGet
} from '@/remote';

export class AddApiM extends AViewModel {
    @observable public departmentId: number = 0;
    @observable public type: number = 1;
    @observable public sceneId: number = 0;
    @observable public visible: boolean = false;
    @observable public saveLoading: boolean = false;
    @observable public apiId: number = 0;
    @observable public apiList: nsMockManageKoasApiManageSceneTempQueryGet.IList[] = [];
    @observable public projectId: number = 0;
    @observable public projectList: nsMockManageKoasApiManageProjectQueryProjectListPost.IProjectList[] = [];
    @observable public moduleId: number = 0;
    @observable public moduleList: nsMockManageKoasApiManageModuleQueryModulePageListPost.IModuleList[] = [];
    @observable public folderId: number = 0;
    @observable public folderList: nsMockManageKoasApiManageFolderQueryFolderListGet.IFolderList[] = [];
    @observable public folderApiId: number = 0;
    @observable public folderApiList: nsMockManageKoasApiManageFolderQueryDocListGet.IDocList[] = [];

    public onSaveCallBack?(): void;

    @action.bound
    public init(departmentId: number, sceneId: number): void {
        this.departmentId = departmentId;
        this.sceneId = sceneId;
        this.visible = true;
        // this.tempQuery('');
        this.queryProjectList('');
        this.queryFolderList();
    }

    @action.bound
    public onCloseCreateScene(): void {
        this.departmentId = 0;
        this.apiId = 0;
        this.visible = false;
    }

    // 选择项目
    @action.bound
    public onSelectProject(val): void {
        this.projectId = val;
        this.moduleId = 0;
        this.apiId = 0;
        this.moduleList = [];
        this.apiList = [];
        this.queryModulePageList('');
    }

    @action.bound
    public onSelectModule(val): void {
        this.moduleId = val;
        this.apiId = 0;
        this.apiList = [];
        this.tempQuery('');
    }

    @action.bound
    public onSelectApiId(val): void {
        this.apiId = val;
    }

    @action.bound
    public onSelectFolderId(val): void {
        this.folderId = val;
        this.folderApiId = 0;
        this.folderApiList = [];
        this.queryDocList();
    }

    @action.bound
    public onSelectFolderApiId(val): void {
        this.folderApiId = val;
    }

    @action.bound
    public checkParams(): void {
        if (this.apiId < 1 && this.type === 1) {
            message.warn('请选择API～');
            return;
        }
        if (this.folderApiId < 1 && this.type === 2) {
            message.warn('请选择API～');
            return;
        }
        this.addApi();
    }

    @action.bound
    protected async addApi(): Promise<void> {
        this.saveLoading = true;
        try {
            const params = {
                sceneId: this.sceneId,
                docId: this.type === 1 ? this.apiId : this.folderApiId,
            };
            await nsMockManageKoasApiManageSceneAddApiPost.remote(params);
            runInAction(() => {
                message.success('添加成功～');
                this.saveLoading = false;
                this.onCloseCreateScene();
                this.onSaveCallBack && this.onSaveCallBack();
            });
        } catch (e) {
            runInAction(() => {
                this.saveLoading = false;
            });
        }
    }

    // 获取api列表
    @action.bound
    public async tempQuery(key: string) {
        try {
            const params = {
                key,
                departmentId: this.departmentId,
                projectId: this.projectId,
                moduleId: this.moduleId
            };
            const result = await nsMockManageKoasApiManageSceneTempQueryGet.remote(params);
            runInAction(() => {
                this.apiList = result?.list || [];
            });
        } catch (e) {
        }
    }

    /**
     * 获取项目列表
     */
    @action.bound
    public async queryProjectList(key: string) {
        try {
            const params = {
                departmentId: this.departmentId,
                key,
                pageSize: 50,
                currentPage: 1,
                filter: 1,
            };
            const result = await nsMockManageKoasApiManageProjectQueryProjectListPost.remote(params);
            runInAction(() => {
                this.projectList = result.list || [];
            });
        } catch (e) {
        }
    }

    // 获取模块列表
    @action
    public async queryModulePageList(key: string) {
        try {
            const params = {
                projectId: this.projectId,
                key,
                filter: 1,
                pageSize: 50,
                currentPage: 1,
            };
            const result = await nsMockManageKoasApiManageModuleQueryModulePageListPost.remote(params);
            runInAction(() => {
                this.moduleList = result?.list || [];
            });
        } catch (e) {
        }
    }

    @Bind
    private async queryFolderList(): Promise<void> {
        try {
            const params = {
                departmentId: this.departmentId
            };
            const result = await nsMockManageKoasApiManageFolderQueryFolderListGet.remote(params);
            runInAction(() => {
                this.folderList = result?.folderList || [];
            });
        } catch {
        }
    }

    @Bind
    public async queryDocList(name: string = ''): Promise<void> {
        try {
            const params = {
                folderId: this.folderId,
                name,
                pageIndex: 1,
                pageSize: 50
            };
            const result = await nsMockManageKoasApiManageFolderQueryDocListGet.remote(params);
            runInAction(() => {
                this.folderApiList = result?.docList || [];
            });
        } catch {
        }
    }
}
