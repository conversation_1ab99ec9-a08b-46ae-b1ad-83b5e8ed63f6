import { <PERSON>Vie<PERSON>, bindObserver } from 'libs';
import React from 'react';
import { AddApiM } from './AddApiM';
import { observer } from 'mobx-react';
import { Modal, Radio, Select } from 'antd';
import css from './AddApi.less';
import { <PERSON>bounce, Bind } from 'lodash-decorators';
import { typeRadioOptions } from './configure';

const { Option } = Select;

const RadioGroup_type = bindObserver(Radio.Group, 'type');
@observer
export class AddApi extends AView<AddApiM> {

    @Debounce(300)
    @Bind
    protected onSearchApiList(keyWord: string) {
        this.model.tempQuery(keyWord);
    }

    @Debounce(300)
    @Bind
    protected onSearchProject(keyWord: string): void {
        this.model.queryProjectList(keyWord);
    }

    @Debounce(300)
    @Bind
    protected onSearchModule(keyWord: string): void {
        this.model.queryModulePageList(keyWord);
    }

    @Debounce(300)
    @Bind
    private onSearchFolderApiList(keyWord: string): void {
        this.model.queryDocList(keyWord);
    }

    @Bind
    protected renderProjectSelect(): React.ReactNode {
        const model = this.model;
        return (
            <>
                <div className={css.formLabel}>选择项目 <span className={css.required}>*</span></div>
                <Select
                    className={css.width100}
                    showSearch
                    onSearch={this.onSearchProject}
                    filterOption={false}
                    placeholder={'请选择项目'}
                    value={model.projectId || undefined}
                    onSelect={model.onSelectProject}
                >
                    {
                        model.projectList.map(item => {
                            return <Option
                                value={item.projectId}
                                key={item.projectId}
                            >
                                {item.projectName}
                            </Option>;
                        })
                    }
                </Select>
            </>
        );
    }

    @Bind
    protected renderModuleSelect(): React.ReactNode {
        const model = this.model;
        return (
            <>
                <div className={css.formLabel}>选择模块 <span className={css.required}>*</span></div>
                <Select
                    className={css.width100}
                    showSearch
                    onSearch={this.onSearchModule}
                    filterOption={false}
                    placeholder={'请选择模块'}
                    value={model.moduleId || undefined}
                    onSelect={model.onSelectModule}
                    disabled={!model.projectId}
                >
                    {
                        model.moduleList.map(item => {
                            return <Option
                                value={item.moduleId}
                                key={item.moduleId}
                            >
                                {item.name}
                            </Option>;
                        })
                    }
                </Select>
            </>
        );
    }

    @Bind
    private renderApiSelect(): React.ReactNode {
        const model = this.model;
        return (
            <>
                <div className={css.formLabel}>选择API <span className={css.required}>*</span></div>
                <Select
                    value={model.apiId || undefined}
                    onChange={model.onSelectApiId}
                    // mode={ 'multiple' }
                    className={css.width100}
                    filterOption={false}
                    onSearch={this.onSearchApiList}
                    showSearch
                    placeholder={'请选择API'}
                    allowClear
                    disabled={!model.moduleId}
                >
                    {
                        model.apiList.map(item => {
                            return <Option key={item.id} value={item.id}>{item.name}</Option>;
                        })
                    }
                </Select>
            </>
        );
    }

    @Bind
    private renderGatherSelect(): React.ReactNode {
        const model = this.model;
        return (
            <>
                <div className={css.formLabel}>选择集合 <span className={css.required}>*</span></div>
                <Select
                    value={model.folderId || undefined}
                    onChange={model.onSelectFolderId}
                    className={css.width100}
                    placeholder={'请选择集合'}
                    showSearch
                    optionFilterProp={'label'}
                >
                    {
                        model.folderList.map(item => {
                            return <Option key={item.id} value={item.id} label={item.name}>{item.name}</Option>;
                        })
                    }
                </Select>
            </>
        );
    }

    @Bind
    private renderFolderApiSelect(): React.ReactNode {
        const model = this.model;
        return (
            <>
                <div className={css.formLabel}>选择API <span className={css.required}>*</span></div>
                <Select
                    value={model.folderApiId || undefined}
                    onChange={model.onSelectFolderApiId}
                    // mode={ 'multiple' }
                    className={css.width100}
                    filterOption={false}
                    onSearch={this.onSearchFolderApiList}
                    showSearch
                    placeholder={'请选择API'}
                    allowClear
                    disabled={!model.folderId}
                >
                    {
                        model.folderApiList.map(item => {
                            return <Option key={item.id} value={item.id}>{item.apiName}</Option>;
                        })
                    }
                </Select>
            </>
        );
    }

    @Bind
    private renderTypeRadio(): React.ReactNode {
        return (
            <RadioGroup_type
                model={this.model}
                options={typeRadioOptions}
            />
        );
    }

    @Bind
    private renderTypeContent(): React.ReactNode {
        if (this.model.type === 1) {
            return (
                <>
                    {this.renderProjectSelect()}
                    {this.renderModuleSelect()}
                    {this.renderApiSelect()}
                </>
            );
        }
        if (this.model.type === 2) {
            return (
                <>
                    {this.renderGatherSelect()}
                    {this.renderFolderApiSelect()}
                </>
            );
        }
    }

    public render(): React.ReactNode {
        const model = this.model;
        return (
            <Modal
                className={css.addApiModalWrap}
                visible={model.visible}
                title={'添加API'}
                onCancel={model.onCloseCreateScene}
                onOk={model.checkParams}
                confirmLoading={model.saveLoading}
            >
                {this.renderTypeRadio()}
                {this.renderTypeContent()}
            </Modal>
        );
    }
}
