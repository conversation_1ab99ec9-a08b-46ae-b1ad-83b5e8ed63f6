import APageModel from '@/pages/APageModel';
import { action, observable, runInAction } from 'mobx';
import { departmentCascader } from '@/business/global';
import {
    nsMockManageKoasApiManageSceneRemoveApiPost, nsMockManageKoasApiManageMockStartPost,
    nsMockManageKoasApiManageSceneQuerySceneApiListGet,
    nsMockManageKoasApiManageMockSetDefaultPost, nsMockManageKoasApiManageMockEditPost,
    nsMockManageKoasApiManageSceneQuerySceneDetailGet, nsMockManageKoasApiManageSceneStartScenePost,
    nsMockManageKoasApiManageSceneStopScenePost
} from '@/remote';
import { message } from 'antd';
import { pushKey } from '@/index.config/tools';
import { AddApiM } from './addApiModal/AddApiM';
import { MockDataDetailM } from '@/business/mockDataDetail/MockDataDetailM';
import { QRCodeM } from 'oldPages/OrganizeSpace/QRCode/QRCodeM';

export interface IQuery {
    id: string;
    sceneName: string;
    selectedKeys: string;
}

interface IApiTreeData {
    key: string;
    name: string;
    // type: string;
    id?: number;
    docId?: number;
    status?: boolean;
    default?: boolean;
    // isLeaf?: boolean;
    selectable?: boolean;
    children?: IApiTreeData[];
}

export class AsceneM extends APageModel<IQuery> {
    @observable public departmentId: number = departmentCascader.getDepartmentId();
    @observable public id: number = 0;
    @observable public status: boolean = false;
    @observable public sceneName: string = '';
    @observable public apiTreeData: IApiTreeData[] = [];
    @observable public paneLeftSize: string = '340px';
    @observable public selectedKeys: string[] = [];
    @observable public expandedKeys: string[] = [];

    public addApiM = new AddApiM();
    public mockDataDetailM = new MockDataDetailM();
    public QRCodeM = new QRCodeM();

    protected getQueryFields(): Array<keyof IQuery> {
        return [
            'id',
            'sceneName',
            // 'selectedKeys',
        ];
    }

    constructor(query: IQuery) {
        super(query);
        this.selectedKeys = query.selectedKeys ? [query.selectedKeys] : [];
        this.initByQueryFields(query);
        this.init();
    }

    @action.bound
    protected init(): void {
        this.id = Number(this.id);
        this.querySceneApiList();
        this.querySceneDetail();
    }

    // 打开绑定设备二维码弹框
    @action.bound
    public onOpenQRCode(): void {
        this.QRCodeM.init(0, this.id);
        this.QRCodeM.onCloseQRCodeCallBack = this.querySceneDetail;
    }

    @action.bound
    public onChangePaneSize(size): void {
        this.paneLeftSize = size[0];
    }

    @action.bound
    public onExpand(expandedKeys): void {
        this.expandedKeys = expandedKeys;
    }

    // 选中keys
    @action.bound
    public onSelectKeys(selectedKeys, e): void {
        this.selectedKeys = selectedKeys;
        pushKey({ selectedKeys: selectedKeys[0] });
        this.mockDataDetailM.init(e.node.docId, 1, e.node.id);
    }

    @action.bound
    protected repaintApiTree(): void {
        this.apiTreeData = [...this.apiTreeData];
    }

    @action.bound
    public onOpenAddApiModal() {
        this.addApiM.init(this.departmentId, this.id);
        this.addApiM.onSaveCallBack = this.querySceneApiList;
    }

    @action.bound
    protected formatSceneApiList(list): void {
        list.forEach((item, index) => {
            item.selectable = false;
            item.children.forEach((it, idx) => {
                it.docId = item.docId;
                if (this.selectedKeys.length > 0) {
                    if (it.key === this.selectedKeys[0]) {
                        this.onExpand([item.key]);
                        this.onSelectKeys(this.selectedKeys, { node: it });
                    }
                } else {
                    if (index === 0 && idx === 0) {
                        this.onExpand([item.key]);
                        this.onSelectKeys([it.key], { node: it });
                    }
                }
            });
        });
        this.apiTreeData = list;
    }

    // 获取场景下的API
    @action.bound
    protected async querySceneApiList() {
        try {
            const params = {
                sceneId: this.id
            };
            const result = await nsMockManageKoasApiManageSceneQuerySceneApiListGet.remote(params);
            runInAction(() => {
                this.formatSceneApiList(result?.list || []);
            });
        } catch (e) {
        }
    }

    // 移除API
    @action.bound
    public async removeApi(docId: number) {
        try {
            const params = {
                sceneId: this.id,
                docId
            };
            await nsMockManageKoasApiManageSceneRemoveApiPost.remote(params);
            runInAction(() => {
                message.success('移除成功～');
                this.querySceneApiList();
            });
        } catch (e) {
        }
    }

    // 启用/关闭API Mock
    @action.bound
    public async startApi(nodeData) {
        try {
            const params = {
                sceneId: this.id,
                docId: nodeData.docId,
                status: !nodeData.status
            };
            await nsMockManageKoasApiManageMockStartPost.remote(params);
            runInAction(() => {
                nodeData.status = !nodeData.status;
                this.repaintApiTree();
            });
        } catch (e) {
        }
    }

    // 设置默认mock数据
    @action.bound
    public async setDefault(nodeData) {
        try {
            const params = {
                docId: nodeData.docId,
                id: nodeData.id,
                sceneId: this.id
            };
            await nsMockManageKoasApiManageMockSetDefaultPost.remote(params);
            runInAction(() => {
                // message.success('设置成功~');
                this.apiTreeData.forEach(item => {
                    if (item.docId === nodeData.docId) {
                        item.children?.forEach(it => {
                            it.default = it.id === nodeData.id;
                        });
                    }
                });
                this.repaintApiTree();
            });
        } catch (e) {
        }
    }

    // 保存编辑mock数据
    @action.bound
    public async saveEditMockData() {
        const params = this.mockDataDetailM.getCurrentSaveParams() as any;
        if (!params.mockName) {
            message.warn('请填写数据名称～');
            return;
        }
        try {
            await nsMockManageKoasApiManageMockEditPost.remote(params);
            runInAction(() => {
                message.success('修改成功～');
                this.mockDataDetailM.queryDetail();
            });
        } catch (e) {
        }
    }

    @action.bound
    private async querySceneDetail() {
        try {
            const result = await nsMockManageKoasApiManageSceneQuerySceneDetailGet.remote({ sceneId: this.id });
            runInAction(() => {
                this.status = result?.status;
            });
        } catch (e) {
        }
    }

    // 启动场景
    @action.bound
    public async startScene(id: number) {
        try {
            const parmas = {
                id
            };
            const result = await nsMockManageKoasApiManageSceneStartScenePost.remote(parmas);
            runInAction(() => {
                if (result?.code !== 0) {
                    this.onOpenQRCode();
                } else {
                    this.status = true;
                }
            });
        } catch (e) {
        }
    }

    @action.bound
    public async stopScene(id: number) {
        try {
            const parmas = {
                id
            };
            await nsMockManageKoasApiManageSceneStopScenePost.remote(parmas);
            runInAction(() => {
                this.status = false;
            });
        } catch (e) {
        }
    }
}
