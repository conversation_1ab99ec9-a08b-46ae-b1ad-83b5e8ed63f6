import { <PERSON>Vie<PERSON> } from 'libs';
import React from 'react';
import { AsceneM } from './AsceneM';
import { observer } from 'mobx-react';
import { Tree, Button, Tooltip, Modal, Switch, Empty } from 'antd';
import Bind from 'lodash-decorators/bind';
import css from './Ascene.less';
import { KdevIconFont } from '@/business/commonComponents/KdevIconFont';
import { AddApi } from './addApiModal/AddApi';
import { SearchEmptyIcon } from '@/business/commonIcon';

const { DirectoryTree } = Tree;

@observer
export class ApiTree extends AView<AsceneM> {

    @Bind
    private renderTreeTitle(nodeData): React.ReactNode {
        const model = this.model;
        return (
            <div className={ css.treeTitleWrap }>
                <Tooltip title={ nodeData.name }>
                    <div className={ css.titleContent }>{ nodeData.name }</div>
                </Tooltip>
                {
                    !nodeData.id ?
                        <div>
                            {/*<Switch*/}
                            {/*    checked={ nodeData.status }*/}
                            {/*    checkedChildren={ '启' }*/}
                            {/*    unCheckedChildren={ '关' }*/}
                            {/*    onChange={ (_, e) => {*/}
                            {/*        e.stopPropagation();*/}
                            {/*        model.startApi(nodeData);*/}
                            {/*    } }*/}
                            {/*/>*/}
                            <Tooltip title={ '移除API' }>
                                <Button
                                    type={ 'link' }
                                    icon={ <KdevIconFont id={ '#iconsubtract-circle' } /> }
                                    danger
                                    onClick={ e => {
                                        e.stopPropagation();
                                        this.onRemoveApi(nodeData);
                                    } }
                                />
                            </Tooltip>
                        </div> :
                        <Switch
                            checked={ nodeData.default }
                            checkedChildren={ '默' }
                            onChange={ e => {
                                model.setDefault(nodeData);
                            } }
                        />
                }
            </div>
        );
    }

    @Bind
    private renderTitleIcon(nodeData): React.ReactNode {
        if (nodeData.isLeaf) {
            return (
                <KdevIconFont id={ '#iconbianzu' } style={ { color: '#327DFF' } } />
            );
        }
    }

    @Bind
    private onRemoveApi(nodeData): void {
        Modal.confirm({
            title: '确认移除API？',
            onOk: () => this.model.removeApi(nodeData.docId)
        });
    }

    @Bind
    private renderApiTree(): React.ReactNode {
        const model = this.model;
        if (model.apiTreeData.length < 1) {
            return (
                <Empty
                    className={ css.empty }
                    image={ <SearchEmptyIcon /> }
                    description={ '暂无API，快去添加吧吧～' }
                >
                    { this.renderAddApiBtn() }
                </Empty>
            );
        }
        return (
            <div>
                <DirectoryTree
                    treeData={ model.apiTreeData }
                    blockNode
                    className={ css.apiTree }
                    titleRender={ this.renderTreeTitle }
                    icon={ '' }
                    selectedKeys={ model.selectedKeys }
                    onSelect={ model.onSelectKeys }
                    expandedKeys={ model.expandedKeys }
                    onExpand={ model.onExpand }
                />
                { this.renderAddApiBtn() }
            </div>
        );
    }

    @Bind
    private renderAddApiBtn(): React.ReactNode {
        return (
            <Button
                type={ 'primary' }
                className={ css.addApiBtn }
                onClick={ this.model.onOpenAddApiModal }
            >
                添加API
            </Button>
        );
    }

    public render(): React.ReactNode {
        const model = this.model;
        return (
            <div className={ css.apiTreeWrap }>
                { this.renderApiTree() }
                <AddApi model={ model.addApiM } />
            </div>
        );
    }
}
