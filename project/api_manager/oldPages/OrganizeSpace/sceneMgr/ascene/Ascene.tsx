import React from 'react';
import APage from '@/pages/APage';
import { AsceneM, IQuery } from './AsceneM';
import { observer } from 'mobx-react';
import css from './Ascene.less';
import { Input, Radio, Button, Empty, Tooltip } from 'antd';
import { bindObserver } from '@libs/mvvm';
import { BackHeader } from '@/business/commonComponents/backHeader/BackHeader';
import { ERouter } from 'CONFIG';
import { ApiTree } from './ApiTree';
import SplitPane from 'react-split-pane';
import Pane from 'react-split-pane/lib/Pane';
import { MockDataDetail } from 'oldPages/OrganizeSpace/component/mockDataDetail/MockDataDetail';
import { Bind } from 'lodash-decorators';
import { SearchEmptyIcon } from '@/business/commonIcon';
import { KdevIconFont } from '@/business/commonComponents/KdevIconFont';
import { QRCode } from 'oldPages/OrganizeSpace/QRCode/QRCode';

const { Search } = Input;

const Input_key = bindObserver(Search, 'key');
const RadioGroup_filter = bindObserver(Radio.Group, 'filter');

@observer
export default class Ascene extends APage<IQuery, AsceneM> {
    protected createModel(): AsceneM {
        return new AsceneM(this.query);
    }

    @Bind
    private renderMockDataOperate(): React.ReactNode {
        const model = this.model;
        return (
            <Button
                type={'primary'}
                disabled={!model.mockDataDetailM.isChangeParams}
                onClick={model.saveEditMockData}
                className={css.saveMockDataBtn}
            >
                保存
            </Button>
        );
    }

    @Bind
    private renderQRcode(): React.ReactNode {
        const model = this.model;
        const clickBtnEve = () => {
            !model.status ? model.startScene(model.id) : model.stopScene(model.id);
        };
        return (
            <>
                <Button
                    onClick={clickBtnEve}
                >
                    {model.status ? '关闭场景' : '启用场景'}
                </Button>
                <Tooltip title={'扫码绑定设备'}>
                    <Button
                        icon={<KdevIconFont id={'#iconerweima'} />}
                        onClick={model.onOpenQRCode}
                        className={css.QRcodeBtn}
                    />
                </Tooltip>
                <Tooltip title={'App 重启后需要点击右侧二维码重新绑定'}>
                    <KdevIconFont id={'#iconquestion'} className={css.codeTooltip} />
                </Tooltip>
                <QRCode model={model.QRCodeM} />
            </>
        );
    }

    public renderContent(): React.ReactNode {
        const model = this.model;
        return (
            <div className={css.asceneWrap}>
                <BackHeader
                    title={model.status ? `${model.sceneName}（已启用）` : `${model.sceneName}（未启用）`}
                    url={ERouter.API_MOCK_ORGANIZESPACE_SCENEMGR}
                    extra={this.renderQRcode()}
                />
                <SplitPane onChange={model.onChangePaneSize} className={css.asceneContent}>
                    <Pane minSize={'267px'} size={model.paneLeftSize}>
                        <ApiTree model={model} />
                    </Pane>
                    <Pane minSize={'918px'}>
                        <div className={css.mockDataBox}>
                            {
                                model?.apiTreeData?.length ?
                                    <MockDataDetail
                                        model={model.mockDataDetailM}
                                        baseInfoTitleRight={this.renderMockDataOperate()}
                                    /> :
                                    <Empty
                                        className={css.empty}
                                        image={<SearchEmptyIcon />}
                                        description={'暂无数据～'}
                                    />
                            }
                        </div>
                    </Pane>
                </SplitPane>
            </div>
        );
    }
}
