.asceneWrap {
  background-color: #fff;
  overflow: auto;

  .QRcodeBtn{
    margin-left: 8px;
  }

  .codeTooltip{
    font-size: 20px;
    margin-left: 4px;
    padding-top: 4px;
  }

  .asceneContent {
    height: calc(100% - 55px);
    position: relative;

    .apiTreeWrap {
      padding: 24px 0;
      overflow: auto;
      height: 100%;

      .apiTree {
        //margin: 0 8px;

        .treeTitleWrap {
          display: flex;
          align-items: center;
          justify-content: space-between;
          height: 40px;

          .titleContent {
            flex: 1;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
          }
        }

        :global {
          .ant-tree-treenode {
            display: flex;
            align-items: center;
          }

          .ant-tree-node-content-wrapper {
            width: 100%;
            overflow: hidden;
          }
        }
      }

      .addApiBtn {
        margin: 8px;
      }
    }
  }

  .empty {
    //padding-top: 140px;

    :global {
      .ant-empty-description {
        opacity: .25;
      }
    }
  }

  .mockDataBox {
    margin: 24px;
    //padding: 24px;
    //border: 1px solid #d9d9d9;
    border-radius: 4px;
    overflow: auto;
    height: calc(100% - 55px);
    //position: relative;

    .saveMockDataBtn{
      position: absolute;
      top: 24px;
      right: 24px;
      z-index: 1;
    }
  }

  :global {
    .ant-tree.ant-tree-directory .ant-tree-treenode-selected:hover::before, .ant-tree.ant-tree-directory .ant-tree-treenode-selected::before {
      background-color: #f5f7fa;
    }

    .ant-tree.ant-tree-directory .ant-tree-treenode .ant-tree-node-content-wrapper.ant-tree-node-selected{
      color: #327DFF;
    }
    .ant-tree.ant-tree-directory .ant-tree-treenode-selected .ant-tree-switcher{
      color: #898A8C;
    }
  }
}
