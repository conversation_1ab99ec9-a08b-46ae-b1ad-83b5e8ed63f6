import { AViewModel } from 'libs';
import { observable, action, runInAction, } from 'mobx';
import {
    nsMockManageKoasApiManageMockCreateGet, nsMockManageKoasApiManageMockGetBindStatusGet,
} from '@/remote';
import { departmentCascader } from '@/business/global';

export class QRCodeM extends AViewModel {
    @observable public departmentId: number = departmentCascader.getDepartmentId();
    @observable public visible: boolean = false;
    @observable public apiId: number = 0;
    @observable public sceneId: number = 0;
    @observable public QRCodeUrl: string = '';
    @observable public startup: boolean = false;
    protected uuId: number = 0;
    public timer: null | number = null;

    public onCloseQRCodeCallBack?(): void;
    public existActiveScene?(): void;

    @action.bound
    public init(apiId?: number, sceneId?: number) {
        if (this.apiId !== apiId || this.sceneId !== sceneId || sceneId === 0) {
            this.apiId = apiId || 0;
            this.sceneId = sceneId || 0;
            this.uuId = new Date().getTime();
            this.createQRCode();
        } else {
            this.uuId && this.timeOutGetBindStatus();
        }
        this.visible = true;
    }

    @action.bound
    public onCloseQRCode() {
        this.timer && clearTimeout(this.timer);
        this.visible = false;
        this.onCloseQRCodeCallBack && this.onCloseQRCodeCallBack();
    }

    @action.bound
    protected timeOutGetBindStatus(): void {
        this.timer = window.setTimeout(this.getBindStatus, 1000);
    }

    @action.bound
    public onChangeStartUp(startup: boolean): void {
        // this.startup = startup;
        this.createQRCode(startup);
    }

    // 获取设备匹配二维码
    @action.bound
    public async createQRCode(startup?: boolean) {
        try {
            const params = {
                docId: this.apiId,
                sceneId: this.sceneId,
                departmentId: this.departmentId,
                uuId: this.uuId
            };
            if (startup || startup === false) {
                params['startup'] = startup;
            }
            const result = await nsMockManageKoasApiManageMockCreateGet.remote(params);
            runInAction(() => {
                this.QRCodeUrl = result?.image || '';
                this.startup = result?.startup || false;
                this.timeOutGetBindStatus();
            });
        } catch (e) {
        }
    }

    @action.bound
    protected async getBindStatus() {
        try {
            const params = {
                uuId: this.uuId
            };
            const result = await nsMockManageKoasApiManageMockGetBindStatusGet.remote(params);
            runInAction(() => {
                this.timer && clearTimeout(this.timer);
                if (result?.result) {
                    this.onCloseQRCode();
                    this.existActiveScene && this.existActiveScene();
                } else if (this.visible) {
                    this.timeOutGetBindStatus();
                }
            });
        } catch (e) {
        }
    }
}
