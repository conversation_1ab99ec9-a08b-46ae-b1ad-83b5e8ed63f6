import { AView } from 'libs';
import React from 'react';
import { QRCodeM } from './QRCodeM';
import { observer } from 'mobx-react';
import { Button, Modal, Switch } from 'antd';
import css from './QRCode.less';

@observer
export class QRCode extends AView<QRCodeM> {

    public render(): React.ReactNode {
        const model = this.model;
        return (
            <Modal
                title={ '绑定设备' }
                className={ css.QRCodeModal }
                visible={ model.visible }
                onCancel={ model.onCloseQRCode }
                footer={ <Button
                    type={ 'primary' }
                    onClick={ model.onCloseQRCode }
                >
                    关闭
                </Button> }
                width={ 360 }
            >
                <img
                    width={ 200 }
                    height={ 200 }
                    src={ `data:image/jpg;base64,${ model.QRCodeUrl }` }
                    className={ css.QRCodeImg }
                />
                <div className={ css.startup }>
                    冷启自动设置代理：
                    <Switch checked={ model.startup } onChange={ model.onChangeStartUp } />
                </div>
            </Modal>
        );
    }
}
