import { AViewModel } from 'libs';
import { observable, action, runInAction } from 'mobx';
import { Bind } from 'lodash-decorators';
import * as QS from 'query-string';
import { pushKey, getKey0OrKey1 } from '@/index.config/tools';
import { message } from 'antd';
import {
    nsMockManageKoasApiManageFolderCreateFolderPost, nsMockManageKoasApiManageFolderDeleteFolderGet,
    nsMockManageKoasApiManageFolderMoveOutFolderRequestPost, nsMockManageKoasApiManageFolderQueryFolderListGet,
    nsMockManageKoasApiManageFolderQueryDocListGet, nsMockManageKoasApiManageFolderUpdateFloderPost
} from '@/remote';
import { CreateEditNameM } from '@/business/createEditName/CreateEditName';
import { departmentCascader } from '@/business/global';
import { AddApiM } from './addApiToGather/AddApiM';
import {ApiListM} from './apiList/ApiListM';
import { ApiDetailM } from './apiDetail/ApiDetailM';
import { EditApiM } from './editApi/EditApiM';

interface ITreeData {
    key: string;
    id: number;
    folderId?: number;
    title: string;
    type: string;
    moduleId?: number;
    status?: number;
    statusDesc?: string;
    isLeaf?: boolean;
    children?: ITreeData[];
}

export class ApiGatherM extends AViewModel {
    @observable public departmentId: number = departmentCascader.getDepartmentId();
    @observable public keyWord: string = '';
    @observable public treeData: ITreeData[] = [];
    @observable public leftPaneSize: string = '400px';
    @observable public selectedKeys: string[] = [];
    @observable public expandedKeys: string[] = [];
    @observable public loadedKeys: string[] = [];
    @observable public showType: string = '';
    @observable public gatherLoading: boolean = false;

    public createEditNameM = new CreateEditNameM();
    public addApiM = new AddApiM();
    public apiListM = new ApiListM();
    public apiDetailM = new ApiDetailM();
    public editApiM = new EditApiM();

    constructor() {
        super();
        this.getUrlSearchParams();
        this.addApiM.onAddApiCallback = this.onAddApiCallback;
    }

    @action
    private getUrlSearchParams(): void {
        const objParams: object = QS.parse(location.search);
        this.selectedKeys = objParams['selectedKeys'] ? [objParams['selectedKeys']] : [];
        this.showType = objParams['showType'] || '';
        this.init();
    }

    @action.bound
    private init(): void {
        this.queryFolderList().then(() => {
            // const folderId: number = getKey0OrKey1(this.selectedKeys)[0];
            this.queryAllDocList(this.getFolderId());
        });
    }

    @action
    public setUrlParams(): void {
        const obj = {
            selectedKeys: this.selectedKeys[0] || '',
            showType: this.showType
        };
        pushKey(obj);
    }

    @action.bound
    public getFolderId(): number {
        return getKey0OrKey1(this.selectedKeys)[0];
    }

    @action.bound
    public onSearchGather(): void {
        // 搜索API集合
        this.loadedKeys = [];
        this.expandedKeys = [];
        this.selectedKeys = [];
        this.init();
    }

    @action.bound
    public openCreateEditModa(record): void {
        this.createEditNameM.init(record?.title, record?.id);
        this.createEditNameM.onSaveNameCallback = this.onSaveGatherCallback;
    }

    @action.bound
    private onSaveGatherCallback(name: string, id: number): void {
        !id ? this.createFolder(name) : this.updateFolder(name, id);
    }

    @action.bound
    public onChangePaneSize(paneSize): void {
        if (paneSize[0] !== this.leftPaneSize) {
            this.leftPaneSize = paneSize[0];
        }
    }

    @action.bound
    private onAddApiCallback(folderId: number): void {
        if (!this.showType) {
            this.showType = '';
        }
        this.queryAllDocList(folderId);
    }

    @action.bound
    protected onSaveApiCallback(): void {
        this.showType = 'view';
        this.queryAllDocList(this.getFolderId());
    }

    @action
    protected onColseApiDetail(): void {
        const folderId: number = this.getFolderId();
        this.selectedKeys = [folderId.toString()];
        this.changeShowType('', folderId);
    }

    @action
    protected onOpenApiDetail(docId: number): void {
        const folderId: number = this.getFolderId();
        this.selectedKeys = [`${folderId}-${docId}`];
        this.changeShowType('view', docId);
    }

    @action
    protected onOpenEditApi(docId: number): void {
        const folderId: number = this.getFolderId();
        this.selectedKeys = [`${folderId}-${docId}`];
        this.changeShowType('edit', docId);
    }

    @action
    public onDeleteApi(docId: number, folderId?: number): void {
        folderId = folderId || this.getFolderId();
        this.deleteRemoveApi(folderId, docId);
    }

    @action.bound
    private changeShowType(showType: string, id: number): void {
        this.showType = showType;
        switch (showType) {
            case '':
                this.apiListM.init(id, this.setApiStatus);
                break;
            case 'view':
                this.apiDetailM.init(id, this);
                break;
            // case 'new':
            //     this.editApiM.init();
            //     break;
            case 'edit':
                this.editApiM.init(id, this);
                break;
        }
        this.setUrlParams();
    }

    @action
    private onChangeShowType(): void {
        const folderId: number = getKey0OrKey1(this.selectedKeys)[0];
        const docId: number = getKey0OrKey1(this.selectedKeys)[1];
        this.changeShowType(this.showType, this.showType ? docId : folderId);
    }

    @action
    private onSelectedNode(nodeData) {
        if (nodeData.type === 'folder') {
            this.changeShowType('', nodeData.id);
        }
        if (nodeData.type === 'file') {
            this.changeShowType('view', nodeData.id);
        }
    }

    @action.bound
    public onSelectKeys(selectedKeys, e): void {
        this.selectedKeys = selectedKeys;
        this.onSelectedNode(e.node);
    }

    @action.bound
    public onExpandKeys(expandedKeys): void {
        this.expandedKeys = expandedKeys;
    }

    @action
    private renderTreeData(): void {
        this.gatherLoading = true;
        this.expandedKeys = [...this.expandedKeys];
        this.loadedKeys = [...this.loadedKeys];
        this.treeData = [...this.treeData];
        this.gatherLoading = false;
    }

    @action.bound
    protected setApiStatus(apiId: number, status: number, statusDesc: string): void {
        const folderId: number = this.getFolderId();
        this.treeData.forEach(item => {
            if (item.id === folderId) {
                item.children?.map(it => {
                    if (it.id === apiId) {
                        it.status = status;
                        it.statusDesc = statusDesc;
                    }
                });
            }
        });
        this.renderTreeData();
    }

    @action
    private addFolder(id: number, name: string): void {
        const obj: ITreeData = {
            key: id.toString(),
            id: id,
            title: name,
            isLeaf: false,
            children: [],
            type: 'folder'
        };
        this.treeData.push(obj);
        this.onSelectKeys([obj.key], {node: obj});
        this.renderTreeData();
    }

    @action
    private formatFolderList(folderList): void {
        if (!folderList.length) {
            this.selectedKeys = [];
            this.showType = '';
        }
        this.treeData = folderList.map(item => {
            const obj: ITreeData = {
                key: item.id.toString(),
                title: item.name,
                type: 'folder',
                id: item.id,
                isLeaf: false,
                children: []
            };
            return obj;
        }) || [];
        if (this.treeData.length && !this.getFolderId()) {
            this.selectedKeys = [this.treeData[0]?.key];
            // this.apiListM.init(this.getFolderId());
        }
        this.setUrlParams();
    }

    @action
    private changeLoadedKeys(loadedKey: string): void {
        if (!this.loadedKeys.includes(loadedKey)) {
            this.loadedKeys.push(loadedKey);
        }
        if (!this.expandedKeys.includes(loadedKey)) {
            this.expandedKeys.push(loadedKey);
        }
    }

    // 初始化API list内容
    @action
    private formatApiList(docList, folderId: number): void {
        const list: ITreeData[] = docList.map(item => {
            const obj: ITreeData = {
                key: `${folderId}-${item.id}`,
                id: item.id,
                title: item.apiName,
                type: 'file',
                isLeaf: true,
                folderId,
                status: item.status,
                statusDesc: item.statusDesc,
                moduleId: item.moduleId
            };
            return obj;
        });
        this.treeData.map(item => {
            if (item.id === folderId) {
                item.children = list;
            }
        });
        this.changeLoadedKeys(folderId.toString());
        this.onChangeShowType();
        this.renderTreeData();
    }

    @action.bound
    public deleteNodeData(nodeData: ITreeData): void {
        if (nodeData.type === 'folder') {
            this.deleteFolder(nodeData.id);
        }
        if (nodeData.type === 'file' && nodeData?.folderId) {
            this.onDeleteApi(nodeData.id, nodeData.folderId);
        }
    }

    @action.bound
    public async loadDataTreeData(nodeData): Promise<void> {
        try {
            await this.queryAllDocList(nodeData.id);
        } catch {
        }
    }

    @Bind
    private async createFolder(name: string): Promise<void> {
        try {
            const params = {
                departMentId: this.departmentId,
                name
            };
            const result = await nsMockManageKoasApiManageFolderCreateFolderPost.remote(params);
            message.success('创建集合成功～');
            result?.id && this.addFolder(result?.id, name);
        } catch {
        }
    }

    @Bind
    private async updateFolder(name: string, id: number): Promise<void> {
        try {
            const params = {
                id,
                name
            };
            await nsMockManageKoasApiManageFolderUpdateFloderPost.remote(params);
            message.success('修改成功～');
            this.treeData.map(item => {
                if (item.id === id) {
                    item.title = name;
                }
            });
            this.renderTreeData();
        } catch {
        }
    }

    @Bind
    private async deleteFolder(folderId: number): Promise<void> {
        try {
            const params = {
                folderId
            };
            await nsMockManageKoasApiManageFolderDeleteFolderGet.remote(params);
            message.success('删除集合成功～');
            runInAction(() => {
                this.selectedKeys = [];
                this.showType = '';
                this.init();
            });
        } catch {
        }
    }

    @Bind
    private async deleteRemoveApi(folderId: number, docId: number): Promise<void> {
        try {
            const params = {
                folderId,
                docId
            };
            await nsMockManageKoasApiManageFolderMoveOutFolderRequestPost.remote(params);
            runInAction(() => {
                message.success('移除成功～');
                this.selectedKeys = [folderId.toString()];
                this.queryAllDocList(folderId);
                this.changeShowType('', folderId);
            });
        } catch {
        }
    }

    @Bind
    private async queryFolderList(): Promise<void> {
        runInAction(() => this.gatherLoading = true);
        try {
            const params = {
                departmentId: this.departmentId,
                name: this.keyWord
            };
            const result = await nsMockManageKoasApiManageFolderQueryFolderListGet.remote(params);
            result?.folderList && this.formatFolderList(result?.folderList);
            runInAction(() => this.gatherLoading = false);
        } catch {
            runInAction(() => this.gatherLoading = false);
        }
    }

    @Bind
    private async queryAllDocList(folderId: number): Promise<void> {
        try {
            const params = {
                folderId: folderId,
                name: '',
                pageIndex: 1,
                pageSize: 9999
            };
            const result = await nsMockManageKoasApiManageFolderQueryDocListGet.remote(params);
            result?.docList && this.formatApiList(result?.docList, folderId);
        } catch {
        }
    }
}
