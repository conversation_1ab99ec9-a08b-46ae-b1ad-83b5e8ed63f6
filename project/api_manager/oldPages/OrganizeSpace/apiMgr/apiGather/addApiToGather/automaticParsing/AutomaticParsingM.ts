import { AViewModel } from 'libs';
import { observable, action, runInAction } from 'mobx';
import { Bind } from 'lodash-decorators';
import {
    nsMockManageKoasApiManageModuleGetHttpModuleListGet, nsMockManageKoasApiManageAutoDocGetHttpApiListGet,
    nsMockManageKoasApiManageModuleGetHttpModuleBranchGet
} from '@/remote';
import { IModuleList, IApiList } from './configure';

export class AutomaticParsingM extends AViewModel {
    @observable public keyWord: string = ''; // 搜索API关键词
    @observable public moduleId: number = 0; // 模块ID
    @observable public moduleList: IModuleList[] = []; // 模块列表
    @observable public branchName: string = ''; // 分支名
    @observable public branchList: string[] = []; // 分支列表

    @observable public apiList: IApiList[] = []; // API列表
    @observable public tableLoading: boolean = false; // table loading
    @observable public currentPage: number = 1; // 当前页
    @observable public pageSize: number = 10; // 每页条数
    @observable public total: number = 0; // API数据总条数
    @observable public selectedRowKeys: number[] = []; // 选中API keys

    // 初始化
    @action.bound
    public init(): void {
        if (this.moduleId) {
            this.onChangePageInfo();
        }
        this.getHttpModuleList();
    }

    // 初始化数据
    @action.bound
    public initData(): void {
        this.keyWord = '';
        this.moduleId = 0;
        this.moduleList = [];
        this.branchName = '';
        this.branchList = [];
        this.apiList = [];
        this.tableLoading = false;
        this.currentPage = 1;
        this.pageSize = 10;
        this.total = 0;
        this.selectedRowKeys = [];
    }

    // 根据关键词搜索API
    @action.bound
    public onSearchApi(keyWord: string): void {
        this.keyWord = keyWord;
        this.onChangePageInfo();
    }

    // 选中API
    @action.bound
    public onChangeSelectedRowKeys(selectedRowKeys): void {
        this.selectedRowKeys = selectedRowKeys;
    }

    // 提供docIdList
    @action.bound
    public getDocIdList(): number[] {
        return this.selectedRowKeys;
    }

    // 分页搜索API列表
    @action.bound
    public onChangePageInfo(currentPage: number = 1, pageSize?: number): void {
        this.currentPage = currentPage;
        this.pageSize = pageSize || this.pageSize;
        this.getHttpApiList();
    }

    // 清空模块
    @action.bound
    public onClearModule(): void {
        this.onSelectModule();
        this.getHttpModuleList();
    }

    // 选择模块
    @action.bound
    public onSelectModule(moduleId: number = 0): void {
        this.moduleId = moduleId;
        this.branchName = '';
        this.branchList = [];
        this.onChangePageInfo();
        // this.getHttpBranch(moduleId);
    }

    // 获取HTTP模块列表
    @Bind
    public async getHttpModuleList(key: string = ''): Promise<void> {
        try {
            const params = {
                key,
                pageSize: 100,
                currentPage: 1
            };
            const result = await nsMockManageKoasApiManageModuleGetHttpModuleListGet.remote(params);
            runInAction(() => {
                this.moduleList = result?.list || [];
            });
        } catch (e) {
        }
    }

    // 获取HTTP API列表
    @Bind
    private async getHttpApiList(): Promise<void> {
        runInAction(() => this.tableLoading = true);
        try {
            const params = {
                key: this.keyWord,
                pageSize: this.pageSize,
                currentPage: this.currentPage,
                moduleId: this.moduleId,
                type: 1,
                branchName: this.branchName
            };
            const result = await nsMockManageKoasApiManageAutoDocGetHttpApiListGet.remote(params);
            runInAction(() => {
                this.apiList = result?.apiList || [];
                this.total = result?.total;
                this.tableLoading = false;
            });
        } catch (e) {
            runInAction(() => {
                this.tableLoading = false;
            });
        }
    }

    // 选择分支
    @action.bound
    public onSelectBranch(branchName: string = ''): void {
        this.branchName = branchName;
        this.onChangePageInfo();
    }

    // 获取模块下的分支
    @Bind
    private async getHttpBranch(moduleId: number): Promise<void> {
        try {
            const result = await nsMockManageKoasApiManageModuleGetHttpModuleBranchGet.remote({ moduleId });
            runInAction(() => {
                this.branchList = result?.branchNameList || [];
            });
        } catch (e) {
        }
    }
}
