import { AView } from 'libs';
import React from 'react';
import { observer } from 'mobx-react';
import { bindObserver } from '@libs/mvvm';
import { Bind, Debounce } from 'lodash-decorators';
import { Input, Select, Table } from 'antd';
import { AutomaticParsingM } from './AutomaticParsingM';
import css from './AutomaticParsing.less';
import { Select_onDropdownVisibleChange } from '@libs/utils';

const { Option } = Select;
const { Search } = Input;
const Search_keyWord = bindObserver(Search, 'keyWord');

@observer
export class AutomaticParsing extends AView<AutomaticParsingM> {

    // 挂载组件
    @Bind
    public componentDidMount(): void {
        this.model.init();
    }

    // API名称
    @Bind
    private renderApiName(record): React.ReactNode {
        return <span className={css.apiName}>{record.apiUrl}</span>;
    }

    // API展示columns
    @Bind
    private apiColumns(): any[] {
        const columns: any[] = [
            {
                title: 'API名称',
                key: 'apiUrl',
                render: this.renderApiName
            },
            {
                title: '模块',
                dataIndex: 'moduleName'
            },
            {
                title: '当前分支',
                dataIndex: 'apiBranch'
            },
            {
                title: '维护人',
                dataIndex: 'createUser'
            }
        ];
        return columns;
    }

    // API列表
    @Bind
    private renderSearchResult(): React.ReactNode {
        const model = this.model;
        return (
            <Table
                columns={this.apiColumns()}
                dataSource={model.apiList}
                bordered
                loading={model.tableLoading}
                className={css.apiTable}
                rowKey="apiId"
                rowSelection={{
                    selectedRowKeys: model.selectedRowKeys,
                    onChange: model.onChangeSelectedRowKeys,
                    preserveSelectedRowKeys: true
                }}
                pagination={{
                    pageSize: model.pageSize,
                    current: model.currentPage,
                    total: model.total,
                    onChange: model.onChangePageInfo
                }}
            />
        );
    }

    // 搜索模块（防抖）
    @Debounce(300)
    @Bind
    private onSearchHttpModuleList(key: string): void {
        this.model.getHttpModuleList(key);
    }

    // 模块Module Select
    @Bind
    private renderModuleSelect(): React.ReactNode {
        const model = this.model;
        return (
            <Select
                className={css.moduleSelect}
                value={model.moduleId || undefined}
                showSearch
                onSelect={model.onSelectModule}
                onSearch={this.onSearchHttpModuleList}
                onDropdownVisibleChange={Select_onDropdownVisibleChange}
                placeholder={'请选择模块'}
                allowClear
                filterOption={false}
                onClear={model.onClearModule}
            >
                {
                    model.moduleList.map(item => {
                        return (
                            <Option key={item.moduleId} value={item.moduleId}>
                                {item.moduleName}
                            </Option>
                        );
                    })
                }
            </Select>
        );
    }

    @Bind
    private renderBranchSelect(): React.ReactNode {
        const model = this.model;
        return (
            <Select
                className={css.branchSelect}
                placeholder={'请选择分支'}
                disabled={!model.moduleId}
                showSearch
                onSelect={model.onSelectBranch}
                value={model.branchName || undefined}
                allowClear
                onClear={() => model.onSelectBranch()}
            >
                {
                    model.branchList.map(item => {
                        return <Option key={item} value={item}>{item}</Option>;
                    })
                }
            </Select>
        );
    }

    // 关键词Search Input
    @Bind
    private renderSearchInput(): React.ReactNode {
        const model = this.model;
        return (
            <Search_keyWord
                model={model}
                placeholder={'输入API名称'}
                className={css.searchInput}
                onSearch={model.onSearchApi}
                autoFocus={true}
                allowClear
            />
        );
    }

    // render函数
    public render(): React.ReactNode {
        return (
            <div className={css.automaticParsing}>
                {this.renderModuleSelect()}
                {/* {this.renderBranchSelect()} */}
                {this.renderSearchInput()}
                {this.renderSearchResult()}
            </div>
        );
    }
}
