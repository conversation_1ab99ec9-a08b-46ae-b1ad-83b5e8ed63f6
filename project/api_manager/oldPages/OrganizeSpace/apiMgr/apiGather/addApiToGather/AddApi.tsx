import { AView } from 'libs';
import React from 'react';
import { observer } from 'mobx-react';
import { bindObserver } from '@libs/mvvm';
import { Bind } from 'lodash-decorators';
import { Modal, Radio } from 'antd';
import { AddApiM } from './AddApiM';
import css from './AddApi.less';
import { apiTypeOptions } from './configure';
import { ManuallyCreate } from './manuallyCreate/ManuallyCreate';
import { AutomaticParsing } from './automaticParsing/AutomaticParsing';

const RadioGroup_apiType = bindObserver(Radio.Group, 'apiType');

interface IProps {
}

@observer
export class AddApi extends AView<AddApiM, IProps> {

    @Bind
    private renderApiType(): React.ReactNode {
        const model = this.model;
        return (
            <div className={css.apiType}>
                API类型：
                <RadioGroup_apiType
                    model={model}
                    options={apiTypeOptions}
                />
            </div>
        );
    }

    @Bind
    private renderApi(): React.ReactNode {
        const model = this.model;
        if (model.apiType === 1) {
            return <ManuallyCreate model={model.manuallyCreateM} />;
        }
        if (model.apiType === 2) {
            return <AutomaticParsing model={model.automaticParsingM} />;
        }
    }

    public render(): React.ReactNode {
        const model = this.model;
        return (
            <Modal
                title={'添加API到集合'}
                visible={model.visible}
                className={css.addApiToGatherModal}
                onCancel={model.closeAddApiModal}
                onOk={model.onAddApiToFolder}
                destroyOnClose={true}
                maskClosable={false}
                width={960}
                // getContainer={ () => document.body }
                okButtonProps={{
                    loading: model.addApiBtnLoading
                }}
            >
                {this.renderApiType()}
                {this.renderApi()}
            </Modal>
        );
    }
}
