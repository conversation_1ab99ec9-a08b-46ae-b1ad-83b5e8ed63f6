import { AView } from 'libs';
import React from 'react';
import { observer } from 'mobx-react';
import { bindObserver } from '@libs/mvvm';
import { Bind } from 'lodash-decorators';
import { Empty, Input, Tree, Tag } from 'antd';
import { ManuallyCreateM } from './ManuallyCreateM';
import css from './ManuallyCreate.less';
import { SearchEmptyIcon } from '@/business/commonIcon';
import { KdevIconFont } from '@/business/commonComponents/KdevIconFont';
import { apiStatusEnum } from '@/business/toolFun/ToolFun';

const {Search} = Input;
const Search_keyWord = bindObserver(Search, 'keyWord');

interface IProps {
}

@observer
export class ManuallyCreate extends AView<ManuallyCreateM, IProps> {

    @Bind
    public componentDidMount(): void {
        this.model.init();
    }

    @Bind
    private renderSearchInput(): React.ReactNode {
        const model = this.model;
        return (
            <Search_keyWord
                model={model}
                placeholder={'支持API名称搜索'}
                onSearch={model.onSearchResult}
                autoFocus={true}
                allowClear
            />
        );
    }

    @Bind
    private titleRender(nodeData): React.ReactNode {
        if (nodeData.groupId) {
            return (
                <div className={css.treeTitle}>
                    <KdevIconFont
                        id={'#iconfolder'}
                        className={css.treeTitleFolderIcon}
                    />
                    {nodeData.title}
                </div>
            );
        }
        if (nodeData.id) {
            return (
                <div className={css.treeTitle}>
                    <Tag
                        className={ css.apiStatusTag }
                        color={apiStatusEnum[nodeData.status].color}
                    >
                        {nodeData.statusDesc}
                    </Tag>
                    <KdevIconFont
                        id={'#iconbianzu'}
                        className={css.treeTitleFileIcon}
                    />
                    {nodeData.title}
                </div>
            );
        }
        return <div className={css.treeTitle}>{nodeData.title}</div>;
    }

    @Bind
    private renderSearchResult(): React.ReactNode {
        const model = this.model;
        if (model.treeData.length) {
            return (
                <Tree
                    treeData={model.treeData}
                    blockNode
                    className={css.tree}
                    selectable={false}
                    checkable
                    defaultExpandAll
                    checkedKeys={model.checkedKeys}
                    onCheck={model.onCheckRowKeys}
                    titleRender={this.titleRender}
                />
            );
        }
        return (
            <Empty className={css.tree} image={ <SearchEmptyIcon /> }/>
        );
    }

    public render(): React.ReactNode {
        return (
            <div className={css.manuallyCreate}>
                {this.renderSearchInput()}
                {this.renderSearchResult()}
            </div>
        );
    }
}
