import { AViewModel, eachNode } from 'libs';
import { observable, action } from 'mobx';
import { Bind } from 'lodash-decorators';
import { nsMockManageKoasApiManageFolderQueryArtiDocByKeyWordGet } from '@/remote';
import { ITreeData } from './configure';
import { message } from 'antd';

export class ManuallyCreateM extends AViewModel {
    @observable public keyWord: string = '';
    @observable public checkedKeys: string[] = [];
    @observable public treeData: ITreeData[] = [];

    public onAddApiCallback?(folderId: number): void;

    @action.bound
    public init(): void {
        this.queryArtiDocByKeyWord();
    }

    @action
    public initData(): void {
        this.keyWord = '';
        this.checkedKeys = [];
        this.treeData = [];
    }

    @action.bound
    public onSearchResult(keyWord: string): void {
        this.keyWord = keyWord;
        this.queryArtiDocByKeyWord();
    }

    @action.bound
    public onCheckRowKeys(checkedKeys): void {
        this.checkedKeys = checkedKeys;
    }

    @action.bound
    public getDocIdList(): number[] {
        const docIdList: number[] = this.checkedKeys.map(item => {
            const keys: string[] = item.split('-');
            const docId: number = Number(keys[keys.length - 1]);
            return docId;
        });
        return docIdList;
    }

    @action.bound
    private formatTreeData(list): void {
        const dataList = list;
        eachNode(dataList, (item) => {
            item.title = item?.apiName || item?.groupName || item?.moduleName || item?.projectName || '';
            item.children = item?.apiList || item?.groupList || item?.moduleList || [];
            item.checkable = item.id ? true : false;
            return false;
        });
        this.treeData = dataList;
    }

    @Bind
    public async queryArtiDocByKeyWord(): Promise<void> {
        if (!this.keyWord) {
            this.treeData = [];
            return;
        }
        if (this.keyWord.length < 3) {
            message.warn('请输入三个及以上字符进行搜索～');
            return;
        }
        try {
            const params = {
                keyWord: this.keyWord
            };
            const result = await nsMockManageKoasApiManageFolderQueryArtiDocByKeyWordGet.remote(params);
            result?.projectList && this.formatTreeData(result.projectList);
        } catch {
        }
    }
}
