import { AViewModel } from 'libs';
import { observable, action, runInAction } from 'mobx';
import { Bind } from 'lodash-decorators';
import { nsMockManageKoasApiManageFolderMoveInFolderRequestPost, nsMockManageKoasApiManageFolderMoveInFolderForAutoDocPost } from '@/remote';
import { message } from 'antd';
import { ManuallyCreateM } from './manuallyCreate/ManuallyCreateM';
import {AutomaticParsingM} from './automaticParsing/AutomaticParsingM';

export class AddApiM extends AViewModel {
    @observable public visible: boolean = false;
    @observable public apiType: number = 1;
    @observable public addApiBtnLoading: boolean = false;
    @observable public folderId: number = 0;

    public onAddApiCallback?(folderId: number): void;
    public manuallyCreateM = new ManuallyCreateM();
    public automaticParsingM = new AutomaticParsingM();

    @action.bound
    public init(folderId: number): void {
        this.visible = true;
        this.folderId = folderId;
    }

    @action.bound
    public closeAddApiModal(): void {
        this.visible = false;
        this.initData();
    }

    @action.bound
    private initData(): void {
        this.folderId = 0;
        this.apiType = 1;
        this.manuallyCreateM.initData();
        this.automaticParsingM.initData();
    }

    @action.bound
    public onAddApiToFolder(): void {
        if (this.apiType === 1) {
            this.moveInFolderRequest();
        }
        if (this.apiType === 2) {
            this.moveInFolderForAutoDoc();
        }
    }

    @Bind
    public async moveInFolderRequest(): Promise<void> {
        const docIdList: number[] = this.manuallyCreateM.getDocIdList();
        if (docIdList.length < 1) {
            message.warn('请选择要添加的API');
            return;
        }
        try {
            runInAction(() => this.addApiBtnLoading = true);
            const params = {
                docIdList,
                folderId: this.folderId
            };
            await nsMockManageKoasApiManageFolderMoveInFolderRequestPost.remote(params);
            message.success('移入成功～');
            runInAction(() => this.addApiBtnLoading = false);
            this.onAddApiCallback && this.onAddApiCallback(this.folderId);
            this.closeAddApiModal();
        } catch {
            runInAction(() => this.addApiBtnLoading = false);
        }
    }

    @Bind
    public async moveInFolderForAutoDoc(): Promise<void> {
        const docIdList: number[] = this.automaticParsingM.getDocIdList();
        if (docIdList.length < 1) {
            message.warn('请选择要添加的API');
            return;
        }
        try {
            runInAction(() => this.addApiBtnLoading = true);
            const params = {
                docIdList,
                folderId: this.folderId
            };
            await nsMockManageKoasApiManageFolderMoveInFolderForAutoDocPost.remote(params);
            message.success('移入成功～');
            runInAction(() => this.addApiBtnLoading = false);
            this.onAddApiCallback && this.onAddApiCallback(this.folderId);
            this.closeAddApiModal();
        } catch {
            runInAction(() => this.addApiBtnLoading = false);
        }
    }
}
