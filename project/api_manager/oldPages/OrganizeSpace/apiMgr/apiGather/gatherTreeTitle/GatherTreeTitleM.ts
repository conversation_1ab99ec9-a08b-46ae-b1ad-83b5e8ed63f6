import { AViewModel } from 'libs';
import { observable, action, runInAction } from 'mobx';
import { CreateEditNameM } from '@/business/createEditName/CreateEditName';
import { nsMockManageKoasApiManageFolderUpdateFloderPost } from '@/remote';
import { Bind } from 'lodash-decorators';
import { message } from 'antd';

interface ITreeData {
    key: string;
    title: string;
    type: string;
    isLeaf?: boolean;
    children?: ITreeData[];
}

export class GatherTreeTitleM extends AViewModel {
    public nodeData: ITreeData | null = null;
    @observable public key: string = '';
    @observable public id: number = 0;
    @observable public title: string = '';
    @observable public status: number = 0;
    @observable public statusDesc: string = '';
    @observable public type: string = '';
    @observable public hover: boolean = false;

    public deleteNodeData?(nodeData: ITreeData): void;

    // public createEditNameM = new CreateEditNameM();

    constructor(nodeData: ITreeData) {
        super();
        this.init(nodeData);
    }

    @action
    public init(nodeData: ITreeData): void {
        this.nodeData = nodeData;
        this.setFields(nodeData);
    }

    @action.bound
    public onMouseEnter(): void {
        this.hover = true;
    }

    @action.bound
    public onMouseLeave(): void {
        this.hover = false;
    }

    // @action.bound
    // public openCreateEditModal(e): void {
    //     e.stopPropagation();
    //     this.createEditNameM.init(this.title, this.id);
    //     this.createEditNameM.onSaveNameCallback = this.updateFolder;
    //     this.hover = false;
    // }

    // @action.bound
    // private onSaveGatherCallback(name: string) {
    //     this.title = name;
    // }

    @action.bound
    public delete(): void {
        this.deleteNodeData && this.nodeData && this.deleteNodeData(this.nodeData);
    }

    @Bind
    private async updateFolder(name: string): Promise<void> {
        try {
            const params = {
                id: this.id,
                name
            };
            await nsMockManageKoasApiManageFolderUpdateFloderPost.remote(params);
            message.success('修改成功～');
            runInAction(() => this.title = name);
        } catch {
        }
    }
}
