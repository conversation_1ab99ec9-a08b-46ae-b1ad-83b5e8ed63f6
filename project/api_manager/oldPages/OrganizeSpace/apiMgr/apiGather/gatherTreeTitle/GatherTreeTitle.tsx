import { AView } from 'libs';
import React from 'react';
import { observer, inject } from 'mobx-react';
import { Bind } from 'lodash-decorators';
import { GatherTreeTitleM } from './GatherTreeTitleM';
import css from './GatherTreeTitle.less';
import { Button, Modal, Tooltip, Tag } from 'antd';
import { KdevIconFont } from '@/business/commonComponents/KdevIconFont';
import { apiStatusEnum } from '@/business/toolFun/ToolFun';

interface IProps {
    addApiM?: any;
    apiGatherM?: any;
}

@inject('addApiM', 'apiGatherM')
@observer
export class GatherTreeTitle extends AView<GatherTreeTitleM, IProps> {

    @Bind
    public componentWillReceiveProps(nextProps): void {
        this.model = nextProps.model;
    }

    @Bind
    private onOpenAddApi(): void {
        this.props.addApiM?.init && this.props.addApiM?.init(this.model.id);
    }

    @Bind
    private renderTreeIcon(): React.ReactNode {
        const model = this.model;
        if (model.type === 'folder') {
            return (
                <KdevIconFont
                    id={'#iconfolder'}
                    className={css.treeTitleFolderIcon}
                />
            );
        }
    }

    @Bind
    private onDeleteConfirm(e): void {
        e.stopPropagation();
        Modal.confirm({
            content: this.model.type === 'folder' ? '确认是否删除？删除后将无法恢复！' : '确认是否移除API？',
            onOk: () => this.model.delete()
        });
    }

    @Bind
    private openCreateEditModal(): void {
        this.props.apiGatherM.openCreateEditModa(this.model.nodeData);
    }

    @Bind
    private renderOperate(): React.ReactNode {
        const model = this.model;
        if (model.hover) {
            return (
                <div className={css.treeTitleRight}>
                    {
                        model.type === 'folder' &&
                        <>
                            <Button
                                icon={<KdevIconFont id={'#iconxingzhuangjiehe'} />}
                                size={'small'}
                                type={'link'}
                                onClick={this.openCreateEditModal}
                            />
                            <Button
                                icon={<KdevIconFont id={'#iconadd'} className={css.addIcon} />}
                                size={'small'}
                                type={'link'}
                                onClick={this.onOpenAddApi}
                            />
                        </>
                    }
                    <Tooltip title={model.type === 'folder' ? '删除集合' : '移除API'}>
                        <Button
                            icon={<KdevIconFont id={'#iconyanse'} />}
                            size={'small'}
                            type={'link'}
                            danger
                            onClick={this.onDeleteConfirm}
                        />
                    </Tooltip>
                </div>
            );
        }
    }

    @Bind
    private renderApiStatus(): React.ReactNode {
        const model = this.model;
        if (model.type === 'file') {
            return (
                <Tag
                    color={apiStatusEnum[model.status]?.color}
                    // onClick={model.onOpenApiStatus}
                    className={css.apiStatusDesc}
                >
                    {model.statusDesc}
                </Tag>
            );
        }
    }

    public render() {
        const model = this.model;
        return (
            <div
                className={css.gatherTreeTitle}
                onMouseEnter={model.onMouseEnter}
                onMouseLeave={model.onMouseLeave}
            >
                <div className={css.treeTitleLeft}>
                    {this.renderApiStatus()}
                    {this.renderTreeIcon()}
                    {model.title}
                </div>
                {this.renderOperate()}
            </div>
        );
    }
}
