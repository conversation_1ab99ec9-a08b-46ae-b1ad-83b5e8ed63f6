.gatherTreeTitle{
    display: flex;
    align-items: center;
    height: 28px;
    justify-content: space-between;

    .treeTitleFolderIcon{
        margin-right: 4px;
    }

    .treeTitleFileIcon{
        font-size: 12px;
        margin-right: 4px;
        color: #327DFF;
    }

    .addIcon{
        font-size: 18px;
    }

    .treeTitleLeft{
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;

        .apiStatusDesc{
            // position: absolute;
            transform: scale(.7);
            left: 0;
            top: 4px;
        }
    }

    .treeTitleRight{
        white-space: nowrap;
    }
}