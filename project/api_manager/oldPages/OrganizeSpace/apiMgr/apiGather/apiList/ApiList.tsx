import { AView, bindObserver } from 'libs';
import React from 'react';
import { observer } from 'mobx-react';
import { Bind } from 'lodash-decorators';
import { ApiListM } from './ApiListM';
import css from './ApiList.less';
import { Button, Modal, Table, Input, Tooltip, Tag } from 'antd';
import { KdevIconFont } from '@/business/commonComponents/KdevIconFont';
import { ApiDiffBranch } from '@/business/httpApiComponents/apiDiffBranch/ApiDiffBranch';
import { StandardInfo } from '@/business/apiDetail/standardInfo/StandardInfo';
import { ERouter } from 'CONFIG';
import { apiStatusEnum } from '@/business/toolFun/ToolFun';
import { ApiStatus } from '@/business/apiStatus/ApiStatus';
import { priorityColor } from '@/business/apiDetail/httpApiBaseInfo/configure';

const { Search } = Input;

const Input_keyWord = bindObserver(Search, 'keyWord');

interface IProps {
    addApiM?: any;
    apiGatherM?: any;
}

@observer
export class ApiList extends AView<ApiListM, IProps> {

    @Bind
    public componentDidMount(): void {
        // this.model.init();
    }

    @Bind
    private onOpenApiDetail(id: number): void {
        this.props.apiGatherM.onOpenApiDetail(id);
    }

    @Bind
    private renderApiName(record): React.ReactNode {
        return (
            <a className={css.apiName}
                onClick={() => this.onOpenApiDetail(record.id)}
            >
                {record.apiName}
            </a>
        );
    }

    @Bind
    private renderStatus(record): React.ReactNode {
        return (
            <Tag
                className={css.tag}
                color={apiStatusEnum[record.status].color}
                onClick={this.model.onOpenApiStatus.bind(this.model, record)}
            >
                {record.statusDesc}
            </Tag>
        );
    }

    @Bind
    private renderApiPath(reocrd): React.ReactNode {
        return (
            <div className={css.apiPath}>
                <span className={css.method}>{reocrd.apiMethod}</span>
                {reocrd.apiUrl}
            </div>
        );
    }

    @Bind
    private onOpenEditApi(docId: number): void {
        this.props.apiGatherM.onOpenEditApi(docId);
    }

    // 确认是否移除API
    @Bind
    public onDeleteApi(docId: number) {
        Modal.confirm({
            content: '确认是否移除API？',
            onOk: () => this.props.apiGatherM.onDeleteApi(docId, this.model.folderId)
        });
    }

    @Bind
    private renderApiOperate(record): React.ReactNode {
        return (
            <>
                <Tooltip title={'编辑'}>
                    <Button
                        icon={<KdevIconFont id={'#iconxingzhuangjiehe'} />}
                        onClick={() => this.onOpenEditApi(record.id)}
                    />
                </Tooltip>
                <Tooltip title={'移除API'}>
                    <Button
                        icon={<KdevIconFont id={'#iconyanse'} />}
                        className={css.deleteBtn}
                        onClick={() => this.onDeleteApi(record.id)}
                    />
                </Tooltip>
            </>
        );
    }

    @Bind
    private renderIsDiffTitle(): React.ReactNode {
        return (
            <span>
                一致性校验
                <Tooltip title="API文档与最新编译的一个分支进行对比，校验文档与代码是否一致">
                    <KdevIconFont id="#iconquestion" />
                </Tooltip>
            </span>
        );
    }

    @Bind
    private ignoreConfirm(docId: number): void {
        Modal.confirm({
            content: '是否要忽略该文档与最新代码的一致性？',
            onOk: () => this.model.ignoredConsistency(docId)
        });
    }

    @Bind
    private renderIsDiff(record): React.ReactNode {
        if (record.isDiff) {
            return (
                <>
                    <Tooltip title={'点击查看详情'}>
                        <a
                            className={record.isDiff !== 2 ? css.redColor : css.greenColor}
                            href={`${ERouter.API_MOCK_VERSIONCOMPARISON}?artificailId=${record.id}`}
                            target="_blank"
                        >
                            {record.isDiff !== 2 ? '未通过' : '通过'}
                        </a>
                    </Tooltip>
                    {
                        record.isDiff === 1 &&
                        <a className={css.ignore} onClick={() => this.ignoreConfirm(record.id)}>
                            忽略
                        </a>
                    }
                    {
                        record.isDiff === 3 &&
                        <span className={css.noIgnore}>（已忽略）</span>
                    }
                </>
            );
        }
        return '-';
    }

    @Bind
    private renderIsStandard(record): React.ReactNode {
        return (
            <>
                <span style={{ color: record.isStandard === 2 ? '#ff4d4f' : '#31bf30' }}>
                    {record.isStandard === 2 ? '未通过' : '通过'}
                </span>
                {
                    record.isStandard === 2 &&
                    <a
                        className={css.detail}
                        onClick={() => this.model.onOpenStandardInfo(record.id)}
                    >
                        详情
                    </a>
                }
            </>
        );
    }

    @Bind
    private columns(): any[] {
        const model = this.model;
        const column = [
            {
                title: 'API名称',
                // dataIndex: 'apiName',
                key: 'apiName',
                width: 120,
                render: this.renderApiName
            },
            {
                title: '状态',
                // dataIndex: 'apiName',
                key: 'status',
                align: 'center',
                width: 100,
                render: this.renderStatus
            },
            {
                title: 'PATH',
                // dataIndex: 'apiUrl',
                key: 'apiUrl',
                width: 200,
                render: this.renderApiPath
            },
            {
                title: '关联分支',
                dataIndex: 'apiBranch',
                key: 'apiBranch',
                width: 100,
            },
            {
                title: '优先级',
                dataIndex: 'priority',
                key: 'priority',
                width: 90,
                render: text => <span style={{color: priorityColor[text]}}>{text}</span>
            },
            {
                title: '负责人',
                // dataIndex: 'createUser',
                key: 'createUser',
                width: 130,
                render: record => record.owner || record.createUser
            },
            {
                title: this.renderIsDiffTitle,
                // dataIndex: 'isTheSame',
                key: 'isDiff',
                align: 'center',
                width: 130,
                render: this.renderIsDiff
            },
            {
                title: '规范性校验',
                // dataIndex: 'isStandard',
                width: 100,
                render: this.renderIsStandard
            },
            {
                title: '操作',
                // dataIndex: 'operation',
                key: 'operation',
                align: 'right',
                fixed: 'right',
                width: 140,
                render: this.renderApiOperate
            }
        ];
        return column;
    }

    @Bind
    private onOpenAddApi(): void {
        this.props.addApiM?.init(this.model.folderId);
    }

    @Bind
    private renderApiListSearch(): React.ReactNode {
        return (
            <div className={css.apiListSearch}>
                <Input_keyWord
                    model={this.model}
                    className={css.searchInput}
                    placeholder="支持API名称搜索"
                    onSearch={this.model.onSearchDocList}
                    allowClear
                />
                <Button
                    type={'primary'}
                    onClick={this.onOpenAddApi}
                    disabled={!this.model.folderId}
                >
                    添加API
                </Button>
            </div>
        );
    }

    public render() {
        const model = this.model;
        return (
            <div className={css.apiListWrap}>
                {this.renderApiListSearch()}
                <Table
                    className={css.apiListTable}
                    columns={this.columns()}
                    bordered
                    dataSource={model.apiList}
                    loading={model.apiListLoading}
                    rowKey="id"
                    pagination={{
                        showTotal: total => `共 ${total} 条`,
                        current: model.currentPage,
                        pageSize: model.pageSize,
                        showSizeChanger: true,
                        total: model.total,
                        onChange: model.onChangePageInfo,
                    }}
                    scroll={{
                        y: model.tableScrollY
                    }}
                />
                <ApiDiffBranch model={model.apiDiffBranchM} />
                <StandardInfo model={model.standardInfoM} />
                <ApiStatus model={model.apiStatusM}/>
            </div>
        );
    }
}
