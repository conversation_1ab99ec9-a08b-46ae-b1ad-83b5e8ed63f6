.apiListWrap {
    padding: 16px;
    height: 100%;

    .apiListSearch {
        display: flex;
        justify-content: space-between;

        .searchInput {
            width: 400px;
        }
    }

    .apiListTable {
        margin-top: 16px;
        height: calc(100% - 48px);
        overflow: auto;

        .apiName {
            word-break: break-word;
            display: block;
            min-width: 60px;
        }

        .apiPath {
            word-break: break-word;

            .method {
                background-color: rgba(50, 125, 255, .08);
                // opacity: 0.08;
                color: #327dff;
                margin-right: 8px;
                border-radius: 4px;
                padding: 0 4px;
            }
        }

        .ignore {
            font-size: 12px;
            margin: 4px 0 0 4px;
        }

        .noIgnore {
            font-size: 12px;
            margin-top: 4px;
            color: #898a8c;
        }

        .redColor {
            color: #ff4d4f;
        }

        .greenColor {
            color: #31bf30;
        }

        .detail{
            margin-left: 4px;
            font-size: 12px;
        }

        .deleteBtn {
            margin: 0 8px;
        }

        .focusBtn {
            background-color: #ffa114;
            border: 1px solid #eb9008;
            color: #ffffff;
        }
    }
}