import { AViewModel } from 'libs';
import { observable, action, runInAction } from 'mobx';
import {
    nsMockManageKoasApiManageFolderQueryDocListGet, nsMockManageKoasApiManageHttpApiAttentionPost,
    nsMockManageKoasApiManageHttpApiIgnoredConsistencyPost
} from '@/remote';
import { Bind } from 'lodash-decorators';
import { message } from 'antd';
import { ApiDiffBranchM } from '@/business/httpApiComponents/apiDiffBranch/ApiDiffBranchM';
import { StandardInfoM } from '@/business/apiDetail/standardInfo/StandardInfoM';
import { ApiStatusModel } from '@/business/apiStatus/ApiStatusModel';

export class ApiListM extends AViewModel {
    @observable public keyWord: string = '';
    @observable public folderId: number = 0;
    @observable public apiList: nsMockManageKoasApiManageFolderQueryDocListGet.IDocList[] = [];
    @observable public apiListLoading: boolean = false;
    @observable public currentPage: number = 1;
    @observable public pageSize: number = 10;
    @observable public total: number = 0;
    @observable public tableScrollY: number = 200;

    private setApiListStatus?(apiId: number, status: number, statusDesc: string): void;

    public apiDiffBranchM = new ApiDiffBranchM();
    public standardInfoM = new StandardInfoM();
    public apiStatusM = new ApiStatusModel();

    @action.bound
    public init(folderId: number, setApiStatus): void {
        this.folderId = folderId;
        if (this.folderId) {
            this.onChangePageInfo(1);
        }
        this.setApiListStatus = setApiStatus;
        this.tableScrollY = document.documentElement.clientHeight - 210;
    }

    // 修改表格列表以及左侧树列表的API状态
    @Bind
    private setApiStatus(status: number, statusDesc: string, apiId: number): void {
        this.queryDocList();
        this.setApiListStatus && this.setApiListStatus(apiId, status, statusDesc);
        // this.parentThis && this.parentThis.setApiStatus(this.groupId, apiId, status);
    }

    // 打开修改状态弹框
    public onOpenApiStatus(record): void {
        this.apiStatusM.init(record.id, record.status);
        this.apiStatusM.onSaveCallback = this.setApiStatus;
    }

    @Bind
    public onOpenApiDiffBranch(docId: number): void {
        this.apiDiffBranchM.init(docId);
    }
    @action.bound
    public onOpenStandardInfo(docId: number): void {
        this.standardInfoM.init(docId, 2);
    }

    @action.bound
    public onChangePageInfo(currentPage: number, pageSize?: number): void {
        this.currentPage = currentPage;
        this.pageSize = pageSize || this.pageSize;
        this.queryDocList();
    }

    @action.bound
    public onSearchDocList(keyWord: string): void {
        this.keyWord = keyWord;
        this.onChangePageInfo(1);
    }

    @Bind
    private async queryDocList(): Promise<void> {
        runInAction(() => this.apiListLoading = true);
        try {
            const params = {
                folderId: this.folderId,
                name: this.keyWord,
                pageIndex: this.currentPage,
                pageSize: this.pageSize
            };
            const result = await nsMockManageKoasApiManageFolderQueryDocListGet.remote(params);
            runInAction(() => {
                this.apiList = result?.docList || [];
                this.total = result?.total || 0;
                this.apiListLoading = false;
            });
        } catch {
            runInAction(() => this.apiListLoading = false);
        }
    }

    // 关注、取消关注模块
    @action.bound
    public async attention(record) {
        try {
            const params = {
                apiId: record.id,
                action: record.focus ? 'unfocus' : 'focus'
            };
            await nsMockManageKoasApiManageHttpApiAttentionPost.remote(params);
            runInAction(() => {
                message.success(record.focus ? '取消关注成功' : '关注成功');
                this.queryDocList();
            });
        } catch (e) {
        }
    }

    @Bind
    public async ignoredConsistency(docId: number): Promise<void> {
        try {
            await nsMockManageKoasApiManageHttpApiIgnoredConsistencyPost.remote({ docId });
            this.queryDocList();
            message.success('已忽略');
        } catch {
        }
    }
}
