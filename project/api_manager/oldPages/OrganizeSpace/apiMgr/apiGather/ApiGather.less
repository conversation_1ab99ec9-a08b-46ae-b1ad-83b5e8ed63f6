.apiGather {
    .gatherTreeWrap{
        padding: 16px 16px 24px;
        height: calc(100% - 48px);

        .gatherTreeTop{
            margin-bottom: 16px;
            display: flex;

            .createGatherBtn {
                margin-left: 8px;
            }
        }

        .gatherTree {
            height: 100%;
            overflow: auto;
        }

        .empty {
            padding-top: 40px;

            :global {
              .ant-empty-description {
                opacity: .25;
              }
            }
          }

        :global{
            .ant-tree.ant-tree-directory .ant-tree-treenode-selected:hover::before, .ant-tree.ant-tree-directory .ant-tree-treenode-selected::before {
                background-color: #f5f7fa;
            }

            .ant-tree.ant-tree-directory .ant-tree-treenode .ant-tree-node-content-wrapper.ant-tree-node-selected{
                color: #327DFF;
            }

            .ant-tree.ant-tree-directory .ant-tree-treenode-selected .ant-tree-switcher{
                color: #898A8C;
            }

            .ant-tree .ant-tree-switcher{
                line-height: 28px;
            }

            .ant-tree.ant-tree-block-node .ant-tree-list-holder-inner .ant-tree-node-content-wrapper{
                width: calc(100% - 48px);
            }
        }
    }
}