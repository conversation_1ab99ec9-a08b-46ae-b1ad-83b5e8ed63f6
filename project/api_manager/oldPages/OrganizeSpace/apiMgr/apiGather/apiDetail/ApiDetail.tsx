import { <PERSON><PERSON>iew } from 'libs';
import React from 'react';
import { inject, observer } from 'mobx-react';
import {
    Button, Divider, Tooltip, Skeleton, Tabs, Modal, Tag
} from 'antd';
import Bind from 'lodash-decorators/bind';
import css from './ApiDetail.less';
import { LeftOutlined } from '@ant-design/icons';
import { KdevIconFont } from '@/business/commonComponents/KdevIconFont';
import { ApiDetailM } from './ApiDetailM';
import { VersionRecord } from '@/business/versionRecord/VersionRecord';
import { QRCode } from 'oldPages/OrganizeSpace/QRCode/QRCode';
import { CommentT } from '@/business/apiDetail/comment/Comment';
import { ApiBaseInfo } from '@/business/apiDetail/httpApiBaseInfo/ApiBaseInfo';
import { HttpApiRequestParams } from '@/business/apiDetail/httpApiRequestParams/HttpApiRequestParams';
import { HttpApiResponseParams } from '@/business/apiDetail/httpApiResponseParams/HttpApiResponseParams';
import { apiStatusEnum } from '@/business/toolFun/ToolFun';
import { ApiMock } from '@/business/httpApiComponents/apiMock/ApiMock';
import { ApiTest } from '@/pages/moduleDetail/apiList/rightApiDetail/apiTest/ApiTest';
import { ApiStatus } from '@/business/apiStatus/ApiStatus';
import { AutoGenerateCode } from '@/business/httpApiComponents/AutoGenerateCode';
import { ruleTypeEnum, activeKeyEnum } from './configure';
import { ERouter } from 'CONFIG';
import * as QS from 'query-string';

const { TabPane } = Tabs;

interface IProps {
    apiGatherM?: any;
}

@inject('apiGatherM')
@observer
export class ApiDetail extends AView<ApiDetailM, IProps> {
    @Bind
    protected renderApiMockOperate(): React.ReactNode {
        const model = this.model;
        if (model.activeKey === activeKeyEnum.ONE) {
            return this.renderApiDetailOperate();
        }
        if (model.activeKey === activeKeyEnum.TWO) {
            return (
                <div>
                    <Button
                        icon={<KdevIconFont id={'#iconerweima'} />}
                        onClick={model.onOpenQRCode}
                    />
                    <Button
                        className={css.stopClientMockBtn}
                        loading={model.ruleSwitchLoading}
                        onClick={model.ruleSwitch}
                    >
                        {model.ruleType === ruleTypeEnum.CLIENT ? '停止客户端 mock' : '开启客户端 mock'}
                    </Button>
                    <QRCode model={model.QRCodeM} />
                </div>
            );
        }
    }

    @Bind
    protected renderDetail(model): React.ReactNode {
        if (!model.commentM) {
            return;
        }
        return <div className={css.apiDetail}>
            <ApiBaseInfo model={model.apiBaseInfoM} />
            <HttpApiRequestParams model={model.httpApiRequestParamsM} />
            <HttpApiResponseParams model={model.httpApiResponseParamsM} />
            <CommentT model={model.commentM} />
        </div>;
    }

    @Bind
    private onOpenEditApi(): void {
        this.props.apiGatherM.onOpenEditApi(this.model.apiId);
    }

    // 确认是否移除API
    @Bind
    public onDeleteApi() {
        Modal.confirm({
            content: '确认是否移除API？',
            onOk: () => this.props.apiGatherM.onDeleteApi(this.model.apiId)
        });
    }

    @Bind
    private renderApiDetailOperate(): React.ReactNode {
        const model = this.model;
        if (model.activeKey === '1') {
            return <div className={css.apiDetailOperate}>
                <Tooltip title={'编辑'}>
                    <Button
                        icon={<KdevIconFont id={'#iconxingzhuangjiehe'} />}
                        className={css.editApiBtn}
                        onClick={this.onOpenEditApi}
                    />
                </Tooltip>
                <Tooltip title={'版本记录'}>
                    <Button
                        icon={<KdevIconFont id={'#iconlishibanben'} />}
                        className={css.versionRecordBtn}
                        onClick={model.onOpenVersionRecord}
                    />
                </Tooltip>
                <Tooltip title={'移除API'}>
                    <Button
                        icon={<KdevIconFont id={'#iconyanse'} />}
                        onClick={this.onDeleteApi}
                    />
                </Tooltip>
            </div>;
        }
    }

    @Bind
    private jumpToGather(): void {
        this.props.apiGatherM.onColseApiDetail();
    }

    @Bind
    private renderApiStatusDesc(): React.ReactNode {
        const model = this.model;
        if (model.statusDesc) {
            return (
                <Tooltip title="点击修改API状态">
                    <Tag
                        color={apiStatusEnum[model.status]?.color}
                        onClick={model.onOpenApiStatus}
                        className={css.apiStatusDesc}
                    >
                        {model.statusDesc}
                    </Tag>
                </Tooltip>
            );
        }
    }

    @Bind
    private renderApiTitle(): React.ReactNode {
        const model = this.model;
        const searchParams: object = QS.parse(location.search) || {};
        searchParams['moduleId'] = model.moduleId;
        return (
            <div className={css.apiTitleWrap}>
                <Button
                    icon={<LeftOutlined />}
                    className={css.backBtn}
                    type={'link'}
                    onClick={this.jumpToGather}
                />
                <Divider type={'vertical'} className={css.dividerVertical} />
                {model.apiName}
                {this.renderApiStatusDesc()}
                <span className={css.lastUpdateTime}>更新时间：{model.lastUpdateTime}</span>
                {
                    model.activeKey === activeKeyEnum.ONE &&
                    <AutoGenerateCode docId={model.apiId} className={css.dropDown} />
                }
                {
                    model.activeKey === activeKeyEnum.TWO &&
                    <a
                        className={css.proxyConfigBtn}
                        href={`${ERouter.API_MOCK_PROXY_CONFIG}?${QS.stringify(searchParams)}`}
                    // target="_blank"
                    >代理配置</a>
                }
            </div>
        );
    }

    @Bind
    private renderCon(): React.ReactNode {
        const model = this.model;
        return (
            <Tabs
                activeKey={model.activeKey}
                onChange={model.onChangeActiveKey}
                tabBarExtraContent={this.renderApiMockOperate()}
                className={css.apiTabs}
            >
                <TabPane tab={'API详情'} key={activeKeyEnum.ONE}>
                    {this.renderDetail(model)}
                </TabPane>
                <TabPane tab={'Mock'} key={activeKeyEnum.TWO}>
                    <ApiMock model={model.apiMockM} />
                </TabPane>
                <TabPane tab={'测试'} key={activeKeyEnum.THREE}>
                    <ApiTest model={model.apiTestM} />
                </TabPane>
            </Tabs>
        );
    }

    public render(): React.ReactNode {
        const model = this.model;
        if (model.queryDetailLoading) {
            return <Skeleton active className={css.skeleton} />;
        }
        return (
            <div className={css.apiDetailWrap}>
                {this.renderApiTitle()}
                {this.renderCon()}
                <VersionRecord model={model.versionRecordM} />
                <ApiStatus model={model.apiStatusM} />
            </div>
        );
    }
}
