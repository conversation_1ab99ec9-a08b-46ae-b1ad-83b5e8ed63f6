.apiDetailWrap {
  height: 100%;
  padding: 16px 16px 24px;
  overflow: hidden;

  .apiTitleWrap {
    line-height: 32px;

    .backBtn {
      color: #252626;
    }

    .backBtn:hover {
      color: #327DFF;
    }

    .dividerVertical {
      height: 20px;
    }

    .apiStatusDesc {
      margin-left: 8px;
    }

    .lastUpdateTime {
      color: #898a8c;
      font-size: 12px;
    }

    .dropDown, .proxyConfigBtn {
      float: right;
    }
  }

  .apiTabs {
    height: 100%;

    .versionRecordBtn {
      margin: 0 8px;
    }

    .stopClientMockBtn {
      margin-left: 8px;
    }

    .apiDetail {
      height: 100%;
      overflow: auto;
    }
  }

  :global {
    .ant-tabs-nav-list {
      padding: 0;
    }

    .ant-tabs-content {
      padding: 16px 0 32px;
    }

    .ant-tabs-content-holder,
    .ant-tabs-content {
      height: 100%;
      overflow: auto;
    }
  }
}

.skeleton {
  padding: 8px 16px;
}

//.menuOverlay {
//  :global {
//    .ant-dropdown-menu-submenu-arrow {
//      display: none;
//    }
//  }
//}