import { AViewModel } from 'libs';
import { observable, action, runInAction, } from 'mobx';
import { JSONbigStringify } from '@/index.config/tools';
import {
    nsMockManageKoasApiManageHttpApiQueryDetailGet,
    nsGenerateCodePost, nsMockManageKoasApiManageHttpApiGenerateCodePost, nsMockManageKoasApiManageMockRuleSwitchPost,
    nsMockManageKoasApiManageSceneQuerySwitchInfoGet,
    nsMockManageKoasApiManageHttpApiAttentionPost
} from '@/remote';
import { VersionRecordModel } from '@/business/versionRecord/VersionRecordModel';
import { QRCodeM } from 'oldPages/OrganizeSpace/QRCode/QRCodeM';
import { CommentM } from '@/business/apiDetail/comment/CommentM';
import { departmentCascader } from '@/business/global';
import { ApiBaseInfoM } from '@/business/apiDetail/httpApiBaseInfo/ApiBaseInfoM';
import { HttpApiRequestParamsM } from '@/business/apiDetail/httpApiRequestParams/HttpApiRequestParamsM';
import { HttpApiResponseParamsM } from 'oldPages/HttpApiDetail/component/httpApiResponseParams/HttpApiResponseParamsM';
import { ExampleAceM } from '@/business/apiDetail/exampleAce/ExampleAceM';
import { message } from 'antd';
import { ApiMockM } from '@/business/httpApiComponents/apiMock/ApiMockM';
import { ApiTestM } from '@/pages/moduleDetail/apiList/rightApiDetail/apiTest/ApiTestM';
import { ApiStatusModel } from '@/business/apiStatus/ApiStatusModel';
import { Bind } from 'lodash-decorators';
import { ruleTypeEnum, focusEnum, activeKeyEnum } from './configure';

interface IUrlParams {
    languageType: string;
    language: string;
}

export class ApiDetailM extends AViewModel {
    @observable public departmentId: number = departmentCascader.getDepartmentId();
    @observable public apiId: number = 0;
    @observable public activeKey: string = activeKeyEnum.ONE;
    @observable public ruleType: number = ruleTypeEnum.WEB; // 2：匹配 1：设备
    @observable public ruleSwitchLoading: boolean = false;
    @observable public QRCodeUrl: string = '';
    @observable public apiName: string = '';
    @observable public groupId: number = 0;
    @observable public status: number = 0;
    @observable public statusDesc: string = '';
    @observable public lastUpdateTime: string = '';
    @observable public focus: boolean = false;
    @observable public queryDetailLoading: boolean = true;

    @observable public reqDataType: string = '';

    @observable public sceneId: number = 0;
    @observable public parentThis: any;
    public moduleId: number = 0;

    public versionRecordM = new VersionRecordModel();
    public QRCodeM = new QRCodeM();
    public commentM: null | CommentM = null;
    public apiBaseInfoM = new ApiBaseInfoM();
    public httpApiRequestParamsM = new HttpApiRequestParamsM();
    public httpApiResponseParamsM = new HttpApiResponseParamsM();
    public reqExampleAceM = new ExampleAceM();
    public resExampleAceM = new ExampleAceM();
    public apiMockM = new ApiMockM();
    public apiTestM = new ApiTestM();
    public apiStatusM = new ApiStatusModel();
    public mockProxyConfigsM: any;

    @action.bound
    public init(docId?: number, parentThis?) {
        this.initData();
        if (docId) {
            this.apiId = docId;
        }
        this.parentThis = parentThis;
        this.queryDetail();
        this.commentM = new CommentM({ apiId: this.apiId });
    }

    @action.bound
    public initData() {
        // this.apiId = 0;
        this.ruleType = ruleTypeEnum.WEB;
        this.QRCodeUrl = '';
        this.reqDataType = '';
        this.status = 0;
        this.statusDesc = '';
        this.apiBaseInfoM.initData();
        this.httpApiRequestParamsM.initData();
        this.reqExampleAceM.initData();
        this.httpApiResponseParamsM.initData();
        this.resExampleAceM.initData();
    }

    // 获取要编辑的代码类型
    @action.bound
    public onChangeCode(e) {
        const urlParams: IUrlParams = {
            languageType: e.keyPath[1], // clients | servers
            language: e.keyPath[0]
        };
        this.getGenerateCodeParams(urlParams);
    }

    // 打开版本记录
    @action.bound
    public onOpenVersionRecord() {
        this.versionRecordM.initLoading(this.apiId);
    }

    // 打开绑定设备二维码弹框
    @action.bound
    public onOpenQRCode(): void {
        this.QRCodeM.init(this.apiId, this.sceneId);
        this.QRCodeM.onCloseQRCodeCallBack = this.onCloseQRCodeCallBack;
    }

    @action.bound
    public onCloseQRCodeCallBack(): void {
        this.querySwitchInfo();
        this.apiMockM.getAllMockDataList();
    }

    // 打开API status记录
    @action.bound
    public onOpenApiStatus() {
        this.apiStatusM.init(this.apiId, this.status);
        this.apiStatusM.onSaveCallback = this.setApiStatus;
    }

    @action.bound
    private setApiStatus(status: number, statusDesc: string): void {
        this.status = status;
        this.statusDesc = statusDesc;
        this.parentThis.setApiStatus(this.apiId, this.status, this.statusDesc);
    }

    @action.bound
    public onChangeActiveKey(activeKey: string): void {
        this.activeKey = activeKey;
        switch (this.activeKey) {
            case activeKeyEnum.ONE:
                this.apiMockM.init(this.apiId, this.departmentId);
                if (this.ruleType === ruleTypeEnum.CLIENT) {
                    this.querySwitchInfo();
                }
                break;
            case activeKeyEnum.THREE:
                this.apiTestM.initLoading(this.apiId, this.status, this.setApiStatus);
                break;
            default:
                break;
        }
    }

    @action.bound
    private getReqBodyExample(parameters): void {
        if (parameters.length) {
            parameters.forEach(item => {
                if (item.in === 'body') {
                    const example = JSONbigStringify(item.example) || '';
                    this.reqExampleAceM.onChangeExample(example);
                }
            });
        }
    }

    @action.bound
    private initBaseInfo(baseInfo): void {
        this.apiName = baseInfo?.name;
        this.ruleType = baseInfo?.ruleType || ruleTypeEnum.WEB;
        this.focus = baseInfo?.focus;
        this.groupId = baseInfo?.groupId;
        this.status = baseInfo?.status;
        this.statusDesc = baseInfo?.statusDesc;
        this.lastUpdateTime = baseInfo?.lastUpdateTime;
        this.moduleId = baseInfo?.moduleId;
        this.onChangeActiveKey(this.activeKey);
        this.apiBaseInfoM.setBaseInfo(baseInfo);
    }

    // 获取详情
    @action.bound
    public async queryDetail() {
        this.queryDetailLoading = true;
        try {
            const params = {
                id: this.apiId
            };
            const result = await nsMockManageKoasApiManageHttpApiQueryDetailGet.remote(params);
            runInAction(() => {
                result?.baseInfo && this.initBaseInfo(result?.baseInfo);
                result?.request && this.httpApiRequestParamsM.setData(result.request);
                result?.request?.parameters && this.getReqBodyExample(result?.request?.parameters);
                result?.response && this.httpApiResponseParamsM.setData(result.response);
                if (result?.response?.body?.example) {
                    const example: string = JSONbigStringify(result?.response.body.example) || '';
                    this.resExampleAceM.onChangeExample(example);
                }
                this.queryDetailLoading = false;
            });
        } catch (e) {
            runInAction(() => {
                this.queryDetailLoading = false;
            });
        }
    }

    // 获取生成代码参数
    @action.bound
    public async getGenerateCodeParams(urlParams: IUrlParams) {
        try {
            const params = {
                id: this.apiId
            };
            const result = await nsMockManageKoasApiManageHttpApiGenerateCodePost.remote(params);
            runInAction(() => {
                this.generateCode(urlParams, result);
            });
        } catch (e) {
        }
    }

    // 一键生成代码
    @action.bound
    public async generateCode(urlParams: IUrlParams, spec) {
        try {
            const parmas = {
                spec
            };
            const result = await nsGenerateCodePost.remote(urlParams, parmas);
            runInAction(() => {
                window.open(`https://generator.swagger.io/api/gen/download/${result.code}`);
            });
        } catch (e) {
        }
    }

    // 返回规则切换
    @Bind
    public async ruleSwitch() {
        runInAction(() => this.ruleSwitchLoading = true);
        const ruleType: number = this.ruleType === ruleTypeEnum.CLIENT ? ruleTypeEnum.WEB : ruleTypeEnum.CLIENT;
        try {
            const params = {
                docId: this.apiId,
                status: ruleType
            };
            const result = await nsMockManageKoasApiManageMockRuleSwitchPost.remote(params);
            runInAction(() => {
                this.ruleSwitchLoading = false;
                if (result?.code !== 0 && ruleType === ruleTypeEnum.CLIENT) {
                    this.onOpenQRCode();
                } else {
                    this.ruleType = ruleType;
                    this.apiMockM.getAllMockDataList();
                }
            });
        } catch (e) {
            runInAction(() => this.ruleSwitchLoading = false);
        }
    }

    @Bind
    public async querySwitchInfo() {
        try {
            const params = {
                docId: this.apiId,
                departmentId: this.departmentId
            };
            const result = await nsMockManageKoasApiManageSceneQuerySwitchInfoGet.remote(params);
            runInAction(() => {
                this.sceneId = result?.sceneId;
                this.ruleType = result?.ruleType;
                if (result?.code !== 0) {
                    this.onOpenQRCode();
                }
                this.apiMockM.mockDataM.setScene({ sceneId: this.sceneId, sceneName: result?.sceneName });
            });
        } catch (e) {
        }
    }

    // 关注、取消关注模块
    @Bind
    public async attention() {
        try {
            const params = {
                apiId: this.apiId,
                action: this.focus ? focusEnum.UNFOCUS : focusEnum.FOCUS
            };
            await nsMockManageKoasApiManageHttpApiAttentionPost.remote(params);
            runInAction(() => {
                this.focus = !this.focus;
                message.success(this.focus ? '关注成功' : '取消关注成功');
            });
        } catch (e) {
        }
    }
}
