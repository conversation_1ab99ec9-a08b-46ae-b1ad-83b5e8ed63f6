import { AView } from 'libs';
import React from 'react';
import { observer, Provider } from 'mobx-react';
import { Bind } from 'lodash-decorators';
import { bindObserver } from '@libs/mvvm';
import { ApiGatherM } from './ApiGatherM';
import css from './ApiGather.less';
import SplitPane from 'react-split-pane';
import Pane from 'react-split-pane/lib/Pane';
import { Input, Button, Tree, Empty, Skeleton } from 'antd';
import { CreateEditName } from '@/business/createEditName/CreateEditName';
import { GatherTreeTitle } from './gatherTreeTitle/GatherTreeTitle';
import { GatherTreeTitleM } from './gatherTreeTitle/GatherTreeTitleM';
import { AddApi } from './addApiToGather/AddApi';
import { ApiList } from './apiList/ApiList';
import { ApiDetail } from './apiDetail/ApiDetail';
import { EditApi } from './editApi/EditApi';
import { SearchEmptyIcon } from '@/business/commonIcon';
import { EditJsonStore } from '@/business/editJsonTable/EditJsonStore';

const { DirectoryTree } = Tree;

const { Search } = Input;

const Search_keyWord = bindObserver(Search, 'keyWord');

@observer
export class ApiGather extends AView<ApiGatherM> {
    public editJsonStore = new EditJsonStore();

    @Bind
    private renderGatherTreeTop(): React.ReactNode {
        const model = this.model;
        return (
            <div className={css.gatherTreeTop}>
                <Search_keyWord model={model} placeholder={'搜索集合'} onSearch={model.onSearchGather} />
                <Button
                    type={'primary'}
                    className={css.createGatherBtn}
                    onClick={() => model.openCreateEditModa(null)}
                >
                    新建
                </Button>
            </div>
        );
    }

    @Bind
    private renderTreeTitle(nodeData): React.ReactNode {
        const gatherTreeTitleM = new GatherTreeTitleM(nodeData);
        gatherTreeTitleM.deleteNodeData = this.model.deleteNodeData;
        return (
            <Provider addApiM={this.model.addApiM} apiGatherM={this.model}>
                <GatherTreeTitle model={gatherTreeTitleM} />
            </Provider>
        );
    }

    @Bind
    private renderGatherTree(): React.ReactNode {
        const model = this.model;
        if (model.gatherLoading) {
            return <Skeleton />;
        }
        if (!model.treeData?.length) {
            return (
                <Empty
                    className={ css.empty }
                    image={ <SearchEmptyIcon /> }
                    description={ '还未创建集合，快去创建吧～' }
                />
            );
        }
        return (
            <DirectoryTree
                treeData={model.treeData}
                blockNode
                className={css.gatherTree}
                titleRender={this.renderTreeTitle}
                icon={false}
                selectedKeys={model.selectedKeys}
                onSelect={model.onSelectKeys}
                expandedKeys={model.expandedKeys}
                onExpand={model.onExpandKeys}
                // expandAction={'doubleClick'}
                loadedKeys={model.loadedKeys}
                loadData={model.loadDataTreeData}
            />
        );
    }

    @Bind
    private renderGather(): React.ReactNode {
        const model = this.model;
        return (
            <div className={css.gatherTreeWrap}>
                {this.renderGatherTreeTop()}
                {this.renderGatherTree()}
                <CreateEditName model={model.createEditNameM} nameLabel="集合名称" title="新建集合" />
                <AddApi model={model.addApiM} />
            </div>
        );
    }

    @Bind
    private renderRightPane(): React.ReactNode {
        const model = this.model;
        if (model.showType === 'view') {
            return (<ApiDetail model={model.apiDetailM} apiGatherM={model}/>);
        }
        if (model.showType === 'new' || model.showType === 'edit') {
            return <EditApi model={model.editApiM} apiGatherM={model}/>;
        }
        return (<ApiList model={model.apiListM} addApiM={model.addApiM} apiGatherM={model} />);
    }

    public render() {
        const model = this.model;
        return (
            <Provider editJsonStore={this.editJsonStore}>
                <SplitPane className={css.apiGather} onChange={model.onChangePaneSize}>
                    <Pane minSize={'246px'} size={model.leftPaneSize}>{this.renderGather()}</Pane>
                    <Pane minSize={'857px'}>{this.renderRightPane()}</Pane>
                </SplitPane>
            </Provider>
        );
    }
}
