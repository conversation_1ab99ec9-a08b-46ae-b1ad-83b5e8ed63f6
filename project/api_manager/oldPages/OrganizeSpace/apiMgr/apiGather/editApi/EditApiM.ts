import { AViewModel } from 'libs';
import { observable, action, runInAction } from 'mobx';
// import * as QS from 'query-string';
import {
    nsMockManageKoasApiManageHttpApiQueryDetailGet, nsMockManageKoasApiManageHttpApiUpdatePost
} from '@/remote';
import { message } from 'antd';
import { ApiBaseInfoM } from './apiBaseInfo/ApiBaseInfoM';
import { RequestParamsM } from '@/business/httpApiComponents/editApi/requestParams/RequestParamsM';
import { ResponseParamsM } from '@/business/httpApiComponents/editApi/responseParams/ResponseParamsM';
import { Bind } from 'lodash-decorators';
import { SaveDocNoticeM } from '@/business/saveDocNotice/SaveDocNotice';

export class EditApiM extends AViewModel {
    @observable public moduleId: number = 0;
    @observable public apiId: number = 0;

    @observable public saveLoading: boolean = false;
    @observable public queryDetailLoading: boolean = false;

    public apiBaseInfoM = new ApiBaseInfoM();
    public requestParamsM = new RequestParamsM();
    public responseParamsM = new ResponseParamsM();

    private parentThis: any;
    public saveDocNoticeM = new SaveDocNoticeM();
    // constructor() {
    //     super();
    //     this.getUrlParams();
    // }

    // @action
    // private getUrlParams(): void {
    //     const objParams: object = QS.parse(location.search);
    //     const selectedKeys = objParams['selectedKeys'] || '';
    //     this.apiId = getKey0OrKey1(selectedKeys)[1];
    // }

    // 加载model
    @action
    public init(docId: number, parentThis) {
        // if (this.apiId === docId) {
        //     return;
        // }
        this.initData();
        this.apiId = docId;
        this.parentThis = parentThis;
        if (this.apiId) {
            this.queryDetail();
        }
        this.apiBaseInfoM.changeMethod = this.onChangeMethod;
    }

    // 初始化数据
    @action.bound
    public initData() {
        this.moduleId = 0;
        this.apiBaseInfoM.initData();
        this.requestParamsM.initData();
        this.responseParamsM.initData();
    }

    @action.bound
    private onChangeMethod(method: string): void {
        this.requestParamsM.changeRequestType(method === 'GET' ? 3 : 2);
    }

    // 基本信息
    @action.bound
    protected initBaseInfo(baseInfo): void {
        this.moduleId = baseInfo.moduleId;
    }

    @action.bound
    protected getParams(): nsMockManageKoasApiManageHttpApiUpdatePost.IParams {
        const baseInfo = this.apiBaseInfoM.getBaseInfo();
        if (this.apiId) {
            baseInfo['id'] = this.apiId;
        }
        return {
            baseInfo,
            request: this.requestParamsM.getRequest(),
            response: this.responseParamsM.getResponse()
        };
    }

    // 校验参数
    @action.bound
    protected checkParams() {
        if (this.apiBaseInfoM.checkParams()) {
            return true;
        }
        if (this.requestParamsM.checkParams()) {
            return true;
        }
        if (this.responseParamsM.checkParams()) {
            return true;
        }
        return false;
    }

    private setReqResBodyExpandedKeys(): void {
        const reqBodyExpandedKeys: string[] = this.requestParamsM.getExpandedKeys();
        const resBodyExpandedKeys: string[] = this.responseParamsM.getExpandedKeys();
        localStorage.setItem('reqBodyExpandedKeys', reqBodyExpandedKeys.join(','));
        localStorage.setItem('resBodyExpandedKeys', resBodyExpandedKeys.join(','));
    }

    // 编辑API
    @Bind
    private onSaveDocCallbck() {
        message.success('更新成功～');
        this.setReqResBodyExpandedKeys();
        this.parentThis.onSaveApiCallback();
    }

    @action.bound
    public onOpenSaveDocNotice(): void {
        if (this.checkParams()) {
            return;
        }
        this.saveDocNoticeM.onOpenSaveDocNotice(this.apiBaseInfoM.cooperList);
        this.saveDocNoticeM.getParams = this.getParams;
        this.saveDocNoticeM.onSaveDocCallbck = this.onSaveDocCallbck;
    }

    // 获取详情
    @Bind
    public async queryDetail() {
        runInAction(() => this.queryDetailLoading = true);
        try {
            const params = {
                id: this.apiId
            };
            const result = await nsMockManageKoasApiManageHttpApiQueryDetailGet.remote(params);
            runInAction(() => {
                this.initBaseInfo(result?.baseInfo);
                result?.baseInfo && this.apiBaseInfoM.init(result.baseInfo);
                result?.request && this.requestParamsM.init(result?.request, result?.baseInfo?.moduleId, this.apiId);
                result?.response && this.responseParamsM.init(result?.response, result?.baseInfo?.moduleId, this.apiId);
                runInAction(() => this.queryDetailLoading = false);
            });
        } catch (e) {
            runInAction(() => this.queryDetailLoading = false);
        }
    }
}
