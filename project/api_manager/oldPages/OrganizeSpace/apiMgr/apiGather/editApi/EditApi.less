.editApiWrap {
  height: calc(100% - 20px);
  position: relative;

  .detail {
    margin-top: 16px;
    height: calc(100% - 68px);
    overflow: auto;
    padding: 0 16px;
  }

  .collapse {
    border: 1px solid #d9d9d9;
    border-radius: 4px;
    margin: 16px 0;

    .collapsePanelRow {
      margin-bottom: 24px;
      display: flex;

      .width50 {
        min-width: calc(50% - 18px);
        display: flex;
        align-items: center;

        .inputPathGroup {
          display: flex;
        }
      }

      .width100 {
        width: 100%;
      }

      .label {
        min-width: 74px;
        line-height: 32px;
      }

      .labelRequired {
        color: #ff4d4f;
      }

      .owner {
        margin-right: 14px;
      }

      .addOwnerBtn {
        line-height: 32px;

        .addOwnerBtnIcon {
          font-size: 20px;
        }
      }
    }

    .margRight32px {
      margin-right: 32px;
      margin-bottom: 16px;
    }

    .margBottom16px {
      margin-bottom: 16px;
    }

    .expandBtn {
      float: right;
    }

    .tableMarg {
      .type {
        width: 100%;
      }

      .deleteBtn {
        margin-right: 8px;
      }

      .addChildRowBtn {
        margin-right: 8px;
      }

      :global {
        .ant-table .ant-table-container .ant-table-tbody .ant-table-row .ant-table-cell:nth-child(1) {
          display: flex;
          align-items: center;
          height: 100%;
        }
      }
    }

    .addRowBtn {
      background-color: #f5f7fa;
      color: #327dff;
      margin-right: 16px;
    }

    .margRight {
      margin-right: 36px;
    }

    .selectWidth200 {
      width: 200px;
      margin-left: 8px;
    }

    .aceEditor {
      border-radius: 4px;
      margin-bottom: 24px;
    }

    .jsonImportBtn {
      margin-top: 16px;
    }

    .jsonTootip {
      margin-left: 4px;
      font-size: 12px;
      color: #faad14;
    }
  }

  .margTop0 {
    margin-top: 0;
  }

  .editApiBottom {
    position: absolute;
    bottom: 0;
    line-height: 68px;
    text-align: center;
    background-color: #ffffff;
    width: 100%;
    z-index: 4;
    padding: 0 24px;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.04), 0 -1px 3px 0 rgba(0, 0, 0, 0.04), 0 1px 2px 1px rgba(0, 0, 0, 0.02), 0 1px 2px 0 rgba(0, 0, 0, 0.05);

    .cancelBtn {
      margin-right: 16px;
    }
  }

  // :global {
  //   .ant-collapse-ghost > .ant-collapse-item > .ant-collapse-content > .ant-collapse-content-box {
  //     padding: 0 24px 24px;
  //   }

  //   .ant-collapse > .ant-collapse-item > .ant-collapse-header {
  //     padding: 24px 24px 24px 40px;
  //   }
  // }
}

.skeleton{
  padding: 8px 16px;
}

.hoverKim {
  color: #252626;

  .kimImg {
    height: 18px;
    margin-right: 6px;
  }
}
