import { AView } from 'libs';
import React from 'react';
import { observer } from 'mobx-react';
import { Bind } from 'lodash-decorators';
import { bindObserver } from '@libs/mvvm';
import { Collapse, Input, Select, Popover, Avatar, Button } from 'antd';
import { ApiBaseInfoM } from './ApiBaseInfoM';
import css from './ApiBaseInfo.less';
import { protocolOptions, methodOptions } from './enum';
import { KdevIconFont } from '@/business/commonComponents/KdevIconFont';
import { CreateMaintainer } from '@/business/createMaintainer/CreateMaintainer';
import { KDevParticipants } from '@/business/commonComponents/KDevParticipants';
import { priorityOptions, readOnlyOptions } from '@/business/httpApiComponents/httpGlobalDefine';
import { RemarkMarkdownEditor } from '@/business/httpApiComponents/editApi/remarkMarkdownEditor/RemarkMarkdownEditor';

const { Panel } = Collapse;
// const { TextArea } = Input;
const { Option } = Select;

const Input_name = bindObserver(Input, 'name');
const Select_protocol = bindObserver(Select, 'protocol');
const Select_method = bindObserver(Select, 'method');
const Input_path = bindObserver(Input, 'path');
// const TextArea_description = bindObserver(TextArea, 'description');

@observer
export class ApiBaseInfo extends AView<ApiBaseInfoM> {

    @Bind
    private renderPopoverName(ele) {
        return (
            <a className={css.hoverKim} href={`kim://username?username=${ele.username}`}>
                <img
                    className={css.kimImg}
                    src={'https://cdnfile.corp.kuaishou.com/kc/files/a/QA-SERVEREE-FE-IN/kim.png'}
                />
                {ele.name + ' ' + ele.username}
            </a>
        );
    }

    @Bind
    private renderOwner(): React.ReactNode {
        const model = this.model;
        return (
            <div className={`${css.width50} ${css.margRight}`}>
                <span className={css.label}>负责人 <span className={css.labelRequired}>*</span></span>
                {
                    model.ownerInfo &&
                    <Popover
                        content={this.renderPopoverName(model.ownerInfo)}
                    >
                        <Avatar
                            className={css.owner}
                            src={model.ownerInfo.photo || ''}
                        >
                            {model.ownerInfo.name}
                        </Avatar>
                    </Popover>
                }
                <CreateMaintainer
                    model={model.createMaintainerM}
                    btnNode={
                        <Button
                            // shape={ 'circle' }
                            type={'link'}
                            icon={<KdevIconFont id={'#iconadd-circle'} className={css.addOwnerBtnIcon} />}
                            className={css.addOwnerBtn}
                        />
                    }
                    handelSearchSelect={model.onHandelSearchSelect}
                />
            </div>
        );
    }

    @Bind
    private renderCooper(): React.ReactNode {
        const model = this.model;
        return (
            <div className={css.width50}>
                <span className={css.label}>研发参与人</span>
                <KDevParticipants
                    value={model.cooperList}
                    onChange={model.onChangeCooper}
                />
            </div>
        );
    }

    @Bind
    private renderQaUsers(): React.ReactNode {
        const model = this.model;
        return (
            <div className={css.qaUsers}>
                <span className={css.label}>测试参与人</span>
                <KDevParticipants
                    value={model.qaUsers}
                    onChange={model.onChangeqaUsers}
                />
            </div>
        );
    }

    @Bind
    private renderApiName(): React.ReactNode {
        return (
            <div className={`${css.width50} ${css.margRight}`}>
                <span className={css.label}>API名称 <span className={css.labelRequired}>*</span></span>
                <Input_name model={this.model} placeholder={'请输入API名称'} />
            </div>
        );
    }

    @Bind
    private renderApiPath(): React.ReactNode {
        const model = this.model;
        return (
            <div className={css.width50}>
                <span className={css.label}>URL地址 <span className={css.labelRequired}>*</span></span>
                <Input.Group compact className={css.inputPathGroup}>
                    <Select_protocol
                        model={model}
                        options={protocolOptions}
                    />
                    <Select_method
                        model={model}
                        dropdownMatchSelectWidth={104}
                        options={methodOptions}
                        onChange={model.onChangeMethod}
                    />
                    <Input_path
                        model={model}
                        placeholder={'请输入path'}
                    />
                </Input.Group>
            </div>
        );
    }

    @Bind
    private renderBranch(): React.ReactNode {
        const model = this.model;
        return (
            <div className={`${css.width50} ${css.margRight}`}>
                <span className={css.label}>关联分支</span>
                <Select
                    value={model.branchId || model.branchName || undefined}
                    className={css.width100}
                    onSelect={model.onSelectBranchId}
                    optionFilterProp={'label'}
                    showSearch
                    placeholder="请选择"
                >
                    {
                        model.branchList.map(item => {
                            return <Option
                                key={item.id}
                                value={item.id}
                                label={item.branchName}
                            >{item.branchName}</Option>;
                        })
                    }
                </Select>
            </div>
        );
    }

    @Bind
    private renderGroup(): React.ReactNode {
        const model = this.model;
        return (
            <div className={css.width50}>
                <span className={css.label}>分组</span>
                <Select
                    className={css.width100}
                    showSearch
                    value={model.groupId || undefined}
                    optionFilterProp={'label'}
                    onSelect={model.onSelectGroupId}
                >
                    {
                        model.groupList.map(item => {
                            return <Option
                                key={item.id}
                                value={item.id}
                                label={item.groupName}
                            >{item.groupName}</Option>;
                        })
                    }
                </Select>
            </div>
        );
    }

    @Bind
    private renderPriority(): React.ReactNode {
        const model = this.model;
        return (
            <div className={`${css.width50} ${css.margRight}`}>
                <span className={css.label}>优先级</span>
                <Select
                    value={model.priority || undefined}
                    options={priorityOptions}
                    className={css.prioritySelect}
                    allowClear
                    onChange={model.onChangePriority}
                    placeholder={'请选择'}
                />
            </div>
        );
    }

    @Bind
    private renderReadOnly(): React.ReactNode {
        const model = this.model;
        return (
            <div className={css.width50}>
                <span className={css.label}>读写属性</span>
                <Select
                    value={model.readOnly || undefined}
                    options={readOnlyOptions}
                    className={css.prioritySelect}
                    // allowClear
                    onChange={model.onChangeReadOnly}
                    placeholder={'请选择'}
                />
            </div>
        );
    }

    public render(): React.ReactNode {
        const model = this.model;
        return (
            <Collapse
                ghost
                className={`${css.apiBaseInfoCollapse} ${css.margTop0}`}
                defaultActiveKey={'1'}
                destroyInactivePanel
            >
                <Panel header={'基本信息'} key={'1'}>
                    <div className={css.collapsePanelRow}>
                        {this.renderApiName()}
                        {this.renderApiPath()}
                    </div>
                    <div className={css.collapsePanelRow}>
                        {this.renderBranch()}
                        {this.renderGroup()}
                    </div>
                    <div className={css.collapsePanelRow}>
                        {this.renderOwner()}
                        {this.renderCooper()}
                    </div>
                    <div className={css.collapsePanelRow}>
                        {this.renderQaUsers()}
                        {this.renderPriority()}
                    </div>
                    <div className={css.collapsePanelRow}>
                        {this.renderReadOnly()}
                    </div>
                    <div className={css.collapsePanelRow}>
                        <span className={css.label}>描述</span>
                        {/* <TextArea_description model={model} placeholder={'请输入'} /> */}
                        <RemarkMarkdownEditor
                            className={css.remarkMarkdownEditorWrap}
                            markdownValue={model.descMarkdown}
                            queryValueMethod={model.queryValueMethod}
                        />
                    </div>
                </Panel>
            </Collapse>
        );
    }
}
