import { AViewModel } from 'libs';
import { observable, action, runInAction } from 'mobx';
import {
    nsMockManageKoasApiManageModuleQueryAllBranchListGet, nsMockManageKoasApiManageGroupQueryModuleGroupListGet,
    nsMockPermissionBatchGetUserGet,
    nsMockManageKoasApiManageHttpApiCanEditApiPriorityGet
} from '@/remote';
import { message, Modal } from 'antd';
import { IOwner } from './enum';
import { team } from '@/business/global';
import { CreateMaintainerModel } from '@/business/createMaintainer/CreateMaintainer';
import { Bind } from 'lodash-decorators';
import css from './ApiBaseInfo.less';

export class ApiBaseInfoM extends AViewModel {
    private apiId: number = 0;
    @observable public name: string = '';
    @observable public protocol: string = 'HTTP';
    @observable public method: string = 'GET';
    @observable public path: string = '/';
    @observable public branchId: number = 0;
    @observable public branchName: string = '';
    @observable public groupId: number = 0;
    @observable private owner: string = team.getUserInfo().userName;
    @observable public ownerInfo: IOwner | null = null;
    @observable private cooper: string[] = [];
    @observable public cooperList: Array<any> = [];
    @observable public qaList: string[] = [];
    @observable public qaUsers: any[] = [];
    @observable public priority: string = '';
    @observable public readOnly: number = 0;
    // @observable public description: string = '';
    @observable public descMarkdown: string = '';
    @observable public branchList: nsMockManageKoasApiManageModuleQueryAllBranchListGet.IBranchList[] = [];
    @observable public groupList: nsMockManageKoasApiManageGroupQueryModuleGroupListGet.IGroupItem[] = [];

    private moduleId: number = 0;

    public changeMethod?(method: string): void;
    private getMarkdownValue?(): string;
    private getHtmlValue?(): string;

    public createMaintainerM = new CreateMaintainerModel();

    @action.bound
    public init(baseInfo): void {
        this.apiId = baseInfo.id;
        this.moduleId = baseInfo.moduleId;
        this.name = baseInfo.name;
        this.protocol = baseInfo.protocol;
        this.method = baseInfo.method || 'POST';
        this.path = baseInfo.path;
        this.branchId = baseInfo.branchId || '';
        this.branchName = baseInfo?.branchName || '';
        this.groupId = baseInfo.groupId;
        this.priority = baseInfo.priority || '';
        this.readOnly = baseInfo.readOnly || 0;
        // this.description = baseInfo.description;
        this.descMarkdown = baseInfo?.descMarkdown || '';
        this.owner = baseInfo.owner || this.owner;
        this.cooper = baseInfo.cooperList || [];
        this.qaList = baseInfo?.qaList || [];
        let userInfo = baseInfo.cooperList || [];
        if (!userInfo.includes(this.owner)) {
            userInfo.push(this.owner);
        }
        if (this.qaList && this.qaList.length) {
            userInfo = [...userInfo, ...this.qaList];
        }
        this.batchGetUser(userInfo);
        this.queryAllBranchList();
        this.queryModuleGroupList();
    }

    @action.bound
    public initData(): void {
        this.moduleId = 0;
        // this.apiId = 0;
        this.name = '';
        this.protocol = 'HTTP';
        this.method = 'GET';
        this.path = '/';
        this.branchId = 0;
        this.groupId = 0;
        // this.description = '';
        this.descMarkdown = '';
        this.branchList = [];
        this.groupList = [];
        this.priority = '';
        this.readOnly = 0;
        this.cooper = [];
        this.cooperList = [];
        this.qaList = [];
        this.qaUsers = [];
        const userInfo = team.getUserInfo();
        this.owner = userInfo.userName;
        this.ownerInfo = {
            username: userInfo.userName,
            name: userInfo.chineseName,
            photo: userInfo.photo,
        };
    }

    @action.bound
    public getBaseInfo() {
        const cooperList = this.cooperList.map(item => item.username);
        const qaList = this.qaUsers.map(item => item.username);
        const baseInfo = {
            name: this.name,
            protocol: this.protocol,
            method: this.method,
            path: this.path,
            branchId: this.branchId,
            branchName: this.branchName,
            groupId: this.groupId,
            cooperList,
            qaList,
            description: this.getHtmlValue ? this.getHtmlValue() : '',
            descMarkdown: this.getMarkdownValue ? this.getMarkdownValue() : '',
            moduleId: this.moduleId,
            owner: this.owner,
            priority: this.priority,
            readOnly: this.readOnly
        };
        return baseInfo;
    }

    @action.bound
    public checkParams(): boolean {
        if (!this.name) {
            message.warn('请输入API名称');
            return true;
        }
        if (!this.path) {
            message.warn('请输入URL地址');
            return true;
        }
        if (this.path[0] !== '/') {
            message.warn('URL地址应以"/"开头');
            return true;
        }
        if (!this.groupId) {
            message.warn('请选择分组');
            return true;
        }
        if (this?.getHtmlValue && this.getHtmlValue().length > 1000) {
            message.warn('文档描述不能超过1000字符，请重新编辑！');
            return true;
        }
        return false;
    }

    @action.bound
    public onChangeMethod(): void {
        this.changeMethod && this.changeMethod(this.method);
    }

    // 切换分支
    @action.bound
    public onSelectBranchId(branchId, e) {
        this.branchId = branchId;
        this.branchName = e.label;
    }

    // 切换分组
    @action.bound
    public onSelectGroupId(groupId: number) {
        this.groupId = groupId;
    }

    // 获取维护人
    @action.bound
    public onChangeCooper(cooperList): void {
        this.cooperList = cooperList;
    }

    @action.bound
    public onChangeqaUsers(qaUsers): void {
        this.qaUsers = qaUsers;
    }

    @action.bound
    public onHandelSearchSelect(ownerInfo: IOwner): void {
        this.ownerInfo = ownerInfo;
        this.owner = ownerInfo.username;
    }

    @action.bound
    public onChangePriority(priority: string = ''): void {
        const newPriority = this.priority;
        this.priority = priority;
        if (priority) {
            this.canEditApiPriority(newPriority);
        }
    }

    @Bind
    private async canEditApiPriority(priority: string) {
        try {
            const params = {
                apiId: this.apiId,
                priority: this.priority,
                type: 0 as 0
            };
            const result = await nsMockManageKoasApiManageHttpApiCanEditApiPriorityGet.remote(params);
            if (!result.canEdit) {
                Modal.warning({
                    className: css.canEditModal,
                    content: result.msg
                });
                runInAction(() => {
                    this.priority = priority;
                });
            }
        } catch {
        }
    }

    @action.bound
    public onChangeReadOnly(readOnly: number): void {
        this.readOnly = readOnly;
    }

    @action.bound
    public queryValueMethod(method): void {
        this.getMarkdownValue = method.getMarkdownValue;
        this.getHtmlValue = method.getHtmlValue;
    }

    // 获取分组下的关联分支
    @action
    protected async queryAllBranchList() {
        try {
            const params = {
                moduleId: this.moduleId
            };
            const result = await nsMockManageKoasApiManageModuleQueryAllBranchListGet.remote(params);
            runInAction(() => {
                this.branchList = result.branchList;
            });
        } catch (e) {
        }
    }

    // 获取分组列表
    @Bind
    protected async queryModuleGroupList() {
        try {
            const params = {
                moduleId: this.moduleId
            };
            const result = await nsMockManageKoasApiManageGroupQueryModuleGroupListGet.remote(params);
            runInAction(() => {
                this.groupList = result.groupList;
            });
        } catch (e) {
        }
    }

    @action
    private batchGetUserCallback(list: IOwner[]): void {
        this.cooperList = [];
        list.forEach(item => {
            if (item.username === this.owner) {
                this.ownerInfo = item;
            }
            if (this.cooper.includes(item.username)) {
                this.cooperList.push({
                    name: item.name,
                    username: item.username,
                    avatarUrl: item?.photo || ''
                });
            }
            if (this.qaList.includes(item.username)) {
                this.qaUsers.push({
                    name: item.name,
                    username: item.username,
                    avatarUrl: item?.photo || ''
                });
            }
        });
        this.cooperList = [...this.cooperList];
        this.qaList = [...this.qaList];
    }

    // 获取头像
    @action
    protected async batchGetUser(usernames: string[]) {
        if (!usernames?.length) {
            return;
        }
        try {
            const params = {
                usernames: usernames.join(',')
            };
            const result = await nsMockPermissionBatchGetUserGet.remote(params);
            runInAction(() => {
                this.batchGetUserCallback(result?.list || []);
            });
        } catch (e) {
        }
    }
}
