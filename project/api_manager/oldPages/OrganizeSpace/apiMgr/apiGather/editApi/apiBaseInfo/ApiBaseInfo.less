.apiBaseInfoCollapse {
    border: 1px solid #d9d9d9;
    border-radius: 4px;
    margin-bottom: 16px;

    .collapsePanelRow {
        margin-bottom: 24px;
        display: flex;

        .remarkMarkdownEditorWrap {
            width: calc(100% - 74px);
        }

        .width50 {
            min-width: calc(50% - 18px);
            display: flex;
            align-items: center;

            .inputPathGroup {
                display: flex;
            }
        }

        .qaUsers {
            .width50();
            margin-right: 36px;
        }

        .width100 {
            width: 100%;
        }

        .label {
            min-width: 74px;
            line-height: 32px;
        }

        .labelRequired {
            color: #ff4d4f;
        }

        .owner {
            margin-right: 14px;
        }

        .addOwnerBtn {
            line-height: 32px;

            .addOwnerBtnIcon {
                font-size: 20px;
            }
        }
    }

    .prioritySelect {
        width: 100px;
    }

    .margRight {
        margin-right: 36px;
    }

    :global {
        .ant-collapse-item>.ant-collapse-content>.ant-collapse-content-box {
            padding: 0 24px;
        }

        .ant-collapse-item>.ant-collapse-header {
            padding: 24px 24px 24px 40px;
        }
    }
}

.hoverKim {
    color: #252626;

    .kimImg {
        height: 18px;
        margin-right: 6px;
    }
}

.canEditModal {
    :global {
        .ant-modal-confirm-content {
            white-space: pre-line;
        }
    }
}