export interface IOwner {
    username: string;
    photo?: string;
    name: string;
}

interface ISelectOptions {
    label?: string;
    value: string;
}

export const methodOptions: ISelectOptions[] = [
    {
        value: 'GET',
    },
    {
        value: 'POST',
    },
    {
        value: 'PUT',
    },
    {
        value: 'DELETE',
    },
    {
        value: 'PATCH',
    }
];

export const protocolOptions: ISelectOptions[] = [
    {
        value: 'HTTP',
    }
];
