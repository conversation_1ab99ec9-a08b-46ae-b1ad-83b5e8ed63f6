import { <PERSON><PERSON>ie<PERSON> } from 'libs';
import React from 'react';
import { EditApiM } from './EditApiM';
import { observer } from 'mobx-react';
import { Button, Skeleton } from 'antd';
import {Bind} from 'lodash-decorators';
import css from './EditApi.less';
import { ApiBaseInfo } from './apiBaseInfo/ApiBaseInfo';
import { RequestParams } from '@/business/httpApiComponents/editApi/requestParams/RequestParams';
import { ResponseParams } from '@/business/httpApiComponents/editApi/responseParams/ResponseParams';
import { SaveDocNotice } from '@/business/saveDocNotice/SaveDocNotice';

interface IProps {
    apiGatherM?: any;
}

@observer
export class EditApi extends AView<EditApiM, IProps> {

    @Bind
    public componentWillUnmount(): void {
        this.model.initData();
    }

    @Bind
    protected renderDetail(model): React.ReactNode {
        return <div className={ css.detail }>
            <ApiBaseInfo model={model.apiBaseInfoM} />
            <RequestParams model={model.requestParamsM}/>
            <ResponseParams model={model.responseParamsM} />
        </div>;
    }

    @Bind
    private cancelSave() {
        this.props.apiGatherM.onOpenApiDetail(this.model.apiId);
    }

    @Bind
    protected renderBottom(): React.ReactNode {
        const model = this.model;
        return <div className={ css.editApiBottom }>
            <Button className={ css.cancelBtn } onClick={ this.cancelSave }>取消</Button>
            <Button
                type={ 'primary' }
                onClick={ model.onOpenSaveDocNotice }
                loading={ model.saveLoading }
            >
                保存
            </Button>
        </div>;
    }

    public render(): React.ReactNode {
        const model = this.model;
        if (model.queryDetailLoading) {
            return <Skeleton active className={css.skeleton} />;
        }
        return (
            <div className={ css.editApiWrap }>
                { this.renderDetail(model) }
                { this.renderBottom() }
                <SaveDocNotice model={model.saveDocNoticeM}/>
            </div>
        );
    }
}
