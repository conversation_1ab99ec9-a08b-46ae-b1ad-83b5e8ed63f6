import { AViewModel } from 'libs';
import { observable, action, runInAction } from 'mobx';
import { Bind } from 'lodash-decorators';
import { router } from '@libs/mvvm';
import { message } from 'antd';
import { departmentCascader } from '@/business/global';
import { ERouter } from 'CONFIG';
import {
    nsMockManageKoasApiManageProjectQueryProjectListPost, nsMockManageKoasApiManageProjectAttentionPost,
    nsMockManageKoasApiManageProjectDeleteProjectPost
} from '@/remote';
import { IProjectList } from './configure';
import { GlobalSearchM } from 'oldPages/MockProject/component/apiGlobalSearch/GlobalSearchM';
import { CreateProjectM } from './createProject/CreateProjectM';

export class ProjectMgrM extends AViewModel {
    @observable public departmentId: number = departmentCascader.getDepartmentId();
    @observable public departmentFullId: number[] = departmentCascader.getDepartmentFullId();
    @observable public key: string = '';
    @observable public filter: number = 1;
    @observable public pageSize: number = 20;
    @observable public currentPage: number = 1;
    @observable public total: number = 0;
    @observable public projectList: IProjectList[] = [];
    @observable public tableScrollYHeight: number = 300;
    @observable public projectListLoading: boolean = false;
    @observable public isModify: number = 0;

    public globalSearchM = new GlobalSearchM();
    public createProjectM = new CreateProjectM();

    @action.bound
    public init(): void {
        this.tableScrollYHeight = document.documentElement.clientHeight - 300;
    }

    /**
     * 获取项目列表
     */
    @Bind
    public async queryProjectList() {
        runInAction(() => {
            this.projectListLoading = true;
        });
        try {
            const params = {
                departmentId: this.departmentId,
                key: this.key,
                pageSize: this.pageSize,
                currentPage: this.currentPage,
                filter: this.filter,
                // isIncludeSub: this.isIncludeSub ? 1 : 0
            };
            const result = await nsMockManageKoasApiManageProjectQueryProjectListPost.remote(params);
            runInAction(() => {
                this.total = result.total || 0;
                this.isModify = result.isModify;
                this.projectList = result.list || [];
                this.projectListLoading = false;
            });
        } catch (e) {
            runInAction(() => {
                this.projectListLoading = false;
            });
        }
    }

    // 根据类型搜索项目列表
    @action.bound
    public onChangeFilter(e) {
        this.filter = e.target.value;
        this.currentPage = 1;
        this.queryProjectList();
    }

    // 根据关键字搜索项目列表
    @action.bound
    public onSearchProjectList() {
        this.currentPage = 1;
        this.queryProjectList();
    }

    // 切换当前页
    @action.bound
    public onChangeCurrent(currentPage: number, pageSize) {
        this.currentPage = currentPage;
        this.pageSize = pageSize;
        this.queryProjectList();
    }

    @Bind
    public openGlobalSearch(): void {
        this.globalSearchM.init(this.departmentId);
    }

    // 打开模块列表
    @Bind
    public onOpenModuleList(projectId: number) {
        router.push(ERouter.API_MOCK_PROJECT_MODULEMGR, {
            projectId,
            // projectName: record.projectName
        });
    }

    @Bind
    private onSaveProjectCallback(type: 'new' | 'edit', projectId: number): void {
        if (type === 'new') {
            this.onOpenModuleList(projectId);
        }
        if (type === 'edit') {
            this.queryProjectList();
        }
    }

    // 打开新建项目弹框
    @Bind
    public onOpenCreateProjectModal(record) {
        this.createProjectM.init(record, this.departmentId, this.departmentFullId);
        this.createProjectM.onSaveCallback = this.onSaveProjectCallback;
    }

    // 删除项目
    @Bind
    public async delete(projectId: number) {
        try {
            const params = {
                projectId,
            };
            await nsMockManageKoasApiManageProjectDeleteProjectPost.remote(params);
            runInAction(() => {
                message.success('删除成功');
                this.currentPage = 1;
                this.queryProjectList();
            });
        } catch (e) {
        }
    }

    // 关注、取消关注项目
    @Bind
    public async attention(record) {
        try {
            const params = {
                projectId: record.projectId,
                action: record.focus ? 'unfocus' : 'focus'
            };
            await nsMockManageKoasApiManageProjectAttentionPost.remote(params);
            runInAction(() => {
                message.success(record.focus ? '取消关注成功' : '关注成功');
                this.queryProjectList();
            });
        } catch (e) {
        }
    }
}
