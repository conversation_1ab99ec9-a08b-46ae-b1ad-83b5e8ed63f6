import { AView } from 'libs';
import React from 'react';
import { observer } from 'mobx-react';
import { bindObserver } from '@libs/mvvm';
import { Bind } from 'lodash-decorators';
import { Radio, Input, Button, Empty, Table, Tooltip, Modal } from 'antd';
import { ProjectMgrM } from './ProjectMgrM';
import css from './ProjectMgr.less';
import { filterOptions } from './configure';
import { SearchEmptyIcon } from '@/business/commonIcon';
import { KdevIconFont } from '@/business/commonComponents/KdevIconFont';
import { CreateProject } from './createProject/CreateProject';
import { GlobalSearch } from '@/business/apiGlobalSearch/GlobalSearch';

const { Search } = Input;
const Input_key = bindObserver(Search, 'key');

@observer
export class ProjectMgr extends AView<ProjectMgrM> {

    @Bind
    public componentDidMount(): void {
        this.model.init();
        this.model.queryProjectList();
    }

    /**
     * 删除项目
     * @param record
     */
    @Bind
    public onDeleteProject(record) {
        Modal.confirm({
            content: '删除后将无法恢复，确认删除项目？',
            onOk: () => this.model.delete(record.projectId)
        });
    }

    @Bind
    private columns(): any[] {
        const model = this.model;
        const columns = [
            {
                title: '项目名称',
                // dataIndex: 'projectName',
                key: 'projectName',
                render: record => {
                    return <a onClick={() => model.onOpenModuleList(record.projectId)}>{record.projectName}</a>;
                }
            },
            {
                title: '项目描述',
                dataIndex: 'projectDesc',
                key: 'projectDesc',
            },
            {
                title: '创建人',
                dataIndex: 'createUser',
                key: 'createUser',
            },
            {
                title: '创建时间',
                dataIndex: 'createTime',
                key: 'createTime',
            },
            {
                title: '操作',
                // dataIndex: 'operation',
                key: 'operation',
                align: 'right',
                // width: 130,
                render: record => {
                    return <>
                        <Button
                            icon={<KdevIconFont id={'#iconxingzhuangjiehe'} />}
                            onClick={() => this.model.onOpenCreateProjectModal(record)}
                        />
                        <Tooltip title={'删除项目'}>
                            <Button
                                icon={<KdevIconFont id={'#iconyanse'} />}
                                className={css.deleteBtn}
                                onClick={() => this.onDeleteProject(record)}
                                disabled={!record.canDelete}
                            />
                        </Tooltip>
                        <Button
                            className={record.focus && css.focusBtn}
                            icon={<KdevIconFont id={'#iconguanzhu'} />}
                            onClick={() => this.model.attention(record)}
                        />
                    </>;
                }
            }
        ];
        return columns;
    }

    @Bind
    private renderCreateBtn(): React.ReactNode {
        const model = this.model;
        return <Button
            type={'primary'}
            className={css.createProjectBtn}
            onClick={() => model.onOpenCreateProjectModal(null)}
        >
            新建项目
        </Button>;
    }

    @Bind
    private renderProjectList(): React.ReactNode {
        const model = this.model;
        if (Boolean(model.total) || model.projectListLoading) {
            return <Table
                columns={this.columns()}
                dataSource={model.projectList}
                rowKey={'projectId'}
                loading={model.projectListLoading}
                pagination={{
                    // size: 'small',
                    pageSize: model.pageSize,
                    current: model.currentPage,
                    showSizeChanger: true,
                    total: model.total,
                    showTotal: total => `共 ${total} 条`,
                    onChange: model.onChangeCurrent,
                }}
                scroll={{
                    y: model.tableScrollYHeight
                }}
                bordered
            />;
        } else {
            return <Empty
                className={css.empty}
                image={<SearchEmptyIcon />}
                description={'还未创建项目，快去添加吧～'}
            >
                {this.renderCreateBtn()}
            </Empty>;
        }
    }

    @Bind
    protected renderProjectMgrTitle(): React.ReactNode {
        const model = this.model;
        return (
            <div className={css.projectMgrTit}>
                <Radio.Group
                    className={css.filterStyle}
                    value={model.filter}
                    onChange={model.onChangeFilter}
                    optionType="button"
                    options={filterOptions}
                />
                搜索：
                <Input_key
                    model={model}
                    className={css.searchKey}
                    placeholder={'支持模块名/API名称/路径/创建人搜索'}
                    onSearch={model.onSearchProjectList}
                    onClick={model.openGlobalSearch}
                    readOnly={true}
                />
                {
                    Boolean(model.total) &&
                    this.renderCreateBtn()
                }
            </div>
        );
    }
    public render() {
        const model = this.model;
        return (
            <div className={css.projectMgr}>
                {this.renderProjectMgrTitle()}
                {this.renderProjectList()}
                <CreateProject model={model.createProjectM} />
                <GlobalSearch model={model.globalSearchM} />
            </div>
        );
    }
}
