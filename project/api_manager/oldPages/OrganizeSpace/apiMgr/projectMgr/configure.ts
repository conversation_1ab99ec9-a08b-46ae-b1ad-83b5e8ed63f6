export interface IProjectList {
    projectId: number;
    projectName: string;
    projectDesc: string;
    createUser: string;
    isModify: number;
    focus: boolean;
}

interface IOptions {
    label: string;
    value: number;
}

export const filterOptions: IOptions[] = [
    {
        label: '全部项目',
        value: 1
    },
    {
        label: '我创建的',
        value: 2
    },
    {
        label: '我关注的',
        value: 3
    }
];
