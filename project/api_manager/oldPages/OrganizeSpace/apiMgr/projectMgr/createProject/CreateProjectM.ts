import { AViewModel } from 'libs';
import { observable, action, runInAction } from 'mobx';
import { Bind } from 'lodash-decorators';
import { message } from 'antd';
import {
    nsMockManageKoasApiManageProjectCreateProjectPost, nsMockManageKoasApiManageProjectUpdateProjectPost,
    nsMockManageApiManageDepartmentQueryAllDepartmentListPost
 } from '@/remote';

interface IDetail {
    projectId: number;
    projectName: string;
    projectDesc: string;
}

export class CreateProjectM extends AViewModel {
    private departmentId: number = 0;
    @observable public visible: boolean = false;
    @observable public projectId: number = 0;
    @observable public projectName: string = '';
    @observable public projectDesc: string = '';
    @observable public departmentAllList: any[] = [];
    @observable public departmentFullId: number[] = [];

    public onSaveCallback?(type: 'new' | 'edit', projectId: number): void;

    @action.bound
    public init(detail: IDetail, departmentId: number, departmentFullId: number[]): void {
        this.departmentId = departmentId;
        this.departmentFullId = departmentFullId;
        this.visible = true;
        if (detail) {
            this.projectId = detail.projectId;
            this.projectName = detail.projectName;
            this.projectDesc = detail.projectDesc;
            if (!this.departmentAllList.length) {
                this.queryAllDepartmentList();
            }
        }
    }

    // 关闭新建项目弹框
    @action.bound
    public onCloseCreateProjectModal() {
        this.departmentId = 0;
        this.departmentFullId = [];
        this.projectId = 0;
        this.projectName = '';
        this.projectDesc = '';
        this.visible = false;
    }

    // 输入项目描述
    @action.bound
    public onChangeProjectDesc(e) {
        this.projectDesc = e.target.value;
    }

    /**
     * 创建项目
     */
    @Bind
    private async ajax_createProject() {
        if (!this.projectName) {
            message.warn('请填写项目名称');
            return;
        }
        try {
            const params = {
                departmentId: this.departmentId,
                projectName: this.projectName,
                projectDesc: this.projectDesc
            };
            const result = await nsMockManageKoasApiManageProjectCreateProjectPost.remote(params);
            runInAction(() => {
                if (result.id) {
                    message.success('创建项目成功');
                    this.onSaveCallback && this.onSaveCallback('new', result.id);
                }
            });
        } catch (e) {
        }
    }

    /**
     * 修改项目
     * @param isDiscard
     */
    @Bind
    private async ajax_updateProject(projectId: number) {
        if (!this.projectName) {
            message.warn('请填写项目名称');
            return;
        }
        if (!this.departmentFullId.length) {
            message.warn('请选择部门');
            return;
        }
        try {
            const params = {
                projectId,
                projectName: this.projectName,
                projectDesc: this.projectDesc,
                departmentId: this.departmentFullId[this.departmentFullId.length - 1]
            };
            await nsMockManageKoasApiManageProjectUpdateProjectPost.remote(params);
            runInAction(() => {
                this.onCloseCreateProjectModal();
                message.success('修改成功');
                this.onSaveCallback && this.onSaveCallback('edit', this.projectId);
            });
        } catch (e) {
        }
    }

    // 创建、编辑项目
    @Bind
    public onCreateProject() {
        if (this.projectId) {
            this.ajax_updateProject(this.projectId);
        } else {
            this.ajax_createProject();
        }
    }

    // 聚焦
    @action.bound
    public onFoucsProjectDescNode(projectDescNode) {
        projectDescNode?.focus({
            cursor: 'end'
        });
    }

    @action.bound
    public changeAllDepartmentId(departmentFullId): void {
        this.departmentFullId = departmentFullId;
    }

    /**
     * 获取全部部门列表
     */
     @Bind
     private async queryAllDepartmentList() {
         try {
             const result = await nsMockManageApiManageDepartmentQueryAllDepartmentListPost.remote({});
             runInAction(() => {
                 this.departmentAllList = result.treeInfos || [];
             });
         } catch (e) {
         }
     }
}
