import { <PERSON><PERSON>ie<PERSON> } from 'libs';
import React from 'react';
import { CreateProjectM } from './CreateProjectM';
import { observer } from 'mobx-react';
import { bindObserver } from '@libs/mvvm';
import { Modal, Input, Cascader } from 'antd';
import { Bind } from 'lodash-decorators';
import css from './CreateProject.less';
import { RealDOM } from '@libs/utils';

const Input_projectName = bindObserver(Input, 'projectName');

@observer
export class CreateProject extends AView<CreateProjectM> {
    @Bind
    private renderDepartment(): React.ReactNode {
        const model = this.model;
        if (model.projectId) {
            return (
                <>
                    <div className={css.formRowLabel}>部门 <span className={css.formRowLabelRequired}>*</span></div>
                    <Cascader
                        placeholder={'请选择部门'}
                        options={model.departmentAllList}
                        showSearch={false}
                        fieldNames={{
                            label: 'departmentName',
                            value: 'departmentId',
                            children: 'children',
                        }}
                        value={model.departmentFullId}
                        onChange={model.changeAllDepartmentId}
                        changeOnSelect
                        className={css.departmentCascader}
                    />
                </>
            );
        }
    }

    public render() {
        const model = this.model;
        const projectDescNode = new RealDOM<HTMLDivElement>();
        return (
            <Modal
                className={css.createProjectModal}
                visible={model.visible}
                title={model.projectId ? '编辑项目' : '新建项目'}
                onCancel={model.onCloseCreateProjectModal}
                onOk={model.onCreateProject}
                maskClosable={false}
            >
                {this.renderDepartment()}
                <div className={css.formRowLabel}>项目名称 <span className={css.formRowLabelRequired}>*</span></div>
                <Input_projectName
                    model={model}
                    placeholder={'请输入项目名称'}
                    autoFocus
                    onPressEnter={() => model.onFoucsProjectDescNode(projectDescNode.dom)}
                />
                <div className={css.formRowLabel}>项目描述</div>
                <Input
                    ref={projectDescNode.setDom}
                    placeholder={'请输入项目描述'}
                    value={model.projectDesc}
                    onChange={model.onChangeProjectDesc}
                    onPressEnter={model.onCreateProject}
                />
            </Modal>
        );
    }
}
