import React from 'react';
import APage from '@/pages/APage';
import { observer, Provider } from 'mobx-react';
import { Space, Tabs } from 'antd';
import { ApiMgrM } from './ApiMgrM';
import css from './ApiMgr.less';
import { ProjectMgr } from './projectMgr/ProjectMgr';
import { ApiGather } from './apiGather/ApiGather';
import { Bind } from 'lodash-decorators';
import { IQuery } from './configure';
import { ToggleVersionBtn } from '@/business/topNavComps/ToggleVersionBtn';
import { GlobalSearchInput } from '@/business/topNavComps/GlobalSearchInput';

const { TabPane } = Tabs;

@observer
export default class ApiMgr extends APage<IQuery, ApiMgrM> {
    protected createModel(): ApiMgrM {
        return new ApiMgrM(this.query);
    }

    @Bind
    private onChangeActivekey(activeKey: string): void {
        this.model.changeActiveKey(activeKey);
        this.push();
    }

    public renderContent(): React.ReactNode {
        const model = this.model;
        return (
            <Tabs
                className={css.apiMgrWrap} activeKey={model.activeKey} onChange={this.onChangeActivekey}
                tabBarExtraContent={
                    <Space size={8} style={{marginRight: 16}}>
                        <ToggleVersionBtn className={css.toggleBtn} />
                        <GlobalSearchInput className={css.globalSearchInput} />
                    </Space>
                }
            >
                <TabPane tab={'项目'} key={'1'}>
                    {
                        model.projectMgrM &&
                        <ProjectMgr model={model.projectMgrM} />
                    }
                </TabPane>
                <TabPane tab={'API集合'} key={'2'}>
                    {
                        model.apiGatherM &&
                        <ApiGather model={model.apiGatherM} />
                    }
                </TabPane>
            </Tabs>
        );
    }
}
