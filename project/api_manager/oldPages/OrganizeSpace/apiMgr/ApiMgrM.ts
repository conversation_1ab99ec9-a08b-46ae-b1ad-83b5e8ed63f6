import APageModel from '@/pages/APageModel';
import { action, observable } from 'mobx';
import { ProjectMgrM } from './projectMgr/ProjectMgrM';
import { ApiGatherM } from './apiGather/ApiGatherM';
import { IQuery } from './configure';

export class ApiMgrM extends APageModel<IQuery> {
    @observable public activeKey: string = '1';

    public projectMgrM?: ProjectMgrM;
    public apiGatherM?: ApiGatherM;

    protected getQueryFields(): Array<keyof IQuery> {
        return ['activeKey'];
    }

    constructor(query: IQuery) {
        super(query);
        this.initByQueryFields(query);
        this.initChildrenModel();
    }

    @action.bound
    public changeActiveKey(activekey: string): void {
        this.activeKey = activekey;
        this.initChildrenModel();
    }

    @action
    private initChildrenModel() {
        if (this.activeKey === '1' && !this.projectMgrM) {
            this.projectMgrM = new ProjectMgrM();
        }
        if (this.activeKey === '2' && !this.apiGatherM) {
            this.apiGatherM = new ApiGatherM();
        }
    }
}
