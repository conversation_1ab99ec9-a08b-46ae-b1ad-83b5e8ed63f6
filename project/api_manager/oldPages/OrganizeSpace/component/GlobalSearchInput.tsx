import React from 'react';
import { Bind } from 'lodash-decorators';
import { Input } from 'antd';
import { SearchOutlined } from '@ant-design/icons';
import { GlobalSearch } from '@/business/apiGlobalSearch/GlobalSearch';
import { GlobalSearchM } from 'oldPages/MockProject/component/apiGlobalSearch/GlobalSearchM';
import { SearchApi } from '../../../src/business/topNavComps/searchApi/SearchApi';
import { kconfStore } from '@/index.config';

interface IProps {
    className?: string;
}

export class GlobalSearchInput extends React.Component<IProps> {
    private globalSearchM = new GlobalSearchM();

    @Bind
    private onOpenGlobalSearch(): void {
        this.globalSearchM.init(0, 1);
    }

    public render(): React.ReactNode {
        return (
            <div className={this.props.className}>
                {
                    kconfStore.isShowNewVersion
                        ? <SearchApi />
                        : <>
                            <span onClick={this.onOpenGlobalSearch}>
                                <Input
                                    placeholder="搜索API"
                                    suffix={<SearchOutlined />}
                                />
                            </span>
                            <GlobalSearch model={this.globalSearchM} />
                        </>
                }
            </div>
        );
    }
}
