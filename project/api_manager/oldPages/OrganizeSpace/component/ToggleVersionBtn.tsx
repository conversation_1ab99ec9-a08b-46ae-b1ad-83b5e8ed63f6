import React from 'react';
import { KdevIconFont } from '@/business/commonComponents';
import { common_system_fire_line1, common_system_xiaojiantouyou, common_system_retract } from '@kid/enterprise-icon/icon/output/icons';
import { router } from '@libs/mvvm';
import { ERouter } from 'CONFIG';
import classNames from 'classnames';
import css from './topNavComps.less';
import { kconfStore, uploadClickEvent, ECLICK_POINT } from '@/index.config';
interface IProps {
    onVersionChange?(): void;
    className?: string;
}

function ToggleVersionBtn({ onVersionChange, ...props }: IProps) {

    const onChangeVersion = () => {
        kconfStore.setVersion(kconfStore.isShowNewVersion ? 'old' : 'new', true);
        kconfStore.isShowNewVersion
            ? router.push(ERouter.API_MOCK_REPO_MGR)
            : router.push(ERouter.API_MOCK_ORGANIZESPACE_PROJECTMGR);
        uploadClickEvent({ record_type: ECLICK_POINT.API_REPO_SETTING });
        // onVersionChange && onVersionChange();
    };

    if (!kconfStore.showToggleVersionBtn) {
        return null;
    }

    return kconfStore.isShowNewVersion ? null : (
        <div
            className={classNames(css.toggleVersionBtn, props.className)}
            style={{
                background: kconfStore.isShowNewVersion
                    ? '#4E4E8E'
                    : 'linear-gradient(90deg, #4259fa -9.65%, #cf3ec1 49.29%, #ffac49 107.02%)',
            }}
            onClick={onChangeVersion}
        >
            <KdevIconFont id={common_system_fire_line1} style={{ fontSize: 18 }} />
            体验新版
        </div>
    );
}

export { ToggleVersionBtn };
