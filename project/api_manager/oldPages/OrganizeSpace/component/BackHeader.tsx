// import React from 'react';
// import { goBack } from '@/business/commonComponents/GoBack';
// import { KdevIconFont } from '@/business/commonComponents/KdevIconFont';
// import css from './BackHeader.less';
// import classNames from 'classnames';

// interface IProps {
//     url: string;            // 返回链接
//     hrefType?: number;
//     params?: object;        // 返回参数
//     extra?: string | React.ReactElement | React.ReactNode;
//     title: string | React.ReactElement | React.ReactNode;          // title
//     onGoBack?(): void;
//     className?: string;
// }

// interface IState {
// }

// export class BackHeader extends React.Component<IProps, IState> {
//     public render(): React.ReactNode {
//         const { url, hrefType, params, onGoBack, className } = this.props;
//         return (
//             <div className={ classNames(css.headerBox, className) }>
//                 <div className={ css.headerLeftBox }>
//                     <div className={css.iconBox} onClick={ onGoBack || goBack.bind(this, url, hrefType || 1, params) }>
//                         <KdevIconFont id="#iconyemian-fanhui1" className={ css.icon } />
//                     </div>
//                     <div className={ css.line } />
//                     <div className={ css.title }>
//                         { this.props.title || '' }
//                     </div>
//                 </div>
//                 <div className={ css.headerRightBox }>
//                     { this.props.extra || '' }
//                 </div>
//             </div>
//         );
//     }
// }
