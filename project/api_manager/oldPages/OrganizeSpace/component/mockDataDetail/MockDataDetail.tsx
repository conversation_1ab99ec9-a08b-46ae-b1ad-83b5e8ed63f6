import { <PERSON><PERSON>ie<PERSON> } from 'libs';
import React, { ReactElement } from 'react';
import { observer } from 'mobx-react';
import { Input, Select, Button, message, Tooltip, Radio } from 'antd';
import { Bind } from 'lodash-decorators';
import { MockDataDetailM } from './MockDataDetailM';
import css from './MockDataDetail.less';
import { bindObserver } from '@libs/mvvm';
import { KdevIconFont } from '@/business/commonComponents/KdevIconFont';
import { KdevTitle } from '@/business/commonComponents/commonTitle/CommonTitle';
import Clipboard from 'clipboard';
import { resTypeOptions } from './configure';
import { AceDrag } from '@/business/aceDrag/AceDrag';

const { Option } = Select;

const Input_mockName = bindObserver(Input, 'mockName');
const RadioGroup_responseType = bindObserver(Radio.Group, 'responseType');

interface IProps {
    baseInfoTitleRight?: React.ReactNode | ReactElement;
}

@observer
export class MockDataDetail extends AView<MockDataDetailM, IProps> {
    protected clipboard = new Clipboard('.copyMockAddress');

    @Bind
    public onCopy() {
        message.success('复制成功');
    }

    @Bind
    private renderBaseInfoTitle(): React.ReactNode {
        return (
            <div className={ css.baseInfoTitle }>
                <KdevTitle text={ '基本信息' } />
                <div>
                    { this.props.baseInfoTitleRight }
                </div>
            </div>
        );
    }

    @Bind
    private renderMockAddress(): React.ReactNode {
        const model = this.model;
        if (model.type !== 0 && model.mockAddress) {
            return (
                <>
                    <div className={ css.rowLabel }>Mock地址</div>
                    <a href={ model.mockAddress } target={ '_blank' }>
                        { model.mockAddress }
                    </a>
                    <Button
                        type={ 'link' }
                        icon={ <KdevIconFont id={ '#iconcopy' } /> }
                        className={ 'copyMockAddress' }
                        data-clipboard-text={ model.mockAddress }
                        onClick={ this.onCopy }
                    />
                </>
            );
        }
    }

    @Bind
    private renderMockDataName(): React.ReactNode {
        const model = this.model;
        return (
            <>
                <div className={ css.rowLabelRequired }>数据名称</div>
                <Input_mockName model={ model } placeholder={ '请输入数据名称' } readOnly={ model.type === 1 } />
            </>
        );
    }

    @Bind
    private renderRequestParams(): React.ReactNode {
        const model = this.model;
        return (
            <>
                <div className={ css.rowLabel }>
                    <span>
                        参数过滤
                        <Tooltip
                            title={
                                '如果只有一条mock数据存在，可忽略此字段；当有多条MOCK数据存在时， 根据入参匹配使用哪条数据，' +
                                '如果都没匹配上，则返回默认数据'
                            }
                        >
                            <KdevIconFont id={ '#iconquestion' } className={ css.margL4 } />
                        </Tooltip>
                    </span>
                </div>
                <AceDrag
                    value={ model.request }
                    readOnly={ model.type === 1 }
                    onChange={ model.onChangeRequest }
                />
            </>
        );
    }

    @Bind
    private renderResponseParams(): React.ReactNode {
        const model = this.model;
        return (
            <>
                <div className={ css.rowLabel }>
                    Body
                    <RadioGroup_responseType
                        model={ model }
                        options={ resTypeOptions }
                    />
                </div>
                <AceDrag
                    value={ model.response }
                    onChange={ model.onChangeResponse }
                />
            </>
        );
    }

    public render(): React.ReactNode {
        const model = this.model;
        return (
            <div className={ css.mockDataDetailWrap }>
                { this.renderBaseInfoTitle() }
                { this.renderMockAddress() }
                { this.renderMockDataName() }
                { this.renderRequestParams() }
                <KdevTitle text={ '响应' } className={ css.responseTitle } />
                { this.renderResponseParams() }
            </div>
        );
    }
}
