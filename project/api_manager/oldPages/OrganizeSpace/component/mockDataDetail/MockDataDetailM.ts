import { AViewModel } from 'libs';
import { observable, action, runInAction, computed } from 'mobx';
import {
    nsMockManageKoasApiManageMockDetailGet
} from '@/remote';
import { beautifyStr<PERSON><PERSON>, JSONbigStringify } from '@/index.config/tools';
import { Bind } from 'lodash-decorators';

interface ISaveParams {
    docId: number;
    mockName: string;
    request: string;
    responseType: number;
    response: string;
    id?: number;
}

export class MockDataDetailM extends AViewModel {
    private apiId: number = 0;
    private mockDataId: number = 0;
    private default: boolean = false;
    @observable public type: number = 0; // 0:new | 1:scene edit
    @observable public mockName: string = '';
    @observable public mockAddress: string = '';
    @observable public request: string = '';
    @observable public responseType: number = 0;
    @observable public response: string = '';
    @observable public originParams: string = '';

    @computed
    public get isChangeParams(): boolean {
        const params = {
            docId: this.apiId,
            mockName: this.mockName,
            request: this.request,
            responseType: this.responseType,
            response: this.response,
        };
        if (this.mockDataId) {
            params['id'] = this.mockDataId;
        }
        if (!this.originParams) {
            return false;
        }
        return JSONbigStringify(params) !== this.originParams;
    }

    @action.bound
    public init(apiId: number, type: number = 0, mockDataId: number = 0): void {
        this.initData();
        this.apiId = apiId;
        this.mockDataId = mockDataId;
        this.type = type;
        if (this.type !== 0) {
            this.queryDetail();
        }
    }

    @action.bound
    public onChangeRequest(request: string): void {
        this.request = request;
    }

    @action.bound
    public onChangeResponse(response: string): void {
        this.response = response;
    }

    @action.bound
    private setMockDataInfo(mockData): void {
        this.mockName = mockData.mockName;
        this.mockAddress = mockData.mockAddress;
        this.request = beautifyStrJson(mockData.request);
        this.responseType = mockData.responseType || 0;
        this.response = beautifyStrJson(mockData.response);
        this.default = mockData.default;
    }

    @action.bound
    public initData(): void {
        this.apiId = 0;
        this.mockDataId = 0;
        this.mockName = '';
        this.mockAddress = '';
        this.request = '';
        this.responseType = 0;
        this.response = '';
        this.default = false;
        this.type = 0;
    }

    // 获取当前参数
    @action.bound
    public getCurrentSaveParams(): ISaveParams {
        const params: ISaveParams = {
            docId: this.apiId,
            mockName: this.mockName,
            request: this.request,
            responseType: this.responseType,
            response: this.response,
        };
        if (this.mockDataId) {
            params['id'] = this.mockDataId;
        }
        return params;
    }

    // 获取最初数据
    @action.bound
    private getOriginParams() {
        this.originParams = JSONbigStringify(this.getCurrentSaveParams());
    }

    @action.bound
    public isExit(): boolean {
        return JSONbigStringify(this.getCurrentSaveParams()) !== this.originParams;
    }

    // 获取mock数据详情
    @action.bound
    public async queryDetail() {
        try {
            const params = {
                id: this.mockDataId
            };
            const result = await nsMockManageKoasApiManageMockDetailGet.remote(params);
            runInAction(() => {
                this.setMockDataInfo(result);
                this.getOriginParams();
            });
        } catch (e) {
        }
    }
}
