.mockDataDetailWrap {
  .baseInfoTitle {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .rowLabel {
    margin: 24px 0 8px;
    display: flex;
    justify-content: space-between;

    .margL4 {
      margin-left: 4px;
      font-size: 16px;
    }
  }

  .rowLabelRequired {
    margin: 24px 0 8px;
  }

  .rowLabelRequired:after {
    content: ' *';
    color: #ff4d4f;
  }

  .responseTitle {
    margin-top: 24px;
  }
}
