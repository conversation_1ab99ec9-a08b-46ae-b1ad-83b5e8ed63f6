import APageModel from '@/pages/APageModel';
import { action, observable } from 'mobx';
import { departmentComponent } from '@/business/global';
import { DepartmentM } from './department/DepartmentM';
import { PersonnelM } from './personnel/PersonnelM';

export interface IQuery {
}

export class DepartmentPersonnelM extends APageModel<IQuery> {
    @observable public departmentId: number = departmentComponent.departmentComponentModel.departmentId;
    @observable public activeKey: string = '1';

    public departmentM = new DepartmentM({ departmentId: this.departmentId });
    public personnelM = new PersonnelM({ departmentId: this.departmentId });

    protected getQueryFields(): any {
        return ['activeKey'];
    }

    constructor(query: IQuery) {
        super(query);
        this.initByQueryFields(query);
    }

    @action
    public changeActiveKey(activeKey: string): void {
        this.activeKey = activeKey;
    }
}
