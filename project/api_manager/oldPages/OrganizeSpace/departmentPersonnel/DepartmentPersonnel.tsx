import React from 'react';
import APage from '@/pages/APage';
import { DepartmentPersonnelM, IQuery } from './DepartmentPersonnelM';
import { observer } from 'mobx-react';
import css from './DepartmentPersonnel.less';
import { Tabs } from 'antd';
import { Department } from './department/Department';
import { Personnel } from './personnel/Personnel';
import Bind from 'lodash-decorators/bind';

const { TabPane } = Tabs;

@observer
export default class DepartmentPersonnel extends APage<IQuery, DepartmentPersonnelM> {
    protected createModel(): DepartmentPersonnelM {
        return new DepartmentPersonnelM(this.query);
    }

    @Bind
    public onChangeActiveKey(activeKey: string): void {
        this.model.changeActiveKey(activeKey);
        this.push();
    }

    public renderContent(): React.ReactNode {
        const model = this.model;
        return (
            <Tabs
                className={ css.departmentPersonnelTab }
                onChange={ this.onChangeActiveKey }
                activeKey={ model.activeKey }
            >
                <TabPane tab={ '部门管理' } key={ '1' }>
                    <Department model={ model.departmentM } />
                </TabPane>
                <TabPane tab={ '人员管理' } key={ '2' }>
                    <Personnel model={ model.personnelM } />
                </TabPane>
            </Tabs>
        );
    }
}
