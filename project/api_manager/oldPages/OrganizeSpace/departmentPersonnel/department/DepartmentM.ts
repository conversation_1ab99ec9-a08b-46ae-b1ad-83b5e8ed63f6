import { AViewModel, eachNode } from 'libs';
import { observable, action, runInAction } from 'mobx';
import { message, notification, Modal } from 'antd';
import {
    nsMockManageApiManageDepartmentQueryDepartmentListGet, nsMockManageApiManageDepartmentQuerySubDepartmentListGet,
    nsMockManageApiManageDepartmentDeleteDepartmentPost, nsMockManageApiManageDepartmentAddDepartmentPost,
    nsMockManageApiManageDepartmentUpdateDepartmentPost, nsMockManageApiManageMarkBatchPost
} from '@/remote';
import { Bind } from 'lodash-decorators';

export class DepartmentM extends AViewModel {
    @observable public departmentId: number = 0;
    @observable public isModify: number = 0;
    @observable public createDepartModalVisible: boolean = false;
    @observable public departmentName: string = '';
    @observable public departmentDesc: string = '';
    @observable public departmentListLoading: boolean = false;
    @observable public departmentList: Array<any> = [];
    @observable public departmentSubList: Array<any> = [];
    @observable public cascaderDepartmentList: Array<any> = [];
    @observable public parentDepartmentId: number[] = [];
    @observable public editDepartmentId: number = 0;
    @observable public isExpanded: boolean = true;
    @observable public fileList: any[] = [];
    @observable public uploadLoading: boolean = false;

    constructor(query) {
        super();
        this.departmentId = query?.departmentId || 0;
        this.init();
    }

    @action
    protected init(): void {
        this.ajax_querySubDepartmentList(this.departmentId).then(() => {
            runInAction(() => {
                this.departmentList = this.departmentSubList;
                this.departmentList = this.getDepartmentList();
            });
        });
    }

    @action
    public getDepartmentList() {
        eachNode(this.departmentList, (data) => {
            !data.isLeaf && (data.children = data.children || []);
            return false;
        });
        return this.departmentList;
    }

    @action.bound
    public onExpandRow(expanded, record) {
        if (!record.children.length) {
            this.ajax_querySubDepartmentList(record.departmentId).then(() => {
                runInAction(() => {
                    record.children = this.departmentSubList.map(item => {
                        !item.isLeaf && (item.children = item.children || []);
                        if (record.isColor) {
                            item.isColor = record.isColor + 1;
                        } else {
                            item.isColor = 1;
                        }
                        return item;
                    });
                    this.departmentList = [...this.departmentList];
                });
            });
        }
    }

    /**
     * 添加部门
     */
    @action.bound
    public addDepartment() {
        if (this.editDepartmentId) {
            this.ajax_updateDepartment();
        } else {
            this.ajax_addDepartment();
        }
    }

    /**
     * 获取一级部门列表
     */
    @action.bound
    public async ajax_queryDepartmentList() {
        try {
            const result = await nsMockManageApiManageDepartmentQueryDepartmentListGet.remote({});
            runInAction(() => {
                this.cascaderDepartmentList = [...result.departmentList] || [];
                this.cascaderDepartmentList.map(item => {
                    if (item.departmentId !== this.departmentId) {
                        item.disabled = true;
                    }
                });
            });
        } catch (e) {
        }
    }

    /**
     * 获取子集部门列表
     */
    @action
    public async ajax_querySubDepartmentList(parentDepartmentId: number) {
        this.departmentListLoading = true;
        try {
            const params = {
                parentDepartmentId,
                topDepartmentId: this.departmentId
            };
            const result = await nsMockManageApiManageDepartmentQuerySubDepartmentListGet.remote(params);
            runInAction(() => {
                this.departmentSubList = result.departmentList || [];
                this.isModify = result.isModify;
                this.departmentListLoading = false;
            });
        } catch (e) {
            runInAction(() => {
                this.departmentSubList = [];
                this.departmentListLoading = false;
            });
        }
    }

    /**
     * 打开编辑部门弹框
     * @param record
     */
    @action.bound
    public openCreateDepartModal(record) {
        if (record) {
            this.editDepartmentId = record.departmentId;
            this.departmentName = record.departmentName;
            this.departmentDesc = record.departmentDesc;
        } else {
            this.ajax_queryDepartmentList();
        }
        this.createDepartModalVisible = true;
    }

    /**
     * 删除部门
     * @param departmentId
     */
    @action
    public async ajax_deleteDepartment(departmentId) {
        try {
            const params = {
                departmentId,
            };
            await nsMockManageApiManageDepartmentDeleteDepartmentPost.remote(params);
            runInAction(() => {
                message.success('删除成功');
                this.operationDepartmentList(this.departmentList, departmentId, 'delete');
            });
        } catch (e) {
        }
    }

    /**
     * 关闭添加部门弹窗
     */
    @action.bound
    public closeCreateDepartModal() {
        this.createDepartModalVisible = false;
        this.departmentName = '';
        this.departmentDesc = '';
        this.parentDepartmentId = [];
        this.editDepartmentId = 0;
    }

    /**
     * 联级选择部门
     * @param darentDepartmentId
     */
    @action.bound
    public changeParentDepartmentId(parentDepartmentId) {
        this.parentDepartmentId = parentDepartmentId;
    }

    @action.bound
    public loadDataCascaderDepartList(selectedOptions) {
        const selectedOption = selectedOptions[selectedOptions.length - 1];
        if (selectedOption.isLeaf) {
            return;
        }
        this.ajax_querySubDepartmentList(selectedOption.departmentId).then(() => {
            runInAction(() => {
                selectedOption.children = this.departmentSubList;
                this.departmentList = [...this.departmentList];
            });
        });
    }

    /**
     * 添加部门
     */
    @action
    public async ajax_addDepartment() {
        if (!this.parentDepartmentId.length) {
            message.warn('请选择父级部门');
            return;
        }
        if (!this.departmentName) {
            message.warn('请输入部门名称');
            return;
        }
        try {
            const params = {
                topDepartmentId: this.departmentId,
                parentDepartmentId: this.parentDepartmentId[this.parentDepartmentId.length - 1],
                departmentName: this.departmentName,
                departmentDesc: this.departmentDesc
            };
            await nsMockManageApiManageDepartmentAddDepartmentPost.remote(params);
            runInAction(() => {
                this.isExpanded = false;
                message.success('添加成功');
                this.closeCreateDepartModal();
                this.ajax_querySubDepartmentList(this.departmentId).then(() => {
                    runInAction(() => {
                        this.departmentList = this.departmentSubList;
                        this.departmentList = this.getDepartmentList();
                        this.isExpanded = true;
                    });
                });
            });
        } catch (e) {
        }
    }

    /**
     * 修改部门
     */
    @action
    public async ajax_updateDepartment() {
        try {
            const params = {
                departmentId: this.editDepartmentId,
                departmentName: this.departmentName,
                departmentDesc: this.departmentDesc
            };
            await nsMockManageApiManageDepartmentUpdateDepartmentPost.remote(params);
            runInAction(() => {
                message.success('修改成功');
                this.operationDepartmentList(this.departmentList, this.editDepartmentId, 'edit', {
                    darpmentName: this.departmentName,
                    darpmentDesc: this.departmentDesc
                });
                this.closeCreateDepartModal();
            });
        } catch (e) {
        }
    }

    @action
    public operationDepartmentList(list: Array<any>, id: number, operation: string, formData?) {
        if (operation === 'edit') {
            eachNode(list, (data) => {
                if (data.departmentId === id) {
                    data.departmentName = formData['darpmentName'];
                    data.departmentDesc = formData['darpmentDesc'];
                    this.departmentList = list;
                    return true;
                }
                return false;
            });
        }
        if (operation === 'delete') {
            const index = list.findIndex(item => item.departmentId === id);
            if (index > -1) {
                list.splice(index, 1);
                this.departmentList = [...list];
                return;
            }
            eachNode(list, (data) => {
                if (data.children && data.children.length && data.children.some(it => it.departmentId === id)) {
                    const ind = data.children.findIndex(item => item.departmentId === id);
                    data.children.splice(ind, 1);
                    this.departmentList = [...list];
                    return true;
                }
                return false;
            });
        }
    }

    @action.bound
    public beforeUpload(file) {
        const fileName = file.name;
        const pos = file.name.lastIndexOf('.');
        const lastName = fileName.substring(pos, fileName.length);
        if (lastName.toLowerCase() !== '.xls' && lastName.toLowerCase() !== '.xlsx') {
            message.warning('文件必须为.xls .xlsx类型～');
            return false;
        }
        this.fileList = [file];
        return true;
    }

    @Bind
    public async uploadFile() {
        try {
            runInAction(() => this.uploadLoading = true);
            const formData: any = new FormData();
            formData.append('departmentId', this.departmentId);
            formData.append('file', this.fileList[0]);
            const result = await nsMockManageApiManageMarkBatchPost.remote(formData);
            runInAction(() => {
                this.uploadLoading = false;
                if (result) {
                    Modal.success({
                        content: result
                    });
                }
            });
        } catch {
            runInAction(() => this.uploadLoading = false);
        }
    }
}
