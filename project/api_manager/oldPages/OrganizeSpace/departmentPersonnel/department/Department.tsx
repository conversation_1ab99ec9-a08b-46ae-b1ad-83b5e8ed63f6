import { AView } from 'libs';
import React from 'react';
import { DepartmentM } from './DepartmentM';
import { observer } from 'mobx-react';
import { Table, Input, Modal, Button, Empty, Tooltip, Cascader, Space, Upload } from 'antd';
import css from './Department.less';
import Bind from 'lodash-decorators/bind';
import { bindObserver } from '@libs/mvvm';
import { KdevIconFont } from '@/business/commonComponents/KdevIconFont';
import { UploadOutlined } from '@ant-design/icons';

const Input_departmentName = bindObserver(Input, 'departmentName');
const Input_departmentDesc = bindObserver(Input, 'departmentDesc');

@observer
export class Department extends AView<DepartmentM> {

    @Bind
    public departmentColumns(): Array<any> {
        const columns = [
            {
                title: '部门名称',
                // dataIndex: 'departmentName',
                key: 'departmentName',
                render: record => {
                    return <div>{`${record.departmentName}`}</div>;
                }
            },
            {
                title: '部门描述',
                dataIndex: 'departmentDesc',
                key: 'departmentDesc',
            },
            {
                title: '操作',
                // dataIndex: 'operation',
                key: 'operation',
                align: 'right',
                width: 150,
                render: (record) => {
                    return <>
                        <Tooltip title={'编辑'}>
                            <Button
                                icon={<KdevIconFont id={'#iconxingzhuangjiehe'} />}
                                disabled={!this.model.isModify}
                                onClick={() => this.model.openCreateDepartModal(record)}
                            />
                        </Tooltip>
                        <Tooltip title={'删除'}>
                            <Button
                                icon={<KdevIconFont id={'#iconyanse'} />}
                                className={css.deleteBtn}
                                disabled={!this.model.isModify}
                                onClick={() => this.onDeleteDepart(record)}
                            />
                        </Tooltip>
                    </>;
                }
            }
        ];
        return columns;
    }

    /**
     * 删除部门
     * @param record
     */
    @Bind
    public onDeleteDepart(record) {
        Modal.confirm({
            content: `确定要删除${record.departmentName}吗`,
            onOk: () => this.model.ajax_deleteDepartment(record.departmentId)
        });
    }

    @Bind
    public renderCreateDepartModal(): React.ReactNode {
        const model = this.model;
        return <Modal
            className={css.createDepartModal}
            title={model.editDepartmentId ? '编辑部门' : '添加部门'}
            visible={model.createDepartModalVisible}
            onCancel={model.closeCreateDepartModal}
            onOk={model.addDepartment}
        >
            {
                !model.editDepartmentId &&
                <>
                    <div className={css.createDepartRowLab}>父级部门 <span className={css.redColor}>*</span></div>
                    <Cascader
                        placeholder={'请选择父级部门'}
                        options={model.cascaderDepartmentList}
                        showSearch={false}
                        fieldNames={{
                            label: 'departmentName',
                            value: 'departmentId',
                            children: 'children',
                        }}
                        value={model.parentDepartmentId}
                        onChange={model.changeParentDepartmentId}
                        loadData={model.loadDataCascaderDepartList}
                        changeOnSelect
                        className={css.width100}
                    />
                </>
            }
            <div className={css.createDepartRowLab}>部门名称 <span className={css.redColor}>*</span></div>
            <Input_departmentName model={model} placeholder={'请输入部门名称'} />
            <div className={css.createDepartRowLab}>部门描述</div>
            <Input_departmentDesc model={model} placeholder={'请输入部门描述'} />
        </Modal>;
    }

    @Bind
    protected renderTop(): React.ReactNode {
        const model = this.model;
        return (
            <Space className={css.departmentTit}>
                <Upload
                    accept=".xls,.xlsx"
                    showUploadList={false}
                    beforeUpload={model.beforeUpload}
                    fileList={model.fileList}
                    withCredentials
                    customRequest={model.uploadFile}
                >
                    <Button
                        type="primary"
                        icon={<UploadOutlined />}
                        loading={model.uploadLoading}
                    >
                        接口批量标记
                    </Button>
                </Upload>
                <Button
                    type={'primary'}
                    className={css.addDepartBtn}
                    disabled={!model.isModify}
                    onClick={() => model.openCreateDepartModal(null)}
                >
                    添加部门
                </Button>
                {this.renderCreateDepartModal()}
            </Space>
        );
    }

    @Bind
    protected renderTable(): React.ReactNode {
        const model = this.model;
        if (model.isExpanded) {
            return (
                <Table
                    columns={this.departmentColumns()}
                    dataSource={model.departmentList}
                    rowKey={'departmentId'}
                    onExpand={model.onExpandRow}
                    loading={model.departmentListLoading}
                    indentSize={18}
                    pagination={false}
                    // scroll={ {
                    //     y: model.tableScrollYHeight
                    // } }
                    rowClassName={(record) => {
                        if ((record.isColor % 2) === 0 || !record.isColor) {
                            return '';
                        } else {
                            return 'rowColor';
                        }
                    }}
                    bordered
                />
            );
        } else {
            return <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />;
        }
    }

    public render(): React.ReactNode {
        const model = this.model;
        return (
            <div
                className={css.deppartmentWrap}
            >
                {this.renderTop()}
                {this.renderTable()}
            </div>
        );
    }
}
