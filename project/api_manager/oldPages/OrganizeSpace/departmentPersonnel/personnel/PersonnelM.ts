import { AViewModel } from 'libs';
import { observable, action, runInAction } from 'mobx';
import { message } from 'antd';
import {
    nsMockManageApiManageDepartmentQueryMaintainerListGet, nsMockPermissionGetUserListGet,
    nsMockManageApiManageDepartmentQueryUserRolePost, nsMockManageApiManageDepartmentAddMaintainerPost,
    nsMockManageApiManageDepartmentDeleteMaintainerPost
} from '@/remote';
import { Debounce } from 'lodash-decorators/debounce';

interface IMaintainers {
    userId: number;
    user: string;
    username: string;
    type: number;
    joinTime: string;
    isModify: number;
}

export class PersonnelM extends AViewModel {
    @observable public departmentId: number = 0;
    @observable public addUserModalVisible: boolean = false;
    @observable public userList: Array<any> = [];
    @observable public loadingUser: boolean = false;
    @observable public type: number = 1;
    @observable public username: string = '';
    @observable public user: string = '';
    @observable public userId: number = 0;
    @observable public key: string = '';

    @observable public maintainers: IMaintainers[] = [];
    @observable public isModify: number = 0;
    @observable public total: number = 0;
    @observable public currentPage: number = 1;
    @observable public pageSize: number = 20;
    @observable public maintainersLoading: boolean = false;
    @observable public addType: number = 0;

    constructor(query) {
        super();
        this.departmentId = query?.departmentId || 0;
        this.init();
    }

    @action
    protected init(): void {
        this.ajax_queryMaintainerList();
    }

    @action.bound
    public onSearchMaintainers(val?: string): void {
        this.key = val || '';
        this.onChangePageInfo(1);
    }

    @action.bound
    public onSaveAddMaintainer() {
        if (this.userId) {
            this.ajax_deleteMaintainer(0);
        } else {
            this.ajax_addMaintainer();
        }
    }

    /**
     * 打开添加人员弹框
     */
    @action.bound
    public openAddUserModal(record) {
        if (record) {
            this.userId = record.userId;
            this.user = record.user;
            this.username = record.username;
            this.type = record.type;
            this.ajax_getUserList();
        }
        this.addUserModalVisible = true;
    }

    /**
     * 关闭添加成员弹窗
     */
    @action.bound
    public closeAddUserModal() {
        this.user = '';
        this.username = '';
        this.type = 1;
        this.userId = 0;
        this.addType = 0;
        this.addUserModalVisible = false;
    }

    /**
     * 选择成员类型
     * @param type
     */
    @action.bound
    public selectUserType(type) {
        this.type = type;
    }

    /**
     * 切换每页条数
     * @param currentPage
     * @param pageSize
     */
    @action.bound
    public onChangePageInfo(currentPage: number, pageSize?: number) {
        this.currentPage = currentPage;
        this.pageSize = pageSize || this.pageSize;
        this.ajax_queryMaintainerList();
    }

    /**
     * 搜索成员列表
     * @param val
     */
    @action.bound
    public searchUserList(val) {
        this.user = val;
        this.ajax_getUserList();
    }

    /**
     * 选择添加成员
     * @param e
     */
    @action.bound
    public changeUser(val, e) {
        this.user = e.value;
        this.username = e.label;
        this.ajax_queryUserRole();
    }

    /**
     * 获取成员列表
     */
    @action
    public async ajax_queryMaintainerList() {
        this.maintainersLoading = true;
        try {
            const params = {
                departmentId: this.departmentId,
                currentPage: this.currentPage,
                pageSize: this.pageSize,
                key: this.key
            };
            const result = await nsMockManageApiManageDepartmentQueryMaintainerListGet.remote(params);
            runInAction(() => {
                this.maintainers = result.maintainers || [];
                this.isModify = result.isModify;
                this.total = result.total || 0;
                this.maintainersLoading = false;
            });
        } catch (e) {
            runInAction(() => {
                this.maintainersLoading = false;
            });
        }
    }

    /**
     * 搜索成员列表
     */
    @Debounce(300)
    @action
    public async ajax_getUserList() {
        if (this.user.length <= 3) {
            return;
        }
        this.loadingUser = true;
        try {
            const params = {
                username: this.user
            };
            const result = await nsMockPermissionGetUserListGet.remote(params);
            runInAction(() => {
                this.userList = result.list || [];
                this.loadingUser = false;
            });
        } catch (e) {
            runInAction(() => {
                this.loadingUser = false;
            });
        }
    }

    @action
    public async ajax_queryUserRole() {
        try {
            const params = {
                departmentId: this.departmentId,
                maintainer: this.user
            };
            const result = await nsMockManageApiManageDepartmentQueryUserRolePost.remote(params);
            runInAction(() => {
                this.addType = result.type || 0;
            });
        } catch (e) {
        }
    }

    /**
     * 添加成员
     */
    @action
    public async ajax_addMaintainer() {
        if (!this.user) {
            message.warn('请选择成员');
            return;
        }
        try {
            const params = {
                departmentId: this.departmentId,
                user: this.user,
                username: this.username,
                type: this.type
            };
            await nsMockManageApiManageDepartmentAddMaintainerPost.remote(params);
            runInAction(() => {
                message.success('添加成功');
                this.ajax_queryMaintainerList();
                this.closeAddUserModal();
            });
        } catch (e) {
        }
    }

    /**
     * 修改删除成员
     * @param isDiscard
     */
    @action
    public async ajax_deleteMaintainer(isDiscard, record?) {
        try {
            const params = {
                departmentId: this.departmentId,
                userId: isDiscard ? (record?.userId || 0) : this.userId,
                type: isDiscard ? record.type : this.type,
                isDiscard
            };
            await nsMockManageApiManageDepartmentDeleteMaintainerPost.remote(params);
            runInAction(() => {
                if (isDiscard) {
                    message.success('删除成功');
                    this.ajax_queryMaintainerList();
                } else {
                    message.success('修改成功');
                    this.ajax_queryMaintainerList();
                    this.closeAddUserModal();
                }
            });
        } catch (e) {
        }
    }
}
