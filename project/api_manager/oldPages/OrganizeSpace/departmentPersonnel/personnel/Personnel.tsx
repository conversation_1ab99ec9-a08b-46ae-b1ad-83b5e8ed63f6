import { AView } from 'libs';
import React from 'react';
import { PersonnelM } from './PersonnelM';
import { observer } from 'mobx-react';
import { Table, Input, Modal, Button, Empty, Avatar, Select, Spin } from 'antd';
import css from './Personnel.less';
import Bind from 'lodash-decorators/bind';
import { bindObserver } from '@libs/mvvm';
import { KdevIconFont } from '@/business/commonComponents/KdevIconFont';
import { Select_onDropdownVisibleChange } from 'libs/utils';

const { Search, TextArea } = Input;
const { Option } = Select;

const Input_key = bindObserver(Search, 'key');

@observer
export class Personnel extends AView<PersonnelM> {

    public state = {
        userRoleEnum: {
            1: '成员',
            2: '管理员'
        }
    };

    @Bind
    public personnelColumns(): Array<any> {
        const model = this.model;
        const columns = [
            {
                title: '成员',
                // dataIndex: 'user',
                key: 'user',
                // align: 'center',
                width: 320,
                render: record => {
                    return (
                        <div className={ css.searPeopleOption }>
                            <Avatar src={ '' } size={ 36 }>{ record.username[0] }</Avatar>
                            <div className={ css.info }>
                                <div style={ { fontWeight: 'bold' } }>
                                    { record.username }
                                    {
                                        record.inherit &&
                                        <span style={ { color: '#999' } }>({ record?.groupName })</span>
                                    }
                                </div>
                                <div>{ `${ record.user }@kuaishou.com` }</div>
                            </div>
                        </div>
                    );
                }
            },
            {
                title: '用户角色',
                // dataIndex: 'type',
                key: 'type',
                // align: 'center',
                width: 100,
                render: record => {
                    return <div>
                        { this.state.userRoleEnum[record.type] }
                    </div>;
                }
            },
            {
                title: '加入时间',
                dataIndex: 'joinTime',
                key: 'joinTime',
                // align: 'center',
                width: 200
            },
            {
                title: '操作',
                // dataIndex: 'operation',
                key: 'operation',
                align: 'right',
                width: 100,
                render: (record) => {
                    return <>
                        <Button
                            icon={ <KdevIconFont id={ '#iconxingzhuangjiehe' } /> }
                            disabled={ !this.model.isModify }
                            onClick={ () => model.openAddUserModal(record) }
                        />
                        <Button
                            icon={ <KdevIconFont id={ '#iconyanse' } /> }
                            disabled={ !this.model.isModify }
                            className={ css.deleteBtn }
                            onClick={ () => this.onDeleteUser(record) }
                        />
                    </>;
                }
            }
        ];
        return columns;
    }

    /**
     * 删除成员
     * @param record
     */
    @Bind
    public onDeleteUser(record) {
        Modal.confirm({
            content: `确认是否移除「${ record.username }」`,
            onOk: () => this.model.ajax_deleteMaintainer(1, record)
        });
    }

    @Bind
    public renderAddUserModal(): React.ReactNode {
        const model = this.model;
        return <Modal
            visible={ model.addUserModalVisible }
            title={ model.userId ? '编辑' : '添加' }
            className={ css.addUserModal }
            onCancel={ model.closeAddUserModal }
            onOk={ model.onSaveAddMaintainer }
        >
            <div className={ css.addUserRowLab }>成员 <span className={ css.redColor }>*</span></div>
            <Select
                notFoundContent={ this.model.loadingUser ?
                    <Spin size="small" /> :
                    <Empty image={ Empty.PRESENTED_IMAGE_SIMPLE } /> }
                className={ css.addUserRowContent }
                showSearch
                placeholder={ '请输入邮箱前缀进行搜索' }
                loading={ model.loadingUser }
                optionLabelProp={ 'label' }
                filterOption={ false }
                value={ model.user || undefined }
                onSearch={ model.searchUserList }
                onSelect={ model.changeUser }
                onDropdownVisibleChange={ Select_onDropdownVisibleChange }
                disabled={ Boolean(model.userId) }
            >
                {
                    model.userList.map(item => {
                        return (
                            <Option
                                key={ item.username }
                                label={ item.name }
                                value={ item.username }
                            >
                                <div style={ { margin: '8px 0' } }>
                                    <div style={
                                        {
                                            display: 'inline-block',
                                            verticalAlign: 'top',
                                            marginRight: '12px'
                                        }
                                    }
                                    >
                                        <Avatar
                                            size={ 36 }
                                            src={ item.photo }
                                        >
                                            { item.name }
                                        </Avatar>
                                    </div>
                                    <div style={ {
                                        display: 'inline-block',
                                        verticalAlign: 'top'
                                    } }>
                                        <div
                                            style={ { lineHeight: '22px' } }
                                        >
                                            { item.name }
                                        </div>
                                        <div
                                            style={ {
                                                lineHeight: '18px',
                                                fontSize: '12px',
                                                color: '#898a8c'
                                            } }
                                        >
                                            {/*{ item.email }*/ }
                                            { `${ item.username }@kuaishou.com` }
                                        </div>
                                    </div>
                                </div>
                            </Option>
                        );
                    })
                }
            </Select>
            {
                model.addType !== 0 &&
                <div className={ css.redColor }>{ `该${ this.state.userRoleEnum[model.addType] }已存在` }</div>
            }
            <div className={ css.addUserRowLab }>角色 <span className={ css.redColor }>*</span></div>
            <Select
                className={ css.addUserRowContent }
                value={ model.type }
                onSelect={ model.selectUserType }
            >
                <Option value={ 2 }>管理员</Option>
                <Option value={ 1 }>成员</Option>
            </Select>
        </Modal>;
    }

    @Bind
    protected renderTop(): React.ReactNode {
        const model = this.model;
        return (
            <div className={ css.personnelTop }>
                <Input_key
                    model={ model }
                    placeholder={ '支持成员名称搜索' }
                    onSearch={ model.onSearchMaintainers }
                    style={ { width: '400px' } }
                    allowClear
                />
                <Button
                    type={ 'primary' }
                    disabled={ !model.isModify }
                    onClick={ () => model.openAddUserModal(null) }
                    style={ { float: 'right' } }
                >添加人员</Button>
                { this.renderAddUserModal() }
            </div>
        );
    }

    @Bind
    protected renderTable(): React.ReactNode {
        const model = this.model;
        return (
            <Table
                className={ css.personnelTable }
                columns={ this.personnelColumns() }
                dataSource={ model.maintainers }
                rowKey={ 'userId' }
                loading={ model.maintainersLoading }
                pagination={ {
                    // size: 'small',
                    current: model.currentPage,
                    pageSize: model.pageSize,
                    showSizeChanger: true,
                    total: model.total,
                    showTotal: total => `共 ${ total } 条`,
                    onChange: model.onChangePageInfo,
                } }
                bordered
            />
        );
    }

    public render(): React.ReactNode {
        const model = this.model;
        return (
            <div
                className={ css.personnelWrap }
            >
                { this.renderTop() }
                { this.renderTable() }
            </div>
        );
    }
}
