import React from 'react';
import APage from '@/pages/APage';
import { observer } from 'mobx-react';
import { bindObserver, router } from '@libs/mvvm';
import { Bind, Debounce } from 'lodash-decorators';
import { HttpApiMgrM } from './HttpApiMgrM';
import css from './HttpApiMgr.less';
import {
    Button, Input, Table, Tooltip, Radio, Select, Space
} from 'antd';
import { KdevIconFont } from '@/business/commonComponents';
import { MoveInProject } from '../../src/pages/automaticParsingApi/httpMoveInProject/MoveInProject';
import { ERouter } from 'CONFIG';
import { IQuery, typeOptions } from '../../src/pages/automaticParsingApi/httpApiMgr/configure';
import { Select_onDropdownVisibleChange } from '@libs/utils';
import { StandardInfo } from '@/business/apiDetail/standardInfo/StandardInfo';
import { kconfStore } from '@/index.config';
import { common_system_fire_line1, common_system_xiaojiantouyou } from '@kid/enterprise-icon/icon/output/icons';
import { HttpSwaggerApi } from '../../src/pages/automaticParsingApi/httpApiMgr/HttpSwaggerApi';
import { sendImmediatelyClick } from '@/index.config/weblogger';

const { Search } = Input;
const { Option } = Select;

const Search_key = bindObserver(Search, 'key');
const RadioGroup_type = bindObserver(Radio.Group, 'type');

@observer
export default class HttpApiMgr extends APage<IQuery, HttpApiMgrM> {
    protected createModel(): HttpApiMgrM {
        return new HttpApiMgrM(this.query);
    }

    @Bind
    private columns(): any[] {
        const model = this.model;
        const column: any[] = [
            {
                title: 'API名称',
                // dataIndex: 'moduleName',
                width: 260,
                render: this.renderApiUrl
            },
            {
                title: '模块',
                dataIndex: 'moduleName',
                width: 150,
                render: text => <span className={css.moduleName}>{text}</span>
            },
            // {
            //     title: '方法',
            //     dataIndex: 'apiMethod',
            //     width: 60
            // },
            {
                title: '当前分支',
                dataIndex: 'apiBranch',
                width: 100
            },
            {
                title: '创建人',
                dataIndex: 'createUser',
                width: 100
            },
            {
                title: '规范性校验',
                // dataIndex: 'isStandard',
                width: 80,
                render: this.renderIsStandard
            },
            {
                title: '创建时间',
                dataIndex: 'createTime',
                width: 120
            },
            {
                title: '是否最新',
                dataIndex: 'isLasted',
                width: 80,
                render: text => Boolean(text) ? '是' : '否'
            },
            {
                title: '操作',
                width: 60,
                render: this.renderOperate
            }
        ];
        return column;
    }

    @Bind
    private renderIsStandard(record): React.ReactNode {
        return (
            <>
                <span style={{ color: record.isStandard === 2 ? '#ff4d4f' : '#31bf30' }}>
                    {record.isStandard === 2 ? '未通过' : '通过'}
                </span>
                {
                    record.isStandard === 2 &&
                    <a
                        className={css.isStandardDetail}
                        onClick={() => this.model.onOpenStandardInfo(record.apiId)}
                    >
                        详情
                    </a>
                }
            </>
        );
    }

    @Bind
    private renderApiUrl(record): React.ReactNode {
        const goApiDetail = (): void => {
            router.push(ERouter.API_MOCK_HTTP_API_DETAIL, {
                moduleId: record.moduleId,
                moduleName: record.moduleName,
                apiId: record.apiId,
                branchName: record.apiBranch,
                type: this.model.type,
                key: this.model.key
            });
        };
        return (
            <>
                <span className={css.apiMethod}>{record.apiMethod}</span>
                <a onClick={goApiDetail} className={css.apiUrl}>
                    {record.apiUrl}
                </a>
            </>
        );
    }

    @Bind
    private onChangeType(): void {
        this.model.onChangePageInfo(1);
        this.push();
    }

    @Bind
    private onSelectModule(val: number, node?): void {
        const moduleName = node?.value ? node.children : '';
        this.model.selectModule(val, moduleName);
        this.push();
    }

    @Bind
    private onSelectModuleBranch(val: string): void {
        this.model.selectBranchName(val);
        this.push();
    }

    @Bind
    private moveModuleToProject(record): void {
        if (record.projectId) {
            this.model.getHttModuleDepartment(record);
        } else {
            this.model.onOpenMoveInProject(record.moduleId);
        }
    }

    @Debounce(300)
    @Bind
    private onSearchHttpModuleList(key: string = ''): void {
        this.model.getHttpModuleList(key);
    }

    @Bind
    private onSearchApi(): void {
        this.model.onChangePageInfo(1);
        this.push();
    }

    @Bind
    private renderOperate(record): React.ReactNode {
        const model = this.model;
        return (
            <Tooltip title={'生成人工编辑文档'}>
                <Button
                    className={css.margL8}
                    icon={<KdevIconFont id={'#iconreport'} />}
                    onClick={() => this.moveModuleToProject(record)}
                />
            </Tooltip>
        );
    }

    @Bind
    private renderTop(): React.ReactNode {
        const model = this.model;
        return (
            <div className={css.httpApiMgrTop}>
                <div className={css.searchLeft}>
                    <RadioGroup_type
                        model={model}
                        options={typeOptions}
                        optionType={'button'}
                        className={css.type}
                        onChange={this.onChangeType}
                    />
                    模块：
                    <Select
                        className={css.moduleSelect}
                        value={model.moduleId || undefined}
                        showSearch
                        onSelect={this.onSelectModule}
                        onSearch={this.onSearchHttpModuleList}
                        onDropdownVisibleChange={Select_onDropdownVisibleChange}
                        placeholder={'请选择模块'}
                        allowClear
                        filterOption={false}
                        onClear={() => {
                            this.onSelectModule(0);
                            this.onSearchHttpModuleList();
                        }}
                    >
                        {
                            model.moduleList.map(item => {
                                return <Option key={item.moduleId} value={item.moduleId}>
                                    {item.moduleName}
                                </Option>;
                            })
                        }
                    </Select>
                    分支：
                    <Select
                        className={css.moduleBrachSelect}
                        placeholder={'请选择分支'}
                        disabled={!model.moduleId}
                        showSearch
                        onSelect={this.onSelectModuleBranch}
                        value={model.branchName || undefined}
                        allowClear
                        onClear={() => {
                            this.onSelectModuleBranch('');
                            this.onSearchHttpModuleList();
                        }}
                    >
                        {
                            model.branchNameList.map(item => {
                                return <Option key={item} value={item}>{item}</Option>;
                            })
                        }
                    </Select>
                </div>
                <Search_key
                    model={model}
                    placeholder={'支持 API名称 搜索'}
                    className={css.keyWord}
                    // onSearch={() => model.onChangePageInfo(1)}
                    onSearch={this.onSearchApi}
                />
            </div>
        );
    }

    @Bind
    private renderTable(): React.ReactNode {
        const model = this.model;
        return (
            <Table
                columns={this.columns()}
                dataSource={model.apiList}
                className={css.table}
                rowKey={'apiId'}
                bordered
                loading={model.tableLoading}
                pagination={{
                    current: model.currentPage,
                    pageSize: model.pageSize,
                    showTotal: total => `共 ${total} 条`,
                    total: this.model.total,
                    showSizeChanger: true,
                    onChange: model.onChangePageInfo,
                }}
            />
        );
    }

    public renderContent(): React.ReactNode {
        const model = this.model;
        // if (kconfStore.isShowSwaggerUI) {
        //     return (
        //         <HttpSwaggerApi
        //             model={model.httpSwaggerApiM}
        //             onBackOldVersionApi={this.onBackOldVersionApi}
        //         />
        //     );
        // }
        return (
            <div className={css.httpApiMgrWrap}>
                {this.renderTop()}
                {this.renderTable()}
                <MoveInProject model={model.moveInProjectM} />
                <StandardInfo model={model.standardInfoM} />
            </div>
        );
    }
}
