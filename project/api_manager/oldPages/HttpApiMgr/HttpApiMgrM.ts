import APageModel from '@/pages/APageModel';
import { action, observable, runInAction } from 'mobx';
import { MoveInProjectM } from '../../src/pages/automaticParsingApi/httpMoveInProject/MoveInProjectM';
import {
    nsMockManageKoasApiManageAutoDocGetHttpApiListGet, nsMockManageKoasApiManageModuleGetHttpModuleListGet,
    nsMockManageKoasApiManageModuleGetHttModuleDepartmentGet, nsMockManageKoasApiManageModuleGetHttpModuleBranchGet
} from '@/remote';
import { IQuery, IModuleList, IApiList } from '../../src/pages/automaticParsingApi/httpApiMgr/configure';
import { ERouter } from 'CONFIG';
import { departmentCascader } from '@/business/global';
import { StandardInfoM } from '@/business/apiDetail/standardInfo/StandardInfoM';
import { HttpSwaggerApiM } from '../../src/pages/automaticParsingApi/httpApiMgr/HttpSwaggerApiM';

export class HttpApiMgrM extends APageModel<IQuery> {
    @observable public moduleId: number = 0;
    @observable public moduleName: string = '';
    @observable public moduleList: IModuleList[] = [];
    @observable public branchName: string = '';
    @observable public branchNameList: string[] = [];
    @observable public key: string = '';
    @observable public type: number = 1;
    @observable public currentPage: number = 1;
    @observable public pageSize: number = 10;
    @observable public total: number = 0;
    @observable public apiList: IApiList[] = [];
    @observable public tableLoading: boolean = false;

    public moveInProjectM = new MoveInProjectM();
    public standardInfoM = new StandardInfoM();
    public httpSwaggerApiM = new HttpSwaggerApiM();

    constructor(query: IQuery) {
        super(query);
        this.initByQueryFields(query);
        this.init();
    }

    protected getQueryFields(): Array<keyof IQuery> {
        return [
            'moduleId',
            'moduleName',
            'type',
            'branchName',
            'key'
        ];
    }

    @action
    private init() {
        this.moduleId = Number(this.moduleId);
        this.type = Number(this.type);
        if (this.moduleId) {
            this.onChangePageInfo(1);
        }
        this.getHttpModuleList(this.moduleName);
        this.moduleId && this.getHttModuleBranch(this.moduleId);
    }

    @action.bound
    public selectModule(moduleId: number, moduleName: string): void {
        this.moduleId = moduleId;
        this.moduleName = moduleName;
        this.branchName = '';
        this.onChangePageInfo(1);
        if (this.moduleId) {
            this.getHttModuleBranch(this.moduleId);
        } else {
            this.branchNameList = [];
        }
    }

    @action.bound
    public selectBranchName(branchName: string): void {
        this.branchName = branchName;
        this.onChangePageInfo(1);
    }

    // 分页查询
    @action.bound
    public onChangePageInfo(currentPage: number, pageSize?: number): void {
        this.currentPage = currentPage;
        this.pageSize = pageSize || this.pageSize;
        this.getHttpApiList();
    }

    // 移入项目
    @action.bound
    public onOpenMoveInProject(moduleId: number): void {
        this.moveInProjectM.init(moduleId);
        this.moveInProjectM.onMoveInProjectCallback = this.getHttpApiList;
    }

    @action.bound
    public onOpenStandardInfo(docId: number): void {
        this.standardInfoM.init(docId, 1);
    }

    @action.bound
    public async getHttModuleDepartment(record) {
        try {
            const params = {
                moduleId: record.moduleId
            };
            const result = await nsMockManageKoasApiManageModuleGetHttModuleDepartmentGet.remote(params);
            runInAction(() => {
                departmentCascader.setDepartment(result.departmentIdList, result.departmentFullName);
                const url: string = `${ERouter.API_MOCK_PROJECT_MODULEDETAIL}?apiId=${record.apiId}&moduleId=${record.moduleId}&projectId=${record.projectId}&showType=autoCopy`;
                window.open(url);
            });
        } catch (e) {
        }
    }

    // 获取HTTP模块列表
    @action.bound
    private async getHttpApiList() {
        this.tableLoading = true;
        try {
            const params = {
                key: this.key,
                pageSize: this.pageSize,
                currentPage: this.currentPage,
                moduleId: this.moduleId,
                type: this.type,
                branchName: this.branchName
            };
            const result = await nsMockManageKoasApiManageAutoDocGetHttpApiListGet.remote(params);
            runInAction(() => {
                this.apiList = result?.apiList || [];
                this.total = result?.total;
                this.tableLoading = false;
            });
        } catch (e) {
            runInAction(() => {
                this.tableLoading = false;
            });
        }
    }

    // 获取HTTP模块列表
    @action.bound
    public async getHttpModuleList(key: string = '') {
        try {
            const params = {
                key,
                pageSize: 100,
                currentPage: 1
            };
            const result = await nsMockManageKoasApiManageModuleGetHttpModuleListGet.remote(params);
            runInAction(() => {
                this.moduleList = result?.list || [];
            });
        } catch (e) {
        }
    }

    // 获取模块下的分支
    @action.bound
    private async getHttModuleBranch(moduleId: number) {
        try {
            const result = await nsMockManageKoasApiManageModuleGetHttpModuleBranchGet.remote({ moduleId });
            runInAction(() => {
                this.branchNameList = result?.branchNameList || [];
            });
        } catch (e) {
        }
    }
}
