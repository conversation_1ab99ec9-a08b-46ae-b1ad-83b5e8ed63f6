.httpApiMgrWrap {
  background-color: #FFFFFF;
  padding: 24px;
  overflow: auto;

  .httpApiMgrTop {
    display: flex;
    // justify-content: space-between;

    .searchLeft {
      margin-right: auto;
    }

    .type {
      margin-right: 24px;
    }

    .moduleSelect {
      width: 294px;
      margin-right: 24px;
    }

    .moduleBrachSelect {
      width: 200px;
    }

    .newVersionBtn {
      float: right;
      display: flex;
      justify-content: space-around;
      align-items: center;
      cursor: pointer;
      padding: 5px 4px;
      width: 114px;
      height: 28px;
      background: linear-gradient(90deg, #4259fa -9.65%, #cf3ec1 49.29%, #ffac49 107.02%);
      border-radius: 22px;
      color: #fff;
      margin: 2px 0 0 8px;
    }
  }

  .keyWord {
    width: 294px;
  }

  .table {
    margin-top: 24px;

    .apiUrl,
    .moduleName {
      word-break: break-all;
    }

    .apiMethod {
      background-color: rgba(50, 125, 255, .08);
      // opacity: 0.08;
      // color: #327dff;
      margin-right: 8px;
      border-radius: 4px;
      padding: 0 4px;
    }

    .focusBtn {
      background-color: #ffa114;
      border: 1px solid #eb9008;
      color: #ffffff;
    }

    .isStandardDetail {
      margin-left: 4px;
    }

    .margR8 {
      margin-right: 8px;
    }

    .margL8 {
      margin-left: 8px;
    }
  }

}