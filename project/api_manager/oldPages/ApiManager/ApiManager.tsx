import React, { Key, useEffect, useRef, useState, useMemo, useCallback, useContext } from 'react';
import { Button, Input, Space, Tree, Spin, Modal, message, Tooltip, Segmented, Skeleton } from 'antd';
import css from './ApiManager.less';
import { KdevIconFont, KEmpty } from '@/business/commonComponents';
import { common_system_more02, common_system_folder_close_line } from '@kid/enterprise-icon/icon/output/icons';
import { ERouter } from 'CONFIG';
import { getUrlSearch, pushKey } from '@/index.config/tools';
import { router } from '@libs/mvvm';
import { ViewApi } from '../httpApi/viewApi/ViewApi';
import {
    nsApiManagerSearch,
    nsApiManagerList,
    nsApiManagerBatchDelete,
    nsApiManagerGroupTree,
    nsMockManageApiManageMainCollectListGET
} from '@/remote';
import { GroupDetail, GroupTitle } from '@/pages/ApiManager/GroupDetail';
import { CreateGroup, ICreateGroupRef } from '@/pages/httpApi/compoents/CreateGroup';
import { editTypeEnum } from '@/pages/httpApi/editApi/EditApi';
import { DirectoryTreeNode } from './ApiDirectoryTree';
import RcTree from 'rc-tree';
import { ImportDropdown } from './importDropdown/ImportDropdown';
import { ApiMgrSplitPane } from './ApiMgrSplitPane';
import { GroupMoreOperate, EMenuType } from '@/pages/ApiManager/groupMoreOperate/GroupMoreOperate';
import { eNodeType } from './relevantToMe/RelevantToMeUtils';
import store from '../../pages/httpApi/viewApi/checkRule/store';
import { NewUserLandingPage } from './newUserLandingPage/NewUserLandingPage';
import { NewUserLandingPageM } from './newUserLandingPage/NewUserLandingPageM';
import { DirMenu } from './dirMenu/DirMenu';
import { DirMenuM } from './dirMenu/DirMenuM';

interface IQuery {
    groupId?: number;
    apiId?: number;
    version?: number;
    activeKey?: string;
}

const newUserLandingPageM = new NewUserLandingPageM();
const dirMenuM = new DirMenuM();

// 判断两个元素是否相交
function isIntersect(oneNode: Element, twoNode: Element) {
    const oneNodeRect = oneNode.getBoundingClientRect();
    const twoNodeRect = twoNode.getBoundingClientRect();
    const intersect = !(
        oneNodeRect.right < twoNodeRect.left ||
        oneNodeRect.left > twoNodeRect.right ||
        oneNodeRect.bottom < twoNodeRect.top ||
        oneNodeRect.top > twoNodeRect.bottom
    );
    return intersect;
}

// 判断输入内容是否小于最小限制字符数
function isExceedByteLimit(str: string, limit: number) {
    let count = 0;
    for (let i = 0, len = str.length; i < len; i++) {
        count += str.charCodeAt(i) < 256 ? 1 : 2;
    }
    return count < limit;
}

/**
 * 此组件在2个路由上面使用
 * 若query上有projectId，代码仓库下的api管理
 * 若无，则代表在目录下的api管理
 * @constructor
 */
function ApiManager(props) {
    const searchParams = getUrlSearch(['groupId', 'apiId', 'version']) as IQuery;
    const [groupId, setGroupId] = useState<number | undefined>(searchParams.groupId);
    const [apiId, setApiId] = useState<number | undefined>(searchParams.apiId || undefined);
    const [apiType, setApiType] = useState<'all' | 'withMe'>('all');
    const createGroupRef = useRef<ICreateGroupRef>(null);
    const treeWrapRef = useRef<HTMLDivElement>(null);
    const treeRef = useRef<RcTree>(null);
    const [treeWrapRefHeight, setTreeWrapRefHeight] = useState<number>();

    const [search, setSearch] = useState<string>('');
    /**
     * 左侧目录树的值
     */
    const [treeData, setTreeData] = useState<Array<nsApiManagerSearch.IItem>>([]);
    const [flatTreeData, setFlatTreeData] = useState<Record<string, nsApiManagerSearch.IItem>>({});
    const [expandedKeys, setExpandedKeys] = useState<React.Key[]>([]);
    const [autoExpandParent, setAutoExpandParent] = useState(false);
    const [loadLock, setLoadLock] = useState(true);

    const [loading, setLoading] = useState<boolean>(true);


    /**
     * 左侧与我相关目录树的值
     */
    const [treeDataWithMe, setTreeDataWithMe] = useState<Array<nsApiManagerSearch.IItem>>([]);

    /**
     * 在编辑、删除后；都要更新对应的目录
     */
    const updateTreeDataByKey = async (key: number) => {
        if (!key) {
            return;
        }
        const group = await getGroupLayer(key);
        if (flatTreeData[`G${key}`]) {
            flatTreeData[`G${key}`].children = group.children;
            flatTreeData[`G${key}`].isLeaf = group.isLeaf;
        }
        setTreeData([...treeData]);
    };

    // 跳转到新建、编辑API界面
    const goEditApi = () => {
        const urlParams = {
            groupId: groupId,
            editType: editTypeEnum.NEW
        };
        router.push(ERouter.API_MOCK_REPO_EDIT_API, urlParams);
    };

    // ---------------------- 左侧树部分 -----------------------

    const onExpand = (newExpandedKeys: React.Key[]) => {
        setExpandedKeys(newExpandedKeys);
        setAutoExpandParent(false);
    };
    const selectedKey = (() => {
        if (apiId) {
            return `A${apiId}`;
        }
        if (groupId) {
            return `G${groupId}`;
        }
        return undefined;
    })();

    const handleSearch = async (key: string) => {
        if (key === search) {
            return;
        }
        setSearch(key);
        try {
            if (!!key) {
                if (isExceedByteLimit(key, 3)) {
                    message.warn('最少需要输入3个字符或者2个汉字');
                    return;
                }
                const { list } = await nsApiManagerSearch.remote({ key });
                // 搜索时展开全部
                setLoadLock(false);
                setExpandedKeys(expandAll(list));
                setLoadLock(true);
                setTreeData(list);
            } else {
                // 当key不存在时， 搜索初始化接口
                const list = await getTreeDataInit();
                setTreeData(list);
            }
        } catch (e) { }
    };

    // 删除分组
    const deleteGroup = async (gId: number, parentId: number, type: number) => {
        try {
            await nsApiManagerBatchDelete.remote({
                list: [
                    {
                        id: gId,
                        type
                    }
                ]
            });
            message.success('操作成功');
            updateTreeDataByKey(parentId);
        } catch (e) { }
    };

    const getGroupLayer = async (id: number) => {
        const params = {
            groupId: id, // -1代表目录初始化
            projectId: undefined // TODO: 待删除，删除后端报错
        };
        const { item } = await nsApiManagerList.remote(params);
        return item;
    };

    /**
     * 初始请求数据， 会根据url上的group、api来请求对应的目录；展开所以能展开的
     */
    const getTreeDataInit = async () => {
        try {
            setLoading(true);
            const type = apiId ? eNodeType.API : (!!groupId ? eNodeType.GROUP : undefined);
            const params = {
                id: type ? apiId : groupId, // -1代表目录初始化
                type
            };
            const { list } = await nsApiManagerGroupTree.remote(params);
            setTreeData(list);
            if (selectedKey) {
                setExpandedKeys(findNodeAndAncestors(list, selectedKey));
            } else {
                setExpandedKeys(expandAll(list, 1));
            }
            setLoading(false);
            return list;
        } catch (e) {
            return [];
        }
    };

    const onMoveCallback = useCallback(() => {
        return getTreeDataInit();
    }, [getTreeDataInit]);

    const onDeleteCallback = useCallback(() => {
        return getTreeDataInit();
    }, [getTreeDataInit]);

    useEffect(() => {
        setFlatTreeData(flatTree(treeData));
    }, [treeData]);

    useEffect(() => {
        if (treeWrapRef?.current) {
            setTreeWrapRefHeight(treeWrapRef.current.clientHeight);
            const resizeObserver = new ResizeObserver(entries => {
                requestAnimationFrame(() => {
                    setTreeWrapRefHeight(entries[0].contentRect.height);
                })
            });
            resizeObserver.observe(treeWrapRef.current);
            return () => {
                resizeObserver.disconnect();
            };
        }
    }, [treeWrapRef?.current]);

    useEffect(() => {
        let timeId;
        const scrollToSelectKey = () => {
            selectedKey && treeRef.current?.scrollTo({ key: selectedKey, align: 'top' } as any);
        };
        /**
         * 初始化获取目录
         */
        getTreeDataInit().then(() => {
            // 若选中元素不在视口内，则让其滚动到顶部
            const antTreeNodeSelected = treeWrapRef.current?.querySelector('.ant-tree-node-selected');
            if (!antTreeNodeSelected || !isIntersect(treeWrapRef?.current as Element, antTreeNodeSelected)) {
                timeId = setTimeout(scrollToSelectKey, 1000);
            }
        });
        return () => {
            clearTimeout(timeId);
        };
    }, []);

    /**
     *  当选择某个目录、api的时候调用，异步请求目录的数据
     */
    const directoryTreeOnSelect = useCallback((
        nodeData: nsApiManagerSearch.IItem,
        isUpdateTreeDataByKey: boolean = true
    ) => {
        // 选中当前数据展开父级节点
        if (nodeData.parentId > 0 && !expandedKeys.includes(nodeData.parentId)) {
            const parentKeys = getAllParentKey(flatTreeData, nodeData);
            setExpandedKeys(Array.from(new Set([...expandedKeys, ...parentKeys])));
        }
        if (nodeData.type === eNodeType.GROUP) {
            setGroupId(nodeData.groupId);
            setApiId(undefined);
            pushKey({ groupId: nodeData.groupId }, ['apiId', 'version']);
            if (isUpdateTreeDataByKey) {
                updateTreeDataByKey(nodeData.groupId);
            }
        } else {
            setGroupId(undefined);
            setApiId(nodeData.apiId);
            pushKey({ apiId: nodeData.apiId }, ['groupId', 'version']);
        }
    }, [flatTreeData, expandedKeys]);

    const onClickMenu = (type: string, nodeData: any) => {
        if (type === EMenuType.ADD_GROUP) {
            createGroupRef.current?.onOpenCreateGroup({
                parentGroupId: nodeData.groupId
            });
        } else if (type === EMenuType.EDIT_GROUP) {
            createGroupRef.current?.onOpenCreateGroup({
                parentGroupId: nodeData.parentId,
                groupId: nodeData.groupId,
                groupName: nodeData.groupName
            });
        } else if (type === EMenuType.DELETE_GROUP) {
            Modal.confirm({
                title: '确认删除？',
                content: '删除后不可恢复，如删除分组，分组内的API也会被删除。',
                onOk: () => deleteGroup(nodeData.groupId!, nodeData.parentId, 0)
            });
        } else if (type === EMenuType.SET_GROUP) {
            // 跳转到目录的设置页面
            router.push(ERouter.API_TEMPLATE, {
                dir: nodeData.groupId
            });
        } else if (type === EMenuType.DELETE_API) {
            Modal.confirm({
                title: '确认删除？',
                content: '删除后不可恢复，如删除分组，分组内的API也会被删除。',
                onOk: () => deleteGroup(nodeData.apiId!, nodeData.parentId, 1)
            });
        }
    };

    const renderTreeLeaf = (nodeData: nsApiManagerSearch.IItem & { title: string; key: string; }) => {
        return (
            <DirectoryTreeNode
                nodeData={nodeData}
                searchKeyword={search}
                onClickMenu={(type) => onClickMenu(type, nodeData)}
                getTreeDataInit={getTreeDataInit}
                deleteFun={deleteGroup}
                onMoveCallback={onMoveCallback}
            />
        );
    };

    const renderApiBackHeaderExtra = (curData: nsApiManagerSearch.IItem) => {
        if (groupId) {
            return (
                <Space>
                    <Button
                        type="primary"
                        onClick={goEditApi}
                    >
                        创建 API
                    </Button>
                    <ImportDropdown dir={groupId} />
                    <GroupMoreOperate
                        onClickMenu={(type) => onClickMenu(type, curData)}
                        editable={curData?.editable}
                    >
                        <Button icon={<KdevIconFont id={common_system_more02} />} />
                    </GroupMoreOperate>
                </Space>
            );
        }
        return null;
    };

    const RenderTree = useMemo(() => {
        // 当搜索时展开所有目录
        const finallyExpandedKeys = expandedKeys;
        if (loading) {
            return <div style={{ marginTop: '24', textAlign: 'center' }} className={css.empty}>
                加载中。。。
            </div>;
        }
        if (treeData.length === 0) {
            return <div style={{ marginTop: '24', textAlign: 'center' }} className={css.empty}>
                暂无数据
            </div>;
        }
        const onLoadData = async ({ key, children, type, groupId: id }: any) => {
            if (!loadLock) { return; }
            if (children && children.length > 0) {
                return;
            }
            return updateTreeDataByKey(id);
        };
        return (
            <Spin spinning={loading}>
                <Tree.DirectoryTree
                    ref={treeRef}
                    treeData={formatGroupList(treeData)}
                    className={css.repoDirectoryTree}
                    icon={false}
                    expandAction={'doubleClick'}
                    autoExpandParent={autoExpandParent}
                    titleRender={renderTreeLeaf as any}
                    onSelect={(selectedKeys, e) => {
                        if (store.isChange) {
                            message.info('请先保存当前修改');
                            return;
                        }
                        const { node } = e as any;
                        directoryTreeOnSelect(node, !(node.children && node.children.length > 0));
                    }}
                    selectedKeys={selectedKey ? [selectedKey] : []}
                    defaultExpandedKeys={['-1']}
                    loadData={onLoadData}
                    expandedKeys={finallyExpandedKeys || []}
                    onExpand={onExpand}
                    height={treeWrapRefHeight}
                />
            </Spin>
        );
    }, [expandedKeys, loading, treeData, autoExpandParent, selectedKey, treeWrapRefHeight, flatTreeData]);
    const RenderTreeWithMe = useMemo(() => {
        // 当搜索时展开所有目录
        const finallyExpandedKeys = expandedKeys;
        const onLoadData = async ({ key, children, type, groupId: id }: any) => {
            if (!loadLock) { return; }
            if (children && children.length > 0) {
                return;
            }
            return updateTreeDataByKey(id);
        };
        return (
            <Spin spinning={loading}>
                <Tree.DirectoryTree
                    ref={treeRef}
                    treeData={formatGroupList(treeDataWithMe)}
                    className={css.repoDirectoryTree}
                    icon={false}
                    expandAction={'doubleClick'}
                    autoExpandParent={true}
                    titleRender={renderTreeLeaf as any}
                    onSelect={(selectedKeys, e) => {
                        if (store.isChange) {
                            message.info('请先保存当前修改');
                            return;
                        }
                        const { node } = e as any;
                        directoryTreeOnSelect(node, !(node.children && node.children.length > 0));
                    }}
                    selectedKeys={selectedKey ? [selectedKey] : []}
                    defaultExpandedKeys={['-1']}
                    loadData={onLoadData}
                    expandedKeys={finallyExpandedKeys || []}
                    onExpand={onExpand}
                    height={treeWrapRefHeight}
                />
            </Spin>
        );
    }, [expandedKeys, loading, treeDataWithMe, autoExpandParent, selectedKey, treeWrapRefHeight, flatTreeData]);

    /**
     * FUNC_DES: 新建按钮
     */
    const renderAddBtn = (): React.ReactNode => {
        return (
            <DirMenu model={dirMenuM} type="addBtn">
                <Button
                    type="primary"
                    style={{ flexShrink: 0, marginLeft: '10px' }}
                    icon={<KdevIconFont id={'#iconadd'} />}
                />
            </DirMenu>
        );
    }

    const renderSearch = () => {
        let title: string | undefined;
        if (!selectedKey) {
            title = '如需创建一级目录，请联系：xuning、yinwenjing03、liufei13';
        }
        return (
            <div className={css.searchBox} style={{ marginBottom: 12 }}>
                <Input.Search
                    allowClear
                    style={{ flex: 1 }}
                    onSearch={handleSearch}
                    placeholder={'搜索分组/API'}
                />
                {renderAddBtn()}
            </div>
        );
    };

    const renderGroupTitle = useMemo(() => {
        const curData = flatTreeData[selectedKey!];
        return (
            <GroupTitle
                key={curData?.groupId}
                title={curData?.groupName}
                renderApiBackHeaderExtra={renderApiBackHeaderExtra(curData)}
                follow={curData?.follow}
                groupId={curData?.groupId}
            />
        );
    }, [flatTreeData, selectedKey, renderApiBackHeaderExtra]);

    const renderLeftContent = () => {
        return (
            <div className={css.leftPaneContent}>
                <div className={css.leftContent}>
                    <h1 className={css.title}>接口目录</h1>
                    {renderSearch()}
                    <Segmented
                        value={apiType}
                        style={{ marginBottom: 12 }}
                        block
                        options={[
                            {
                                label: '全部接口',
                                value: 'all'
                            },
                            {
                                label: '与我相关',
                                value: 'withMe'
                            },
                        ]}
                        onChange={async (e) => {
                            setApiType(e as 'all' | 'withMe')
                        }}
                    />
                    {apiType === 'all' &&
                        <div className={css.repoDirectoryTreeBox} ref={treeWrapRef}>
                            {RenderTree}
                        </div>
                    }
                    {apiType === 'withMe' &&
                        <div className={css.repoDirectoryTreeBox} ref={treeWrapRef}>
                            {RenderTreeWithMe}
                            {/* <RelevantToMe /> */}
                        </div>
                    }
                    <CreateGroup
                        onSubmitCallback={() => getTreeDataInit()}
                        ref={createGroupRef}
                    />
                </div>
            </div>
        );
    };

    const renderRightContent = () => {
        if (apiId) {
            return (
                <ViewApi
                    apiId={apiId!}
                    setGroupId={setGroupId}
                />
            );
        }
        return (
            <GroupDetail
                directoryTreeOnSelect={directoryTreeOnSelect}
                data={selectedKey ? flatTreeData[selectedKey]?.children! : treeData || []}
                onMoveCallback={onMoveCallback}
                renderGroupTitle={renderGroupTitle}
                onDeleteCallback={onDeleteCallback}
                search={search}
                updateFun={getTreeDataInit}
            />
        );
    };

    return (
        <div className={css.ApiManager} style={props.style}>
            <ApiMgrSplitPane
                renderLeftContent={renderLeftContent}
                renderRightContent={renderRightContent}
            />
        </div>
    );
}

function formatGroupList(list: Array<nsApiManagerSearch.IItem> = []) {
    return list.map(item => ({
        ...item,
        key: item.type === eNodeType.GROUP ? `G${item.groupId}` : `A${item.apiId}`,
        title: item.type === eNodeType.GROUP ? item.groupName : item.apiName,
        isLeaf: typeof item.isLeaf === 'boolean' ? item.isLeaf : true,
        children: formatGroupList(item.children),
    }));
}

export function getKey(item: nsApiManagerSearch.IItem) {
    if (item.type === eNodeType.API) {
        return `A${item.apiId}`;
    }
    return `G${item.groupId}`;
}
function flatTree(list: Array<nsApiManagerSearch.IItem> = []) {
    const obj: Record<string, nsApiManagerSearch.IItem> = {};
    flat(list);
    function flat(arr: Array<nsApiManagerSearch.IItem> = []) {
        arr.forEach(item => {
            obj[getKey(item)] = item;
            item.children && flat(item.children);
        });
    }
    return obj;
}

function expandAll(list: Array<nsApiManagerSearch.IItem> = [], dep?: number) {
    const keys: Array<string> = [];
    let cur = 1;
    function Loop(arr) {
        if (dep && cur > dep) {
            return;
        }
        arr.forEach(item => {
            if (item.children && item.children.length > 0) {
                keys.push(getKey(item));
                Loop(item.children);
            }
        });
        cur++;
    }
    Loop(list);
    return keys;
}

function findNodeAndAncestors(tree: Array<nsApiManagerSearch.IItem>, targetKey: string) {
    const path: Array<nsApiManagerSearch.IItem> = [];
    function dfs(node, p) {
        path.push(node);
        if (getKey(node) === targetKey) {
            return true;
        }
        if (node.children) {
            for (const child of node.children) {
                if (dfs(child, p)) {
                    return true;
                }
            }
        }
        path.pop();
        return false;
    }
    for (const node of tree) {
        if (dfs(node, path)) {
            return path.map(it => getKey(it));
        }
    }
    return [];
}

interface IFlatTreeData {
    [key: string]: nsApiManagerSearch.IItem;
}

function getAllParentKey(flatTreeData: IFlatTreeData, curNodeData: nsApiManagerSearch.IItem): string[] {
    const keys: string[] = [];
    let parentId = curNodeData?.parentId;
    while (parentId > 0) {
        const parentKey = `G${parentId}`;
        keys.push(parentKey);
        parentId = flatTreeData[parentKey]?.parentId;
    }
    return keys;
}

export { ApiManager, IQuery };
