import APageModel from '@/pages/APageModel';
import { action, observable, runInAction } from 'mobx';
import {
    nsMockManageApiManageGrpcQueryGrpcVersionListGet,
    nsMockManageApiManageGrpcQueryGrpcVersionDifGet
} from '@/remote';
import { message } from 'antd';

export interface IQuery {
}

export class ComparisonReportModel extends APageModel<IQuery> {
    @observable public activeCardKey: number = 1; // 卡片选中样式
    @observable public baseModuleId: number = 0;
    @observable public currentModuleId: number = 0;
    @observable public diffId: number = 0;
    @observable public versionDetails: any = [];
    @observable public gitUrl: string = '';
    @observable public moduleName: string = '';
    @observable public versionDifInfo: any = {};
    @observable public details: any = [];
    @observable public diffInfoLoading: boolean = false;
    @observable public timer: any = null;
    @observable public diffFirstStatus: boolean = true;

    protected getQueryFields(): any {
        return ['baseModuleId', 'currentModuleId', 'diffId'];
    }

    constructor(query: IQuery) {
        super(query);
        this.initByQueryFields(query);
        this.init();
    }

    @action
    public tabCard(key) {
        this.activeCardKey = key;
        this.changeDetail();
    }

    @action
    public init() {
        this.baseModuleId = Number(this.baseModuleId);
        this.currentModuleId = Number(this.currentModuleId);
        this.ajax_getVersionDifInfo();
        // this.ajax_getAllBranchVersion();
    }

    /**
     * 获取版本对比信息
     */
    @action
    public async ajax_getVersionDifInfo() {
        clearTimeout(this.timer);
        this.diffInfoLoading = true;
        try {
            const params = {
                baseModuleId: this.baseModuleId,
                currentModuleId: this.currentModuleId,
                difId: this.diffId
            };
            const result = await nsMockManageApiManageGrpcQueryGrpcVersionDifGet.remote(params);
            runInAction(() => {
                this.baseModuleId = result.baseModuleId;
                this.currentModuleId = result.currentModuleId;
                if (Object.keys(result).length && result.baseModuleId && result.currentModuleId) {
                    this.versionDifInfo = result;
                    this.diffInfoLoading = false;
                    this.changeDetail();
                    this.ajax_getAllBranchVersion();
                    clearTimeout(this.timer);
                } else if (!result.baseModuleId || !result.currentModuleId) {
                    if (this.diffFirstStatus) {
                        message.warn('当前项目正在编译，请耐心等待');
                    }
                    this.timer = setTimeout(() => {
                        this.diffFirstStatus = false;
                        this.ajax_getVersionDifInfo();
                    }, 2000);
                }
            });
        } catch (e) {
            runInAction(() => {
                this.diffInfoLoading = false;
            });
            clearTimeout(this.timer);
        }
    }

    /**
     * 获取版本对比分支
     */
    @action
    public async ajax_getAllBranchVersion() {
        try {
            const params = {
                artifactId: this.versionDifInfo.artifactId,
                groupId: this.versionDifInfo.groupId
            };
            const result = await nsMockManageApiManageGrpcQueryGrpcVersionListGet.remote(params);
            runInAction(() => {
                this.versionDetails = result.list || [];
            });
        } catch (e) {
        }
    }

    private changeDetail() {
        if (this.activeCardKey === 3) {
            this.details = this.versionDifInfo.modifyDetail.detailVoList;
        } else if (this.activeCardKey === 2) {
            this.details = this.versionDifInfo.deleteDetail.detailVoList;
        } else if (this.activeCardKey === 1) {
            this.details = this.versionDifInfo.addDetail.detailVoList;
        }
    }

    // @action
    // public selectBaseModuleId(baseModuleId) {
    //     this.baseModuleId = baseModuleId;
    //     this.ajax_getVersionDifInfo();
    // }
    //
    // @action
    // public selectCurrentModuleId(currentModuleId) {
    //     this.currentModuleId = currentModuleId;
    //     this.ajax_getVersionDifInfo();
    // }
}
