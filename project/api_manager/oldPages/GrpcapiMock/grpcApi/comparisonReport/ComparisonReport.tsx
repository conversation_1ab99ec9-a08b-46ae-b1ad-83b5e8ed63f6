import React from 'react';
import APage from '@/pages/APage';
import { observer } from 'mobx-react';
import { IQuery, ComparisonReportModel } from './ComparisonReportModel';
import css from './ComparisonReport.less';
import { Card, Row, Col, Avatar, Select, Tooltip, Collapse, Tag, Table, Statistic, Empty } from 'antd';
import { SwapOutlined, BranchesOutlined, InfoCircleOutlined } from '@ant-design/icons';
import Bind from 'lodash-decorators/bind';

const { Meta } = Card;
const { Panel } = Collapse;
const { Option } = Select;

@observer
export default class ComparisonReport extends APage<IQuery, ComparisonReportModel> {
    protected createModel(): ComparisonReportModel {
        return new ComparisonReportModel(this.query);
    }

    public renderColumns() {
        return [
            {
                title: '属性',
                dataIndex: 'property',
                key: 'property',
                width: 200
            },
            {
                title: '参数',
                dataIndex: 'paramName',
                key: 'paramName',
                // align: 'center'
            },
            {
                title: '请求值/返回值',
                // dataIndex: 'requestOrResponse'
                key: 'requestOrResponse',
                width: 300,
                render: (record) => {
                    return <div>{ record.requestOrResponse ? '返回值' : '请求值' }</div>;
                }
            },
            {
                title: '类型',
                dataIndex: 'paramType',
                key: 'paramType',
                width: 300,
                // align: 'center'
            }
        ];
    }

    /**
     * 卡片切换
     * @param key
     */
    @Bind
    public onTabCard(key) {
        this.model.tabCard(key);
    }

    @Bind
    public onSelectBaseModuleId(baseModuleId) {
        // this.model.selectBaseModuleId(baseModuleId);
        this.model.setFields({
            baseModuleId,
            details: [],
            diffId: 0
        });
        this.push();
        this.model.ajax_getVersionDifInfo();
    }

    @Bind
    public onSelectCurrentModuleId(currentModuleId) {
        // this.model.selectCurrentModuleId(currentModuleId);
        this.model.setFields({
            currentModuleId,
            details: [],
            diffId: 0
        });
        this.push();
        this.model.ajax_getVersionDifInfo();
    }

    public renderReportDetailList(): React.ReactNode {
        return this.model.details.length ? <Collapse>
            {
                this.model.details.map((item, index) => {
                    return <Panel
                        key={ index }
                        header={ <div style={ { display: 'flex' } }>
                            {
                                this.model.activeCardKey === 3 &&
                                <>
                                    <div style={ {
                                        display: 'flex',
                                        flexDirection: 'column'
                                    } }>
                                        <Tag color="green"
                                             style={ { marginBottom: '4px' } }>{ `+${ item.fieldAddNum }` }</Tag>
                                        <Tag color="red">{ `-${ item.fieldDeleteNum }` }</Tag>
                                    </div>
                                    <div style={ {
                                        width: '1px',
                                        background: '#000',
                                        marginRight: '10px'
                                    } } />
                                </>
                            }
                            <div>
                                <div>服务名：{ item.serviceName }</div>
                                <div>方法名：{ item.funcName }
                                    {/*<span style={ { marginLeft: '20px' } }>对应接口：{ item.method }</span>*/}
                                </div>
                            </div>
                        </div> }>
                        <Table rowClassName={ (record) => {
                            if (record.property === '新增') {
                                return 'rowGreen';
                            } else if (record.property === '删除') {
                                return 'rowRed';
                            } else {
                                return '';
                            }
                        } } columns={ this.renderColumns() } dataSource={ item.modifyFields } pagination={ false } />
                    </Panel>;
                })
            }
        </Collapse> : <Empty
            image={ Empty.PRESENTED_IMAGE_SIMPLE }
            description={ '暂无数据' }
            style={ { marginTop: '80px' } } />;
    }

    public renderContent(): React.ReactNode {
        return (
            <div className={ css.reportWrap }>
                <h2>对比版本：{ this.model.versionDifInfo.moduleName }</h2>
                <Card>
                    <Row>
                        <Col span={ 12 }>groupId：{ this.model.versionDifInfo.groupId }</Col>
                        <Col span={ 12 }>artifactId：{ this.model.versionDifInfo.artifactId }</Col>
                    </Row>
                </Card>
                <Card className={ css.reportContentWrap }>
                    <h3>对比</h3>
                    <div className={ css.reportContent }>
                        <div className={ css.version }>
                            <div>
                                基线版本
                                <Card>
                                    <Avatar
                                        shape="square"
                                        icon={ <BranchesOutlined /> } />
                                    <Select
                                        onSelect={ this.onSelectBaseModuleId }
                                        value={ this.model.baseModuleId || '' }
                                        className={ css.versionSelect }>
                                        {
                                            this.model.versionDetails.map(item => {
                                                return <Option
                                                    disabled={ item.moduleId === this.model.currentModuleId }
                                                    key={ item.moduleId }
                                                    value={ item.moduleId }>
                                                    {
                                                        item.version ?
                                                            <div>{ item.version }</div> :
                                                            <div>{ item.moduleId }</div>
                                                    }
                                                </Option>;
                                            })
                                        }
                                    </Select>
                                </Card>
                            </div>
                            <SwapOutlined className={ css.switchIcon } />
                            <div>
                                当前版本
                                <Card>
                                    <Avatar
                                        shape="square"
                                        icon={ <BranchesOutlined /> } />
                                    <Select
                                        onSelect={ this.onSelectCurrentModuleId }
                                        className={ css.versionSelect }
                                        // showSearch
                                        value={ this.model.currentModuleId || '' }>
                                        {
                                            this.model.versionDetails.map(item => {
                                                return <Option
                                                    disabled={ item.moduleId === this.model.baseModuleId }
                                                    key={ item.moduleId }
                                                    value={ item.moduleId }>
                                                    {
                                                        item.version ?
                                                            <div>{ item.version }</div> :
                                                            <div>{ item.moduleId }</div>
                                                    }
                                                </Option>;
                                            })
                                        }
                                    </Select>
                                </Card>
                            </div>
                        </div>
                        <div className={ css.reportDetail }>
                            <Card
                                onClick={ () => this.onTabCard(1) }
                                bodyStyle={ { width: '300px' } }
                                hoverable
                                className={ this.model.activeCardKey === 1 ? css.reportDetailCard : '' }>
                                <Meta
                                    avatar={
                                        <Avatar size={ 'large' }
                                                style={ { backgroundColor: '#afd9fb', color: '#000' } }>A</Avatar>
                                    }
                                    title={ <Statistic title={ <div>
                                        新增接口
                                        <Tooltip title={ '新增接口' }>
                                            <InfoCircleOutlined style={ { marginLeft: '124px' } } />
                                        </Tooltip>
                                    </div> } value={ this.model.versionDifInfo.addDetail ?
                                        this.model.versionDifInfo.addDetail.detailNum : 0 } /> }
                                />
                            </Card>
                            <Card
                                onClick={ () => this.onTabCard(2) }
                                bodyStyle={ { width: '300px' } }
                                hoverable
                                className={ this.model.activeCardKey === 2 ? css.reportDetailCard : '' }>
                                <Meta
                                    avatar={
                                        <Avatar size={ 'large' }
                                                style={ { backgroundColor: '#DCDCDC', color: '#000' } }>D</Avatar>
                                    }
                                    title={ <Statistic title={ <div>
                                        删除接口
                                        <Tooltip title={ '删除接口' }>
                                            <InfoCircleOutlined style={ { marginLeft: '124px' } } />
                                        </Tooltip>
                                    </div> } value={ this.model.versionDifInfo.deleteDetail ?
                                        this.model.versionDifInfo.deleteDetail.detailNum : 0 } /> }
                                />
                            </Card>
                            <Card
                                onClick={ () => this.onTabCard(3) }
                                bodyStyle={ { width: '300px' } }
                                hoverable
                                className={ this.model.activeCardKey === 3 ? css.reportDetailCard : '' }>
                                <Meta
                                    avatar={
                                        <Avatar
                                            size={ 'large' }
                                            style={ { backgroundColor: '#ead3a4', color: '#000' } }>M</Avatar>
                                    }
                                    title={ <Statistic title={ <div>
                                        变更接口
                                        <Tooltip title={ '变更接口' }>
                                            <InfoCircleOutlined style={ { marginLeft: '124px' } } />
                                        </Tooltip>
                                    </div> } value={ this.model.versionDifInfo.modifyDetail ?
                                        this.model.versionDifInfo.modifyDetail.detailNum : 0 } /> }
                                />
                            </Card>
                        </div>
                        {
                            Object.keys(this.model.versionDifInfo).length ?
                                this.renderReportDetailList() :
                                this.model.diffInfoLoading ?
                                    <div className={ css.spinner }>
                                        <div className={ css.rect1 } />
                                        <div className={ css.rect2 } />
                                        <div className={ css.rect3 } />
                                        <div className={ css.rect4 } />
                                        <div className={ css.rect5 } />
                                    </div> : <Empty
                                        image={ Empty.PRESENTED_IMAGE_SIMPLE }
                                        description={ '暂无数据' }
                                        style={ { marginTop: '80px' } } />
                        }
                    </div>
                </Card>
            </div>
        );
    }
}
