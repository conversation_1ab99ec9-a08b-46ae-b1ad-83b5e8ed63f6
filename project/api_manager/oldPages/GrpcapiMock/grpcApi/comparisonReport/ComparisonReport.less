@import '~@libs/utils/lib.less';

.reportWrap{
  background: #ffffff;
  padding: 20px;
  overflow: auto;
  .reportContentWrap{
    margin-top: 20px;
    .reportContent{
      padding: 10px 60px;
      .version{
        display: flex;
        justify-content: space-between;
        .switchIcon{
          font-size: 30px;
          line-height: 112px;
        }
        .versionSelect{
          width: 300px;
          margin-left: 20px;
          //height: 40px;
          //:global{
          //  .ant-select-selector{
          //    height: 40px;
          //  }
          //  .ant-select-selection-search-input{
          //    height: 40px;
          //  }
          //}
        }
        :global{
          .ant-card-body{
            display: flex;
          }
        }
      }
      .reportDetail{
        display: flex;
        justify-content: space-between;
        margin: 20px 0;
        .reportDetailCardTit{
          font-weight: normal;
          font-size: 14px;
        }
        .reportDetailCard{
          box-shadow: 2px 4px 10px 0 #ccc;
        }
      }
    }
  }
  :global{
    .rowGreen{
      //background: palegreen;
      background: #afd9fb;
    }
    .rowRed{
      background: #DCDCDC;
      //background: lightgrey;
    }
    .ant-table-tbody > tr:hover:not(.ant-table-expanded-row):not(.ant-table-row-selected) > td {
      background: inherit;
    }
  }

  .spinner {
    margin: 100px auto;
    width: 100px;
    height: 100px;
    text-align: center;
    font-size: 10px;
  }

  .spinner > div {
    background-color: #7FFFAA;
    height: 100%;
    width: 6px;
    display: inline-block;
    margin: 0 5px;

    -webkit-animation: stretchdelay 1.2s infinite ease-in-out;
    animation: stretchdelay 1.2s infinite ease-in-out;
  }

  .spinner .rect2 {
    -webkit-animation-delay: -1.1s;
    animation-delay: -1.1s;
  }

  .spinner .rect3 {
    -webkit-animation-delay: -1.0s;
    animation-delay: -1.0s;
  }

  .spinner .rect4 {
    -webkit-animation-delay: -0.9s;
    animation-delay: -0.9s;
  }

  .spinner .rect5 {
    -webkit-animation-delay: -0.8s;
    animation-delay: -0.8s;
  }

  @-webkit-keyframes stretchdelay {
    0%, 40%, 100% { -webkit-transform: scaleY(0.4) }
    20% { -webkit-transform: scaleY(1.0) }
  }

  @keyframes stretchdelay {
    0%, 40%, 100% {
      transform: scaleY(0.4);
      -webkit-transform: scaleY(0.4);
    }  20% {
         transform: scaleY(1.0);
         -webkit-transform: scaleY(1.0);
       }
  }
}
