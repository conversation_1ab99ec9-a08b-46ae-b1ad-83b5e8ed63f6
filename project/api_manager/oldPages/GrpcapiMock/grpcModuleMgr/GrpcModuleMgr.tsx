import React from 'react';
import APage from '@/pages/APage';
import { observer } from 'mobx-react';
import { Bind, debounce } from 'lodash-decorators';
import moment from 'moment';
import { Input, Table, Radio, Space, Button, Tooltip } from 'antd';
import { bindObserver } from 'libs/mvvm';
import { ERouter } from 'CONFIG';
import { GrpcModuleMgrM } from './GrpcModuleMgrM';
import css from './GrpcModuleMgr.less';
import { KdevIconFont } from '@/business/commonComponents';
import { IModuleItem, IQuery } from './configure';
import { MoveInProject } from '@/business/grpcComponents/moveInProject/MoveInProject';

const { Search } = Input;
const Input_keyWord = bindObserver(Search, 'keyWord'); // 模块搜索key
// const RadioGroup_searchType = bindObserver(Radio.Group, 'searchType');

interface ISearchTypeOptions {
    label: string;
    value: number;
}

const searchTypeOptions: ISearchTypeOptions[] = [
    {label: '全部', value: 2},
    {label: '收藏', value: 1}
];

@observer
export default class GrpcApiModule extends APage<IQuery, GrpcModuleMgrM> {
    protected createModel(): GrpcModuleMgrM {
        return new GrpcModuleMgrM(this.query);
    }

    @Bind
    private renderModuleName(record: IModuleItem): React.ReactNode {
        return (
            <a href={`${ERouter.API_MOCK_GRPCMOCK_INTERFACE}?moduleId=${record.id}&moduleSearchKeyWord=${record.moduleName}`}>
                {record.moduleName}
            </a>
        );
    }

    @Bind
    private renderOperate(record: IModuleItem): React.ReactChild {
        return (
            <Space>
                <Tooltip title="移入项目">
                    <Button
                        icon={<KdevIconFont id="#iconAddfolder"/>}
                        onClick={() => this.model.onOpenMoveInProject(record.id)}
                    />
                </Tooltip>
            </Space>
        );
    }

    @Bind
    private moduleColumns(): any[] {
        const columns: any[] = [
            {
                title: '模块',
                key: 'moduleName',
                render: this.renderModuleName
            },
            {
                title: '项目',
                dataIndex: 'gitUrl',
                key: 'gitUrl'
            },
            {
                title: '创建人',
                dataIndex: 'createUser',
                key: 'createUser',
                width: 120
            },
            {
                title: '最后更新时间',
                dataIndex: 'updateTime',
                key: 'updateTime',
                render: text => moment(text).format('YYYY-MM-DD HH:mm:ss')
            },
            {
                title: '操作',
                key: 'operate',
                width: 100,
                render: this.renderOperate
            }
        ];
        return columns;
    }

    // 根据关键字搜索模块列表
    @debounce(200)
    @Bind
    private onChangeKeyWord(): void {
        this.model.onChangePageInfo(1);
        this.push();
    }

    @Bind
    private onChangeSearchType(): void {
        this.model.onChangePageInfo(1);
        this.push();
    }

    @Bind
    private renderSearchCriteria(): React.ReactNode {
        return (
            <div className={css.searchCriteria}>
                <Input_keyWord
                    placeholder={'支持 模块名和git地址 搜索'}
                    model={this.model}
                    onChange={this.onChangeKeyWord}
                    className={css.keyWord}
                />
                {/* <RadioGroup_searchType
                    className={css.searchType}
                    model={this.model}
                    onChange={this.onChangeSearchType}
                    optionType="button"
                    options={searchTypeOptions}
                /> */}
            </div>
        );
    }

    public renderContent(): React.ReactNode {
        return (
            <div className={css.moduleMgrWrap}>
                {this.renderSearchCriteria()}
                <Table
                    columns={this.moduleColumns()}
                    dataSource={this.model.moduleList}
                    scroll={{ y: this.model.tableScrollHeight }}
                    pagination={{
                        // size: 'small',
                        showTotal: total => `共 ${total} 条`,
                        current: this.model.currentPage,
                        pageSize: this.model.pageSize,
                        showSizeChanger: true,
                        total: this.model.total,
                        onChange: this.model.onChangePageInfo
                    }}
                    rowKey="id"
                    loading={this.model.moduleListLoading}
                    bordered
                    className={css.moduleTable}
                />
                <MoveInProject model={this.model.moveInProjectM}/>
            </div>
        );
    }
}
