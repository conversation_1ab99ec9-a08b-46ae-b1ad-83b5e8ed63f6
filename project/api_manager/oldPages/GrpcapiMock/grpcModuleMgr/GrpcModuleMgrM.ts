import APageModel from '@/pages/APageModel';
import { action, observable, runInAction } from 'mobx';
import { Bind } from 'lodash-decorators';
import {
    nsMockManageApiManageRpcQueryModulesByPagePost
} from '@/remote';
import { MoveInProjectM } from '@/business/grpcComponents/moveInProject/MoveInProjectM';
import { IQuery } from './configure';

export class GrpcModuleMgrM extends APageModel<IQuery> {
    @observable public keyWord: string = ''; // 搜索module关键字
    // @observable public searchType: number = 2; // 全部/收藏
    @observable public pageSize: number = 20;
    @observable public currentPage: number = 1;
    @observable public moduleList: nsMockManageApiManageRpcQueryModulesByPagePost.IResult[] = []; // 模块列表
    @observable public total: number = 0; // 模块总条数
    @observable public tableScrollHeight: number = 300; // 表格初始化高度
    @observable public moduleListLoading: boolean = false; // 表格loading
    // @observable public departmentId: number = 0;

    public moveInProjectM: MoveInProjectM = new MoveInProjectM();

    protected getQueryFields(): Array<keyof IQuery> {
        return [
            'keyWord',
            // 'searchType'
        ];
    }

    constructor(query: IQuery) {
        super(query);
        this.initByQueryFields(query);
        this.init();
    }

    @action
    private init() {
        // this.searchType = Number(this.searchType);
        this.queryKoasGrpcModuleList();
        const layoutContentNode = document.getElementById('layoutContent');
        if (layoutContentNode) {
            this.tableScrollHeight = layoutContentNode.clientHeight - 191;
        }
    }

    /**
     * 获取模块列表
     */
    @Bind
    private async queryKoasGrpcModuleList() {
        runInAction(() => {
            this.moduleListLoading = true;
        });
        try {
            const params = {
                pageSize: this.pageSize,
                currentPage: this.currentPage,
                keyWord: this.keyWord
            };
            const result = await nsMockManageApiManageRpcQueryModulesByPagePost.remote(params);
            runInAction(() => {
                this.moduleList = result.result || [];
                this.total = result.total || 0;
                this.moduleListLoading = false;
            });
        } catch (e) {
            runInAction(() => {
                this.moduleListLoading = false;
            });
        }
    }

    /**
     * 切换每页条数
     * @param currentPage
     * @param pageSize
     */
    @action.bound
    public onChangePageInfo(currentPage: number, pageSize?: number) {
        this.currentPage = currentPage;
        if (pageSize) {
            this.pageSize = pageSize;
        }
        this.queryKoasGrpcModuleList();
    }

    // 打开移入项目弹框
    @action.bound
    public onOpenMoveInProject(moduleId: number): void {
        this.moveInProjectM.initLoading(moduleId);
    }
}
