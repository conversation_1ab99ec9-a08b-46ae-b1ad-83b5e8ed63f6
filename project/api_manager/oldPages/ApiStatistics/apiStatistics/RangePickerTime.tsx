import React from 'react';
import moment from 'moment';
import { DatePicker } from 'antd';
import { observer } from 'mobx-react';

const { RangePicker } = DatePicker;

function disabledDate(current) {
    // Can not select days before today and today
    return current && (current > moment().endOf('day'));
}

function ApiRangePickerTime(props) {
    return (
        <RangePicker
            allowClear={false}
            disabledDate={disabledDate}
            format={'YYYY-MM-DD HH:mm:ss'}
            showTime={{
                hideDisabledOptions: true,
                // defaultValue: [
                //     moment('00:00:00', 'HH:mm:ss'),
                //     moment('11:59:59', 'HH:mm:ss')
                // ],
            }}
            {...props}
        />
    );
}

export const RangePickerTime = observer(ApiRangePickerTime);
