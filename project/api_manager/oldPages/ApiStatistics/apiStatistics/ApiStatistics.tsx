import React from 'react';
import { observer } from 'mobx-react';
import Bind from 'lodash-decorators/bind';
import APage from '@/pages/APage';
import { IQuery, ApiStatisticsM } from './ApiStatisticsM';
import css from './ApiStatistics.less';
import { Button, Divider, Tabs } from 'antd';
import { LeftOutlined } from '@ant-design/icons';
import { TimeConsuming } from './timeConsuming/TimeConsuming';
import { ErrorRate } from './errorRate/ErrorRate';

const { TabPane } = Tabs;

@observer
export default class ApiStatistics extends APage<IQuery, ApiStatisticsM> {
    protected createModel(): ApiStatisticsM {
        return new ApiStatisticsM(this.query);
    }

    @Bind
    protected renderGoBack(): React.ReactNode {
        const model = this.model;
        return (
            <div className={css.title}>
                <Button
                    icon={<LeftOutlined />}
                    className={css.backBtn}
                    type={'link'}
                    onClick={model.onBackProjectList}
                />
                <Divider type={'vertical'} className={css.dividerVertical} />
                {model.moduleName}
            </div>
        );
    }

    @Bind
    private onChangeActivekey(activeKey: string): void {
        this.model.changeActiveKey(activeKey);
        this.push();
    }

    public renderContent(): React.ReactNode {
        const model = this.model;
        return (
            <div className={css.apiStatistics}>
                {this.renderGoBack()}
                <Tabs activeKey={model.activeKey} onChange={this.onChangeActivekey}>
                    <TabPane tab={'API耗时'} key={'1'}>
                        <TimeConsuming model={model.timeConsumingM} />
                    </TabPane>
                    <TabPane tab={'API错误率'} key={'2'}>
                        <ErrorRate model={model.errorRateM}/>
                    </TabPane>
                </Tabs>
            </div>
        );
    }
}
