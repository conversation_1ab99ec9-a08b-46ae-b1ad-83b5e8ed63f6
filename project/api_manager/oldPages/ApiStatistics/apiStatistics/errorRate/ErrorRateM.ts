import { AViewModel } from 'libs';
import { observable, action, runInAction } from 'mobx';
import { Bind } from 'lodash-decorators';
import { message } from 'antd';
import {
    nsMockManageKoasApiManageModuleErrorApiListGet
} from '@/remote';

export class ErrorRateM extends AViewModel {
    @observable public moduleId: number = 0;
    @observable public startTime: number = new Date().getTime() - 7 * 24 * 60 * 60 * 1000;
    @observable public endTime: number = new Date().getTime();
    @observable public key: string = '';
    @observable public pageSize: number = 20;
    @observable public currentPage: number = 1;
    @observable public total: number = 0;
    @observable public errorRateList: nsMockManageKoasApiManageModuleErrorApiListGet.IList[] = [];
    @observable public loading: boolean = false;
    @observable public tableScrollYHeight: number = 300;
    @observable public countSortOrder: string = 'descend';
    @observable public sortType: number = 2;

    @action.bound
    public init(moduleId: number): void {
        this.moduleId = moduleId;
        this.tableScrollYHeight = document.documentElement.clientHeight - 333;
    }

    @action.bound
    public changeTime(dateString) {
        this.startTime = dateString[0].valueOf();
        this.endTime = dateString[1].valueOf();
        if (this.startTime && this.endTime) {
            const diff: number = this.endTime - this.startTime;
            const day: number = Math.floor(diff / ( 24 * 3600 * 1000));
            if (day > 7) {
                message.warn('开始时间与结束时间相隔不能大于七天');
                return;
            }
            this.onSearchErrorRateListList();
        }
    }

    @action
    private setSorter(sorter): void {
        if (sorter.columnKey === 'count') {
            if (!sorter.order) {
                sorter.order = this.countSortOrder === 'ascend' ? 'descend' : 'ascend';
            }
            this.countSortOrder = sorter.order;
            this.sortType = 2;
        }
    }

    @action.bound
    public onChangeTable(pagination, filters, sorter): void {
        this.onChangePageInfo(pagination.current, pagination.pageSize);
        this.setSorter(sorter);
        this.queryErrorRateList();
    }

    @Bind
    public async queryErrorRateList() {
        runInAction(() => this.loading = true);
        try {
            const params = {
                moduleId: this.moduleId,
                key: this.key,
                pageSize: this.pageSize,
                currentPage: this.currentPage,
                startTime: this.startTime,
                endTime: this.endTime,
                sort: this.countSortOrder === 'ascend' ? 2 : 1,
                sortType: this.sortType
            };
            const result = await nsMockManageKoasApiManageModuleErrorApiListGet.remote(params);
            runInAction(() => {
                this.total = result.total || 0;
                this.errorRateList = result.list || [];
                this.loading = false;
            });
        } catch (e) {
            runInAction(() => {
                this.loading = false;
            });
        }
    }

    // 根据关键字搜索项目列表
    @action.bound
    public onSearchErrorRateListList() {
        this.onChangePageInfo();
        this.queryErrorRateList();
    }

    // 切换当前页
    @action.bound
    public onChangePageInfo(currentPage: number = 1, pageSize: number = this.pageSize) {
        this.currentPage = currentPage;
        this.pageSize = pageSize;
        // this.queryErrorRateList();
    }
}
