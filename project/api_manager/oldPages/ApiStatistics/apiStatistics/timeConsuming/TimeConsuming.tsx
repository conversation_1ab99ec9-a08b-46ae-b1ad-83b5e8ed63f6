import { AView } from 'libs';
import React from 'react';
import { observer } from 'mobx-react';
import moment from 'moment';
import { bindObserver } from '@libs/mvvm';
import { Bind } from 'lodash-decorators';
import { Input, Table } from 'antd';
import { TimeConsumingM } from './TimeConsumingM';
import css from './TimeConsuming.less';
import { RangePickerTime } from '../RangePickerTime';

const { Search } = Input;
const Input_key = bindObserver(Search, 'key');

@observer
export class TimeConsuming extends AView<TimeConsumingM> {

    @Bind
    public componentDidMount(): void {
        this.model.queryTimeConsumingList();
    }

    @Bind
    private renderApiPath(record): React.ReactNode {
        return (
            <div className={css.apiPath}>
                <span className={css.method}>{record.apiMethod}</span>
                {record.apiUrl}
            </div>
        );
    }

    @Bind
    private columns(): any[] {
        const model = this.model;
        const columns = [
            {
                title: 'API名称',
                dataIndex: 'apiName',
                key: 'apiName',
                width: 150
            },
            {
                title: 'PATH',
                // dataIndex: 'apiUrl',
                key: 'apiUrl',
                render: this.renderApiPath
            },
            {
                title: '负责人',
                dataIndex: 'owner',
                key: 'owner',
                width: 120
            },
            {
                title: '请求次数',
                dataIndex: 'count',
                key: 'count',
                width: 100,
                sorter: true,
                sortOrder: model.countSortOrder
            },
            {
                title: 'P95(ms)',
                dataIndex: 'p95',
                key: 'p95',
                width: 120,
                sorter: true,
                sortOrder: model.p95SortOrder
            }
        ];
        return columns;
    }

    @Bind
    private renderTimeConsumingList(): React.ReactNode {
        const model = this.model;
        return <Table
            columns={this.columns()}
            dataSource={model.timeConsumingList}
            rowKey={'apiUrl'}
            loading={model.loading}
            className={css.table}
            pagination={{
                size: 'small',
                pageSize: model.pageSize,
                current: model.currentPage,
                showSizeChanger: true,
                total: model.total,
                showTotal: total => `共 ${total} 条`,
                // onChange: model.onChangePageInfo,
            }}
            scroll={{
                y: model.tableScrollYHeight
            }}
            bordered
            onChange={model.onChangeTable}
        />;
    }

    @Bind
    protected renderSearchParams(): React.ReactNode {
        const model = this.model;
        return (
            <div className={css.searchParams}>
                <RangePickerTime
                    className={css.rangePickerTime}
                    value={[
                        moment(model.startTime),
                        moment(model.endTime)
                    ]}
                    onChange={model.changeTime}
                />
                搜索：
                <Input_key
                    model={model}
                    className={css.searchKey}
                    placeholder={'支持PATH搜索'}
                    onSearch={model.onSearchTimeConsumingList}
                />
            </div>
        );
    }
    public render() {
        const model = this.model;
        return (
            <div className={css.timeConsuming}>
                {this.renderSearchParams()}
                {this.renderTimeConsumingList()}
            </div>
        );
    }
}
