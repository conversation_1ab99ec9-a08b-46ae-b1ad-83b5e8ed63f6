import { AViewModel } from 'libs';
import { observable, action, runInAction } from 'mobx';
import { Bind } from 'lodash-decorators';
import { message } from 'antd';
import {
    nsMockManageKoasApiManageModuleProcTimeListGet
} from '@/remote';

export class TimeConsumingM extends AViewModel {
    @observable public moduleId: number = 0;
    @observable public startTime: number = new Date().getTime() - 7 * 24 * 60 * 60 * 1000;
    @observable public endTime: number = new Date().getTime();
    @observable public key: string = '';
    @observable public pageSize: number = 20;
    @observable public currentPage: number = 1;
    @observable public total: number = 0;
    @observable public timeConsumingList: nsMockManageKoasApiManageModuleProcTimeListGet.IList[] = [];
    @observable public loading: boolean = false;
    @observable public tableScrollYHeight: number = 300;
    @observable public countSortOrder: string = '';
    @observable public p95SortOrder: string = 'descend';
    @observable public sortType: number = 1;

    @action.bound
    public init(moduleId: number): void {
        this.moduleId = moduleId;
        this.tableScrollYHeight = document.documentElement.clientHeight - 333;
    }

    @action.bound
    public changeTime(dateString) {
        this.startTime = dateString[0].valueOf();
        this.endTime = dateString[1].valueOf();
        if (this.startTime && this.endTime) {
            const diff: number = this.endTime - this.startTime;
            const day: number = Math.floor(diff / (24 * 3600 * 1000));
            if (day > 7) {
                message.warn('开始时间与结束时间相隔不能大于七天');
                return;
            }
            this.onSearchTimeConsumingList();
        }
    }

    @action
    private setSorter(sorter): void {
        if (sorter.columnKey === 'count') {
            if (!this.countSortOrder) {
                this.countSortOrder = 'descend';
            } else {
                this.countSortOrder = this.countSortOrder === 'descend' ? 'ascend' : 'descend';
            }
            this.p95SortOrder = '';
            this.sortType = 2;
        }
        if (sorter.columnKey === 'p95') {
            if (!this.p95SortOrder) {
                this.p95SortOrder = 'descend';
            } else {
                this.p95SortOrder = this.p95SortOrder === 'descend' ? 'ascend' : 'descend';
            }
            this.countSortOrder = '';
            this.sortType = 1;
        }
    }

    // 切换当前页
    @action.bound
    public onChangePageInfo(currentPage: number = 1, pageSize: number = this.pageSize) {
        this.currentPage = currentPage;
        this.pageSize = pageSize;
        // this.queryTimeConsumingList();
    }

    @action.bound
    public onChangeTable(pagination, filters, sorter): void {
        this.onChangePageInfo(pagination.current, pagination.pageSize);
        this.setSorter(sorter);
        this.queryTimeConsumingList();
    }

    /**
     * 获取项目列表
     */
    @Bind
    public async queryTimeConsumingList() {
        runInAction(() => {
            this.loading = true;
        });
        let sort: number = 1;
        if (this.sortType === 1) {
            sort = this.p95SortOrder === 'ascend' ? 2 : 1;
        }
        if (this.sortType === 2) {
            sort = this.countSortOrder === 'ascend' ? 2 : 1;
        }
        try {
            const params = {
                moduleId: this.moduleId,
                key: this.key,
                pageSize: this.pageSize,
                currentPage: this.currentPage,
                startTime: this.startTime,
                endTime: this.endTime,
                sortType: this.sortType,
                sort
            };
            const result = await nsMockManageKoasApiManageModuleProcTimeListGet.remote(params);
            runInAction(() => {
                this.total = result.total || 0;
                this.timeConsumingList = result.list || [];
                this.loading = false;
            });
        } catch (e) {
            runInAction(() => {
                this.loading = false;
            });
        }
    }

    // 根据关键字搜索项目列表
    @action.bound
    public onSearchTimeConsumingList() {
        this.onChangePageInfo();
        this.queryTimeConsumingList();
    }
}
