import APageModel from '@/pages/APageModel';
import { action, observable } from 'mobx';
import { router } from '@libs/mvvm';
import { ERouter } from 'CONFIG';
import { TimeConsumingM } from './timeConsuming/TimeConsumingM';
import { ErrorRateM } from './errorRate/ErrorRateM';

export interface IQuery {
}

export class ApiStatisticsM extends APageModel<IQuery> {
    @observable public projectId: string = '';
    @observable public moduleId: number = 0;
    @observable public moduleName: string = '';
    @observable public activeKey: string = '1';

    public timeConsumingM = new TimeConsumingM();
    public errorRateM = new ErrorRateM();

    protected getQueryFields(): any {
        return [
            'projectId',
            'moduleId',
            'moduleName',
            'activeKey'
        ];
    }

    constructor(query: IQuery) {
        super(query);
        this.initByQueryFields(query);
        this.init();
    }

    @action
    private init() {
        this.moduleId = Number(this.moduleId) || 0;
        this.timeConsumingM.init(this.moduleId);
        this.errorRateM.init(this.moduleId);
        // this.queryModulePageList();
    }

    @action.bound
    public changeActiveKey(activekey: string): void {
        this.activeKey = activekey;
    }

    // 返回项目列表
    @action.bound
    public onBackProjectList() {
        router.push(ERouter.API_MOCK_PROJECT_MODULEMGR, {
            projectId: this.projectId
        });
    }
}
