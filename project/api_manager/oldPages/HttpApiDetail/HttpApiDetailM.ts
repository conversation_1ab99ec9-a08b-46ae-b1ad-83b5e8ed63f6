import APageModel from '@/pages/APageModel';
import { action, observable, runInAction } from 'mobx';
import { IQuery, IApiTreeData } from './component/httpApiDetail/configure';
import {
    nsMockManageKoasApiManageAutoDocGetSimpleHttpApiListGet, nsMockManageKoasApiManageAutoDocQueryDetailGet,
    nsMockManageKoasApiManageModuleGetHttModuleDepartmentGet
} from '@/remote';
import { JSONbigStringify, pushKey } from '@/index.config/tools';
import { router } from '@libs/mvvm';
import { ERouter } from 'CONFIG';
import { ApiBaseInfoM } from '@/business/apiDetail/httpApiBaseInfo/ApiBaseInfoM';
import { HttpApiRequestParamsM } from '@/business/apiDetail/httpApiRequestParams/HttpApiRequestParamsM';
import { ExampleAceM } from '@/business/apiDetail/exampleAce/ExampleAceM';
import { HttpApiResponseParamsM } from 'oldPages/HttpApiDetail/component/httpApiResponseParams/HttpApiResponseParamsM';
import { VersionRecordModel } from '@/business/versionRecord/VersionRecordModel';
import { MoveInProjectM } from '../HttpModuleMgr/component/httpMoveInProject/MoveInProjectM';
import { departmentCascader } from '@/business/global';

export class HttpApiDetailM extends APageModel<IQuery> {
    private moduleId: number = 0;
    @observable private projectId: number = 0;
    private branchName: string = '';
    private key: string = '';
    private type: number = 1;
    @observable public apiId: number = 0;
    @observable public apiUrl: string = '';
    @observable public moduleName: string = '';
    @observable public selectedKeys: string[] = [];
    @observable public paneLeftSize: string = '340px';
    @observable public apiTreeData: IApiTreeData[] = [];
    @observable public directoryTreeHeight: number = 0;

    public apiBaseInfoM = new ApiBaseInfoM();
    public httpApiRequestParamsM = new HttpApiRequestParamsM();
    public reqExampleAceM = new ExampleAceM();
    public httpApiResponseParamsM = new HttpApiResponseParamsM();
    public resExampleAceM = new ExampleAceM();
    public versionRecordM = new VersionRecordModel();
    public moveInProjectM = new MoveInProjectM();

    constructor(query: IQuery) {
        super(query);
        query && this.initByQueryFields(query);
        this.init();
    }

    protected getQueryFields(): Array<keyof IQuery> {
        return [
            'moduleId',
            'moduleName',
            'apiId',
            'type',
            'branchName',
            'key'
        ];
    }

    @action.bound
    private init(): void {
        this.moduleId = Number(this.moduleId);
        this.apiId = Number(this.apiId);
        this.selectedKeys = [this.apiId.toString()];
        this.type = Number(this.type);
        this.getSimpleHttpApiList();
        this.queryDetail();
    }

    @action.bound
    public onResizeTreeHeight(): void {
        this.directoryTreeHeight = document.body.clientHeight - 133;
    }

    @action.bound
    public moveModuleToProject(): void {
        if (this.projectId) {
            this.getHttModuleDepartment();
        } else {
            this.moveInProjectM.init(this.moduleId);
            this.moveInProjectM.onMoveInProjectCallback = this.onMoveInProjectCallback;
        }
    }

    @action.bound
    private onMoveInProjectCallback(params): void {
        this.projectId = params.projectId;
        this.getHttModuleDepartment();
    }

    // 打开版本记录
    @action.bound
    public onOpenVersionRecord() {
        this.versionRecordM.initLoading(this.apiId, 3);
    }

    @action.bound
    public onChangePaneSize(size): void {
        this.paneLeftSize = size[0];
    }

    @action.bound
    public goBackApiMgr(): void {
        router.push(ERouter.API_MOCK_HTTP_API_MGR, {
            moduleId: this.moduleId,
            moduleName: this.moduleName,
            key: this.key,
            type: this.type,
            branchName: this.branchName
        });
    }

    @action.bound
    public onChangeType(): void {
        pushKey({ type: this.type });
    }

    @action.bound
    public onSelectKeys(selectedKeys, e) {
        this.apiUrl = e.node.apiUrl;
        this.apiId = Number(selectedKeys[0]);
        this.selectedKeys = selectedKeys;
        pushKey({ apiId: this.apiId });
        this.reqExampleAceM.initData();
        this.resExampleAceM.initData();
        this.queryDetail();
    }

    @action.bound
    private getReqBodyExample(parameters): void {
        if (parameters.length) {
            parameters.forEach(item => {
                if (item.in === 'body') {
                    const example = JSONbigStringify(item.example) || '';
                    this.reqExampleAceM.onChangeExample(example);
                }
            });
        }
    }

    @action.bound
    private async getSimpleHttpApiList() {
        try {
            const params = {
                moduleId: this.moduleId,
                branchName: this.branchName
            };
            const result = await nsMockManageKoasApiManageAutoDocGetSimpleHttpApiListGet.remote(params);
            runInAction(() => {
                const apiList: IApiTreeData[] = [];
                result?.apiList?.forEach(item => {
                    if (item.apiId === Number(this.selectedKeys[0])) {
                        this.apiUrl = item.apiUrl;
                    }
                    apiList.push({
                        key: item.apiId.toString(),
                        apiId: item.apiId,
                        apiUrl: item.apiUrl
                    });
                });
                this.apiTreeData = apiList;
            });
        } catch (e) {
        }
    }

    @action.bound
    public async getHttModuleDepartment() {
        try {
            const params = {
                moduleId: this.moduleId
            };
            const result = await nsMockManageKoasApiManageModuleGetHttModuleDepartmentGet.remote(params);
            runInAction(() => {
                departmentCascader.setDepartment(result.departmentIdList, result.departmentFullName);
                const url: string = `${ERouter.API_MOCK_PROJECT_MODULEDETAIL}?apiId=${this.apiId}&moduleId=${this.moduleId}&projectId=${this.projectId}&showType=autoCopy`;
                window.open(url);
            });
        } catch (e) {
        }
    }

    // 获取详情
    @action.bound
    public async queryDetail() {
        try {
            const params = {
                id: this.apiId
            };
            const result = await nsMockManageKoasApiManageAutoDocQueryDetailGet.remote(params);
            runInAction(() => {
                this.projectId = result?.baseInfo?.projectId && result?.baseInfo?.projectId;
                result?.baseInfo && this.apiBaseInfoM.setBaseInfo(result.baseInfo);
                result?.request && this.httpApiRequestParamsM.setData(result.request);
                result?.request?.parameters && this.getReqBodyExample(result?.request?.parameters);
                result?.response && this.httpApiResponseParamsM.setData(result.response);
                if (result?.response?.body?.example) {
                    const example: string = JSONbigStringify(result?.response.body.example) || '';
                    this.resExampleAceM.onChangeExample(example);
                }
            });
        } catch (e) {
        }
    }
}
