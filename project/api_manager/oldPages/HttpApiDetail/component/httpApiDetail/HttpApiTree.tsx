import React from 'react';
import { observer } from 'mobx-react';
import { AView } from 'libs';
import { HttpApiDetailM } from '../../HttpApiDetailM';
import css from './HttpApiDetail.less';
import { Tooltip, Tree } from 'antd';
import { Bind } from 'lodash-decorators';

const { DirectoryTree } = Tree;

@observer
export class HttpApiTree extends AView<HttpApiDetailM> {

    @Bind
    public componentWillMount(): void {
        this.model.onResizeTreeHeight();
        window.addEventListener('resize', this.model.onResizeTreeHeight);
    }

    @Bind
    public componentWillUnmount() {
        window.removeEventListener('resize', this.model.onResizeTreeHeight);
    }

    @Bind
    private renderTreeTitle(nodeData): React.ReactNode {
        return (
            <div className={css.treeTitleWrap}>{nodeData.apiUrl}</div>
        );
    }

    public render(): React.ReactNode {
        const model = this.model;
        return (
            <div className={css.httpApiList}>
                <Tooltip title={model.moduleName} placement="right">
                    <div className={css.apiListTitle}>{model.moduleName}</div>
                </Tooltip>
                <DirectoryTree
                    treeData={model.apiTreeData}
                    blockNode
                    className={css.apiTree}
                    titleRender={this.renderTreeTitle}
                    selectedKeys={model.selectedKeys}
                    icon={''}
                    onSelect={model.onSelectKeys}
                    height={model.directoryTreeHeight}
                />
            </div>
        );
    }
}
