export interface IQuery {
    moduleId: number;
    moduleName: string;
    apiId: number;
    type: string;
    branchName: string;
    key: string;
}

export interface IApiTreeData {
    apiId: number;
    apiUrl: string;
    key: string;
}

interface ITypeOptions {
    label: string;
    value: string;
    disabled?: boolean;
}

export const typeOptions: ITypeOptions[] = [
    {
        label: 'API详情',
        value: '1',
    },
    {
        label: 'Mock',
        value: '2',
        disabled: true
    }
];
