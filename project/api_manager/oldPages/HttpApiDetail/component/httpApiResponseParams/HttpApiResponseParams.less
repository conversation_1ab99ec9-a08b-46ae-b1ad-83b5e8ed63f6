.httpApiResponseParamsWrap {
  //padding: 0 24px;
  margin-top: 16px;

  .collapse {
    border: 1px solid #d9d9d9;
    border-radius: 4px;

    .kdevTitle {
      font-weight: normal;
      margin-bottom: 16px;

      .kdevTitleExtra {
        flex: 1;
        display: flex;
        justify-content: space-between;
        align-items: center;

        .exampleBtn {
          margin-left: 8px;
        }
      }
    }

    .viewJsonTable {
      margin-bottom: 24px;
    }
  }

  :global {
    .ant-collapse-ghost>.ant-collapse-item>.ant-collapse-content>.ant-collapse-content-box {
      padding: 0 24px;
    }

    .ant-collapse>.ant-collapse-item>.ant-collapse-header {
      padding: 24px 24px 24px 40px;
    }
  }
}