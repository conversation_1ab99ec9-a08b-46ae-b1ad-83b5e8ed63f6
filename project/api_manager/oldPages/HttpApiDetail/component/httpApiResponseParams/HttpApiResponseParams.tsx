import React from 'react';
import { observer } from 'mobx-react';
import { AView } from 'libs';
import { HttpApiResponseParamsM } from './HttpApiResponseParamsM';
import css from './HttpApiResponseParams.less';
import { Collapse, Button } from 'antd';
import { Bind } from 'lodash-decorators';
import { KdevTitle, JsonDetail } from '@/business/commonComponents';
import { ViewJsonTable } from '@/business/apiDetail/viewJsonTable/ViewJsonTable';
import { API_MGR_TABLE_COLUMN_KEYS } from '@/business/httpApiComponents/customColumn/API_MGR_TABLE_COLUMN_KEYS';

const { Panel } = Collapse;

@observer
export class HttpApiResponseParams extends AView<HttpApiResponseParamsM> {
    protected tableTitleKey: any[] = ['value', 'name', 'type', 'required', 'description', 'valueMaxLength', 'valueMinLength', 'valueScope', 'operationsType'];

    @Bind
    private renderKdevTitleExtra(): React.ReactNode {
        const model = this.model;
        return (
            <span className={css.kdevTitleExtra}>
                <Button
                    size="small"
                    type="primary"
                    className={css.exampleBtn}
                    onClick={model.onOpenExample}
                >
                    点击查看示例
                </Button>
                <span>
                    <a onClick={() => model.isExpandAllKeys(true)}>全部展开</a>/
                    <a onClick={() => model.isExpandAllKeys(false)}>全部收起</a>
                </span>
            </span>
        );
    }

    @Bind
    private renderHeaderParams(): React.ReactNode {
        const model = this.model;
        if (model.headerList.length) {
            return (
                <>
                    <KdevTitle
                        text={'Header参数'}
                        size={'small'}
                        className={css.kdevTitle}
                    />
                    <ViewJsonTable
                        className={css.viewJsonTable}
                        model={model.headerViewJsonTableM}
                        tableTitleKey={this.tableTitleKey}
                        checkColumnKey={API_MGR_TABLE_COLUMN_KEYS.RES_HEADER_COLUMN}
                        // unifiedId={KEYS_TABLE.RES_API_HEADER_KEYS_TABLE}
                        enableResize
                    />
                </>
            );
        }
    }

    @Bind
    private renderBodyParams(): React.ReactNode {
        const model = this.model;
        if (model.bodyList.length) {
            return (
                <>
                    <KdevTitle
                        text={'Body参数'}
                        size={'small'}
                        className={css.kdevTitle}
                        extra={this.renderKdevTitleExtra()}
                    />
                    <ViewJsonTable
                        className={css.viewJsonTable}
                        model={model.bodyViewJsonTableM}
                        tableTitleKey={this.tableTitleKey}
                        checkColumnKey={API_MGR_TABLE_COLUMN_KEYS.RES_BODY_COLUMN}
                        // unifiedId={KEYS_TABLE.RES_API_BODY_KEYS_TABLE}
                        enableResize
                    />
                    <JsonDetail title="返回示例" model={model.jsonDetailM}/>
                </>
            );
        }
    }

    public render(): React.ReactNode {
        const model = this.model;
        return (
            <div className={css.httpApiResponseParamsWrap}>
                <Collapse ghost className={css.collapse} defaultActiveKey={'1'}>
                    <Panel header={'返回参数'} key={'1'}>
                        {this.renderHeaderParams()}
                        {this.renderBodyParams()}
                    </Panel>
                </Collapse>
            </div>
        );
    }
}
