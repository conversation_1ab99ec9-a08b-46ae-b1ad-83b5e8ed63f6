import { AViewModel } from '@libs/mvvm';
import { observable, action } from 'mobx';
import { ViewJsonTableM } from '@/business/apiDetail/viewJsonTable/ViewJsonTableM';
import { formatKey1, JSONbigStringify } from '@/index.config/tools';
import { JsonDetailM } from '@/business/commonComponents';
import { Bind } from 'lodash-decorators';

interface IParamsList {
    in?: string;
    key: string;
    name: string;
    example?: object;
    description: string;
    required: boolean;
    type: string;
    value: string;
    children?: IParamsList[];
}

interface IResponse {
    body: {
        type: string;
        example: object;
        model: object;
        children?: IParamsList[];
    };
    type: string;
    headers: object;
}

export class HttpApiResponseParamsM extends AViewModel {
    @observable public headerList: IParamsList[] = [];
    @observable public bodyList: IParamsList[] = [];
    @observable public resBodyExample: string = '';

    public headerViewJsonTableM = new ViewJsonTableM();
    public bodyViewJsonTableM = new ViewJsonTableM();
    public jsonDetailM = new JsonDetailM();

    @action.bound
    public setData(response: IResponse): void {
        this.initData();
        response?.body && this.formatResBody(response.body);
        response?.headers && this.formatHeaderList(response.headers);
        this.resBodyExample = response?.body?.example && JSONbigStringify(response.body.example) || '';
    }

    @action.bound
    public initData(): void {
        this.headerList = [];
        this.bodyList = [];
        this.resBodyExample = '';
        this.headerViewJsonTableM.initData();
        this.bodyViewJsonTableM.initData();
    }

    @Bind
    public onOpenExample(): void {
        this.jsonDetailM.onOpenJsonDetail(this.resBodyExample);
    }

    // 处理返回header参数
    @action.bound
    protected formatHeaderList(headerObj: object) {
        const headerList: any[] = [];
        const headerKeys = Object.keys(headerObj);
        headerKeys.length && headerKeys.forEach(item => {
            headerList.push({
                name: item,
                type: headerObj[item]?.type || '',
                required: headerObj[item]?.required || false,
                value: headerObj[item]?.value || '',
                description: headerObj[item]?.description || ''
            });
        });
        this.headerList = formatKey1(headerList)[0];
        this.headerViewJsonTableM.setListAndExpanedKeys(this.headerList);
    }

    // 处理返回body参数
    @action.bound
    private formatResBody(body): void {
        if (body.model && body.model.length) {
            body['children'] = body.model;
        }
        if (body['name'] || body['model']) {
            const [list, keys] = formatKey1([body]);
            const newExpandedKey = localStorage.getItem('resBodyExpandedKeys')?.split(',') || keys;
            this.bodyList = list;
            this.bodyViewJsonTableM.setListAndExpanedKeys(this.bodyList, newExpandedKey);
        }
        localStorage.removeItem('resBodyExpandedKeys');
    }

    @action.bound
    public isExpandAllKeys(boo: boolean): void {
        this.bodyViewJsonTableM.onExpandAllKeys(boo);
    }
}
