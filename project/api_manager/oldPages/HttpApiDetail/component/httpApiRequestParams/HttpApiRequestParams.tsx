import React from 'react';
import { observer } from 'mobx-react';
import { AView } from 'libs';
import { HttpApiRequestParamsM } from './HttpApiRequestParamsM';
import css from './HttpApiRequestParams.less';
import { Collapse, Button } from 'antd';
import { Bind } from 'lodash-decorators';
import { KdevTitle, JsonDetail } from '@/business/commonComponents';
import { ViewJsonTable } from '@/business/apiDetail/viewJsonTable/ViewJsonTable';
import { API_MGR_TABLE_COLUMN_KEYS } from '@/business/httpApiComponents/customColumn/API_MGR_TABLE_COLUMN_KEYS';

const { Panel } = Collapse;

@observer
export class HttpApiRequestParams extends AView<HttpApiRequestParamsM> {

    protected tableTitleKey: any[] = ['value', 'name', 'type', 'required', 'description', 'valueMaxLength', 'valueMinLength', 'valueScope', 'operationsType'];

    @Bind
    private renderKdevTitleExtra(type: string): React.ReactNode {
        const model = this.model;
        if (type === 'JSON') {
            return (
                <span className={ css.kdevTitleExtra }>
                    <span className={css.title}>
                        <span className={ css.dataType }>{ type }</span>
                        <Button
                            size="small"
                            type="primary"
                            className={css.exampleBtn}
                            onClick={model.onOpenExample}
                        >
                            点击查看示例
                        </Button>
                    </span>
                    <span>
                        <a onClick={ () => model.isExpandAllKeys(true) }>全部展开</a>/
                        <a onClick={ () => model.isExpandAllKeys(false) }>全部收起</a>
                    </span>
                </span>
            );
        }
        if (type === 'FORM') {
            return <span className={ css.dataType }>{ type }</span>;
        }
    }

    @Bind
    private renderHeaderParams(): React.ReactNode {
        const model = this.model;
        if (model.headerList.length) {
            return (
                <>
                    <KdevTitle
                        text={ 'Header参数' }
                        size={ 'small' }
                        className={ css.kdevTitle }
                    />
                    <ViewJsonTable
                        className={ css.viewJsonTable }
                        model={ model.headerViewJsonTableM }
                        tableTitleKey={this.tableTitleKey}
                        checkColumnKey={API_MGR_TABLE_COLUMN_KEYS.REQ_HEADER_COLUMN}
                        // unifiedId={KEYS_TABLE.REQ_API_HEADER_KEYS_TABLE}
                        enableResize
                    />
                </>
            );
        }
    }

    @Bind
    private renderBodyParams(): React.ReactNode {
        const model = this.model;
        if (model.bodyList.length) {
            return (
                <>
                    <KdevTitle
                        text={ 'Body参数' }
                        size={ 'small' }
                        className={ css.kdevTitle }
                        extra={ this.renderKdevTitleExtra('JSON') }
                    />
                    <ViewJsonTable
                        className={ css.viewJsonTable }
                        model={ model.bodyViewJsonTableM }
                        tableTitleKey={this.tableTitleKey}
                        checkColumnKey={API_MGR_TABLE_COLUMN_KEYS.REQ_BODY_COLUMN}
                        // unifiedId={KEYS_TABLE.REQ_API_BODY_KEYS_TABLE}
                        enableResize
                    />
                    <JsonDetail title="请求示例" model={model.jsonDetailM}/>
                </>
            );
        }
    }

    @Bind
    private renderFormParams(): React.ReactNode {
        const model = this.model;
        if (model.formList.length) {
            return (
                <>
                    <KdevTitle
                        text={ 'Body参数' }
                        size={ 'small' }
                        className={ css.kdevTitle }
                        extra={ this.renderKdevTitleExtra('FORM') }
                    />
                    <ViewJsonTable
                        className={ css.viewJsonTable }
                        model={ model.formViewJsonTableM }
                        tableTitleKey={this.tableTitleKey}
                        checkColumnKey={API_MGR_TABLE_COLUMN_KEYS.REQ_FORM_COLUMN}
                        // unifiedId={KEYS_TABLE.REQ_API_FORM_KEYS_TABLE}
                        enableResize
                    />
                </>
            );
        }
    }

    @Bind
    private renderQueryParams(): React.ReactNode {
        const model = this.model;
        if (model.queryList.length) {
            return (
                <>
                    <KdevTitle
                        text={ 'Query参数' }
                        size={ 'small' }
                        className={ css.kdevTitle }
                    />
                    <ViewJsonTable
                        className={ css.viewJsonTable }
                        model={ model.queryViewJsonTableM }
                        tableTitleKey={this.tableTitleKey}
                        checkColumnKey={API_MGR_TABLE_COLUMN_KEYS.REQ_QUERY_COLUMN}
                        // unifiedId={KEYS_TABLE.REQ_API_QUERY_KEYS_TABLE}
                        enableResize
                    />
                </>
            );
        }
    }

    @Bind
    private renderPathParams(): React.ReactNode {
        const model = this.model;
        if (model.pathList.length) {
            return (
                <>
                    <KdevTitle
                        text={ 'Path参数' }
                        size={ 'small' }
                        className={ css.kdevTitle }
                    />
                    <ViewJsonTable
                        className={ css.viewJsonTable }
                        model={ model.pathViewJsonTableM }
                        tableTitleKey={this.tableTitleKey}
                        checkColumnKey={API_MGR_TABLE_COLUMN_KEYS.REQ_PATH_COLUMN}
                        // unifiedId={KEYS_TABLE.REQ_API_PATH_KEYS_TABLE}
                        enableResize
                    />
                </>
            );
        }
    }

    public render(): React.ReactNode {
        const model = this.model;
        return (
            <div className={ css.httpApiRequestParamsWrap }>
                <Collapse ghost className={ css.collapse } defaultActiveKey={ '1' }>
                    <Panel header={ '请求参数' } key={ '1' }>
                        { this.renderHeaderParams() }
                        { this.renderBodyParams() }
                        { this.renderFormParams() }
                        { this.renderQueryParams() }
                        { this.renderPathParams() }
                    </Panel>
                </Collapse>
            </div>
        );
    }
}
