import React from 'react';
import { observer, Provider } from 'mobx-react';
import APage from '@/pages/APage';
import { HttpApiDetailM } from './HttpApiDetailM';
import { IQuery, typeOptions } from './component/httpApiDetail/configure';
import css from './HttpApiDetail.less';
import { HttpApiTree } from './component/httpApiDetail/HttpApiTree';
import SplitPane from 'react-split-pane';
import Pane from 'react-split-pane/lib/Pane';
import { Button, Divider, Radio, Tooltip } from 'antd';
import { LeftOutlined, FileTextOutlined } from '@ant-design/icons';
import { Bind } from 'lodash-decorators';
import { bindObserver } from '@libs/mvvm';
import { ApiBaseInfo } from '@/business/apiDetail/httpApiBaseInfo/ApiBaseInfo';
import { HttpApiRequestParams } from '@/business/apiDetail/httpApiRequestParams/HttpApiRequestParams';
import { HttpApiResponseParams } from '@/business/apiDetail/httpApiResponseParams/HttpApiResponseParams';
import { VersionRecord } from '@/business/versionRecord/VersionRecord';
import { MoveInProject } from '../../src/pages/automaticParsingApi/httpMoveInProject/MoveInProject';
import { EditJsonStore } from '@/business/editJsonTable/EditJsonStore';

const RadioGroup_type = bindObserver(Radio.Group, 'type');

@observer
export default class HttpApiDetail extends APage<IQuery, HttpApiDetailM> {
    protected createModel(): HttpApiDetailM {
        return new HttpApiDetailM(this.query);
    }

    protected editJsonStore = new EditJsonStore();

    @Bind
    private renderApiTitle(): React.ReactNode {
        const model = this.model;
        return (
            <div className={css.apiName}>
                <div className={css.apiUrl}>
                    <Button
                        icon={<LeftOutlined />}
                        className={css.backBtn}
                        type={'link'}
                        onClick={model.goBackApiMgr}
                    />
                    <Divider type={'vertical'} className={css.dividerVertical} />
                    {model.apiUrl}
                </div>
                <div>
                    <Tooltip title={'版本记录'}>
                        <Button
                            icon={<FileTextOutlined />}
                            onClick={model.onOpenVersionRecord}
                        />
                    </Tooltip>
                    <Button
                        type={'primary'}
                        className={css.createDocBtn}
                        onClick={model.moveModuleToProject}
                    >
                        生成人工编辑文档
                    </Button>
                    <VersionRecord model={model.versionRecordM} />
                    <MoveInProject model={model.moveInProjectM} />
                </div>
            </div>
        );
    }

    @Bind
    private renderMainContent(): React.ReactNode {
        const model = this.model;
        return (
            <>
                <ApiBaseInfo model={model.apiBaseInfoM} isAutoApiDoc={true} />
                <HttpApiRequestParams model={model.httpApiRequestParamsM} />
                <HttpApiResponseParams model={model.httpApiResponseParamsM} />
            </>
        );
    }

    public renderContent(): React.ReactNode {
        const model = this.model;
        return (
            <Provider editJsonStore={this.editJsonStore}>
                <div className={css.httpApiDetailWrap}>
                    <SplitPane onChange={model.onChangePaneSize}>
                        <Pane minSize={'267px'} size={model.paneLeftSize}>
                            <HttpApiTree model={model} />
                        </Pane>
                        <Pane minSize={'918px'}>
                            {this.renderApiTitle()}
                            <div className={css.apiDetailContent}>
                                {this.renderMainContent()}
                            </div>
                        </Pane>
                    </SplitPane>
                </div>
            </Provider>
        );
    }
}
