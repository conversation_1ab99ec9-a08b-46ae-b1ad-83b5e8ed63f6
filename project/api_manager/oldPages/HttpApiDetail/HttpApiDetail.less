.httpApiDetailWrap {
  background-color: #FFFFFF;

  .httpApiList {
    // overflow: auto;
    // height: 100%;

    .apiListTitle {
      padding: 16px;
      font-weight: bolder;
      font-size: 16px;
      line-height: 32px;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }

    .apiTree {
      // height: calc(100% - 88px);
      // overflow: auto;

      .treeTitleWrap {
        line-height: 32px;
        overflow: hidden;
        text-overflow: ellipsis;
      }

      :global {
        .ant-tree-node-content-wrapper {
          overflow: hidden;
        }
      }
    }
  }

  .apiName {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-right: 24px;
  }

  .apiUrl {
    padding: 16px 24px 16px 16px;
    font-weight: bolder;
    font-size: 16px;

    .backBtn {
      color: #252626;
    }

    .dividerVertical {
      height: 20px;
    }
  }

  .createDocBtn{
    margin-left: 12px;
  }

  .radioTabs {
    padding: 0 24px 16px 24px;
  }

  .apiDetailContent {
    height: calc(100% - 88px);
    overflow: auto;
    margin: 0 24px;
  }
}
