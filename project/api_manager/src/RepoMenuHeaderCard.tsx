import React from 'react';
import { kwaipilot_system_smallarrow_down } from '@kid/enterprise-icon/icon/output/icons';
import { RepoAvatar } from 'business/repo/RepoAvatar';
import { KdevIconFont } from '@/business/commonComponents';
import css from './RepoMenuHeaderCard.less';
import PopoverEllipsis from '@/business/common/PopoverEllipsis';
import { Tooltip } from 'antd';
import classnames from 'classnames';

export function RepoMenuHeaderCard(props: {
    imageUrl?: string;
    name?: string;
    toggleIcon?: boolean;
    popoverOpen?: boolean;
}) {
    return (
        <div className={classnames(css.menuTopInfoBox, props.popoverOpen ? css.hover : '')}>
            <Tooltip title={props.name} mouseEnterDelay={0.5}>
                <div className={css.menuTopInfo}>
                    <RepoAvatar
                        avatarUrl={props.imageUrl}
                        name={props.name || ''}
                        className={css.avatar}
                        size={24}
                        fontSize={12}
                    />
                    <PopoverEllipsis
                        title={props.name}
                        placement="bottomLeft"
                        getPopupContainer={() => document.body}
                    >
                        <div
                            className={css.title}
                            style={{
                                color: props.popoverOpen ? '#898A8C' : '#252626'
                            }}
                        >{props.name}</div>
                    </PopoverEllipsis>
                    {props.toggleIcon && (
                        <div className={css.toggleIcon}>
                            <KdevIconFont
                                id={kwaipilot_system_smallarrow_down}
                                style={{
                                    transform: props.popoverOpen ? 'rotate(180deg)' : 'rotate(0deg)'
                                }}
                            />
                        </div>
                    )}
                </div>
            </Tooltip>
        </div>
    );
}
