import React, { useEffect, useState } from 'react';
import { nsMockManageApiManageMainVersionDiffDetailGET } from '@/remote';
import { ApiParamKtrace } from '@/pages/httpApi/apiParamKtrace/ApiParamKtrace';
import { ParamDiffTable, IRecord } from './ParamDiffTable';
import { formatEditParam } from '@/business/httpApiComponents/editApi/apiParamsInfo/EditParamTable';
import { EParamType } from '@/business/httpApiComponents/editApi/apiParamsInfo/formatParamsTools';
import { CommentType } from '@/business/Commit/Commit';
import { Empty } from 'antd';

interface ICollapseTableProps {
    header: string;
    dataSource: nsMockManageApiManageMainVersionDiffDetailGET.IApiParam[];
    isShowChange?: boolean;
    collapseClassName?: string;
    commentType: CommentType;
}

enum EParamHeader {
    REQ_HEADER = '请求头部',
    REQ_QUERY = 'Query参数',
    REQ_BODY = '请求体',
    RES_HEADER = '返回头部',
    RES_BODY = '返回结果'
}

function KtraceTable(props: ICollapseTableProps) {
    const [dataSource, setDataSource] = useState<IRecord[]>([]);
    const [expandedRowKeys, setExpandedRowKeys] = useState<string[]>([]);
    const [showRoot, setShowRoot] = useState<boolean>(true);

    useEffect(() => {
        const { params, expandedAllkeys } = formatEditParam(props.dataSource) as {
            params: IRecord[];
            expandedAllkeys: string[];
        };
        setDataSource(params);
        setExpandedRowKeys(expandedAllkeys);
        // root类型为object、array且children为空，则不展示root
        if (props.header === EParamHeader.REQ_BODY || props.header === EParamHeader.RES_BODY) {
            if (params[0]) {
                const { T, children, isAdd, isRemove, changeItem } = params[0];
                if ((T === EParamType.object || T === EParamType.array) && !isAdd && !isRemove && !changeItem) {
                    setShowRoot(children?.length ? true : false);
                }
            }
        }
    }, [props.dataSource]);

    return (
        <ApiParamKtrace
            header={props.header}
            className={props.collapseClassName}
            defaultActiveKey={showRoot ? ['1'] : []}
        >
            <ParamDiffTable
                dataSource={showRoot ? dataSource : []}
                commentType={props.commentType}
                expandable={{
                    expandedRowKeys: expandedRowKeys,
                    onExpandedRowsChange: (expandedKeys: any[]) => {
                        setExpandedRowKeys(expandedKeys);
                    }
                }}
            />
        </ApiParamKtrace>
    );

}

export { KtraceTable };
