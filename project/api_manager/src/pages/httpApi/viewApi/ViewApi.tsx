import React, { useEffect, useRef, useState, useMemo } from 'react';
import css from './ViewApi.less';
import { Button, Tabs, Spin, Popover, message, Modal, Alert, Tooltip } from 'antd';
import {
    nsMockManageApiCompoents,
    nsMockManageApiManageMainApiBaseGET,
    nsMockManageApiManageMainRepoTemplateNewInfo,
    nsMockManageApiManageMainApiFollowPOST,
    nsMockManageApiManageMainCollectListGET,
    nsMockManageApiManageMainApiCurlExport,
    nsMockManageApiManageMainApiProgramApiBase,
    nsMockManageApiManageMainApiKtraceAutoSyncSwitch
} from '@/remote';
import { router } from '@libs/mvvm';
import { ERouter } from 'CONFIG';
import { KdevIconFont, FollowBtn, AceEditor } from '@/business/commonComponents';
import {
    common_system_arrowxia, common_system_jiantouyou, common_system_double_arrow_down,
    common_system_edit, common_system_history_record, common_system_export, common_system_copy02
} from '@kid/enterprise-icon/icon/output/icons';
import { ApiBaseInfo, ApiBaseTitle } from './apiBaseInfo/ApiBaseInfo';
import { ApiModuleBox } from '@/pages/httpApi/apiModuleBox/ApiModuleBox';
import { ViewApiParam, IViewApiParamRef } from './viewApiParam/ViewApiParam';
import { SubscribeApi } from './SubscribeApi';
import { VersionRecord, IVersionRecordRef } from './apiVersion/VersionRecord';
import { getUrlSearch, pushKey } from '@/index.config/tools';
import { ApiStatusTag } from './ApiStatusTag';
import { VersionSelect, IVersionSelectRef } from '@/business/httpApiComponents/versionSelect/VersionSelect';
import { CreateVersion } from './apiVersion/CreateVersion';
import { ApiStatusSelect } from './ApiStatusSelect';
import { ECLICK_POINT, LOCAL_STORAGE_KEYS, uploadClickEvent } from '@/index.config';
import { ApiMock } from './apiMock/ApiMock';
import { ApiTest, ApiTestM } from './apiTest';
import { useLocalStore, observer } from 'mobx-react';
import { ApiTestResult } from './apiTest/ApiTestResult';
import { GenerateCode } from './generateCode/GenerateCode';
import { CommentProvider, CommentDraw, CommentAffix, commentContentScrollContanier, overDomId } from '@/business/Commit/Commit';
import { CheckRule } from './checkRule/CheckRule';
import { CheckRuleM } from './checkRule/CheckRuleM';
import CopyToClipboard from 'react-copy-to-clipboard';
import copy from 'copy-to-clipboard';
import store from './checkRule/store';
import 'react-quill/dist/quill.snow.css';
import { KDEV_API_WITHME_SELECTEDKEYS, KDEV_API_MENU_SELECTEDKEYS } from '@/pages/ApiManager/apiManager.config';

export type TOnConfirmCallback = () => void;

interface IViewApiProps {
    apiId: number;
    extraUrlParams?: object;
    setGroupId?(groupId: number): void;
    renderConfirmBtn?(isEdit: boolean, onConfirmCallback?: TOnConfirmCallback): React.ReactNode;
    follow?: boolean;
    followCallback?(follow: nsMockManageApiManageMainApiFollowPOST.EFollow, apiId: number): void;
    // catalogId?: number;
}

function getRecentViewsApiIds(): number[] {
    const apiIds: number[] = [];
    const apiIdsStr = localStorage.getItem(LOCAL_STORAGE_KEYS.API_MGR_RECENT_VIEWS_API_IDS);
    if (apiIdsStr) {
        apiIdsStr.split(',').forEach(item => {
            apiIds.push(Number(item));
        });
    }
    return apiIds;
}

export enum EApiDocTab {
    DOCS = 'docs',
    MOCK = 'mock',
    TEST = 'test',
    CHECK_RULE = 'check_rule'
}

const apiDocsTabs = [EApiDocTab.DOCS, EApiDocTab.MOCK, EApiDocTab.TEST];
const checkRuleM = new CheckRuleM();

function clearApiIdAndRefresh() {
    pushKey({}, ['apiId', 'version', 'relateKey']);
    localStorage.removeItem(KDEV_API_WITHME_SELECTEDKEYS)
    localStorage.removeItem(KDEV_API_MENU_SELECTEDKEYS)
    location.reload();
}

function ViewApiInner(props: IViewApiProps) {
    const [version, setVersion] = useState<number>(-1); // -1 代表为最新版本
    const [apiBaseInfo, setApiBaseInfo] = useState<
        nsMockManageApiManageMainApiBaseGET.IReturn | nsMockManageApiManageMainApiProgramApiBase.IReturn
    >({} as any);
    const [confirmStatus, setConfirmStatus] = useState<boolean>(false);
    const [loading, setLoading] = useState<boolean>(true);
    const versionRecordRef = useRef<IVersionRecordRef>(null);
    const versionSelectRef = useRef<IVersionSelectRef>(null);
    const descRef = useRef<HTMLDivElement>(null);
    const [showMoreDesc, setShowMoreDesc] = useState<boolean>(false);
    const checkRuleRef = useRef<any>(null);
    const [activeKey, setActiveKey] = useState(() => {
        const apiDocTab = getUrlSearch()['apiDocTab'];
        if (apiDocsTabs.includes(apiDocTab)) {
            return apiDocTab;
        }
        return EApiDocTab.DOCS;
    });
    const apiTestM = useLocalStore(() => new ApiTestM());
    const [apiTestResultMaxHeight, setApiTestResultMaxHeight] = useState<number>();
    const apiWrapRef = useRef<HTMLDivElement>(null);
    const apiBaseInfoWrapRef = useRef<HTMLDivElement>(null);
    const viewApiParamRef = useRef<IViewApiParamRef>(null);
    const [isFollow, setIsFollow] = useState<boolean>(false);
    const [descExpand, setDescExpand] = useState<boolean>(apiBaseInfo.desc ? true : false);
    const [diffWithKtrace, setDiffWithKtrace] = useState<boolean>(false);
    const [ktrace, setKtrace] = useState<number>(0);
    const [apiBaseInfoPromise, setApiBaseInfoPromise] = useState<Promise<any>>(null);

    const [isModalOpen, setIsModalOpen] = useState({
        res: '',
        open: false
    });
    const [components, setComponents] = useState<Array<nsMockManageApiManageMainRepoTemplateNewInfo.Component>>([]);
    async function getComponents(ver?: number) {
        try {
            const {
                templateInfo
            } = await nsMockManageApiCompoents.remote({ apiId: props.apiId, version: ver === undefined ? version : ver });
            setComponents(templateInfo?.components || []);
        } catch (e) {
            // clearApiIdAndRefresh();
        }
    }
    const urlParams = getUrlSearch() as { jumpDomain?: string; jumpPathPrefix?: string; viewType?: string }


    const apiTestResult = useMemo(() => {
        if (!apiBaseInfoPromise) {
            return null
        }
        return <ApiTestResult
            model={apiTestM}
            apiTestResultMaxHeight={apiTestResultMaxHeight}
            apiBaseInfoPromise={apiBaseInfoPromise}
            apiId={props.apiId}
            style={{ display: activeKey === EApiDocTab.TEST ? 'block' : 'none' }}

        />
    }, [props.apiId, apiBaseInfoPromise, activeKey])

    // 获取API详情
    const getApiBaseInfo = async (ver?: number) => {
        setLoading(true);
        try {
            let res:
                nsMockManageApiManageMainApiBaseGET.IReturn | nsMockManageApiManageMainApiProgramApiBase.IReturn =
                {} as any;
            if (urlParams.viewType === 'projectView') {
                res = await nsMockManageApiManageMainApiProgramApiBase.remote({
                    id: props.apiId,
                    version: ver || version
                }) || {};
                setApiBaseInfo(res);
                setLoading(false);
                return res;
            } else {
                res = await nsMockManageApiManageMainApiBaseGET.remote({
                    apiId: props.apiId,
                    version: ver === undefined ? version : ver
                }) || {};
                setApiBaseInfo(res);
                setDiffWithKtrace(res.diffWithKtrace);
                apiTestM.setApiGatewayPrefix(res.apiGatewayPrefix);
                // setVersion(res.version);
                setLoading(false);
                setConfirmStatus(res.confirmStatus);
                props.setGroupId && props.setGroupId(res.groupId);
                return res;
            }
        } catch (err) {
            // clearApiIdAndRefresh();
            setLoading(false);
            return;
        }
    };

    const onOpenVersionRecord = () => {
        versionRecordRef.current?.onOpen();
        uploadClickEvent({ record_type: ECLICK_POINT.VERSION_RECORD });
    };

    const onExportCurl = async () => {
        try {
            const res = await apiTestM.exportCurl();
            setIsModalOpen({
                res,
                open: true
            });
        } catch (e) {
            message.error('导出失败');
        }
    };

    const exportCurlHandleCancel = async () => {
        setIsModalOpen({
            res: '',
            open: false
        });
    };

    const exportCurlHandleOk = async () => {
        copy(isModalOpen.res);
        message.success('已复制到剪贴板');
        exportCurlHandleCancel();
    };

    const goEditApi = () => {
        const searchParams: any = {
            groupId: apiBaseInfo.groupId || '',
            apiId: props.apiId || '',
            version: apiBaseInfo.version || -1,
            editType: 'edit',
            projectId: urlParams.projectId || undefined
        };
        props.extraUrlParams && Object.assign(searchParams, props.extraUrlParams);
        router.push(ERouter.API_MOCK_REPO_EDIT_API, searchParams);
        uploadClickEvent({ record_type: ECLICK_POINT.API_EDIT });
    };

    // 切换版本
    const onChangeVersion = (val: number) => {
        if (store.isChange) {
            message.info('请先保存当前修改');
            return;
        }
        checkRuleRef.current?.model?.getVerificationRule(props.apiId, val);
        setVersion(val);
        getApiBaseInfoByVersion(val);
        pushKey({ version: val });
    };

    const onCreateCallback = (ver: number) => {
        onChangeVersion(ver);
        versionSelectRef.current?.getVersionList();
    };

    const getApiBaseInfoByVersion = async (ver: number) => {
        getComponents(ver);
        setApiBaseInfoPromise(getApiBaseInfo(ver).then((res) => {
            apiTestM.initLoading(res);
            if (apiWrapRef.current && apiBaseInfoWrapRef.current) {
                // 112 = (Tab头部高度 + Tab头部下margin + 发送内容的高度)
                setApiTestResultMaxHeight(
                    apiWrapRef.current?.clientHeight
                    - apiBaseInfoWrapRef.current?.clientHeight
                    - 112
                );
            }
        }))
    };

    const getApiParamsList = () => {
        return viewApiParamRef.current?.getParams();
    };

    // 收藏
    const handleCollect = async (follow: boolean) => {
        try {
            await nsMockManageApiManageMainApiFollowPOST.remote({
                relationId: props.apiId,
                relationType: nsMockManageApiManageMainCollectListGET.ENodeType.API,
                follow: follow ? 1 : 0
            });
            message.success(follow ? '收藏成功' : '取消收藏成功');
            setIsFollow(follow);
            props.followCallback?.(follow ? 1 : 0, +props.apiId);
            return 'SUCCESS';
        } catch {
            return 'FAIL';
        }
    };

    // Ktrace
    const handleKtrace = async () => {
        try {
            const res = await nsMockManageApiManageMainApiKtraceAutoSyncSwitch.remoteGet({
                apiId: props.apiId
            });
            setKtrace(res as number);
        }
        catch (e) {

        }
    }

    useEffect(() => {
        setIsFollow(apiBaseInfo.follow === nsMockManageApiManageMainApiFollowPOST.EFollow.FOLLOW);
    }, [apiBaseInfo.follow]);

    useEffect(() => {
        setDescExpand(apiBaseInfo.desc ? true : false);
        setTimeout(() => {
            if (descRef.current) {
                if (descRef.current.clientHeight <= 66) {
                    setShowMoreDesc(true);
                }
            }
        }, 0);
    }, [apiBaseInfo.desc]);

    useEffect(() => {
        setIsFollow(props.follow ?? false);
    }, [props.follow]);

    useEffect(() => {
        if (props.apiId > 0) {
            getApiBaseInfoByVersion(-1);
            // 缓存访问API ID
            const apiIds = getRecentViewsApiIds();
            const index = apiIds.indexOf(props.apiId);
            if (index > -1) {
                apiIds.splice(index, 1);
            } else if (apiIds.length > 9) {
                apiIds.pop();
            }
            apiIds.unshift(props.apiId);
            localStorage.setItem(LOCAL_STORAGE_KEYS.API_MGR_RECENT_VIEWS_API_IDS, apiIds.join());
            onChangeVersion(-1);
            handleKtrace();
            apiTestM.setReadyFalse()
        }
    }, [props.apiId]);

    const nameAffix = () => {
        return (
            <div className={css.nameAffixBox}>
                <VersionSelect
                    apiId={props.apiId}
                    className={css.versionSelect}
                    bordered={false}
                    value={version > -1 ? version : apiBaseInfo.version}
                    onChange={(v) => {
                        onChangeVersion(v);
                        uploadClickEvent({ record_type: ECLICK_POINT.API_VERSION_CHANGE });
                    }}
                    ref={versionSelectRef}
                    virtual={false}
                />
                {
                    apiBaseInfo.state === '已发布'
                        ? <ApiStatusTag style={{ marginLeft: 8, fontSize: 14 }} className={css.apiStatusTag}>
                            {apiBaseInfo.state}
                        </ApiStatusTag>
                        : <ApiStatusSelect
                            value={apiBaseInfo.state}
                            apiId={apiBaseInfo.apiId}
                            version={apiBaseInfo.version}
                            onReleaseApiCallback={() => {
                                getApiBaseInfo();
                                versionSelectRef.current?.getVersionList();
                            }}
                            onChange={(val) => {
                                setApiBaseInfo({ ...apiBaseInfo, state: val });
                            }}
                            style={{ margin: '0 4px 0 8px' }}
                        />
                }
                {urlParams.viewType !== 'projectView' && <FollowBtn
                    follow={isFollow}
                    type="text"
                    onFollow={(follow: boolean, e) => {
                        e.stopPropagation();
                        uploadClickEvent({ record_type: ECLICK_POINT.API_COLLECT });
                        return handleCollect(follow);
                    }}
                    iconStyle={{ fontSize: 20 }}
                    className={css.followBtn}
                    tooltipTitle={{
                        unFollow: '收藏',
                        follow: '取消收藏'
                    }}
                />}
                <SubscribeApi
                    apiId={props.apiId}
                    subscribe={apiBaseInfo.subscription}
                />
                {ktrace === 1 && <Tooltip title="KTrace 自动采集中，可点击关闭">
                    <img className={css.ktraceIcon} src="/assets/img/tianwen.png"
                        onClick={() => renderKtraceModal()}
                    />
                </Tooltip>}
            </div>
        );
    };

    // 渲染Ktrace模态框
    const renderKtraceModal = () => {
        Modal.warning({
            title: '确认关闭 KTrace 自动采集功能',
            content: <p>关闭后，将不再自动采集KTrace，更新接口文档数据</p>,
            okText: '确认',
            closable: true,
            onOk: () => {
                closeKtrace();
            },
        })
    }

    const closeKtrace = async () => {
        await nsMockManageApiManageMainApiKtraceAutoSyncSwitch.remote({
            apiId: props.apiId,
            autoSyncKtrace: 0
        })
        setKtrace(0);
    }

    // 编辑 api button
    const renderEditBtn = () => {
        return (
            <Button
                icon={<KdevIconFont id={common_system_edit} style={{ marginRight: 4 }} />}
                type="link"
                className={css.apiOperateBtn}
                disabled={props.apiId < 1 || apiBaseInfo.state === '已发布'}
                onClick={goEditApi}
            >
                编辑
            </Button>
        );
    };

    const nameExtra = () => {
        const editPopoverContent = apiBaseInfo.state === '已发布'
            ? `当前API版本「Version ${apiBaseInfo.version}」已经发布，如需修改API请「新建版本」`
            : '';
        return (
            <div className={css.apiNameRowRight}>
                <CreateVersion
                    className={css.apiOperateBtn}
                    disabled={props.apiId < 1}
                    apiId={props.apiId}
                    version={apiBaseInfo.version}
                    onCreateCallback={onCreateCallback}
                />
                {
                    editPopoverContent
                        ? <Popover
                            content={editPopoverContent}
                            overlayClassName={apiBaseInfo.state === '已发布' ? css.editBtnTooltip : ''}
                            placement="topRight"
                        >
                            {renderEditBtn()}
                        </Popover>
                        : renderEditBtn()
                }
                <GenerateCode apiId={props.apiId} />
                <Button
                    icon={<KdevIconFont id={common_system_history_record} style={{ fontSize: 16 }} />}
                    type="link"
                    className={css.apiOperateBtn}
                    disabled={props.apiId < 1}
                    onClick={onOpenVersionRecord}
                >
                    版本记录
                </Button>
                <VersionRecord
                    ref={versionRecordRef}
                    apiId={props.apiId}
                    version={apiBaseInfo.version}
                    versionId={apiBaseInfo.versionId}
                />
                <Button
                    icon={<KdevIconFont id={common_system_export} style={{ fontSize: 16 }} />}
                    type="link"
                    className={css.apiOperateBtn}
                    onClick={onExportCurl}
                >
                    导出cURL
                </Button>
                {props.renderConfirmBtn && props.renderConfirmBtn(confirmStatus, () => {
                    setConfirmStatus(true);
                    getApiBaseInfo();
                })}
                <Modal
                    title="导出cURL"
                    open={isModalOpen.open}
                    onOk={exportCurlHandleOk}
                    okText="复制"
                    onCancel={exportCurlHandleCancel}
                >

                    <div className={css.resultBox}>
                        <CopyToClipboard text={isModalOpen.res}>
                            <Button
                                type="text"
                                icon={<KdevIconFont id={common_system_copy02} style={{ marginRight: 4 }} />}
                                style={{ padding: '5px 12px', margin: '4px 0' }}
                                onClick={() => message.success('复制成功')}
                            >
                                复制
                            </Button>
                        </CopyToClipboard>
                        <div style={{ height: 1, backgroundColor: '#D5D6D9' }} />
                        <AceEditor
                            setOptions={{
                                wrap: true, // 启用换行
                            }}
                            theme="xcode"
                            width="100%"
                            height={'200px'}
                            showPrintMargin={false}
                            readOnly
                            // onChange={this.model.changeReponse}
                            value={isModalOpen.res}
                        />
                    </div>
                </Modal>
            </div>
        );
    };

    const url = `${ERouter.API_MOCK_REPO_API_TRACE_DIFF}?apiId=${props.apiId}&apiName=${apiBaseInfo.name}&versionId=${apiBaseInfo.versionId}`;

    return (
        <div className={css.apiWrap} ref={apiWrapRef}>
            <div className={css.viewApi} id={commentContentScrollContanier}>
                <Spin spinning={loading}>
                    <div ref={apiBaseInfoWrapRef}>
                        <ApiBaseTitle
                            components={components}
                            {...apiBaseInfo}
                            nameAffix={nameAffix()}
                            nameExtra={nameExtra()}
                        />
                    </div>
                </Spin>
                <div className={css.apiTabsBox}>
                    <Tabs
                        tabBarStyle={{
                            marginBottom: 8
                        }}
                        activeKey={activeKey}
                        onChange={(val) => {
                            if (store.isChange) {
                                message.info('请先保存当前修改');
                                return;
                            }
                            setActiveKey(val);
                            pushKey({ apiDocTab: val });
                            if (val === EApiDocTab.CHECK_RULE) {
                                uploadClickEvent({ record_type: ECLICK_POINT.RULECHECK_TAB });
                                checkRuleM.getVerificationRule(props.apiId, version);
                            }
                        }}
                    >
                        <Tabs.TabPane style={{
                            padding: '0 8px'
                        }} tab="文档" key={EApiDocTab.DOCS}>
                            {diffWithKtrace &&
                                <Alert
                                    style={{
                                        marginBottom: 8
                                    }}
                                    message={<span className={css.diffTip}>
                                        <span className={css.blob}>一致性校验提示：</span>
                                        <span>
                                            「当前文档定义」与「最新 Ktrace 解析定义」不一致，
                                            <span className={css.link} onClick={() => {
                                                window.open(url, '_blank');
                                            }}>请查看 DIFF </span>
                                            并更新文档 API 定义。
                                        </span>
                                    </span>}
                                    type="warning"
                                    showIcon
                                    closable
                                />
                            }
                            <ApiBaseInfo
                                components={components}
                                {...apiBaseInfo}
                                nameAffix={nameAffix()}
                                nameExtra={nameExtra()}
                            />
                            <ApiModuleBox title={<div>
                                详细说明
                                <span style={{
                                    marginLeft: 4
                                }} onClick={() => {
                                    setDescExpand(!descExpand)
                                }}>
                                    <KdevIconFont id={descExpand ? common_system_arrowxia : common_system_jiantouyou} />
                                </span>
                            </div>}
                                className={css.apiModuleBox}
                            >
                                {descExpand ? <div
                                    className={css.apiDesc}
                                    style={showMoreDesc ? {
                                        maxHeight: 'none'
                                    } : {}}
                                >
                                    <div className="ql-editor" ref={descRef} dangerouslySetInnerHTML={{
                                        __html: apiBaseInfo.desc
                                    }}>
                                    </div>
                                </div> : null}
                                {!showMoreDesc && apiBaseInfo.desc ? <div className={css.iconWarp}>
                                    <div className={css.icon}>
                                        <KdevIconFont
                                            id={common_system_double_arrow_down}
                                            style={{ marginRight: 4 }}
                                        />
                                        <div onClick={() => {
                                            setShowMoreDesc(true)
                                        }}>展开描述</div>
                                    </div>
                                </div> : null}
                            </ApiModuleBox>
                            <CommentProvider
                                apiId={props.apiId}
                                version={apiBaseInfo.version}
                                getApiParamsList={getApiParamsList}
                            >
                                <div>
                                    <ViewApiParam
                                        apiId={props.apiId}
                                        version={version}
                                        className={css.viewApiParam}
                                        ref={viewApiParamRef}
                                        activeKey={activeKey}
                                    />
                                    <CommentDraw />
                                    <CommentAffix />
                                    {/* 占位使用填补空白区域 */}
                                    <div id={overDomId} />
                                </div>
                            </CommentProvider>
                        </Tabs.TabPane>
                        <Tabs.TabPane style={{
                            padding: '0 8px'
                        }} tab="Mock" key={EApiDocTab.MOCK} >
                            <ApiMock apiId={props.apiId}
                                jumpDomain={urlParams.jumpDomain}
                                jumpPathPrefix={urlParams.jumpPathPrefix}
                                viewType={urlParams.viewType}
                            />
                        </Tabs.TabPane>
                        <Tabs.TabPane style={{
                            padding: '0 8px'
                        }} tab="测试" key={EApiDocTab.TEST}>
                            {apiBaseInfo.apiId && <ApiTest model={apiTestM} base={apiBaseInfo} />}
                        </Tabs.TabPane>
                        <Tabs.TabPane style={{
                            padding: '0 8px'
                        }} tab="校验规则" key={EApiDocTab.CHECK_RULE}>
                            <CheckRule ref={checkRuleRef} version={version} model={checkRuleM} apiId={props.apiId} />
                        </Tabs.TabPane>
                    </Tabs>
                </div>
            </div>
            {
                apiTestResult
            }
        </div>
    );
}

export const ViewApi = observer(ViewApiInner);
