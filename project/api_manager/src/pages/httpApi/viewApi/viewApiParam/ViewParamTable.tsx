
import React, { useState, useEffect, useRef, forwardRef, useImperativeHandle, Ref } from 'react';
import { message, Button, Tooltip, Radio, Segmented } from 'antd';
import { TableProps } from 'antd/lib/table';
import { nsHttpApiInterface, nsMockManageApiManageMainApiParamTypeListGET } from '@/remote';
import css from './ViewApiParam.less';
import PopoverEllipsis from '@/business/common/PopoverEllipsis';
import {
    VirtualTable,
    VirtualTableRef,
} from '@/business/httpApiComponents/editApi/apiParamsInfo/VirtualTable';
import { EApiParam } from '@/business/httpApiComponents/editApi/apiParamsInfo/formatParamsTools';
import { ExpandComps } from '@/business/httpApiComponents/editApi/apiParamsInfo/CellContent';
import copy from 'copy-to-clipboard';
import { useExpand, useFlatTree } from '@/business/httpApiComponents/editApi/apiParamsInfo/useFlatTree';
import { FuzzySearch } from '@/business/httpApiComponents/editApi/apiParamsInfo/fuzzy-search';
import {
    CommentIconWrap,
    useCommentContainer,
    getStaff,
    CommentType
} from '@/business/Commit/Commit';
import { KdevIconFont, AceEditor } from '@/business/commonComponents';
import {
    common_system_upfold,
    common_system_underfold,
    common_system_data_form,
    common_system_pullcode,
    common_system_copy02,
    common_system_arrowxia,
    common_system_jiantouyou
} from '@kid/enterprise-icon/icon/output/icons';
import CopyToClipboard from 'react-copy-to-clipboard';

interface IRecord extends nsHttpApiInterface.IApiParam {
    key: string;
    parent?: IRecord;
    children?: IRecord[];
}

interface IViewParamTableProps extends TableProps<IRecord> {
    nameTitle?: string;
    commentType: CommentType;
    bodyJsonStr?: string;
    needShowCommon?: boolean;
    bodyType?: string;
    commentName?: string;
    isShowExpandIcon?: boolean;
}

interface IViewParamTableRef {
    handleSelectInSearch(path: string): void;
}

enum EViewTypeEnum {
    TABLE,
    CODE
}

const ViewParamTable = forwardRef((
    { nameTitle, bodyJsonStr, ...props }: IViewParamTableProps,
    ref: Ref<IViewParamTableRef>
) => {
    const [paramTypeMap, setParamTypeMap] = useState<Map<number, string>>();
    // 请求体,响应体默认以json显示
    let typeInit = [CommentType.requestBody, CommentType.responseBody].includes(props.commentType) ?
        EViewTypeEnum.CODE : EViewTypeEnum.TABLE;

    // 需要定位评论的接口,默认以table显示
    if (props.needShowCommon) {
        typeInit = EViewTypeEnum.TABLE;
    }
    const [viewType, setViewType] = useState<EViewTypeEnum>(typeInit);
    const [isExpand, setIsExpand] = useState<boolean>(false);
    const virtualTableRef = useRef<VirtualTableRef<IRecord>>();
    const {
        dataSourceMap
    } = useFlatTree({ list: props.dataSource || [] });
    // 获取参数类型列表
    const getParamTypeList = async () => {
        try {
            const res = await nsMockManageApiManageMainApiParamTypeListGET.remotePromise();
            const map = new Map<number, string>();
            res?.paramTypes?.forEach(item => {
                map.set(item.code, item.name);
            });
            setParamTypeMap(map);
        } catch { }
    };

    useEffect(() => {
        getParamTypeList();
    }, [props.dataSource]);

    useEffect(() => {
        // 请求体如果为form-data,默认以table显示
        if ([CommentType.requestBody].includes(props.commentType)) {
            setTimeout(() => {
                setViewType(props.bodyType === nsHttpApiInterface.EReqBodyFormat.JSON
                    ? EViewTypeEnum.CODE : EViewTypeEnum.TABLE);
            }, 0);
        }
    }, [props.bodyType]);

    useEffect(() => {
        if (!props.dataSource || props.dataSource.length === 0) {
            setIsExpand(false);
        } else {
            setIsExpand(true);
        }
    }, [props.dataSource]);

    const renderText = (text: string, isCopy: boolean = false) => {
        return (
            <PopoverEllipsis title={text}>
                <div
                    className={css.textStyle}
                    onDoubleClick={() => {
                        if (isCopy) {
                            copy(text);
                            message.success('复制成功');
                        }
                    }}
                >
                    {text}
                </div>
            </PopoverEllipsis>
        );
    };

    const renderValue = (text: string, record) => {
        if (record.T === 14) {
            return (
                <a href={text} download="file_name">
                    {text.split('/').pop()}
                </a>
            )
        }
        return (
            <PopoverEllipsis title={text}>
                <div
                    className={css.textStyle}
                >
                    {text}
                </div>
            </PopoverEllipsis>
        );
    };

    const {
        active,
        commentList,
        eventEmitter,
        scrollCommentToIconPos
    } = useCommentContainer();

    const {
        expanded,
        setExpanded,
        expandedWithComputer,
        handleSelectInSearch,
        handleScrollContentContainer
    } = useExpand({
        dataSourceMap: dataSourceMap,
        virtualTableRef: virtualTableRef,
        scrollCommentToIconPos
    });

    useImperativeHandle(ref, () => ({
        handleSelectInSearch
    }));

    /**
     * 评论定位
     */
    useEffect(() => {
        const handle = (path: string) => {
            handleSelectInSearch(path);
            handleScrollContentContainer(path);
        };
        eventEmitter.on(props.commentType, handle);
        return () => {
            eventEmitter.off(props.commentType, handle);
        };
    }, [handleSelectInSearch]);
    const commentMap = commentList[props.commentType];
    const commentStaff = getStaff(props.commentType);
    const newColumns = [
        {
            header: <div style={{ display: 'flex', alignItems: 'center' }}>
                <div>{nameTitle || '参数名'}</div>
                {
                    dataSourceMap.firstFloorNodeKey.length > 0 &&
                    <Tooltip title={expandedWithComputer ? '收起全部' : '展开全部'}>
                        <Button
                            type="text"
                            size="small"
                            onClick={() => setExpanded(pre => !pre)}
                            style={{ color: '#898A8C', height: 22, padding: '2px 5px' }}
                            icon={
                                <KdevIconFont
                                    id={
                                        expandedWithComputer
                                            ? common_system_upfold
                                            : common_system_underfold
                                    }
                                />
                            }
                        />
                    </Tooltip>
                }
            </div>,
            size: 480,
            minSize: 150,
            accessorKey: EApiParam.NAME,
            cell: ({ row, getValue, column: { id }, table, isHoverRow }) => {
                const record = row.original;
                const hasChildren = dataSourceMap.firstFloorNodeKey.length > 0;
                const realPath = dataSourceMap.keyToNode?.[record.key]?.path.trim();
                const comments = commentMap[realPath];
                const num = comments?.filter(it => !!it.commentId).length || 0;
                return <ExpandComps row={row} hasChildren={hasChildren}>
                    <CommentIconWrap
                        num={num}
                        hasChildren={hasChildren}
                        commentType={props.commentType}
                        realPath={realPath}
                        virtualTable={table}
                        rowKey={record.key}
                        isHoverRow={isHoverRow}
                    >
                        {renderText(record[EApiParam.NAME], true)}
                    </CommentIconWrap>
                </ExpandComps>;
            }
        },
        {
            header: '类型',
            size: 80,
            minSize: 80,
            accessorKey: EApiParam.TYPE,
            cell: ({ row, getValue, column: { id }, table }) => {
                const record = row.original;
                return (paramTypeMap && record[EApiParam.TYPE]) ? paramTypeMap?.get(record[EApiParam.TYPE]) : 'string';
            }
        },
        {
            header: '必填',
            size: 60,
            minSize: 60,
            accessorKey: EApiParam.REQUIRED,
            cell: ({ row, getValue, column: { id }, table }) => {
                const record = row.original;
                return record[EApiParam.REQUIRED] ? '是' : '否';
            }
        },
        {
            header: '说明',
            size: 148,
            minSize: 80,
            accessorKey: EApiParam.DESC,
            cell: ({ row, getValue, column: { id }, table }) => {
                const record = row.original;
                return renderText(record[EApiParam.DESC], true);
            }
        },
        {
            header: '参数值',
            size: 148,
            minSize: 80,
            accessorKey: 'value',
            cell: ({ row, getValue, column: { id }, table }) => {
                const record = row.original;
                return renderValue(record[EApiParam.VALUE], record);
            }
        }
    ];

    const rowClassName = (record) => {
        const classList: Array<string> = [`key_${record.key}`];
        const path = dataSourceMap.keyToNode?.[record.key]?.path;
        if (!!active && active === commentStaff + path) {
            classList.push('_comment_active');
        }
        return classList.join(' ') || '';
    };

    const renderTab = () => {
        return (
            <Segmented
                value={viewType}
                style={{ position: 'absolute', right: 0, top: '-44px', borderRadius: 4 }}
                options={[
                    {
                        label: '列表视图',
                        value: EViewTypeEnum.TABLE
                    },
                    {
                        label: 'JSON 视图',
                        value: EViewTypeEnum.CODE
                    },
                ]}
                onChange={(e) => {
                    setViewType(e as EViewTypeEnum)
                }}
            />
        );
    };

    const renderJsonView = () => {
        const getMode = () => {
            if ([CommentType.requestHeader,
            CommentType.requestRest,
            CommentType.requestQuery].includes(props.commentType)) {
                return 'yaml';
            } else {
                return props.bodyType === nsHttpApiInterface.EReqBodyFormat.FORM_DATA ? 'yaml' : 'json5';
            }
        };
        return (
            <div className={css.jsonViewBox} style={{ display: viewType === EViewTypeEnum.CODE ? 'block' : 'none' }}>
                <CopyToClipboard text={bodyJsonStr || ''}>
                    <Button
                        type="text"
                        icon={<KdevIconFont id={common_system_copy02} style={{ marginRight: 4 }} />}
                        style={{ padding: '5px 12px', margin: '4px 0' }}
                        onClick={() => message.success('复制成功')}
                    >
                        复制
                    </Button>
                </CopyToClipboard>
                <div style={{ height: 1, backgroundColor: '#D5D6D9' }} />
                <AceEditor
                    mode={getMode()}
                    minLines={5}
                    maxLines={50}
                    theme="xcode"
                    width="100%"
                    showPrintMargin={false}
                    readOnly
                    value={bodyJsonStr}
                />
            </div>
        );
    };
    const renderExpandIcon = () => {
        return (
            <span onClick={() => setIsExpand(!isExpand)}>
                {props.isShowExpandIcon && <KdevIconFont id={isExpand ?
                    common_system_arrowxia : common_system_jiantouyou} />}
            </span>
        );
    }

    const expandIconVisible = !props.isShowExpandIcon || isExpand

    return (
        <div style={{ ...props.style, position: 'relative' }}
            className={[css.viewParamTable, css.clearfix].join(' ')}>
            {viewType === EViewTypeEnum.TABLE && dataSourceMap.pathList.length > 10 && <FuzzySearch
                style={{ position: 'absolute', right: 170, top: '-44px' }}
                list={dataSourceMap.pathList}
                handleSelectInSearch={handleSelectInSearch}
            />}
            <div style={{ position: 'absolute', left: 0, top: '-30px', borderRadius: 4 }}>
                {props.commentName}
                <span style={{ marginLeft: 4 }}>
                    {renderExpandIcon()}
                </span>
            </div>
            {expandIconVisible && renderTab()}
            {expandIconVisible && renderJsonView()}
            {expandIconVisible && viewType === EViewTypeEnum.TABLE && (
                <div>
                    <VirtualTable<IRecord>
                        ref={virtualTableRef}
                        expanded={expanded}
                        rowClassName={rowClassName}
                        setExpanded={setExpanded as any}
                        data={props.dataSource || []}
                        columns={newColumns}
                        columnsCanResize
                        id={props.commentType}
                    />
                </div>
            )}

        </div >
    );
});

export { ViewParamTable };
export type { IViewParamTableRef, IRecord };
