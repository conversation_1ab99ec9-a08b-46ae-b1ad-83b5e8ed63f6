.insertApiDocsWrap {
    display: flex;
    flex-direction: column;
    gap: 24px;
    padding: 16px 16px 0;
    height: 100vh;
    overflow: auto;
    position: relative;

    .icon {
        color: #898A8C;
    }

    .row {
        display: flex;
        gap: 16px;
        align-items: center;

        >label {
            text-align: right;
            width: 72px;
            color: #575859;
            position: relative;
            align-self: flex-start;
            line-height: 32px;
            flex-shrink: 0;

            &[aria-required=true] span {
                position: relative;
            }

            &[aria-required=true] span::before {
                position: absolute;
                left: -12px;
                content: '*';
                color: #f5222d;
            }
        }

        &.apiName {
            position: relative;

            :global {
                .ant-input-group-addon {
                    width: 86px;
                }

                .ant-input-group-wrapper-status-error .ant-input-group-addon {
                    color: rgba(0, 0, 0, 0.85) !important;
                    border-color: #d9d9d9 !important;
                }
            }
        }

        .valueWarp {
            width: calc(100% - 72px - 16px);
            display: flex;
            justify-content: space-between;
            align-items: center;
            position: relative;

            .errorTip {
                position: absolute;
                top: 34px;
                left: 84px;
                color: #FA4E3E;
                font-family: "PingFang SC";
                font-size: 12px;
                line-height: 18px;

                &.repo,
                &.project {
                    left: 0;
                }
            }

            .icon {
                margin-left: 8px;
            }

            &.params {
                flex-direction: column;
            }

            .typeAndOtherQuery {
                width: 100%;
                display: flex;
                justify-content: space-between;
                margin-bottom: 8px;

                .radio {
                    flex: 1;
                }

                .other {
                    /* 保留空间 */
                }
            }
        }

        .input {
            width: calc(100% - 86px);
        }

        :global {
            .ant-table-thead {
                .ant-table-cell {
                    font-size: 14px !important;
                    font-weight: 400 !important;
                }
            }
        }

        &.bottom {
            position: sticky;
            background: #fff;
            padding: 16px 16px;
            bottom: 0;
            left: 0;
            right: 0;
            z-index: 99;
        }
    }

    .repoGroupSelect {
        width: 100%;
    }

    .apiTeamsBox {
        margin-top: 8px;
        width: 100%;
    }

    .projectRepoRow {
        display: flex;
        // gap: 16px;
        width: 100%;

        .projectDirSection {
            flex: 1 0 50%;
            width: 50%;
            display: flex;
            flex-direction: row;
            gap: 16px;
            flex-wrap: nowrap;

            .label {
                text-align: right;
                flex: 0 0 72px;
                width: 72px;
                color: #575859;
                position: relative;
                align-self: flex-start;
                line-height: 32px;
                flex-shrink: 0;
            }

            .content {
                flex: 0 0 calc(100% - 72px - 16px);
                display: flex;
                position: relative;
                flex-direction: row;
                flex-wrap: nowrap;
                overflow: hidden;


                .projectSelect {
                    flex: 0 0 50%;
                    width: 50%;

                    .projectSelect_ {
                        flex: 1;
                        width: 100%;
                    }

                }

                .dirSelect {
                    flex: 0 0 50%;
                    width: 50%;

                }
            }
        }

        .repoSection {
            flex: 1 0 50%;
            display: flex;
            gap: 16px;
            width: 50%;

            .label {
                text-align: right;
                width: 72px;
                color: #575859;
                position: relative;
                align-self: flex-start;
                line-height: 32px;
                flex-shrink: 0;
            }

            .content {
                position: relative;
                flex: 0 0 calc(100% - 72px - 16px);
                overflow: hidden;
                display: flex;

                .repoSelect {
                    flex: 1;
                    width: 100%;
                    overflow: hidden;
                }
            }
        }
    }

}