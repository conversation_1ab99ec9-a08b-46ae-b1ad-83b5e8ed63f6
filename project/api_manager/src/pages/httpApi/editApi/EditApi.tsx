import React, { useState, useRef, useEffect } from 'react';
import css from './EditApi.less';
import { But<PERSON>, Modal } from 'antd';
import { ApiBackHeader } from '@/business/commonComponents';
import { ERouter } from 'CONFIG';
import { getUrlSearch, pushKey } from '@/index.config/tools';
import {
    EditApiBaseInfo, IEditApiBaseInfoRef,
    EditApiParamsInfo, IEditApiParamsInfoRef,
    EditApiResponseJsonInfo, EditApiResponseJsonInfoRef
} from '@/business/httpApiComponents/editApi';
import {
    nsMockManageApiManageMainApiBaseGET,
    nsMockManageApiManageMainApiV1AddPOST,
    nsMockManageApiManageMainApiParamGET,
    nsMockManageApiCurlParse,
    nsHttpApiInterface,
    nsMockManageApiCompoents,
    nsMockManageApiManageMainRepoTemplateNewInfo,
    nsMockManageApiManageMainApiHttpCodeList,
    nsMockManageApiManageMainApiRequiredFiled,
    nsMockManageApiManageMainApiProgramApi,
    nsMockManageApiManageMainApiProgramApiBase
} from '@/remote';
import { router } from '@libs/mvvm';
import { useGetState } from 'ahooks';
import { ETabKey } from '@/pages/ApiManager/Header';
import { uploadClickEvent, ECLICK_POINT, EFMPKey } from '@/index.config';
import { initApiRequest, EBodyJsonViewTypeEnum } from '@/business/httpApiComponents/editApi/apiParamsInfo/EditApiRequest';
import { Api_manafer_Test_Sync } from '../viewApi/apiTest/ApiTest';
import * as commonFun from '@/commonFun';
import { fmp } from 'kdev-fe-common/src/shared/common/fmpReport';
import { ConsoleSqlOutlined } from 'node_modules/@ant-design/icons/lib';
interface IQuery {
    groupId: number;
    editType: TEditType;
    version: number;
    apiId?: number; // 编辑必填
    relationId?: string;
    relationType?: string;
    selectedKey?: string;
    projectCatalogId?: number; // 项目目录ID
    projectId?: number; // 项目ID
    catalogId?: number; // 目录ID
}

type TEditType = 'new' | 'curl' | 'edit' | 'copy' | 'testSync';

enum editTypeEnum {
    NEW = 'new',
    EDIT = 'edit',
    CURL = 'curl',
    COPY = 'copy',
    TEST_SYNC = 'testSync',
}

const headerTitle = {
    new: '新建',
    curl: '新建',
    edit: '编辑',
    testSync: '同步'
};

interface IEditApiContextProps {
    projectId?: number;
    groupId?: number;
}

const EditApiContext = React.createContext<IEditApiContextProps>({});
function getNewSelectedKey(selectedKey: string, apiId: number) {
    const selectedKeyArr = selectedKey.split('-');
    if (selectedKeyArr.length > 3) {
        return `${selectedKeyArr[0]}-${selectedKeyArr[1]}-${apiId}-1`;
    }
    return selectedKey;
}

function EditApi() {
    const searchParams = getUrlSearch(['apiId', 'version', 'groupId', 'projectCatalogId',
        'catalogId', 'projectId']) as IQuery;

    const [editType] = useState<TEditType>(searchParams.editType || editTypeEnum.NEW);
    const [apiBaseInfo, setApiBaseInfo] = useState<nsHttpApiInterface.IApiBaseInfo>({
        groupId: searchParams.groupId || -1,
        apiId: searchParams.apiId || -1,
        version: searchParams.version > 0 ? searchParams.version : 1,
    } as nsHttpApiInterface.IApiBaseInfo);
    const [projectId, setProjectId] = useState<number>();
    const [apiRequest, setApiRequest] = useState<nsHttpApiInterface.IApiRequest>(initApiRequest());
    const [apiRequestJson5Body, setApiRequestJson5Body] = useState<string>('');
    const [apiResponse, setApiResponse] = useState<nsHttpApiInterface.IApiResponse>();
    const [apiResponseJson5Body, setApiResponseJson5Body] = useState<string>('');
    const [apiResponseJsonExample, setApiResponseJsonExample] = useState<{
        succ: nsMockManageApiManageMainApiParamGET.JsonExample,
        fail: nsMockManageApiManageMainApiParamGET.JsonExample
    }>();
    const editApiParamsInfoRef = useRef<IEditApiParamsInfoRef>(null);
    const editApiJsonExampleRef = useRef<EditApiResponseJsonInfoRef>(null);
    const editApiBaseInfoRef = useRef<IEditApiBaseInfoRef>(null);
    const [saveLoading, setSaveLoading] = useState<boolean>(false);
    const [initParamsStr, setInitParamsStr] = useState<string>('');
    const [path, setPath] = useState<string>('');
    const [requiredList, setRequiredList] = useState<number[]>([]);

    const [components, setComponents, getComponentsState] = useGetState<
        Array<nsMockManageApiManageMainRepoTemplateNewInfo.Component>
    >([]);

    const [templateDetail, setTemplateDetail] = useState<nsMockManageApiManageMainRepoTemplateNewInfo.IReturn>({} as any); // -
    const [statusCodeList, setStatusCodeList] = useState<nsMockManageApiManageMainApiHttpCodeList.StatusCodeType[]>([]);
    let setInitParamsStrTimeout: NodeJS.Timeout | null = null;

    async function getTemplateDetail() {
        if (!searchParams.groupId) {
            return;
        }
        try {
            const res = await nsMockManageApiManageMainRepoTemplateNewInfo.remote({
                groupId: searchParams.groupId
            });
            setTemplateDetail(res);
            setComponents(res?.templateInfo?.components || []);
            return res;
        } catch (e) {
        }
    }

    async function getStatusCodeList(): Promise<nsMockManageApiManageMainApiHttpCodeList.StatusCodeType[]> {
        try {
            const res = await nsMockManageApiManageMainApiHttpCodeList.remote({});
            return res.list;
        } catch (e) {
            return [];
        }
    }

    async function getComponents() {
        try {
            const templateWrap = await nsMockManageApiCompoents.remote({
                apiId: searchParams.apiId!,
                version: searchParams.version || -1
            });
            setComponents(templateWrap.templateInfo?.components || []);
        } catch (e) { }
    }

    async function getTabIsRequired(groupId?: number) {
        if (!searchParams.groupId && !groupId) {
            return;
        }
        try {
            const templateWrap = await nsMockManageApiManageMainApiRequiredFiled.remote({
                groupId: groupId === undefined ? searchParams.groupId : groupId
            });
            setRequiredList(templateWrap || []);
        } catch (e) { }
    }

    const getParams = (): nsMockManageApiManageMainApiV1AddPOST.IParams => {
        const baseInfo = editApiBaseInfoRef.current?.getParams() as nsHttpApiInterface.IApiBaseInfo;
        // 传0表示新增，删除的话ts会报错
        if (editType === editTypeEnum.COPY) {
            baseInfo.apiId = 0;
        }
        // 如果URL中包含projectCatalogId，则添加到baseInfo中
        if (searchParams.projectCatalogId) {
            baseInfo.catalogId = baseInfo.catalogId || searchParams.projectCatalogId;
        }
        const JsonExample = editApiJsonExampleRef.current?.getParams();
        return {
            templateInfo: {
                components: getComponentsState()
            },
            baseInfo,
            ...editApiParamsInfoRef.current?.getParams(),
            successRespExample: JsonExample?.succJson,
            failedRespExample: JsonExample?.failJson
        } as any as nsMockManageApiManageMainApiV1AddPOST.IParams;
    };

    // 校验必填参数 true-未通过 false-通过
    const checkRequiredParams = (): boolean => {
        if (!editApiBaseInfoRef.current?.checkRequiredParams()) {
            return false;
        }
        if (editApiParamsInfoRef.current?.checkParams()) {
            return false;
        }
        if (editApiJsonExampleRef.current?.checkParams()) {
            return false;
        }
        return true;
    };

    const checkIsEdit = (): boolean => {
        try {
            if (JSON.stringify(getParams()) === initParamsStr) {
                return false;
            }
            return true;
        } catch {
            return false;
        }
    };

    const getApiBaseInfo = async () => {
        try {
            let res;
            if (searchParams.catalogId) {
                res = await nsMockManageApiManageMainApiProgramApiBase.remote({
                    id: searchParams.apiId!,
                    version: apiBaseInfo.version
                });
            } else {
                res = await nsMockManageApiManageMainApiBaseGET.remote({
                    apiId: apiBaseInfo.apiId,
                    version: apiBaseInfo.version
                });
            }
            let baseInfo = { ...apiBaseInfo, ...res };
            // 复制增加'副本-'前缀
            if (editType === editTypeEnum.COPY) {
                baseInfo = { ...baseInfo, name: `副本-${res.name}` };
            }
            setApiBaseInfo(baseInfo);
            fmp.upload(EFMPKey.API_EDIT);
            return res;
        } catch { }
    };

    const getApiParam = async () => {
        try {
            const res = await nsMockManageApiManageMainApiParamGET.remote({
                apiId: apiBaseInfo.apiId,
                version: apiBaseInfo.version
            });
            setApiRequestJson5Body(res.request.json5Body);
            setApiResponseJson5Body(res.response.json5Body);
            setApiRequest(res.request);
            setApiResponse(res.response);
            setApiResponseJsonExample({
                succ: res.successRespExample,
                fail: res.failedRespExample
            });
            return res;
        } catch { }
    };

    const setInitDataWhenCreate = async (template) => {
        try {
            if (template) {
                setApiBaseInfo(Object.assign(template.baseInfo, apiBaseInfo));
                setApiRequest(template.request);
                setApiResponse(template.response);
                setComponents(template.templateInfo.components);
            }
            setInitParamsStrTimeout = setTimeout(() => {
                setInitParamsStr(JSON.stringify(getParams()));
            });
        } catch { }
    };

    const getCurlParse = async (curlParseParams: nsMockManageApiCurlParse.IParams) => {
        try {
            const res = await nsMockManageApiCurlParse.remote(curlParseParams);
            setApiBaseInfo(Object.assign(res.baseInfo, apiBaseInfo));
            setApiRequest(res.request);
            setApiResponse(res.response);
            setComponents(templateDetail.templateInfo.components);
            setInitParamsStrTimeout = setTimeout(() => {
                setInitParamsStr(JSON.stringify(getParams()));
            });
        } catch { }
    };

    const onSaveApi = async () => {
        const apiParams = editApiParamsInfoRef.current!.getParams();
        if (apiParams.request.bodyJsonViewType === EBodyJsonViewTypeEnum.FORMAT) {
            const formattedRequestJson = await commonFun.formatConvertToJson5([apiParams.request.body[0]]);
            editApiParamsInfoRef.current?.setRequestJson(formattedRequestJson.data);
        }
        if (apiParams.response.bodyJsonViewType === EBodyJsonViewTypeEnum.FORMAT) {
            const formattedResponseJson = await commonFun.formatConvertToJson5([apiParams.response.body[0]]);
            editApiParamsInfoRef.current?.setResponseJson(formattedResponseJson.data);
        }
        // json格式下同步json字符串到格式化
        const replaceCheck = await editApiParamsInfoRef.current?.replaceJsonData();
        if (!checkRequiredParams() || !replaceCheck) {
            return;
        }
        setSaveLoading(true);
        try {
            const params = getParams();
            let res;
            // 如果URL中包含projectCatalogId
            if (searchParams.projectCatalogId) {
                // 确保baseInfo中包含catalogId
                if (!params.baseInfo.catalogId) {
                    params.baseInfo.catalogId = searchParams.projectCatalogId;
                }

                // 如果是编辑已有接口（apiId > 0）
                if (params.baseInfo.apiId > 0) {
                    res = await nsMockManageApiManageMainApiProgramApi.update(params);
                    onBack(params.baseInfo.apiId);
                } else {
                    // 新建接口
                    delete params.baseInfo.apiId;
                    res = await nsMockManageApiManageMainApiProgramApi.create(params);
                    onBack(res);
                }
            } else {
                res = await nsMockManageApiManageMainApiV1AddPOST.remote(params);
                if (res.apiId) {
                    apiBaseInfo.apiId = res.apiId;
                }
                apiBaseInfo.groupId = params.baseInfo.groupId;
                onBack();
            }
            setSaveLoading(false);
        } catch {
            setSaveLoading(false);
        }
        uploadClickEvent({ record_type: ECLICK_POINT.API_EDIT_SAVE });
    };

    const onBack = (res?: any) => {
        // const baseInfo = editApiBaseInfoRef.current?.getParams()!;
        const params = {};
        let base = ERouter.API_MOCK_REPO_MGR;

        if (searchParams.projectCatalogId) {
            router.routerProps.history.block(null);
            router.push(ERouter.API_MOCK_REPO_MGR_PROJECT_INFO, {
                projectCatalogId: searchParams.projectCatalogId,
                viewType: 'projectView',
                projectId: searchParams.projectId,
                // selectedKey: localStorage.getItem('REJECT_SELECT') || ''
                selectedKey: `API-${res}`
            });
            return
        }
        if (searchParams.relationId && searchParams.relationType) {
            base = ERouter.API_MOCK_REPO_MR_CHECK_API;
            Object.assign(params, {
                relationId: searchParams.relationId,
                relationType: searchParams.relationType
            });
        } else if (searchParams.selectedKey) {
            Object.assign(params, {
                activeKey: ETabKey.RELATE,
                selectedKey: searchParams.selectedKey
            });
        } else {
            Object.assign(params, {
                activeKey: ETabKey.GROUP
            });
        }
        // 有API ID则返回详情页面并定位到该API，没有则返回到对应的目录位置
        if (apiBaseInfo.apiId > 0) {
            Object.assign(params, {
                apiId: apiBaseInfo.apiId,
                version: apiBaseInfo.version
            });
            if (searchParams.selectedKey) {
                Object.assign(params, {
                    activeKey: ETabKey.RELATE,
                    selectedKey: getNewSelectedKey(searchParams.selectedKey, apiBaseInfo.apiId)
                });
            }
        } else if (!searchParams.selectedKey) {
            Object.assign(params, {
                groupId: ''
            });
        }
        // 决定跳转不阻止
        // tslint:disable-next-line:no-empty
        router.routerProps.history.block(() => { });
        router.push(base, params);
    };

    // 返回未保存提示
    const onBackConfirm = () => {
        if (checkIsEdit()) {
            Modal.confirm({
                title: '有修改尚未保存',
                content: '确定放弃修改并返回吗？',
                onOk: onBack
            });
        } else {
            onBack();
        }
    };

    const setParentPathFun = (val: string) => {
        setPath(val);
    };

    useEffect(() => {
        apiBaseInfo.projectId && setProjectId(apiBaseInfo.projectId);
    }, [apiBaseInfo]);

    // 刷新未保存提示
    window.onbeforeunload = () => {
        return checkIsEdit() ? true : null;
    };

    // 跳转未保存提示
    useEffect(() => {
        router.routerProps.history.block((nextLocation) => {
            if (checkIsEdit() && nextLocation.pathname !== ERouter.API_MOCK_REPO_EDIT_API) {
                return '有修改尚未保存，确定放弃修改并返回吗？';
            }
        });
        return () => {
            // tslint:disable-next-line:no-empty
            router.routerProps.history.block(() => { });
        };
    }, [initParamsStr]);

    useEffect(() => {
        (async () => {
            const template = await getTemplateDetail();
            setStatusCodeList(await getStatusCodeList());
            // 请求各个参数是否必填
            getTabIsRequired();
            if (editType === editTypeEnum.NEW) {
                setInitDataWhenCreate(template);
                setApiResponseJsonExample({
                    succ: {
                        exampleJson: '',
                        statusCode: 200
                    },
                    fail: {
                        exampleJson: '',
                        statusCode: 500
                    }
                });
            } else if (editType === editTypeEnum.CURL) {
                const curlUploadStr = sessionStorage.getItem('api_mgr_curl_upload');
                if (curlUploadStr) {
                    try {
                        const curlUploadObj = JSON.parse(curlUploadStr);
                        if (curlUploadObj.baseInfo) {
                            setApiBaseInfo(Object.assign(curlUploadObj.baseInfo, { groupId: searchParams.groupId }));
                            setApiRequest(curlUploadObj.request);
                            setApiRequestJson5Body(curlUploadObj.request.json5Body);
                            setApiResponse(curlUploadObj.response);
                            setInitParamsStrTimeout = setTimeout(() => {
                                setInitParamsStr(JSON.stringify(getParams()));
                            });
                        } else if (curlUploadObj.groupId && curlUploadObj.curl) {
                            getCurlParse({
                                groupId: curlUploadObj.groupId,
                                curl: curlUploadObj.curl
                            });
                        }
                    } catch { }
                }
            } else if (editType === editTypeEnum.EDIT || editType === editTypeEnum.COPY) { // 编辑和复制需要回显
                if (apiBaseInfo.apiId > 0) {
                    await Promise.all([
                        getComponents(),
                        getApiBaseInfo(),
                        getApiParam()
                    ]);
                    setInitParamsStrTimeout = setTimeout(() => {
                        setInitParamsStr(JSON.stringify(getParams()));
                    });
                }
            } else if (editType === editTypeEnum.TEST_SYNC) {
                const syncStr = sessionStorage.getItem(Api_manafer_Test_Sync) || '{}';
                const syncObj = JSON.parse(syncStr);
                const synComponents = syncObj.templateDto.templateInfo.components;
                setComponents(synComponents);
                setApiBaseInfo(syncObj.baseInfo);
                setApiRequestJson5Body(syncObj.request.json5Body);
                setApiResponseJson5Body(syncObj.response.json5Body);
                setApiRequest(syncObj.request);
                setApiResponse(syncObj.response);
                setInitParamsStrTimeout = setTimeout(() => {
                    setInitParamsStr(JSON.stringify(getParams()));
                });
            }
        })();
        // 项目视图下先写死Components
        if (searchParams.projectCatalogId) {
            setComponents([{ 'id': 'team', 'enable': 1, 'name': 'Team任务', 'required': 0, 'value': [], 'component': 'team', 'params': [] }, { 'id': 'priority', 'enable': 0, 'name': '优先级', 'required': 0, 'value': '', 'component': 'select', 'params': ['P0', 'P1', 'P2', 'P3', 'P4'] }, { 'id': 'read_or_write', 'enable': 0, 'name': '读写属性', 'required': 0, 'value': '读', 'component': 'select', 'params': ['读', '写'] }]);
        }
        return () => {
            window.onbeforeunload = null;
            setInitParamsStrTimeout && clearTimeout(setInitParamsStrTimeout);
        };
    }, []);

    return (
        <EditApiContext.Provider value={{ projectId, groupId: searchParams.groupId }}>
            <div className={css.editApi}>
                <ApiBackHeader
                    title={headerTitle[editType]}
                    onBackClick={onBackConfirm}
                />
                <div className={css.main}>
                    <EditApiBaseInfo
                        className={css.editApiBaseInfo}
                        apiId={apiBaseInfo.apiId}
                        apiBaseInfo={apiBaseInfo}
                        ref={editApiBaseInfoRef}
                        components={components}
                        setComponents={setComponents}
                        onChangeProjectCallback={(id) => {
                            setProjectId(id);
                        }}
                        setParentPathFun={setParentPathFun}
                        requiredList={requiredList}
                        getTabIsRequired={getTabIsRequired}
                        projectId={searchParams.projectId}
                    />
                    <EditApiParamsInfo
                        className={css.editApiParamsInfo}
                        requestParams={apiRequest}
                        responseParams={apiResponse}
                        setApiResponse={setApiResponse}
                        ref={editApiParamsInfoRef}
                        path={path}
                        requestJson5Body={apiRequestJson5Body}
                        responseJson5Body={apiResponseJson5Body}
                    />
                    <EditApiResponseJsonInfo
                        className={css.editApiParamsInfo}
                        succJson={apiResponseJsonExample?.succ}
                        failJson={apiResponseJsonExample?.fail}
                        apiResponse={apiResponse}
                        statusCodeList={statusCodeList}
                        ref={editApiJsonExampleRef}
                    />
                </div>
                <div className={css.footer}>
                    <Button
                        type="primary"
                        onClick={onSaveApi}
                        loading={saveLoading}
                    >
                        保存
                    </Button>
                    <Button onClick={onBackConfirm}>取消</Button>
                </div>
            </div>
        </EditApiContext.Provider>
    );
}

export { EditApi, editTypeEnum, EditApiContext, IEditApiContextProps };
