import {
    nsMockManageApiManageMainVersionListGET,
    nsMockManageApiManageMainApiKtraceParamDiffGet,
    nsMockManageApiManageMainApiKtraceVersionApplyPost,
    nsMockManageApiManageMainApiKtraceAutoSyncSwitch
} from '@/remote';
import { action, observable, runInAction } from 'mobx';
import { getUrlSearch, pushKey } from '@/index.config/tools';
import { AViewModel, router } from '@libs/mvvm';
import { message } from 'antd';
import { ERouter } from 'CONFIG';

export class apiTraceDiffModuleM extends AViewModel {
    @observable public versionList: (nsMockManageApiManageMainVersionListGET.IVersionItem & { expend: boolean })[] = [];
    @observable public diffVersionId: number | undefined = undefined;
    @observable public isSyncModalOpen: boolean = false;
    @observable public checked: boolean = false;
    @observable public autoSyncKtrace: number = 0;
    @observable public env: string = '';
    @observable public host: string = '';
    @observable public landId: string = '';
    @observable public diffInfo: nsMockManageApiManageMainApiKtraceParamDiffGet.IReturn | undefined = undefined;
    @observable public leftRequestHeader: any[] = [];
    @observable public rightRequestHeader: any[] = [];
    @observable public leftResponseHeader: any[] = [];
    @observable public rightResponseHeader: any[] = [];
    @observable public leftRequestDiff: any[] = [];
    @observable public rightRequestDiff: any[] = [];
    @observable public leftResponseDiff: any[] = [];
    @observable public rightResponseDiff: any[] = [];
    @observable public requestHeaderOpen: boolean = true;
    @observable public queryDiffOpen: boolean = true;
    @observable public responseHeaderOpen: boolean = true;
    @observable public responseDiffOpen: boolean = true;
    @observable public showMode: string = 'ALL'; // DIFF | ALL
    @observable public version: number = 0; // 版本号

    @action.bound
    public setRequestHeaderDiff() {
        this.requestHeaderOpen = !this.requestHeaderOpen;
    }

    @action.bound
    public setQueryDiff() {
        this.queryDiffOpen = !this.queryDiffOpen;
    }

    @action.bound
    public setResponseHeaderDiff() {
        this.responseHeaderOpen = !this.responseHeaderOpen;
    }

    @action.bound
    public setResponseDiff() {
        this.responseDiffOpen = !this.responseDiffOpen;
    }

    @action.bound
    public setShowMode(v: string) {
        this.showMode = v;
        if (v === 'DIFF') {
            this.requestHeaderOpen = false;
            this.queryDiffOpen = false;
            this.responseHeaderOpen = false;
            this.responseDiffOpen = false;
        } else {
            this.requestHeaderOpen = true;
            this.queryDiffOpen = true;
            this.responseHeaderOpen = true;
            this.responseDiffOpen = true;
        }
    }

    @action.bound
    public async init() {
        const params = getUrlSearch() as { apiId: number, versionId: number }
        const res = await nsMockManageApiManageMainVersionListGET.remote({
            apiId: params.apiId
        })
        runInAction(() => {
            this.versionList = res.list.map(item => ({
                ...item,
                expend: false
            }));
            this.diffVersionId = Number(params.versionId);
            this.fetchDiffInfo();
        })
    }

    @action.bound
    public async fetchDiffInfo() {
        const params = getUrlSearch() as { apiId: number }
        const resData = await nsMockManageApiManageMainApiKtraceParamDiffGet.remote({
            apiId: params.apiId,
            versionId: this.diffVersionId
        })
        runInAction(() => {
            this.env = resData.ktraceCollectSource.env;
            this.host = resData.ktraceCollectSource.host;
            this.landId = resData.ktraceCollectSource.landId;
            this.diffInfo = resData;
            this.leftRequestHeader = resData.leftParamWithDiff.request.header;
            this.rightRequestHeader = resData.rightParamWithDiff.request.header;
            this.leftResponseHeader = resData.leftParamWithDiff.response.header;
            this.rightResponseHeader = resData.rightParamWithDiff.response.header;
            this.leftRequestDiff = resData.leftParamWithDiff.request.query;
            this.rightRequestDiff = resData.rightParamWithDiff.request.query;
            this.leftResponseDiff = resData.leftParamWithDiff.response.body;
            this.rightResponseDiff = resData.rightParamWithDiff.response.body;
        })
    }

    @action.bound
    public checkboxChange(v) {
        this.checked = v.target.checked;
        this.autoSyncKtrace = Number(this.checked);
    }

    @action.bound
    public async syncConfirm() {
        const params = getUrlSearch() as { apiId: number }
        await nsMockManageApiManageMainApiKtraceVersionApplyPost.remote({
            apiId: params.apiId
        })
        await nsMockManageApiManageMainApiKtraceAutoSyncSwitch.remote({
            apiId: params.apiId,
            autoSyncKtrace: this.autoSyncKtrace
        })
        runInAction(() => {
            message.success('更新成功');
            this.changeSyncModelOpen(false);
            router.push(ERouter.API_MOCK_REPO_MGR, {
                activeKey: 'group',
                apiId: params.apiId,
            });
        })
    }

    @action.bound
    public setDiffVersionId(v) {
        this.diffVersionId = v;
        pushKey({ versionId: v });
        this.fetchDiffInfo();
    }

    @action.bound
    public changeVersionListExpend(v) {
        this.versionList[v].expend = !this.versionList[v].expend;
    }

    // 改变同步弹框开启状态
    @action.bound
    public changeSyncModelOpen(boo) {
        this.isSyncModalOpen = boo;
        this.checked = false;
    }

}
