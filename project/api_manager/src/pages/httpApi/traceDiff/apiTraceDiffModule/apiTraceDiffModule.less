.traceDiff {
    padding: 24px;
    height: 100%;
    overflow: auto;

    .diff {
        margin-bottom: 14px;

        .title {
            color: #1F2329;
            font-family: "PingFang SC";
            font-size: 18px;
            font-weight: 500;
            line-height: 28px;
            margin-bottom: 16px;
        }

        .content {
            height: 100px;
            display: flex;
            justify-content: flex-start;
            align-items: center;
            flex: 1 0 0;

            .textline {
                width: 8px;
                height: 4px;
                background-color: #F0F2F5;
            }

            .text {
                width: 36px;
                height: 24px;
                padding: 3px 6px;
                border-radius: 2px;
                gap: 4px;
                background-color: #D5D6D9;

                .compare {
                    width: 24px;
                    height: 18px;
                    font-family: "PingFang SC";
                    font-weight: 400;
                    font-size: 12px;
                    line-height: 18px;
                    color: #FFFFFF;
                }
            }

            .new,
            .old {
                border-radius: 4px;
                background: #FAFCFF;
                padding: 16px;
                box-sizing: border-box;
                flex: 1 0 0;
                position: relative;

                .sync {
                    position: absolute;
                    right: 16px;
                    top: 17px;
                    color: #326BFB;
                    cursor: pointer;
                }

                .subTitle {
                    color: #252626;
                    font-size: 16px;
                    font-weight: 500;
                    line-height: 24px;
                    margin-bottom: 12px;
                }

                .traceInfo {
                    display: flex;
                    align-items: center;
                    height: 32px;

                    >.icon {
                        width: 20px;
                        height: 20px;
                    }

                    .apiIcon {
                        width: 20px;
                        height: 20px;
                        border-radius: 4px;
                        text-align: center;
                        line-height: 20px;
                        font-size: 14px;
                        color: #EA3A9B;
                        background: #FFEBF2;
                    }

                    .name {
                        color: #575859;
                        padding: 5px 12px;
                        font-size: 14px;
                        line-height: 22px;
                    }

                    .env {
                        display: flex;
                        padding: 5px 8px;
                        align-items: center;
                        flex: 1 0 0;
                        border-radius: 4px;
                        border: 1px solid #EBEDF0;
                        background: #FFF;

                        .envName,
                        .stagingName {
                            display: flex;
                            align-items: center;
                        }

                        .line {
                            width: 1px;
                            height: 14px;
                            background: var(---stroke_form, #D5D6D9);
                            margin: 0 16px;
                        }
                    }
                }
            }

        }

    }

    .response,
    .request {
        margin-bottom: 24px;

        .title {
            color: #1F2329;
            font-family: "PingFang SC";
            font-size: 16px;
            font-weight: 600;
            line-height: 24px;
            margin-bottom: 12px;
        }

        .contentContainer {
            display: flex;
            justify-content: flex-start;
            gap: 52px;
            flex: 1 0 0;
        }

        .old,
        .new {
            border-radius: 4px;
            border: 1px solid #EBEDF0;
            box-sizing: border-box;
            flex: 1 0 0;
            position: relative;

            .top {
                display: flex;
                align-items: center;
                padding: 12px 16px;
                cursor: pointer;
            }

            .arrow {
                margin: 0 4px;
            }
        }

        .header {
            color: #1F2329;
            font-family: "PingFang SC";
            font-weight: 600;
            line-height: 24px;
            padding-left: 8px;
        }
    }

}

.row {
    color: #30C453;
}