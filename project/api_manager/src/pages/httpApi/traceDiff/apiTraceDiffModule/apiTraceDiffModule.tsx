import { AView } from '@libs/mvvm';
import React from 'react';
import { apiTraceDiffModuleM } from './apiTraceDiffModuleM';
import { observer } from 'mobx-react';
import css from './apiTraceDiffModule.less';
import { KdevIconFont, AceEditor } from '@/business/commonComponents';
import { common_system_switch, common_system_api, common_system_add03, common_system_minus_square, common_system_history_record } from '@kid/enterprise-icon/icon/output/icons';
import { Avatar, Modal, Select, Tooltip, Checkbox, Button } from 'antd';
import moment from 'moment';
import { Bind } from 'lodash-decorators';
import { CaretRightOutlined, CaretDownOutlined } from '@ant-design/icons';
import { CommentType } from '@/business/Commit/Commit';
import { KtraceTable } from '@/pages/httpApi/apiVersionDiff/KtraceTable';

interface IProps {
}

@observer
export class ApiTraceDiffModule extends AView<apiTraceDiffModuleM, IProps> {

    // 应用确认弹框
    private syncModal() {
        return (
            <Modal
                title="确认应用参数"
                open={this.model.isSyncModalOpen}
                onCancel={() => {
                    this.model.changeSyncModelOpen(false); // 处理关闭按钮点击事件
                }}
                footer={null}
            >
                <p style={{ marginBottom: 24 }}>当前操作将更新「Ktrace 解析定义」到该文档的新版本中</p>
                <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                    <Checkbox checked={this.model.checked} onChange={this.model.checkboxChange}>
                        后续自动更新 Ktrace 最新版本
                    </Checkbox>
                    <div>
                        <Button onClick={() => this.model.changeSyncModelOpen(false)}>取消</Button>
                        <Button type="primary" onClick={this.model.syncConfirm} style={{ marginLeft: 8 }}>
                            确认
                        </Button>
                    </div>
                </div>
            </Modal>
        )
    }

    // 版本下拉框内容
    private versionArr() {
        const res: any[] = [];
        this.model.versionList.forEach((version, index) => {
            res.push(<div key={index}>
                <KdevIconFont
                    id={this.model.versionList[index].expend ? common_system_add03 : common_system_minus_square}
                    style={{ marginRight: 8, color: '#898A8C' }}
                />
                <span>Version {version.version}</span>
            </div>)
            if (!version.expend) {
                version.children.forEach((item, versionIndex) => {
                    res.push(<Select.Option value={item.versionId}
                        label={<span>
                            {index === 0 && versionIndex === 0 ?
                                null : <Tooltip title={'更新于' + moment(item.updateTime).format('YYYY-MM-DD HH:mm:ss')}>
                                    <span>
                                        <KdevIconFont id={common_system_history_record} style={{ marginRight: 4 }} />
                                    </span>
                                </Tooltip>}
                            Version {version.version}</span>}
                        key={item.versionId}
                    >
                        <div style={{
                            display: 'flex', alignItems: 'center'
                        }}>
                            <Avatar style={{ marginRight: 4 }} size="small" src={item.updateUser.photo}>
                                {item.updateUser.name[0]}
                            </Avatar>
                            <span style={{ flex: '1 0 0', color: '#252626' }}>{item.updateUser.name}</span>
                            <span
                                style={{
                                    fontSize: 12,
                                    color: '#898A8C'
                                }}
                            >更新于 {moment(item.updateTime).format('YYYY-MM-DD HH:mm:ss')}</span>
                        </div>
                    </Select.Option>)
                })
            }
        })
        return res;
    }

    @Bind
    public componentDidMount(): void {
        this.model.init()
    }


    public render(): React.ReactNode {
        return (
            <div className={css.traceDiff}>
                <div className={css.diff}>
                    <div className={css.title}>版本对比</div>
                    <div className={css.content}>

                        <div className={css.old}>
                            <div className={css.subTitle}>当前版本</div>
                            <div className={css.traceInfo}>
                                <div className={css.apiIcon}>
                                    <KdevIconFont id={common_system_api} />
                                </div>
                                <div className={css.name}>API 文档定义:</div>
                                <Select
                                    style={{ flex: '1 0 0' }}
                                    className={css.versionSelect}
                                    placeholder="请选择版本"
                                    optionLabelProp="label"
                                    value={this.model.diffVersionId}
                                    dropdownStyle={{
                                        width: 800
                                    }}
                                    popupClassName={css.resetPopupWidth}
                                    onSelect={(v) => {
                                        if (typeof v === 'number') {
                                            this.model.setDiffVersionId(v);
                                        } else {
                                            this.model.changeVersionListExpend(+v);
                                        }
                                    }}
                                >
                                    {this.versionArr()}
                                </Select>
                            </div>
                        </div>

                        <div className={css.textline}></div>
                        <div className={css.text}>
                            <div className={css.compare}>对比</div>
                        </div>
                        <div className={css.textline}></div>

                        <div className={css.new}>
                            <div className={css.subTitle}>Ktrace 解析版本</div>
                            <div className={css.sync} onClick={() => {
                                this.model.changeSyncModelOpen(true);
                            }}>一键应用</div>
                            <div className={css.traceInfo}>
                                <img className={css.icon} src="/assets/img/tianwen.png" />
                                <div className={css.name}>Ktrace 解析定义:</div>
                                <div className={css.env}>
                                    <div className={css.envName}>
                                        <img src="/assets/img/env.svg" />
                                        环境: {`${this.model.env} (${this.model.host})`}
                                    </div>
                                    <div className={css.line}></div>
                                    <div className={css.stagingName}>
                                        <img src="/assets/img/lane.svg" />
                                        测试泳道: {this.model.landId ||
                                            <span style={{ color: '#898A8C', paddingLeft: 4 }}>--</span>}
                                    </div>
                                </div>
                            </div>
                        </div>

                    </div>
                </div>
                <div className={css.request}>
                    <div className={css.title} style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                        请求参数
                        <Button
                            type="link"
                            size="small"
                            icon={
                                <KdevIconFont
                                    id={common_system_switch}
                                    style={{ fontSize: 16, verticalAlign: '-0.2em' }}
                                />
                            }
                            onClick={() => {
                                this.model.setShowMode(this.model.showMode === 'ALL' ? 'DIFF' : 'ALL');
                            }}
                        >
                            {this.model.showMode === 'DIFF' ? '展示全部信息' : '收起全部信息'}
                        </Button>
                    </div>
                    <div className={css.contentContainer} style={{ marginBottom: 8 }}>
                        <div className={css.old}>
                            <div className={css.top} onClick={this.model.setRequestHeaderDiff}>
                                {!this.model.requestHeaderOpen ?
                                    <CaretRightOutlined className={css.arrow} />
                                    : <CaretDownOutlined className={css.arrow} />
                                }
                                <div className={css.header}>请求头部</div>
                            </div>
                            {this.model.requestHeaderOpen &&
                                <KtraceTable
                                    header=""
                                    dataSource={this.model.leftRequestHeader}
                                    commentType={CommentType.requestHeader} />
                            }
                        </div>
                        <div className={css.new}>
                            <div className={css.top} onClick={this.model.setRequestHeaderDiff}>
                                {!this.model.requestHeaderOpen ?
                                    <CaretRightOutlined className={css.arrow} />
                                    : <CaretDownOutlined className={css.arrow} />
                                }
                                <div className={css.header}>请求头部</div>
                            </div>
                            {this.model.requestHeaderOpen &&
                                <KtraceTable
                                    header=""
                                    dataSource={this.model.rightRequestHeader}
                                    commentType={CommentType.requestHeader} />
                            }
                        </div>
                    </div>
                    <div className={css.contentContainer}>
                        <div className={css.old}>
                            <div className={css.top} onClick={this.model.setQueryDiff}>
                                {!this.model.queryDiffOpen ?
                                    <CaretRightOutlined className={css.arrow} />
                                    : <CaretDownOutlined className={css.arrow} />
                                }
                                <div className={css.header}>Query参数</div>
                            </div>
                            {this.model.queryDiffOpen &&
                                <KtraceTable
                                    header=""
                                    dataSource={this.model.leftRequestDiff}
                                    commentType={CommentType.requestBody} />
                            }
                        </div>
                        <div className={css.new}>
                            <div className={css.top} onClick={this.model.setQueryDiff}>
                                {!this.model.queryDiffOpen ?
                                    <CaretRightOutlined className={css.arrow} />
                                    : <CaretDownOutlined className={css.arrow} />
                                }
                                <div className={css.header}>Query参数</div>
                            </div>
                            {this.model.queryDiffOpen &&
                                <KtraceTable
                                    header=""
                                    dataSource={this.model.rightRequestDiff}
                                    commentType={CommentType.requestBody} />
                            }
                        </div>
                    </div>
                </div>
                <div className={css.response}>
                    <div className={css.title}>返回参数</div>
                    <div className={css.contentContainer} style={{ marginBottom: 8 }}>
                        <div className={css.old}>
                            <div className={css.top} onClick={this.model.setResponseDiff}>
                                {!this.model.responseDiffOpen ?
                                    <CaretRightOutlined className={css.arrow} />
                                    : <CaretDownOutlined className={css.arrow} />
                                }
                                <div className={css.header}>返回结果</div>
                            </div>
                            {this.model.responseDiffOpen &&
                                <KtraceTable
                                    header=""
                                    collapseClassName={css.collapse}
                                    dataSource={this.model.leftResponseDiff}
                                    commentType={CommentType.responseBody}
                                />
                            }
                        </div>
                        <div className={css.new}>
                            <div className={css.top} onClick={this.model.setResponseDiff}>
                                {!this.model.responseDiffOpen ?
                                    <CaretRightOutlined className={css.arrow} />
                                    : <CaretDownOutlined className={css.arrow} />
                                }
                                <div className={css.header}>返回结果</div>
                            </div>
                            {this.model.responseDiffOpen &&
                                <KtraceTable
                                    header=""
                                    dataSource={this.model.rightResponseDiff}
                                    commentType={CommentType.responseBody} />
                            }
                        </div>
                    </div>
                    <div className={css.contentContainer}>
                        <div className={css.old}>
                            <div className={css.top} onClick={this.model.setResponseHeaderDiff}>
                                {!this.model.responseHeaderOpen ?
                                    <CaretRightOutlined className={css.arrow} />
                                    : <CaretDownOutlined className={css.arrow} />
                                }
                                <div className={css.header}>返回头部</div>
                            </div>
                            {this.model.responseHeaderOpen &&
                                <KtraceTable
                                    header=""
                                    dataSource={this.model.leftResponseHeader}
                                    commentType={CommentType.requestHeader} />
                            }
                        </div>
                        <div className={css.new}>
                            <div className={css.top} onClick={this.model.setResponseHeaderDiff}>
                                {!this.model.responseHeaderOpen ?
                                    <CaretRightOutlined className={css.arrow} />
                                    : <CaretDownOutlined className={css.arrow} />
                                }
                                <div className={css.header}>返回头部</div>
                            </div>
                            {this.model.responseHeaderOpen &&
                                <KtraceTable
                                    header=""
                                    dataSource={this.model.rightResponseHeader}
                                    commentType={CommentType.requestHeader} />
                            }
                        </div>
                    </div>
                </div>
                {this.syncModal()}
            </div>
        );
    }
}
