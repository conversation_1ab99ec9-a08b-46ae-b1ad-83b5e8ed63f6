.traceDiff {
    width: 100vw;
    height: 100vh;
    display: flex;
    flex-direction: column;
    align-items: center;
    background: linear-gradient(180deg, #F3F7FF 0%, #F7F8FA 12.58%);
    padding-bottom: 8px;

    .header {
        width: 100%;
        height: 58px;
        padding: 18px 20px;
        box-sizing: border-box;
        display: flex;
        align-items: center;
        justify-content: start;
        gap: 12px;

        color: #252626;
        font-family: "PingFang SC";
        font-size: 14px;
        font-weight: 500;
        line-height: 22px;

        .backBtn {
            display: flex;
            align-items: center;
            margin-right: 16px;

            &:hover {
                cursor: pointer;
            }

            .backIcon {
                margin-right: 8px;
                font-size: 16px;
                color: #898A8C;
            }
        }

        .line {
            width: 1px;
            height: 14px;
            background: #D5D6D9;
        }
    }

    .content {
        width: calc(100vw - 16px);
        border-radius: 8px;
        height: 0;
        flex: 1 0 0;
        background: #fff;
        overflow: hidden;
    }
}