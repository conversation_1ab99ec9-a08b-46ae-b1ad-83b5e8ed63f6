import React from 'react';
import css from './ApiTraceDiffPage.less';
import { ApiTraceDiffModule } from './apiTraceDiffModule/apiTraceDiffModule';
import { apiTraceDiffModuleM } from './apiTraceDiffModule/apiTraceDiffModuleM';
import { KdevIconFont } from '@/business/commonComponents';
import { common_system_arrowback } from '@kid/enterprise-icon/icon/output/icons';
import { router } from '@libs/mvvm';
import { ERouter } from '@/CONFIG';

export function ApiVersionDiff() {
    const searchParams = new URLSearchParams(window.location.search);
    const onBack = () => {
        try {
            router.push(ERouter.API_MOCK_REPO_MGR, {
                activeKey: 'group',
                apiId: searchParams.get('apiId')
            })
        } catch {
        }
    };

    return (
        <div className={css.traceDiff}>
            <div className={css.header}>
                <div className={css.backBtn} onClick={onBack}>
                    <KdevIconFont id={common_system_arrowback} className={css.backIcon} />
                    <b>返回</b>
                </div>
                <div className={css.line}></div>
                {searchParams.get('apiName')}
            </div>
            <div className={css.content}>
                <ApiTraceDiffModule model={new apiTraceDiffModuleM()} />
            </div>
        </div>
    )
}
