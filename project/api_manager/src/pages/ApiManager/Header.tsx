/*
 * @Author: han<PERSON><PERSON>
 * @Date: 2024-07-08 14:13:10
 * @LastEditors: hanweiqi
 * @LastEditTime: 2024-07-08 14:28:09
 * @Description: 请填写简介
 */
import { message, Segmented, Tooltip } from 'antd';
import React, { useState, createContext, useContext, useRef, useEffect, Key, useCallback, useMemo } from 'react';
import css from './ApiManager.less';
import { getUrlSearch, pushKey } from '@/index.config/tools';
import store from '../../pages/httpApi/viewApi/checkRule/store';
import { ApiMgrSplitPane } from '@/pages/ApiManager/ApiMgrSplitPane';
import { RightContentWithMe } from '@/pages/ApiManager/relevantToMe/RightContent';
import { ApiViewM } from '@/pages/ApiManager/apiView/ApiViewM';
import { RelevantDirectoryTree } from './relevantToMe/RelevantDirectoryTree';
import {
    nsApiManagerGroupTree,
    nsApiManagerSearch,
    nsApiManagerList,
    nsMockManageApiManageMainCollectSearchGET,
    nsMockManageApiManageMainCollectListGET,
    nsMockManageApiManageMainApiFollowPOST,
    nsMockManageApiManageMainGroupUserDepartmentInfo
} from '@/remote';
import { computedExpandKeys, computedIsSearchEmpty, ECustomKey, eNodeType, formatCGTreeData, formatTreeData, getExpandAllKeys, TDataItem } from './relevantToMe/RelevantToMeUtils';
import { renderSearch } from './Search';
import ApiTree from './ApiMenu/ApiTree';
import RcTree from 'rc-tree';
import { EFMPKey } from '@/index.config';
import { fmp } from 'kdev-fe-common/src/shared/common/fmpReport';
import { RightContent } from './ApiMenu/rightContent';
// import { KDEV_API_GROUP_PROJECT } from '@/appV2';
import { SearchApi } from '@/business/topNavComps/searchApi/SearchApi';
import { KDEV_API_GROUP_PROJECT, KDEV_API_MENU_SELECTEDKEYS, KDEV_API_MENU_SEGMENTED, KDEV_API_WITHME_SELECTEDKEYS, KDEV_API_SEARCH_GROUP, KDEV_API_GROUP_REPO } from './apiManager.config'
import { debounce } from 'lodash';
import { KdevIconFont } from '@/business/commonComponents';
import { common_system_switch } from '@kid/enterprise-icon/icon/output/icons';
import { ViewTypeMap } from './ApiMenuEntry';
import { EGray_Key, storeGrayFeatures } from '@kdev-fe-common/store';
import { RenderDepartmentRepo } from '@/RenderDepartmentRepo';

const apiViewM = new ApiViewM();
export enum ETabKey {
    RELATE = 'relate',
    GROUP = 'group',
    API_VIEW = 'apiView'
}
interface IQuery {
    groupId?: number;
    apiId?: number;
    version?: number;
    activeKey?: string;
}

export interface IHeaderContextProps {
    onChangeTab?(type: ETabKey): void;
}

export const HeaderContext = createContext({});

export interface IRelevantToMeContextProps {
    apiId: number | undefined;
    setApiId: (apiId: number | undefined) => void;
    apiIdWithMe: number | undefined;
    setApiIdWithMe: (apiId: number | undefined) => void;
    searchParams: IQuery;
    groupId: number | undefined;
    setGroupId: (groupId: number | undefined) => void;
    search: string;
    setSearch: (val: string) => void;
    treeData: any;
    setTreeData: (val: any) => void;
    apiType: 'all' | 'withMe';
    setApiType: (val: 'all' | 'withMe') => void;
    expandedKeys: React.Key[];
    setExpandedKeys: (res: React.Key[]) => void;
    loadLock: boolean;
    setLoadLock: (res: boolean) => void;
    loading: boolean;
    setLoading: (res: boolean) => void;
    autoExpandParent: boolean;
    setAutoExpandParent: (res: boolean) => void;
    flatTreeData: Record<string, nsApiManagerSearch.IItem>;
    setFlatTreeData: (res: Record<string, nsApiManagerSearch.IItem>) => void;
    treeRef?: React.RefObject<RcTree>;
    treeWrapRef?: React.RefObject<HTMLDivElement>;
    withMeTreeRef: React.RefObject<RcTree>;
    withMeWrapRef?: React.RefObject<HTMLDivElement>;

    searchLoadLock: boolean;
    setSearchLoadLock: (res: boolean) => void;
    // searchExpandedKeys: Key[];
    searchExpandedKeys: React.Key[];
    setSearchExpandedKeys: (res: React.Key[]) => void;
    searchTreeData: nsApiManagerSearch.IItem[];
    setSearchTreeData: (res: nsApiManagerSearch.IItem[]) => void;
    searchLoading: boolean;
    setSearchLoading: (res: boolean) => void;
    autoSearchExpandParent: boolean;
    setAutoSearchExpandParent: (res: boolean) => void;
    searchWithMeTreeData: any
    setSearchWithMeTreeData: (res: any) => void;
    dropDownIsEmpty: boolean;
    setDropDownIsEmpty: (res: boolean) => void;

    selectedKey?: string;
    setSelectedKey?: (val: string) => void;
    onLoadData?(nodeData: any): Promise<void>;
    onSelectDir?(selectKey: string, apiId?: number): void; // 选中API时，必须有apiId
    onSearchTreeData?(keyword?: string): Promise<void>;
    searchWithMe?(keyword?: string): Promise<void>;
    treeLoading?: boolean;
    isSearchEmpty?: boolean;
    expandKeys?: Key[];
    setExpandKeys?(keys: Key[]): void;
    treeDataWithMe: any;
    setTreeDataWithMe: (res: any) => void;
    flatTreeDataWithMe: Record<string, TDataItem>;
    followCallBack?(
        follow: nsMockManageApiManageMainApiFollowPOST.EFollow,
        id: number,
        type: nsMockManageApiManageMainCollectListGET.ENodeType
    ): void;
    getTreeDataInit: () => Promise<nsApiManagerSearch.IItem[]>;
    getSearchTreeDataInit: () => Promise<nsApiManagerSearch.IItem[]>;
    onMoveCallback: any;
    loadDataLoding: boolean;
    isFollow: boolean;
    onLoadDataWithMe: (res: any) => Promise<void>
    updateTreeDataByKey: (key: string, id: number) => Promise<void>
}

function flatTree(list: Array<nsApiManagerSearch.IItem> = []) {
    const obj: Record<string, nsApiManagerSearch.IItem> = {};
    flat(list);
    function flat(arr: Array<nsApiManagerSearch.IItem> = []) {
        arr.forEach(item => {
            obj[getKey(item)] = item;
            item.children && flat(item.children);
        });
    }
    return obj;
}

export const RelevantToMeContext = createContext<IRelevantToMeContextProps>({});

interface IRelevantToMeProviderProps {
    children?: React.ReactNode;
}

export function getKey(item: nsApiManagerSearch.IItem) {
    if (item.type === eNodeType.API) {
        return `A${item.apiId}`;
    }
    return `G${item.groupId}`;
}
export function expandAll(list: Array<nsApiManagerSearch.IItem> = [], dep?: number) {
    const keys: Array<string> = [];
    let cur = 1;
    function Loop(arr) {
        if (dep && cur > dep) {
            return;
        }
        arr.forEach(item => {
            if (item.children && item.children.length > 0) {
                keys.push(getKey(item));
                Loop(item.children);
            }
        });
        cur++;
    }
    Loop(list);
    return keys;
}

// 判断两个元素是否相交
function isIntersect(oneNode: Element, twoNode: Element) {
    const oneNodeRect = oneNode.getBoundingClientRect();
    const twoNodeRect = twoNode.getBoundingClientRect();
    const intersect = !(
        oneNodeRect.right < twoNodeRect.left ||
        oneNodeRect.left > twoNodeRect.right ||
        oneNodeRect.bottom < twoNodeRect.top ||
        oneNodeRect.top > twoNodeRect.bottom
    );
    return intersect;
}
export function findNodeAndAncestors(tree: Array<nsApiManagerSearch.IItem>, targetKey: string) {
    const path: Array<nsApiManagerSearch.IItem> = [];
    function dfs(node, p) {
        path.push(node);
        if (getKey(node) === targetKey) {
            return true;
        }
        if (node.children) {
            for (const child of node.children) {
                if (dfs(child, p)) {
                    return true;
                }
            }
        }
        path.pop();
        return false;
    }
    for (const node of tree) {
        if (dfs(node, path)) {
            return path.map(it => getKey(it));
        }
    }
    return [];
}

// 获取并设置用户目前选择的组
export const getUserLastSelectedItem = () => {
    const userLastItem: string =
        localStorage.getItem(KDEV_API_MENU_SELECTEDKEYS) || '';
    const res: {
        groupId?: string;
        apiId?: string;
    } = {};
    if (userLastItem.startsWith('G')) {
        res.groupId = userLastItem.split('G')[1];
    } else if (userLastItem.startsWith('A')) {
        res.apiId = userLastItem.split('A')[1];
    }
    return res
}

// 获取并设置用户目前选择的组
export const getUserLastSelectedItemWithMe = () => {
    const searchParams = getUrlSearch() as { relateKey?: string }
    const userLastItem: string =
        searchParams.relateKey ? searchParams.relateKey :
            localStorage.getItem(KDEV_API_WITHME_SELECTEDKEYS) || '';
    searchParams.relateKey ? localStorage.setItem(KDEV_API_WITHME_SELECTEDKEYS, searchParams.relateKey) : ''
    const res: {
        groupId?: string;
        apiId?: string;
        key: string;
    } = {
        key: userLastItem
    };
    const arr = userLastItem.split('-');
    if (arr[arr.length - 1] === '0') {
        res.groupId = arr[arr.length - 2];
    } else if (arr[arr.length - 1] === '1') {
        res.apiId = arr[arr.length - 2];
    }
    return res
}
// 格式化key
export const formatKey = (key) => {
    const res: {
        groupId?: string;
        apiId?: string;
        key: string;
    } = {
        key: key
    };
    const arr = key.split('-');
    if (arr[arr.length - 1] === '0') {
        res.groupId = arr[arr.length - 2];
    } else if (arr[arr.length - 1] === '1') {
        res.apiId = arr[arr.length - 2];
    }
    return res
}

function RelevantToMeProvider(props: IRelevantToMeProviderProps) {
    const searchParams = getUrlSearch(['version', 'selectedType']) as IQuery &
    { selectedKey: string; relateKey: string }
    const [apiId, setApiId] = useState<number | undefined>(searchParams.apiId);
    const [groupId, setGroupId] = useState<number | undefined>(searchParams.groupId);
    const [search, setSearch] = useState<string>('');
    const [treeData, setTreeData] = useState<any>([]);
    const [apiType, setApiType] = useState<'all' | 'withMe'>('all');
    const [expandedKeys, setExpandedKeys] = useState<React.Key[]>([]);
    const [loadLock, setLoadLock] = useState(true);
    const [loading, setLoading] = useState<boolean>(true);
    const [autoExpandParent, setAutoExpandParent] = useState(false);
    const [flatTreeData, setFlatTreeData] = useState<Record<string, nsApiManagerSearch.IItem>>({});
    const treeRef = useRef<RcTree>(null);
    const treeWrapRef = useRef<HTMLDivElement>(null);
    const withMeTreeRef = useRef<RcTree>(null);
    const withMeWrapRef = useRef<HTMLDivElement>(null);
    const [searchLoadLock, setSearchLoadLock] = useState(true);
    const [searchExpandedKeys, setSearchExpandedKeys] = useState<Key[]>([]);
    const [searchTreeData, setSearchTreeData] = useState<nsApiManagerSearch.IItem[]>([]);
    const [searchLoading, setSearchLoading] = useState<boolean>(false);
    const [autoSearchExpandParent, setAutoSearchExpandParent] = useState(false);
    const [searchWithMeTreeData, setSearchWithMeTreeData] = useState<TDataItem[]>([]);


    const [apiIdWithMe, setApiIdWithMe] = useState<number | undefined>();
    const [selectedKey, setSelectedKey] = useState<string>();
    const [flatTreeDataWithMe, setFlatTreeDataWithMe] = useState<Record<string, TDataItem>>({});
    const [treeLoading, setTreeLoading] = useState<boolean>(false);
    const [loadDataLoding, setLoadDataLoading] = useState<boolean>(false);
    const [isSearchEmpty, setIsSearchEmpty] = useState<boolean>(false);
    const [dropDownIsEmpty, setDropDownIsEmpty] = useState<boolean>(false);
    const [expandKeys, setExpandKeys] = useState<Key[]>([]);
    const [treeDataWithMe, setTreeDataWithMe] = useState<TDataItem[]>([]);
    const [isFollow, setIsFollow] = useState<boolean>();
    const [currentGroupId, setCurrentGroupId] = useState<number>(0);


    // treeData变更后，做一个映射
    useEffect(() => {
        setFlatTreeData(flatTree(treeData));
    }, [treeData]);

    const selectedKeyFn = (() => {
        if (apiId) {
            return `A${apiId}`;
        }
        if (groupId) {
            return `G${groupId}`;
        }
        const res = getUserLastSelectedItem();
        if (res.apiId) {
            return `A${res.apiId}`;
        }
        if (res.groupId) {
            return `G${res.groupId}`;
        }
        return undefined;
    })();

    // 获取并设置用户目前选择的组
    const getUserGroup = async () => {
        const isSearch = window.search;
        const userLastGroup = isSearch
            ? JSON.parse(localStorage.getItem(KDEV_API_SEARCH_GROUP) || '{}')
            : JSON.parse(localStorage.getItem(KDEV_API_GROUP_REPO) || '{}');

        if (userLastGroup.groupId) {
            setCurrentGroupId(userLastGroup.groupId);
            return userLastGroup;
        } else {
            const res = await nsMockManageApiManageMainGroupUserDepartmentInfo.remote({});
            return res.secondDepartmentGroup;
        }

    }
    /**
     * 初始请求数据， 会根据url上的group、api来请求对应的目录；展开所以能展开的
     */
    const getTreeDataInit = async () => {
        try {
            setLoading(true);
            // 确定类型
            let type: number | undefined;
            let $apiId;
            let $groupId;

            // 从localStorage中获取
            const userLastSelectedItem = getUserLastSelectedItem();
            if (userLastSelectedItem.apiId || userLastSelectedItem.groupId) {
                $apiId = userLastSelectedItem.apiId;
                $groupId = userLastSelectedItem.groupId;
            } else {
                $apiId = apiId;
                $groupId = groupId;
            }

            // 从url中获取，并覆盖从localStorage中获取的
            const urlparams = getUrlSearch() as { apiId?: string; groupId?: string }
            if (urlparams.apiId) {
                $apiId = urlparams.apiId;
                $groupId = undefined
            }
            if (urlparams.groupId) {
                $apiId = undefined;
                $groupId = urlparams.groupId
            }

            if ($apiId) {
                type = eNodeType.API;
            } else if ($groupId) {
                type = eNodeType.GROUP;
            } else {
                type = undefined;
            }
            const params = {
                id: type ? $apiId : $groupId,
                type,
                // currentGroupId: await getUserGroup()
                currentGroupId: (await getUserGroup()).groupId
            };
            setTreeLoading(true);
            const { list } = await nsApiManagerGroupTree.remote(params);
            setTreeLoading(false);
            setTreeData(list);

            // 设置选中的key
            if (selectedKeyFn) {
                const temp = findNodeAndAncestors(list, selectedKeyFn)
                setExpandedKeys(temp.length ? temp : expandAll(list, 1));
            } else {
                setExpandedKeys(expandAll(list, 1));
            }
            setGroupId($groupId);
            setApiId($apiId);
            setLoading(false);
            return list;
        } catch (e) {
            return [];
        }
    };
    const getSearchTreeDataInit = async () => {
        getTreeDataInit();
        executeScrollLogic();
    };

    const onMoveCallback = useCallback(() => {
        return getTreeDataInit();
    }, [getTreeDataInit]);

    // 切换当前组
    const changeGroup = async () => {
        setApiId(undefined);
        setGroupId(undefined);
        setCurrentGroupId((await getUserGroup()).groupId);
    }
    useEffect(() => {
        document.addEventListener('selectedGroup', changeGroup);
        return () => {
            document.removeEventListener('selectedGroup', changeGroup);
        }
    })

    const executeScrollLogic = () => {
        const scrollToSelectKey = () => {
            selectedKeyFn && treeRef.current?.scrollTo({ key: selectedKeyFn, align: 'top' } as any);
        };
        /**
         * 初始化获取目录
         */
        getTreeDataInit().then(() => {
            // 若选中元素不在视口内，则让其滚动到顶部
            const antTreeNodeSelected = treeWrapRef.current?.querySelector('.ant-tree-node-selected');
            if (!antTreeNodeSelected || !isIntersect(treeWrapRef?.current as Element, antTreeNodeSelected)) {
                setTimeout(scrollToSelectKey, 500);
            }
        });
    };


    useEffect(() => {
        // 若选中元素不在视口内，则让其滚动到顶部
        if (apiType === 'all') {
            executeScrollLogic();
        }
    }, [apiType]);

    useEffect(() => {
        // 若选中元素不在视口内，则让其滚动到顶部
        executeScrollLogic();
    }, [currentGroupId]);


    //////////////// 与我相关

    // 选中目录或API，如果选中API则aId必有值
    const onSelectDir = (selectKey: string, aId?: number) => {
        if (store.isChange) {
            message.info('请先保存当前修改');
            return;
        }
        setSelectedKey(selectKey);
        setApiIdWithMe(aId);
    };

    // 搜索
    const searchCollectList = async (key: string) => {
        if (!key) {
            return;
        }
        try {
            const result = await nsMockManageApiManageMainCollectSearchGET.remote({ key });
            return result;
        } catch { }
    };

    // 获取初始化列表
    const getCollectList = async () => {
        try {
            const params = {
                positionPath: '',
                positionPathType: undefined
            };
            // 只有选中收藏目录下的内容，传递postitionPath、positionPathType参数
            const userLastSelectedItem = getUserLastSelectedItemWithMe();
            if (userLastSelectedItem.key.indexOf(ECustomKey.COLLECT_GROUP) > -1) {
                Object.assign(params, {
                    positionPath: userLastSelectedItem.key.slice(3),
                    positionPathType: userLastSelectedItem.apiId ? eNodeType.API : eNodeType.GROUP
                });
            }
            searchParams.relateKey ?
                Object.assign(params, {
                    sharePath: userLastSelectedItem.key
                }) : '';
            const result = await nsMockManageApiManageMainCollectListGET.remote(params);
            return result;
        } catch {
            return null;
        }
    };


    // 异步加载目录树
    const getGroupLayer = async (id: number) => {
        // setLoadDataLoading(true);
        const params = {
            groupId: id, // -1代表目录初始化
            projectId: undefined // TODO: 待删除，删除后端报错
        };
        const { item } = await nsApiManagerList.remote(params);
        // setLoadDataLoading(false);
        return item;
    };

    // 更新目录树
    const updateTreeDataByKey = async (key: string, id: number, flag: boolean) => {
        if (!id) {
            return;
        }
        const group: any = await getGroupLayer(id);
        const treeNodeData = flatTreeDataWithMe[key];
        if (treeNodeData) {
            const res = formatCGTreeData(group.children, treeNodeData['topGroupKey']);
            treeNodeData.children = res.treeData;
            Object.assign(flatTreeDataWithMe, res.flatTreeData);
        }
        !flag && setTreeDataWithMe([...treeDataWithMe]);
    };

    // 不在视口区域自动滚动到视口
    const withMeScrollToSelectKey = async () => {
        let timeId;
        const scrollToSelectKey = async () => {
            const newSelectedKey = await setUserLastSelectedItem();
            newSelectedKey && withMeTreeRef.current?.scrollTo({ key: newSelectedKey, align: 'top' } as any);
        };

        onSearchTreeData().then(() => {
            const antTreeNodeSelected = withMeWrapRef.current?.querySelector('.ant-tree-treenode-selected');
            if (!antTreeNodeSelected || !isIntersect(withMeWrapRef?.current as Element, antTreeNodeSelected)) {
                timeId = setTimeout(scrollToSelectKey, 1000);
            }
        });
        return () => {
            clearTimeout(timeId);
        };
    };
    useEffect(() => {
        withMeScrollToSelectKey();
    }, []);


    const setUserLastSelectedItem = async (position?: string) => {
        let userLastSelectedItem;
        if (position) {
            userLastSelectedItem = formatKey(position);
        } else {
            userLastSelectedItem = getUserLastSelectedItemWithMe();
        }
        searchParams.activeKey === 'relate' && userLastSelectedItem.key && pushKey({
            relateKey: userLastSelectedItem.key
        })
        let selectedGroupId;
        let selectedApiId;
        if (userLastSelectedItem.groupId) {
            await updateTreeDataByKey(userLastSelectedItem.key, +userLastSelectedItem.groupId, true);
            selectedGroupId = userLastSelectedItem.groupId;
            setSelectedKey!(userLastSelectedItem.key);
        } else if (userLastSelectedItem.key && !userLastSelectedItem.apiId && !userLastSelectedItem.groupId) {
            onSelectDir?.(userLastSelectedItem.key)
            selectedGroupId = userLastSelectedItem.key;
        }
        if (userLastSelectedItem.apiId) {
            setSelectedKey(userLastSelectedItem.key)
            setApiIdWithMe(+userLastSelectedItem.apiId!)
            selectedApiId = userLastSelectedItem.key;
        }
        if (selectedGroupId) {
            return selectedGroupId;
        }
        if (selectedApiId) {
            return selectedApiId;
        }
        return undefined;
    }



    const onSearchTreeData = async (keyword: string = '') => {
        // 如果有值走搜索逻辑
        const userLastSelectedItem = getUserLastSelectedItemWithMe();
        setTreeLoading(true);
        if (keyword) {
            setLoadDataLoading(true);
            const res = await searchCollectList(keyword);
            if (res) {
                // setIsSearchEmpty(computedIsSearchEmpty(res));
                setDropDownIsEmpty(computedIsSearchEmpty(res));
                const { treeData: data, flatTreeData: flatData } = formatTreeData(res);
                setExpandKeys(getExpandAllKeys(flatData));
                setSearchWithMeTreeData(data);
                // setTreeDataWithMe(data);
                setFlatTreeDataWithMe(flatData);
            }
            setLoadDataLoading(false);
            setUserLastSelectedItem();
        } else {
            setLoadDataLoading(true);
            const res = await getCollectList();
            if (res) {
                setIsSearchEmpty(false);
                const { treeData: data, flatTreeData: flatData } = formatTreeData(res);
                if (searchParams.relateKey) {
                    if (res.position) {
                        setExpandKeys(computedExpandKeys(flatData, res.position));
                    } else {
                        if (searchParams.relateKey &&
                            searchParams.relateKey !== 'null' &&
                            searchParams.relateKey.length > 2
                        ) {
                            setApiType('all');
                            const relateKeyArr = searchParams.relateKey.split('-');
                            pushKey({
                                groupId: relateKeyArr[relateKeyArr.length - 2]
                            }, ['relateKey'])
                        }
                        getTreeDataInit();
                    }
                } else {
                    setExpandKeys(computedExpandKeys(flatData, userLastSelectedItem.key));
                }
                setTreeDataWithMe(data);
                setFlatTreeDataWithMe(flatData);
                setUserLastSelectedItem(res.position);
            }
            setTreeLoading(false);
            setLoadDataLoading(false);
            fmp.upload(EFMPKey.API_RELATE);
        }
        // 这块逻辑放在onLoadDataWithMe里面了
        // if (userLastSelectedItem.groupId) {
        //    setSelectedKey(userLastSelectedItem.key)
        //    updateTreeDataByKey(userLastSelectedItem.key, +userLastSelectedItem.groupId, true)
        // } else
        // const userLastSelectedItem = getUserLastSelectedItemWithMe();
    };
    const searchWithMe = async (keyword: string) => {
        onSearchTreeData(keyword);
        withMeScrollToSelectKey();
    }

    // 关注后操作逻辑
    const followCallBack = (
        follow: nsMockManageApiManageMainApiFollowPOST.EFollow,
        id: number,
        type: nsMockManageApiManageMainCollectListGET.ENodeType
    ) => {
        // 查找所有相关数据设置关注状态
        Object.keys(flatTreeData).map(key => {
            if (type === eNodeType.GROUP && flatTreeData[key]?.groupId === id) {
                (flatTreeData[key] as any).follow = follow;
            }
            if (type === eNodeType.API && (flatTreeData[key] as any)?.apiId === id) {
                (flatTreeData[key] as any).follow = follow;
            }
        });
        // setTreeDataWithMe([...treeData]);
        setIsFollow((flatTreeData[selectedKey] as any)?.follow === 1);
    };

    useEffect(() => {
        onSearchTreeData();
        // 如果url中有apiId的话，说明用户是通过分享的链接来的，直接选中“目录”
        const urlParams = getUrlSearch(['apiId']) as IQuery;
        if (urlParams.apiId) {
            setApiType('all');
        } else {
            let apiMenuSegmented: 'all' | 'withMe' = 'all';
            if (searchParams.activeKey) {
                apiMenuSegmented = searchParams.activeKey === 'relate' ? 'withMe' : 'all'
            } else {
                apiMenuSegmented = localStorage.getItem(KDEV_API_MENU_SEGMENTED) as 'all' | 'withMe' || 'all'
            }
            localStorage.setItem(KDEV_API_MENU_SEGMENTED, apiMenuSegmented);
            setApiType(apiMenuSegmented);
            pushKey({
                activeKey: apiMenuSegmented === 'all' ? 'group' : 'relate'
            })
        }
    }, []);


    return (
        <RelevantToMeContext.Provider
            value={{
                apiId, setApiId,
                apiIdWithMe, setApiIdWithMe,
                groupId, setGroupId,
                searchParams,
                search,
                setSearch,
                treeData,
                setTreeData,
                searchTreeData, setSearchTreeData,
                searchLoadLock, setSearchLoadLock,
                searchExpandedKeys, setSearchExpandedKeys,
                autoSearchExpandParent, setAutoSearchExpandParent,
                searchLoading, setSearchLoading,
                apiType, setApiType,
                expandedKeys, setExpandedKeys,
                loadLock, setLoadLock,
                loading, setLoading,
                autoExpandParent, setAutoExpandParent,
                flatTreeData, setFlatTreeData,
                treeRef,
                treeWrapRef,
                withMeTreeRef,
                withMeWrapRef,
                onSelectDir,
                onSearchTreeData,
                searchWithMe,
                treeLoading,
                isSearchEmpty,
                dropDownIsEmpty,
                selectedKey,
                expandKeys,
                setExpandKeys,
                treeDataWithMe,
                searchWithMeTreeData,
                flatTreeDataWithMe,
                followCallBack,
                getTreeDataInit,
                getSearchTreeDataInit,
                onMoveCallback,
                loadDataLoding,
                isFollow,
                setTreeDataWithMe,
                updateTreeDataByKey,
                setSelectedKey
            }}
        >
            <ApiMgrSplitPane
                renderLeftContent={() => <Left />}
                renderRightContent={() => <Right />}
            />
        </RelevantToMeContext.Provider>
    );
}

function Left() {
    const relevantToMeContext = useContext<IRelevantToMeContextProps>(RelevantToMeContext);
    const { apiType, setApiType, treeWrapRef, onSearchTreeData, withMeWrapRef, treeLoading } = relevantToMeContext;

    const allTree = useMemo(() => {
        return <div className={[css.repoDirectoryTreeBox].join(' ')} ref={treeWrapRef}>
            <ApiTree hideSetSearchKeywords={true} ></ApiTree>
        </div>
    }, [])
    const withMeTree = useMemo(() => {
        return <div className={[css.repoDirectoryTreeBox].join(' ')} ref={withMeWrapRef}>
            <RelevantDirectoryTree />
        </div>
    }, [])

    return <div className={css.leftPaneContent}>
        <div className={css.leftContent}>
            {/* <Tooltip title="切换至项目视图"> */}
            <div className={css.title}>
                <h1>仓库管理</h1>
                <RenderDepartmentRepo></RenderDepartmentRepo>
            </div>
            {/* </Tooltip> */}
            {renderSearch()}
            <Segmented
                disabled={treeLoading}
                value={apiType}
                style={{ marginBottom: 12 }}
                block
                options={[
                    { label: '全部接口', value: 'all' },
                    { label: '与我相关', value: 'withMe' },
                ]}
                onChange={async (e) => {
                    // 修改后记住，用于下次复原
                    localStorage.setItem(KDEV_API_MENU_SEGMENTED, e as 'all' | 'withMe');
                    setApiType!(e as 'all' | 'withMe');
                    if (e === 'all') {
                        const storage = getUserLastSelectedItem();
                        pushKey({ activeKey: 'group' })
                        if (storage.apiId) {
                            pushKey({ activeKey: 'group', apiId: `${storage.apiId}` }, ['relateKey'])
                        }
                        if (storage.groupId) {
                            pushKey({ activeKey: 'group', groupId: `${storage.groupId}` }, ['relateKey'])
                        }
                    } else {
                        pushKey({
                            relateKey: localStorage.getItem(KDEV_API_WITHME_SELECTEDKEYS),
                            activeKey: 'relate',
                        }, ['apiId', 'groupId']);
                        onSearchTreeData!();
                    }
                }}
            />
            {apiType === 'all' && allTree}
            {apiType === 'withMe' && withMeTree}
        </div>
    </div>
}

function Right() {
    const { apiType } = useContext<IRelevantToMeContextProps>(RelevantToMeContext);
    if (apiType === 'all') {
        return <RightContent />
    } else {
        return <RightContentWithMe />
    }

}

export function Header(props) {
    return (
        <div className={css.ApiManager} style={props.style}>
            <RelevantToMeProvider>
            </RelevantToMeProvider>
        </div>
    );
}