.projectViewList {
    height: 100%;
    padding: 12px 16px;
    background: #fff;
    display: flex;
    flex-direction: column;

    .header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;
        height: 32px;

        .title {
            font-size: 16px;
            font-weight: 500;
            display: flex;
            color: #252626;
            align-items: center;
            // margin: 0 0 12px 4px;
            cursor: pointer;


            h1 {
                font-size: 16px;
                font-weight: 500;
                margin: 0;
                margin-right: 8px;
            }
        }

        .right {
            .createProjectBtn {}

            .importProjectBtn {
                margin-left: 8px;
            }

        }
    }

    .search {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin: 0 0 16px 0;
    }

    .scroll {
        height: 0;
        flex: 1;
        overflow-y: auto;
    }

}


.allProjectList {
    display: flex;
    flex-wrap: wrap;
    gap: 16px;

    .empty {
        width: 100%;
        height: 100%;
        margin-top: 30vh;
    }
}

.projectItem {
    flex: 0 0 calc(25% - 12px);
    width: 0;
    height: 142px;
    padding: 12px 16px;
    background: #FFFFFF;
    border: 1px solid #EBEDF0;
    border-radius: 4px;
    display: flex;
    flex-direction: column;
    cursor: pointer;

    &:hover {
        border: 1px solid var(---stroke_brand_hover, #5C8FFF);
        background: linear-gradient(180deg, #F0F7FF 0%, #FFF 100%);

        .line1 {
            .options {
                display: flex;
            }
        }
    }

    .line1 {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 12px;

        .projectName {
            color: var(---text_primary, #252626);
            width: 100%;
            font-size: 14px;
            font-weight: 500;
            line-height: 22px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }


        .options {
            display: none;
            align-items: center;
            gap: 4px;

            .optionIcon {
                font-size: 16px;
                color: #898A8Cff;
                cursor: pointer;

                &.mian {
                    color: #326BFBff
                }
            }
        }
    }

    .line2 {
        color: var(---text_tertiary, #898A8C);
        /* font_body */
        font-family: "PingFang SC";
        height: 44px;
        font-size: 14px;
        font-style: normal;
        font-weight: 400;
        line-height: 22px;
        // 两行溢出隐藏
        word-break: break-all;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
        margin-bottom: 12px;
    }

    .line3 {
        display: flex;
        align-items: center;
        justify-content: space-between;

        color: var(---text_tertiary, #898A8C);
        /* font_caption */
        font-family: "PingFang SC";
        font-size: 12px;
        font-style: normal;
        font-weight: 400;
        line-height: 18px
            /* 150% */
        ;
    }

    .projectIcon {
        display: flex;
        width: 32px;
        padding: 5.333px;
        align-items: center;
        gap: 13.333px;
        margin-right: 12px;
        border-radius: 6px;

        &.color0 {
            background: #FFEBF2ff;
            color: #EA3A9Bff;
        }

        &.color1 {
            background: #F0F7FFff;
            color: #326BFBff;
        }

        &.color2 {
            background: #FFF4F0ff;
            color: #FA4E3Eff;
        }

        &.color3 {
            background: #F6EBFFff;
            color: #7735D3ff;
        }

        &.color4 {
            background: #E6FFF4ff;
            color: #36CFA2ff;
        }

        &.color5 {
            background: #FFFBE6ff;
            color: #FFAA00ff;
        }
    }

}