import React, { useState, useImperativeHandle, forwardRef, Ref, useEffect, useContext } from 'react';
import { message, Modal, Tooltip } from 'antd';
import css from './ImportKapiProjectModal.less';
import classnames from 'classnames';
import {
    nsMockManageApiManageMainApiProgram,
    nsMockManageApiManageMainApiProgramImportKapiProject
} from '@/remote';
import { KdevIconFont } from '@/business/commonComponents';
import { common_system_help02 } from '@kid/enterprise-icon/icon/output/icons';
import SelectRepoList from '@/business/SelectRepoList/SelectRepoList';
import DepartmentGroupSelect from '../DepartmentGroupSelect';
import ProjectNameSelect from '../ProjectNameSelect';
import { ApiManageProjectInfoContext, ApiManageProjectInfoContextType } from '../ProjectInfo/ProjectInfo';

export interface ImportKapiProjectRef {
    getParams(): any;
    reset(): void;
    check(): boolean;
    setFormData(data: {
        group: number | null;
        projectId: number | null;
    }): void;
    open(): void;
    close(): void;
}

interface ImportKapiProjectProps {
    onSuccess?: () => void;
    currentGroupId: number;
}

export const ImportKapiProject = forwardRef((props: ImportKapiProjectProps, ref: Ref<ImportKapiProjectRef>) => {
    const { onSuccess, currentGroupId } = props;
    const [isOpen, setIsOpen] = useState(false);
    const [createProjectLoading, setCreateProjectLoading] = useState(false);

    // 表单数据状态
    const [group, setGroup] = useState<number | null>(null);
    const [projectId, setProjectId] = useState<number | null>(null);
    const [repoArr, setRepoArr] = useState<nsMockManageApiManageMainApiProgram.BindProjectType[]>([]);



    // 当部门分组变化时，重置项目选择
    useEffect(() => {
        if (!group) {
            setProjectId(null);
        }
    }, [group]);

    const reset = () => {
        setGroup(null);
        setProjectId(null);
    };

    const check = () => {
        // 这里只简单校验，实际可根据需求完善
        if (!group) {
            message.error('请选择部门分组');
            return false;
        }
        if (!projectId) {
            message.error('请选择项目名称');
            return false;
        }
        return true;
    };

    const setFormData = (data: {
        targetDir: number | null;
        group: number | null;
        projectId: number | null;
        checkedKeys?: React.Key[];
        programId: number;
    }) => {
        setGroup(data.group || 0);
        setProjectId(data.projectId || 0);

    };

    const getParams = () => ({
        group,
        projectId,
    });

    const handleCreateModalOk = () => {
        if (!check()) {
            return;
        }

        setCreateProjectLoading(true);

        // 这里是模拟API调用，实际项目中应该调用真实的API
        // 导入KAPI项目
        try {
            // 实际调用API的代码
            nsMockManageApiManageMainApiProgramImportKapiProject.remote({
                projectId: projectId!,
                targetGroupId: currentGroupId!,
                bindProjects: repoArr,
            });

            // 成功后重置表单并关闭弹窗
            reset();
            setIsOpen(false);
            onSuccess?.();
            message.success('开始导入，完成后通过KIM通知');
        } catch (error) {
            message.error('导入KAPI项目失败');
        } finally {
            setCreateProjectLoading(false);
        }
    };

    const handleCreateModalCancel = () => {
        setIsOpen(false);
        reset();
    };

    useImperativeHandle(ref, () => ({
        getParams,
        reset,
        check,
        setFormData,
        open: () => setIsOpen(true),
        close: () => setIsOpen(false)
    }));

    return (
        <Modal
            title={<span className={css.title}>
                导入 KAPI 项目
                <span className={css.subTitle}>（导入并新建项目）</span>
            </span>}
            open={isOpen}
            onCancel={handleCreateModalCancel}
            onOk={handleCreateModalOk}
            okButtonProps={{ loading: createProjectLoading }}
            width="480px"
        >
            <div>
                <div className={css.importKapiContentItem}>
                    <span
                        className={classnames(css.required, css.importKapiContentItemTitle)}>部门分组</span>
                    <DepartmentGroupSelect
                        value={group}
                        onChange={setGroup}
                    />
                </div>
                <div className={css.importKapiContentItem}>
                    <span
                        className={classnames(css.required, css.importKapiContentItemTitle)}>项目名称</span>
                    <ProjectNameSelect
                        value={projectId}
                        onChange={(value) => setProjectId(value as number | null)}
                        groupId={group}
                    />
                </div>
                <span className={css.gitRepo}>关联 git 仓库
                    <Tooltip
                        overlayStyle={{ whiteSpace: 'nowrap', maxWidth: 'none' }}
                        title="支持比对「代码仓库」与「项目」中 API 的差异，并进行一致性校验提示"
                    >
                        <span className={css.tip}>
                            <KdevIconFont id={common_system_help02} />
                        </span>
                    </Tooltip>
                </span>
                <div className={css.addRepoBtn}>
                    <SelectRepoList repoList={repoArr} onChange={(repoList) => {
                        setRepoArr(repoList)
                    }} />
                </div>
            </div>
        </Modal>
    );
});