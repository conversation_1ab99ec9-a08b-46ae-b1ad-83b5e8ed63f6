.required {
    position: relative;
    width: fit-content;

    &::after {
        content: '*';
        color: red;
        position: absolute;
        right: -12px;
        top: 0;
    }
}

.importKapiContentItem {
    margin-bottom: 24px;
    display: flex;
    flex-direction: column;

    &:last-child {
        margin-bottom: 0;
    }

    .importKapiContentItemTitle {
        margin-bottom: 8px;
    }
}

.gitRepo {
    display: flex;
    align-items: center;
    margin-bottom: 8px;

    .tip {
        margin-left: 4px;
        color: #898A8C;
    }
}

.title {
    display: flex;
    align-items: center;

    .subTitle {
        font-size: 12px;
        color: #898A8C;
    }
}