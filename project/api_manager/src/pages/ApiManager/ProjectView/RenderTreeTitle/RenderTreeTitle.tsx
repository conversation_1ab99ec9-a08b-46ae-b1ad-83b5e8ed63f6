import React, { useContext } from 'react';
import { But<PERSON>, Dropdown, Tooltip } from 'antd';
import { KdevIconFont } from '@/business/commonComponents';
import { common_system_folder_expand1, common_system_folder_close_line, common_system_collection, common_system_more, common_system_file_addition, common_system_folder_addition, common_system_delete02, common_system_revise } from '@kid/enterprise-icon/icon/output/icons';
import css from './RenderTreeTitle.less';
import { ApiManageProjectInfoContext, ApiManageProjectInfoContextType } from '../ProjectInfo/ProjectInfo';
import { ERouter } from '@/CONFIG';
import { router } from '@libs/mvvm';
import PopoverEllipsis from '@/business/common/PopoverEllipsis';

export default function RenderTreeTitle(props) {
    const {
        projectId
    } = useContext(ApiManageProjectInfoContext) as ApiManageProjectInfoContextType;
    const { nodeData, expandedKeys, onCreateCatalog, onEditCatalog,
        onDeleteCatalog, onDeleteApi, searchSelectedKey } = props;
    return <div className={css.title}>
        {
            nodeData.type === 'CATALOG' ? <KdevIconFont
                style={{ color: '#898A8C', marginRight: '4px' }}
                id={
                    expandedKeys.includes(nodeData.key) ?
                        common_system_folder_expand1 : common_system_folder_close_line
                }
            /> : null
        }

        <PopoverEllipsis title={nodeData.title}>
            {/* <span className={css.treeNodeName} dangerouslySetInnerHTML={{
                __html: nodeData.title
            }} /> */}
            <span className={css.treeNodeName} dangerouslySetInnerHTML={{
                __html: props.searchSelectedKey ? nodeData.title.replace(
                    new RegExp(props.searchSelectedKey, 'gi'),
                    (text) => '<span style="color: #326BFB">' + text + '</span>'
                ) : nodeData.title
            }} />
        </PopoverEllipsis>
        <div className={css.options}>
            {/* <KdevIconFont
                className={css.collection}
                style={{ color: '#898A8C' }}
                id={common_system_collection}
            /> */}

            <Dropdown menu={{
                items: [
                    nodeData.type === 'CATALOG' ? {
                        key: 'edit',
                        label: <span className={css.w100} onClick={(e) => {
                            e.stopPropagation();
                            onEditCatalog(nodeData.name);
                        }}>
                            <KdevIconFont
                                style={{ color: '#898A8C', marginRight: '4px' }}
                                id={common_system_revise}
                            />
                            编辑
                        </span>,
                    } : null,
                    nodeData.type === 'CATALOG' ? {
                        key: '1',
                        label: <span className={css.w100} onClick={(e) => {
                            e.stopPropagation();
                            localStorage.setItem('REJECT_SELECT', nodeData.key);
                            router.push(ERouter.API_MOCK_REPO_EDIT_API,
                                {
                                    projectCatalogId: nodeData.id, editType: 'new', projectId
                                });
                        }}>
                            <KdevIconFont
                                style={{ color: '#898A8C', marginRight: '4px' }}
                                id={common_system_file_addition}
                            />
                            新建 API 接口
                        </span>,
                    } : null,
                    nodeData.type === 'CATALOG' ? {
                        key: '2',
                        label: <span className={css.w100} onClick={(e) => {
                            e.stopPropagation();
                            onCreateCatalog(nodeData.name);
                        }}>
                            <KdevIconFont
                                style={{ color: '#898A8C', marginRight: '4px' }}
                                id={common_system_folder_addition}
                            />
                            新建子目录
                        </span>,
                    } : null,
                    {
                        key: '3',
                        label: <span className={css.w100} onClick={(e) => {
                            e.stopPropagation();
                            nodeData.type === 'CATALOG' ? onDeleteCatalog() : onDeleteApi();
                        }}>
                            <KdevIconFont
                                style={{ color: '#898A8C', marginRight: '4px' }}
                                id={common_system_delete02}
                            />
                            删除
                        </span>,
                    },
                ]
            }}>
                <Button
                    type="link"
                    size="small"
                    icon={
                        <KdevIconFont
                            className={css.more}
                            id={common_system_more}
                            style={{ color: '#898A8C' }}
                        />
                    }
                    className={css.groupMoreOperateBtn}
                />
            </Dropdown>

        </div>
    </div>
}
