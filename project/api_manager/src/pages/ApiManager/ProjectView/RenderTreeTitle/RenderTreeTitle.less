.title {
    display: flex;
    align-items: center;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    width: 100%;
    box-sizing: border-box;
    position: relative;
    line-height: 22px;
    padding: 0;

    &:hover {
        .options {
            .more {
                display: block;
            }

            .collection {
                display: block;
            }
        }
    }

    .titleText {
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
        display: inline-block;
        width: 100%;
    }

    .options {
        display: flex;
        align-items: center;
        gap: 8px;
        flex-shrink: 0;

        .more {
            font-size: 16px;
            display: none;

            &:hover {
                color: #326BFB !important;
            }
        }

        .collection {
            font-size: 16px;
            display: none;
        }
    }
}

.treeNodeName {
    flex: 1;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    font-weight: 400 !important;
}

.w100 {
    width: 100%;
    display: flex;
    align-items: center;
}