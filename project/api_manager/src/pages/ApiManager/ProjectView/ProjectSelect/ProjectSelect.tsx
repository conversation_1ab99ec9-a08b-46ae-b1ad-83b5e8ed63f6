import React, { useEffect, useState } from 'react';
import { Select, Spin } from 'antd';
import { nsMockManageApiManageMainApiProgramList, nsMockManageApiManageMainApiProgramSearch } from '@/remote';
import { debounce } from 'lodash';

interface IProjectSelectProps {
    value?: number;
    onChange?: (value: number) => void;
    placeholder?: string;
    style?: React.CSSProperties;
    className?: string;
    disabled?: boolean;
    name?: string;
}

function ProjectSelect(props: IProjectSelectProps) {
    const [loading, setLoading] = useState<boolean>(false);
    const [projectList, setProjectList] = useState<nsMockManageApiManageMainApiProgramSearch.ResponseItem[]>([]);
    // 获取项目列表
    const getProjectList = async (searchText: string = '') => {
        setLoading(true);
        try {
            if (searchText) {
                // 使用搜索接口
                const filters = [
                    {
                        type: 'fuzzy' as const,
                        field: 'name',
                        value: searchText
                    }
                ];
                const res = await nsMockManageApiManageMainApiProgramSearch.remote({
                    pageNumber: 1,
                    pageSize: 50,
                    filters
                });
                setProjectList(res.items || []);
            } else {
                // // 使用列表接口
                // const res = await nsMockManageApiManageMainApiProgramList.remote({
                //     pageNumber: 1,
                //     pageSize: 50,
                //     groupId: 0 // 默认组ID，可以根据需要调整
                // });
                // setProjectList(res.items || []);
            }
            setLoading(false);
        } catch (error) {
            console.error('获取项目列表失败', error);
            setLoading(false);
        }
    };
    useEffect(() => {
        getProjectList(props.name);
    }, [props.name]);

    // 搜索项目
    const handleSearch = debounce((value: string) => {
        getProjectList(value);
    }, 300);

    // 初始化加载项目列表
    useEffect(() => {
        getProjectList();
    }, []);

    // 当value变化时，查找对应的项目
    useEffect(() => {
        if (props.value && projectList.length === 0) {
            // 如果有value但没有项目列表，重新获取项目列表
            getProjectList();
        }
    }, [props.value, projectList]);

    // 处理选择变化
    const handleChange = (value: number) => {
        props.onChange && props.onChange(value);
    };

    // 将项目列表转换为Select选项
    const options = projectList.map(item => ({
        label: item.name,
        value: item.id
    }));

    return (
        <Select
            showSearch
            value={props.value}
            placeholder={props.placeholder || '请选择项目'}
            style={{ ...props.style }}
            className={props.className}
            loading={loading}
            disabled={props.disabled}
            filterOption={false}
            onSearch={handleSearch}
            onChange={handleChange}
            notFoundContent={loading ? <Spin size="small" /> : null}
            options={options}
            allowClear
        />
    );
}

export { ProjectSelect };
