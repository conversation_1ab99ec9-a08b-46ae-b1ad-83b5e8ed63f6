import React, { useState, createContext, useEffect, useCallback, useMemo, useRef } from 'react';
import css from './ProjectView.less';
import { ProjectViewList } from './ProjectViewList/ProjectViewList';


// 上下文类型
export interface ApiManageProjectViewContextType {

}

// 上下文
export const ApiManageProjectViewContext = createContext<ApiManageProjectViewContextType>({});

export interface PropsType {

}
export function ApiManageProjectView(props: PropsType) {
    const [] = useState(false);


    return (
        <ApiManageProjectViewContext.Provider
            value={{

            }}
        >
            <ProjectViewList />

        </ApiManageProjectViewContext.Provider>
    )
}
