import React, { useState, useImperativeHandle, forwardRef, Ref } from 'react';
import { Input, message, Button, Tooltip } from 'antd';
import { KdevIconFont } from '@/business/commonComponents';
import { common_system_add, common_system_help02 } from '@kid/enterprise-icon/icon/output/icons';
import css from './CreateProject.less';
import classnames from 'classnames';
import { RecentRepoSelectWithGroup } from '@/business/recentRepoSelector/RecentRepoSelectWithGroup';
import { nsMockManageApiManageMainApiProgram } from '@/remote/mock/manage/apiManage/main/api/program';
import SelectRepoList from '@/business/SelectRepoList/SelectRepoList';

export interface CreateProjectRef {
    getParams(): any;
    reset(): void;
    check(): boolean;
    setFormData(data: {
        name: string;
        desc: string;
        bindProjects: nsMockManageApiManageMainApiProgram.BindProjectType[]
    }): void;
}

export const CreateProject = forwardRef((props, ref: Ref<CreateProjectRef>) => {
    const [projectName, setProjectName] = useState<string>('');
    const [projectDesc, setProjectDesc] = useState<string>('');
    const [gitRepos, setGitRepos] = useState<any[]>([]);
    const [repoArr, setRepoArr] = useState<nsMockManageApiManageMainApiProgram.BindProjectType[]>([]);

    useImperativeHandle(ref, () => ({
        getParams: () => {
            return {
                name: projectName,
                desc: '',
                bindProjects: repoArr
            }
        },
        reset: () => {
            setProjectName('');
            setProjectDesc('');
            setGitRepos([]);
            setRepoArr([]);
        },
        check: () => {
            if (!projectName) {
                message.error('请输入项目名称');
                return false;
            }
            return true;
        },
        setFormData: (data) => {
            setProjectName(data.name);
            setProjectDesc(data.desc);
            setRepoArr(data.bindProjects);
        }
    }));

    return (
        <div className={css.createProject}>
            <span className={classnames(css.projectName, css.required)}>项目名称</span>
            <Input
                className={css.projectNameInput}
                placeholder="请输入"
                value={projectName}
                onChange={(e) => {
                    setProjectName(e.target.value);
                }}
            />
            <span className={css.gitRepo}>关联 git 仓库
                <Tooltip
                    overlayStyle={{ whiteSpace: 'nowrap', maxWidth: 'none' }}
                    title="支持比对「代码仓库」与「项目」中 API 的差异，并进行一致性校验提示"
                >
                    <span className={css.tip}>
                        <KdevIconFont id={common_system_help02} />
                    </span>
                </Tooltip>
            </span>
            <div className={css.addRepoBtn}>
                <SelectRepoList repoList={repoArr.map((item) => ({
                    projectId: item.projectId,
                    projectName: item.projectName,
                    sshUrlToRepo: item.projectSsh
                }))} onChange={(repoList) => {
                    setRepoArr(repoList.map((item) => ({
                        projectId: item.projectId,
                        projectSsh: item.sshUrlToRepo,
                        projectName: item.projectName
                    })))
                }} />
            </div>

        </div>
    )
});

