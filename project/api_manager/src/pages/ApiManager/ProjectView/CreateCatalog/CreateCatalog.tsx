import React, { useState, useImperativeHandle, forwardRef, Ref, useEffect } from 'react';
import { Input, message, Button, Tooltip, TreeSelect } from 'antd';
import { KdevIconFont } from '@/business/commonComponents';
import { common_system_add, common_system_help02 } from '@kid/enterprise-icon/icon/output/icons';
import css from './CreateCatalog.less';
import classnames from 'classnames';
import { RecentRepoSelectWithGroup } from '@/business/recentRepoSelector/RecentRepoSelectWithGroup';
import { nsMockManageApiManageMainApiProgram } from '@/remote/mock/manage/apiManage/main/api/program';
import SelectRepoList from '@/business/SelectRepoList/SelectRepoList';

export interface CreateCatalogRef {
    getParams(): any;
    reset(): void;
    check(): boolean;
    setFormData(data: {
        name: string;
        parantId: number;
        parantName: string;
        programId?: number;
    }): void;
}

export const CreateCatalog = forwardRef((props, ref: Ref<CreateCatalogRef>) => {
    const [catalogName, setCatalogName] = useState<string>('');
    const [programId, setProgramId] = useState<number>(0);
    const [parantId, setParantId] = useState<number>(0);
    const [parantName, setParantName] = useState<string>(''); // 新增的 useState
    const [treeData, setTreeData] = useState<any[]>([]);

    useImperativeHandle(ref, () => ({
        getParams: () => {
            return {
                name: catalogName,
                parantId: parantId
            }
        },
        reset: () => {
            setCatalogName('');
        },
        check: () => {
            if (!catalogName) {
                message.error('请输入目录名称');
                return false;
            }
            return true;
        },
        setFormData: (data) => {
            setCatalogName(data.name);
            setParantId(data.parantId);
            setParantName(data.parantName); // 新增的 setProjectName
            setProgramId(data.programId || 0);
        }
    }));

    useEffect(() => {
        if (parantId !== 0) {
            setTreeData([
                {
                    value: parantId,
                    label: parantName || '未知项目',
                    isLeaf: true,
                    disabled: true
                }
            ]);
        } else {
            setTreeData([]);
        }
    }, [parantId, parantName]);

    return (
        <div className={css.createProject}>

            {
                parantId !== 0 ? <><span className={classnames(css.projectName, css.required)}>所属目录</span> <TreeSelect
                    showSearch
                    style={{
                        width: '100%',
                        marginBottom: '24px'
                    }}
                    value={parantId}
                    dropdownStyle={{ maxHeight: 400, overflow: 'auto' }}
                    placeholder="Please select"
                    allowClear
                    treeDefaultExpandAll
                    disabled={true}
                    onChange={(value) => {
                        setParantId(value);
                    }}
                    treeData={treeData}
                /></> : null
            }

            <span className={classnames(css.projectName, css.required)}>目录名称</span>
            <Input
                className={css.projectNameInput}
                placeholder="请输入"
                value={catalogName}
                onChange={(e) => {
                    setCatalogName(e.target.value);
                }}
            />
        </div>
    )
});
