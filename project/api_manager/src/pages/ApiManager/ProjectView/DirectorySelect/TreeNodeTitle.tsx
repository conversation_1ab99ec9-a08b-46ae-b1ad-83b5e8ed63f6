import React from 'react';
import { KdevIconFont } from '@/business/commonComponents';
import { common_system_folder_expand1, common_system_folder_close_line } from '@kid/enterprise-icon/icon/output/icons';
import css from './DirectorySelect.less';

interface TreeNodeTitleProps {
    nodeData: any;
    expandedKeys: React.Key[];
}

const TreeNodeTitle: React.FC<TreeNodeTitleProps> = ({ nodeData, expandedKeys }) => {
    return (
        <div className={css.treeNodeTitle}>
            {
                nodeData.type === 'CATALOG' ? (
                    <KdevIconFont
                        style={{ color: '#898A8C', marginRight: '4px' }}
                        id={
                            expandedKeys.includes(nodeData.key)
                                ? common_system_folder_expand1
                                : common_system_folder_close_line
                        }
                    />
                ) : null
            }
            <span className={css.titleText}>{nodeData.title}</span>
        </div>
    );
};

export default TreeNodeTitle;
