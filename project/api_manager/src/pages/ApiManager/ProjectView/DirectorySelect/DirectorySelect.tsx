import React, { useEffect, useState } from 'react';
import { TreeSelect, Spin, Empty, Tree, Divider } from 'antd';
import { TreeSelectProps } from 'antd/lib/tree-select';
import {
    nsMockManageApiManageMainApiProgramCatalogRootList,
    nsMockManageApiManageMainApiProgramCatalogSubList,
    nsMockManageApiManageMainApiProgramItemsSearch
} from '@/remote';
import { debounce } from 'lodash';
import css from './DirectorySelect.less';
import TreeNodeTitle from './TreeNodeTitle';

interface IDirectorySelectProps extends Omit<TreeSelectProps<number>, 'onChange'> {
    programId?: number; // 项目ID
    onChange?: (value: number | undefined) => void;
    openCreateCatalogModal?: (parentId: number, parentName: string) => void; // 打开创建目录弹窗的函数
    dropdownFooter?: React.ReactNode; // 下拉框底部插槽，用于自定义底部内容
}

// 格式化目录列表数据
export function formatCatalogList(
    catalogList: any[],
    baseName: string = '-',
    catalogMap: Record<string, any> = {},
    isSearch: boolean = false
) {
    const formattedList = catalogList.map(item => {
        const formattedItem = {
            ...item,
            key: (item.type || 'CATALOG') + baseName + item.id,
            title: item.name,
            value: item.id,
            children: item.children
                ? formatCatalogList(item.children || [], baseName + item.id + '-', catalogMap, isSearch)
                : [],
            isLeaf: isSearch ? !item.children?.length : item.type === 'API',
            hasChild: item.hasChild === undefined ? true : item.hasChild,
            selectable: item.type === 'CATALOG' // 只允许选择目录节点
        };
        // 将当前节点添加到 catalogMap
        if (catalogMap) {
            catalogMap[formattedItem.key] = formattedItem;
        }
        return formattedItem;
    });
    return formattedList;
}

function DirectorySelect({ programId, ...props }: IDirectorySelectProps) {
    const [treeData, setTreeData] = useState<any[]>([]);
    const [loading, setLoading] = useState<boolean>(false);
    const [expandedKeys, setExpandedKeys] = useState<React.Key[]>([]);
    const [searchValue, setSearchValue] = useState<string>('');
    const [catalogMap, setCatalogMap] = useState<Record<string, any>>({});
    const [value, setValue] = useState<number | undefined>(props.value);
    const [open, setOpen] = useState<boolean>(false);

    // 获取根目录列表
    const getRootCatalogList = async (locationId?: number) => {
        if (!programId) {
            setTreeData([]);
            setExpandedKeys([]);
            return;
        }

        setLoading(true);
        try {
            const res = await nsMockManageApiManageMainApiProgramCatalogRootList.remote({
                programId,
                locationId,
                locationType: 'CATALOG'
            });

            // 过滤出目录类型的节点
            const catalogNodes = res.filter(item => item.type === 'CATALOG');

            // 使用formatCatalogList格式化数据
            const newCatalogMap: Record<string, any> = {};
            const formattedData = formatCatalogList(catalogNodes, '-', newCatalogMap);

            setCatalogMap(newCatalogMap);
            setTreeData(formattedData);

            // 如果有选中的值，尝试找到并展开对应的父节点
            if (props.value) {
                // 查找包含该值的节点
                const findNodePath = (nodes: any[], nodeValue: number, path: string[] = []): string[] | null => {
                    for (const node of nodes) {
                        if (node.value === nodeValue) {
                            return [...path, node.key];
                        }
                        if (node.children && node.children.length > 0) {
                            const result = findNodePath(node.children, nodeValue, [...path, node.key]);
                            if (result) {
                                return result;
                            }
                        }
                    }
                    return null;
                };

                const nodePath = findNodePath(formattedData, props.value);
                if (nodePath && nodePath.length > 0) {
                    // 展开除了最后一个节点（选中的节点）之外的所有父节点
                    setExpandedKeys(nodePath.slice(0, -1));
                }
            }

            setLoading(false);
        } catch (error) {
            setLoading(false);
        }
    };

    // 获取子目录列表
    const loadSubCatalogList = async (node: any) => {
        const catalogId = node.id;

        try {
            const res = await nsMockManageApiManageMainApiProgramCatalogSubList.remote({
                id: catalogId
            });

            // 过滤出目录类型的节点
            const catalogNodes = res.filter(item => item.type === 'CATALOG');

            // 使用formatCatalogList格式化数据
            const baseKey = node.key.replace('CATALOG-', '');
            const newCatalogMap = { ...catalogMap };
            const children = formatCatalogList(catalogNodes, `-${baseKey}-`, newCatalogMap);

            // 更新树数据
            setTreeData(origin => updateTreeData(origin, node.key, children));
            setCatalogMap(newCatalogMap);

            // 自动展开当前节点
            setExpandedKeys(prevKeys => {
                if (!prevKeys.includes(node.key)) {
                    return [...prevKeys, node.key];
                }
                return prevKeys;
            });

            return children;
        } catch (error) {
            return [];
        }
    };

    // 搜索目录
    const searchCatalog = async (searchText: string) => {
        if (!programId || !searchText) {
            getRootCatalogList(); // 如果搜索词为空，恢复显示根目录
            setExpandedKeys([]); // 重置展开状态
            return;
        }

        setLoading(true);
        try {
            const res = await nsMockManageApiManageMainApiProgramItemsSearch.remote({
                programId,
                key: searchText
            });

            // 过滤出目录类型的节点
            const catalogNodes = res.filter(item => item.type.toUpperCase() === 'CATALOG');

            // 使用formatCatalogList格式化数据
            const newCatalogMap: Record<string, any> = {};
            const formattedData = formatCatalogList(catalogNodes, '-', newCatalogMap, true);

            // 搜索结果默认全部展开
            const allKeys: React.Key[] = [];
            const collectKeys = (nodes: any[]) => {
                nodes.forEach(node => {
                    if (node.children && node.children.length > 0) {
                        allKeys.push(node.key);
                        collectKeys(node.children);
                    }
                });
            };
            collectKeys(formattedData);

            setCatalogMap(newCatalogMap);
            setTreeData(formattedData);
            setExpandedKeys(allKeys); // 设置所有节点展开
            setLoading(false);
        } catch (error) {
            setLoading(false);
        }
    };

    // 更新树数据的辅助函数
    const updateTreeData = (list: any[], key: string, children: any[]): any[] => {
        return list.map(node => {
            if (node.key === key) {
                return {
                    ...node,
                    children
                };
            }
            if (node.children) {
                return {
                    ...node,
                    children: updateTreeData(node.children, key, children)
                };
            }
            return node;
        });
    };

    // 异步加载数据
    const onLoadData = (node: any) => {
        return new Promise<void>(resolve => {
            loadSubCatalogList(node).then(() => {
                resolve();
            });
        });
    };



    // 搜索处理
    const onSearch = debounce((searchText: string) => {
        if (!open) {
            return
        }
        setSearchValue(searchText);
        searchCatalog(searchText);
    }, 300);

    // 当项目ID变化时，重新获取根目录列表
    useEffect(() => {
        getRootCatalogList(props.value);
        setSearchValue('');
        setExpandedKeys([]); // 重置展开状态
    }, [programId]);



    // 自定义下拉菜单渲染
    const dropdownRender = (_: React.ReactElement) => {
        if (treeData.length === 0 && !searchValue) {
            return (
                <div>
                    <Empty
                        image={Empty.PRESENTED_IMAGE_SIMPLE}
                        description={'暂无目录数据'}
                        className={css.kEmpty}
                    />
                    {props.dropdownFooter && (
                        <>
                            <Divider style={{ margin: '8px 0' }} />
                            {props.dropdownFooter}
                        </>
                    )}
                </div>
            );
        }

        if (searchValue && treeData.length === 0) {
            return (
                <Empty
                    image={Empty.PRESENTED_IMAGE_SIMPLE}
                    description={'未找到匹配的目录'}
                    className={css.kEmpty}
                />
            );
        }

        return (
            <div>
                <Tree.DirectoryTree
                    treeData={treeData}
                    className={css.repoDirectoryTree}
                    icon={false}
                    autoExpandParent={false}
                    expandedKeys={expandedKeys}
                    loadedKeys={expandedKeys}
                    onExpand={(keys) => setExpandedKeys(keys)}
                    loadData={onLoadData}
                    onSelect={(keys) => {
                        if (keys.length > 0) {
                            const selectedKey = keys[0] as string;
                            const keyParts = selectedKey.split('-');
                            const id = parseInt(keyParts[keyParts.length - 1], 10);
                            setValue(id);
                            props.onChange && props.onChange(id);
                            // 关闭下拉框
                            setOpen(false);
                        }
                    }}
                    titleRender={(nodeData) => (
                        <TreeNodeTitle
                            nodeData={nodeData}
                            expandedKeys={expandedKeys}
                        />
                    )}
                />
                {props.dropdownFooter && (
                    <>
                        <Divider style={{ margin: '8px 0' }} />
                        {props.dropdownFooter}
                    </>
                )}
            </div>
        );
    };

    return (
        <Spin spinning={loading}>
            <TreeSelect
                treeData={treeData}
                placeholder={props.placeholder || '请选择目录'}
                allowClear
                showSearch
                value={value}
                open={open}
                onDropdownVisibleChange={(v) => {
                    setOpen(v);
                    if (v) {
                        getRootCatalogList(value);
                        setExpandedKeys([]);
                        setValue(undefined);
                    }
                }}
                popupClassName={css.directorySelectDropdown}
                dropdownStyle={{ padding: 0 }}
                onSearch={onSearch}
                dropdownRender={dropdownRender}
                {...props}
            />
        </Spin>
    );
}

export { DirectorySelect };
