@import '~@lynx/design-token/dist/less/token.less';

.directorySelectDropdown {
  padding: 0 !important;
  min-width: 300px !important;
}

.repoDirectoryTree {
  padding-left: 0;

  :global {
    .ant-tree-treenode {
      padding: 0;
    }

    .ant-tree-list-scrollbar {
      width: 6px !important;
      right: -6px !important;
    }

    .ant-tree-list-scrollbar-thumb {
      background-color: @color-bg-tertiary !important;
      border-radius: 6px !important;
    }

    .ant-tree-treenode {
      height: 38px;
      display: flex;
      align-items: center;
      border-radius: 4px;
      color: @color-text-primary;
    }

    .ant-tree-treenode::before {
      bottom: 0;
      border-radius: 4px;
      height: 38px;
    }

    .ant-tree-treenode:hover::before {
      background-color: @color-bg-basic-hover;
    }

    .ant-tree-node-content-wrapper.ant-tree-node-selected {
      color: @color-text-brand !important;
    }

    .ant-tree-title {
      display: flex;
      align-items: center;
      width: 100%;
      height: 100%;
      cursor: pointer;
    }

    .ant-tree-node-content-wrapper {
      overflow: hidden;
      padding: 0 0 0 4px;
      height: 100%;
    }

    .ant-tree-switcher {
      width: 16px !important;
      height: 32px;
      display: flex;
      align-items: center !important;
      align-self: center !important;
      padding-left: 4px;
      margin-top: -1px;
    }

    .ant-tree-switcher .ant-tree-switcher-icon {
      font-size: 14px;
      color: @color-text-icon-secondary;
    }

    .ant-tree-indent-unit {
      width: 16px;
    }

    .ant-tree-treenode-selected {
      &::before {
        background-color: #F0F4FF !important;
      }

      .svg-common_system_folder_close_line,
      .svg-common_system_auto_import_folder {
        color: @color-bg-brand !important;
      }
    }
  }
}

.treeNodeTitle {
  display: flex;
  align-items: center;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  width: 100%;
  box-sizing: border-box;
  position: relative;
  line-height: 22px;
  padding: 0;

  .titleText {
    flex: 1;
    overflow: hidden;
    text-overflow: ellipsis;
  }
}

.kEmpty {
  padding: 20px 0;
}
