import React, { useEffect } from 'react'
import css from './EnvSettings.less'
import { EnvMgr } from '@/pages/apiTemplate/envMgr/EnvMgr'
import { EnvMgrM } from '@/pages/apiTemplate/envMgr/EnvMgrM'
import { ApiTemplatePageVM } from '@/pages/apiTemplate/ApiTemplatePageVM'
import { getUrlSearch } from '@/index.config/tools'

const EnvSettings: React.FC = () => {
    // 创建一个简单的ApiTemplatePageVM实例
    const params = getUrlSearch(['projectId']) as { projectId: number }
    const apiTemplatePageVM = new ApiTemplatePageVM({ dir: params.projectId || 0, viewType: 'project' })

    // 使用ApiTemplatePageVM实例初始化EnvMgrM
    const envMgrM = new EnvMgrM(apiTemplatePageVM)

    // 组件挂载时初始化加载数据
    useEffect(() => {
        // 如果需要的话，这里可以设置一些初始化参数
        envMgrM.initLoading()
    }, [])

    return (
        <div className={css.container}>
            <div className={css.header}>
                <h2>环境设置</h2>
            </div>
            <EnvMgr model={envMgrM} addBtnIsGhost={true} />
        </div>
    )
}

export default EnvSettings 