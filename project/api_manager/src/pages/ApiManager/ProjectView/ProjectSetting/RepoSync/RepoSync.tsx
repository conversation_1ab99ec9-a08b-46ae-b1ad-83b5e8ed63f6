import React from 'react'
import css from './RepoSync.less'
import { Button, Switch } from 'antd'

const RepoSync: React.FC = () => {
    return (
        <div className={css.container}>
            <h2>仓库同步设置</h2>
            <div className={css.content}>
                <div className={css.autoImport}>
                    <div className={css.left}>

                        <div className={css.title}>API 自动录入</div>
                        <div className={css.description}>如关联仓库中自动扫描的接口在当前项目中不存在，自动将对应接口新增录入到项目中。</div>
                    </div>
                    <Switch defaultChecked />
                </div>
                <div className={css.consistencyCheck}>
                    <div className={css.left}>

                        <div className={css.title}>API 一致性校验</div>
                        <div className={css.description}>
                            如关联仓库中自动扫描的接口定义/类型等，与项目中同path接口定义不一致时，通过「页面提示」&「kim通知」的方式提示给接口相关人员。
                        </div>
                    </div>
                    <Switch defaultChecked />
                </div>
            </div>
            <div className={css.buttonContainer}>
                <Button
                    type="primary"
                >
                    保存
                </Button>
            </div>
        </div>
    )
}

export default RepoSync 