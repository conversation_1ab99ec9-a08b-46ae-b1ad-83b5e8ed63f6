.container {
    display: flex;
    flex-direction: column;
    height: 100%;
    width: 100%;
}

.header {
    height: 52px;
    width: 100%;
    background-color: #fff;
    border-bottom: 1px solid #e8e8e8;
    display: flex;
    align-items: center;
    padding: 0 20px;
}

.backBtn {
    display: flex;
    align-items: center;
    cursor: pointer;
    margin-right: 12px;
}

.line {
    width: 1px;
    height: 14px;
    background-color: #D5D6D9ff;
    margin-right: 12px;
}

.backText {
    margin-left: 8px;
    font-size: 16px;
    color: #252626;
    font-weight: 500;
}

.projectName {
    font-size: 16px;
    font-weight: 500;
    color: #252626;
}

.content {
    display: flex;
    flex: 1;
    overflow: hidden;
}

.sidebar {
    width: 198px;
    background-color: #fff;
    border-right: 1px solid #e8e8e8;
    overflow-y: auto;
}

.menuContainer {
    padding: 16px 20px;
}

.parentMenu {
    height: 38px;
    line-height: 38px;
    font-size: 14px;
    color: #898A8Cff;
    cursor: default;
}

.subMenu {
    height: 38px;
    line-height: 38px;
    padding: 0 12px 0 12px;
    font-size: 14px;
    color: #252626;
    cursor: pointer;
    transition: all 0.3s;
    border-radius: 4px;
}

.subMenu:hover {
    background: var(---bg_hover, #F5F7FA);
}

.subMenu.active {
    background: var(---B100, #F0F7FF);
    color: #327DFF;
}

.mainContent {
    flex: 1;
    // padding: 16px 20px;
    overflow-y: auto;
    background-color: #fff;
}