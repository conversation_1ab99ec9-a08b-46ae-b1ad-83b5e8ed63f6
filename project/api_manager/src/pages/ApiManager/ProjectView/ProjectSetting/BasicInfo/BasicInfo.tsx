import React, { useContext, useEffect, useState, useRef } from 'react'
import css from './BasicInfo.less'
import { Input, Button, Tooltip, message } from 'antd'
import { KdevIconFont } from '@/business/commonComponents'
import { common_system_help02 } from '@kid/enterprise-icon/icon/output/icons'
import classNames from 'classnames'
import SelectRepoList from '@/business/SelectRepoList/SelectRepoList'
import { nsMockManageApiManageMainApiProgram } from '@/remote/mock/manage/apiManage/main/api/program'
import { getUrlSearch } from '@/index.config/tools'
import { ApiManageProjectInfoContext } from '../ProjectSetting'

const { TextArea } = Input

const BasicInfo: React.FC = () => {
    const [loading, setLoading] = useState<boolean>(false)
    const [projectId, setProjectId] = useState<number | null>(null);
    const { projectInfo, setProjectInfo } = useContext(ApiManageProjectInfoContext);
    const initialProjectInfo = useRef<any>(null);

    useEffect(() => {
        const params = getUrlSearch(['projectId']) as { projectId: number };
        if (params.projectId) {
            setProjectId(params.projectId);
        }
    }, []);

    // 保存初始 projectInfo
    useEffect(() => {
        if (projectInfo && !initialProjectInfo.current) {
            // 深拷贝，避免后续被修改
            initialProjectInfo.current = JSON.parse(JSON.stringify(projectInfo));
        }
    }, [projectInfo]);

    // 判断是否有改动
    const isChanged = (() => {
        if (!projectInfo || !initialProjectInfo.current) return false;
        // name/desc 直接对比
        if (projectInfo.name !== initialProjectInfo.current.name) return true;
        if (projectInfo.desc !== initialProjectInfo.current.desc) return true;
        // bindProjects 只对比 projectId 数组
        const getIds = (arr: any[] = []) => arr.map(i => i.projectId).sort();
        const a = getIds(projectInfo.bindProjects);
        const b = getIds(initialProjectInfo.current.bindProjects);
        if (a.length !== b.length) return true;
        for (let i = 0; i < a.length; i++) {
            if (a[i] !== b[i]) return true;
        }
        return false;
    })();

    const handleSave = async () => {
        if (!projectInfo?.name) {
            message.error('请输入项目名称')
            return
        }

        if (!projectId) {
            message.error('项目ID不存在')
            return
        }

        setLoading(true)
        try {
            if (projectInfo.name.length > 60) {
                message.error('项目名称过长');
                return;
            }
            if (projectInfo.desc.length > 150) {
                message.error('项目描述过长');
                return;
            }
            // 调用更新接口
            await nsMockManageApiManageMainApiProgram.update({
                id: projectId,
                name: projectInfo.name,
                desc: projectInfo.desc,
                bindProjects: projectInfo.bindProjects.map((item) => ({
                    projectId: item.projectId
                }))
            })

            message.success('保存成功');
        } catch (error) {
            console.error(error);
        } finally {
            setLoading(false)
        }
    }

    return (
        <div className={css.container}>
            <h2>基本信息</h2>
            <div className={css.content}>
                <div className={css.formItem}>
                    <div className={css.labelWrapper}>
                        <span className={classNames(css.label, css.required)}>项目名称</span>
                    </div>
                    <div className={css.inputWrapper}>
                        <Input
                            className={css.projectNameInput}
                            placeholder="请输入项目名称"
                            value={projectInfo?.name}
                            onChange={(e) => {
                                if (!projectInfo) return
                                setProjectInfo({ ...projectInfo, name: e.target.value })
                            }}
                        />
                    </div>
                </div>

                <div className={css.formItem}>
                    <div className={css.labelWrapper}>
                        <span className={css.label}>项目描述</span>
                    </div>
                    <div className={css.inputWrapper}>
                        <TextArea
                            className={css.projectDescInput}
                            placeholder="请输入项目描述"
                            value={projectInfo?.desc}
                            onChange={(e) => {
                                if (!projectInfo) return
                                setProjectInfo({ ...projectInfo, desc: e.target.value })
                            }}
                            rows={4}
                        />
                    </div>
                </div>

                <div className={css.formItem}>
                    <div className={css.labelWrapper}>
                        <span className={css.label}>关联 git 仓库</span>
                    </div>
                    <div className={css.inputWrapper}>
                        {/* <Tooltip
                            overlayStyle={{ whiteSpace: 'nowrap', maxWidth: 'none' }}
                            title="支持比对「代码仓库」与「项目」中 API 的差异，并进行一致性校验提示"
                        >
                            <span className={css.tip}>
                                <KdevIconFont id={common_system_help02} />
                            </span>
                        </Tooltip> */}
                        <div className={css.repoSelectContainer}>
                            <SelectRepoList
                                repoList={projectInfo?.bindProjects || []}
                                onChange={(repoList) => {
                                    if (!projectInfo) return
                                    setProjectInfo({ ...projectInfo, bindProjects: repoList })
                                }}
                                showSelectedRepoType="table"
                            />
                        </div>
                    </div>
                </div>

                <div className={css.buttonContainer}>
                    <Button
                        type="primary"
                        onClick={handleSave}
                        loading={loading}
                        disabled={!isChanged}
                    >
                        保存
                    </Button>
                </div>
            </div>
        </div>
    )
}

export default BasicInfo 