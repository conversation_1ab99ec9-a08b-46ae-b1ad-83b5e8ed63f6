.container {
    padding: 16px 20px;

    .header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 24px;

        h2 {
            font-size: 16px;
            font-weight: 500;
            margin-bottom: 24px;
        }
    }

    .newTag {
        cursor: pointer;
        color: #326BFB;
        margin-bottom: 16px;
    }

    .operateBtn {
        color: #326BFB;
        cursor: pointer;
        margin-right: 16px;

        &:last-child {
            margin-right: 0;
        }

        &:hover {
            opacity: 0.8;
        }
    }
}


.content {
    padding: 0;
    border-radius: 4px;
}

.addTag {
    color: #326BFB;
    cursor: pointer;
}