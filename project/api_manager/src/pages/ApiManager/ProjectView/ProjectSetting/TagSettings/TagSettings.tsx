import React, { useEffect, useState } from 'react'
import { But<PERSON>, message, Modal } from 'antd'
import { ExclamationCircleOutlined } from '@ant-design/icons'
import css from './TagSettings.less'
import { TagTable } from '@/pages/groupSetting/tagMgr/TagTable'
import { nsMockManageApiManageMainApiProgramSettingsTag as TagAPI } from '@/remote/mock/manage/apiManage/main/api/program/settings/tag'
import {
    nsMockManageApiManageMainApiProgramSettingsTagList as TagListAPI,
} from '@/remote/mock/manage/apiManage/main/api/program/settings/tag/list'
import { nsMockManageApiManageMainTagSavePOST } from '@/remote'
import { getUrlSearch } from '@/index.config/tools'
import { EditTagModal } from '@/pages/groupSetting/tagMgr/editTag/EditTag'
import { EditTagModalM } from '@/pages/groupSetting/tagMgr/editTag/EditTagM'
import { KdevIconFont, KEmpty } from '@/business/commonComponents'
import { common_system_add } from '@kid/enterprise-icon/icon/output/icons'

const TagSettings = () => {
    const [tagList, setTagList] = useState<nsMockManageApiManageMainTagSavePOST.IReturn[]>([])
    const [loading, setLoading] = useState(false)
    const [editTagModel] = useState(() => {
        const model = new EditTagModalM()
        model.onSaveCallback = () => {
            fetchTagList()
        }
        model.setViewType('project')
        return model
    })

    const params = getUrlSearch(['projectId']) as { projectId: number }

    const fetchTagList = async () => {
        setLoading(true)
        try {
            const response: TagListAPI.ITagItem[] = await TagListAPI.getProgramTagList({
                programId: params.projectId
            })
            // 转换数据格式以匹配 TagTable 所需的类型
            const formattedData = response.map(item => ({
                id: item.id,
                name: item.name,
                desc: item.desc,
                color: item.color,
                groupId: item.groupId,
                groupPath: '',  // 由于原数据中没有这个字段，设为空字符串
                groupName: '',  // 设置默认值
                updateUser: item.updater,  // 使用 updater 作为 updateUser
                distinctUpOrDown: 0  // 设置默认值
            })) as nsMockManageApiManageMainTagSavePOST.IReturn[]
            setTagList(formattedData)

        } catch (error) {
            message.error('获取标签列表失败')
        } finally {
            setLoading(false)
        }
    }

    const handleDelete = (record: nsMockManageApiManageMainTagSavePOST.IReturn) => {
        Modal.confirm({
            title: '确认删除',
            icon: <ExclamationCircleOutlined />,
            content: `确定要删除标签"${record.name}"吗？`,
            onOk: async () => {
                try {
                    const response = await TagAPI.deleteTag({
                        tagId: record.id,
                        programId: params.projectId
                    })

                    message.success('删除成功')
                    fetchTagList()

                } catch (error) {
                    message.error('删除失败')
                }
            }
        })
    }

    const handleEdit = (record: nsMockManageApiManageMainTagSavePOST.IReturn) => {
        editTagModel.onOpen(params.projectId, record)
    }

    const renderCurGroupOperate = (record: nsMockManageApiManageMainTagSavePOST.IReturn) => {
        return (
            <div>
                <span className={css.operateBtn} onClick={() => handleEdit(record)}>
                    编辑
                </span>
                <span className={css.operateBtn} onClick={() => handleDelete(record)}>
                    删除
                </span>
            </div>
        )
    }

    const handleCreateTag = () => {
        editTagModel.onOpen(params.projectId)
    }

    useEffect(() => {
        fetchTagList()
    }, [])

    return (
        <div className={css.container}>
            <div className={css.header}>
                <h2 style={{
                    marginBottom: 0
                }}>标签设置</h2>
                <Button
                    type="primary"
                    ghost
                    icon={<KdevIconFont id={common_system_add} style={{ marginRight: 4 }} />}
                    onClick={handleCreateTag}
                >
                    新建标签
                </Button>
            </div>
            {/* <div className={css.newTag} onClick={handleCreateTag}>
                新建标签
            </div> */}
            <TagTable
                dataSource={tagList}
                loading={loading}
                renderOperate={renderCurGroupOperate}
                showGroupPath={false}
                locale={{
                    emptyText: <KEmpty
                        image="NOMAL_SIMPLE_EMPTY"
                        description={<div>暂无数据，进行<span className={css.addTag}
                            onClick={handleCreateTag}
                        > 新建标签</span></div>}
                    />
                }}

            />
            <EditTagModal model={editTagModel} />
        </div>
    )
}

export default TagSettings 