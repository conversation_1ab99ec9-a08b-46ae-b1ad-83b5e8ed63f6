.projectInfoRight {
    display: flex;
    flex-direction: column;
    height: 100%;
    align-items: center;
    justify-content: center;

    .commnonBox {
        width: 580px;
        padding: 16px;
        border-radius: 6px;
        border: 1px solid #EBEDF0;
        background: #fff;
        display: flex;
        align-items: center;
        background: linear-gradient(114.07deg, #F5FAFF -7.7%, rgba(240, 247, 255, 0) 30.22%);

        &:hover {
            cursor: pointer;
            border-color: #326BFB;
            box-shadow: 0px 2px 4px 0px #0C121F0F;
        }

        .left {
            margin-right: 16px;
        }

        .right {
            .title {
                color: #252626;
                font-size: 16px;
                font-weight: 500;
                margin-bottom: 8px;
            }

            .subTitle {
                color: #575859;
                font-size: 14px;
                font-weight: 400;
            }
        }
    }

    .newInterface,
    .interfaceTest,
    .importCurl,
    .importOriginRepo,
    .importKapi {
        margin-bottom: 24px;
    }

    .newInterfaceIconBox,
    .interfaceTestIconBox,
    .importCurlIconBox,
    .importOriginRepoIconBox,
    .importKapiIconBox {
        width: 36px;
        height: 36px;
        border-radius: 6px;
        text-align: center;
        line-height: 36px;
        font-size: 24px;
    }

    .newInterfaceIconBox {
        color: #EA3A9B;
        background: #FFEBF2;
    }

    .interfaceTestIconBox {
        color: #FF7429;
        background: #FFF4E8;
    }

    .importCurlIconBox {
        color: #30C453;
        background: #E6FFF4;
    }

    .importOriginRepoIconBox {
        color: #7735D3;
        background: #F6EBFF;
    }

    .importKapiIconBox {
        color: #FA4E3E;
        background: #FFF4F0;
    }

    .moreOperationBox {
        text-align: center;

        .moreOperateBtn {
            cursor: pointer;
            color: #575859;
        }

        .moreOperateBtnHover {
            color: #5C8FFF;
        }
    }
}