import React, { useState, useEffect, useMemo } from 'react';
import { Select, SelectProps } from 'antd';
import { nsMockManageApiManageMainApiProgramImportKapiGroupProjectList } from '@/remote';
import { useDebounceFn } from 'ahooks';
import css from './ProjectNameSelect.less';

/**
 * 项目名称选择器组件属性接口
 */
export interface ProjectNameSelectProps extends Omit<SelectProps, 'options'> {
    /**
     * 选中的项目ID
     */
    value?: string | number | null;
    /**
     * 项目变化回调
     */
    onChange?: (value: string | number | null) => void;
    /**
     * 部门分组ID，用于获取对应的项目列表
     */
    groupId?: number | null;
    /**
     * 是否显示加载状态
     */
    loading?: boolean;
}

/**
 * 项目名称选择器组件
 * 用于选择项目名称，支持搜索过滤
 */
export const ProjectNameSelect: React.FC<ProjectNameSelectProps> = (props) => {
    // 解构props，提取需要的属性
    const { value, onChange, groupId, loading: propLoading, ...restProps } = props;

    // 组件内部状态
    const [loading, setLoading] = useState<boolean>(propLoading || false);
    const [projectSearchValue, setProjectSearchValue] = useState<string>('');
    const [projectList, setProjectList] = useState<
        nsMockManageApiManageMainApiProgramImportKapiGroupProjectList.IReturn_Item[]
    >([]);

    // 防抖搜索函数
    const debouncedSearch = useDebounceFn(
        (v: string) => {
            setProjectSearchValue(v);
        },
        { wait: 300 }
    );

    // 过滤项目名称列表
    const filteredProjectList = useMemo(() => {
        if (!projectSearchValue) return projectList;
        return projectList.filter(item =>
            item.name.toLowerCase().includes(projectSearchValue.toLowerCase())
        );
    }, [projectList, projectSearchValue]);

    // 当部门分组变化时，获取项目列表
    useEffect(() => {
        if (groupId) {
            setLoading(true);
            nsMockManageApiManageMainApiProgramImportKapiGroupProjectList.remote({
                groupId: String(groupId)
            })
                .then(res => {
                    setProjectList(res);
                    setLoading(false);
                })
                .catch(() => {
                    setLoading(false);
                });
        } else {
            setProjectList([]);
        }
    }, [groupId]);

    // 处理选择变化
    const handleChange = (v: string | number | null) => {
        onChange?.(v);
    };

    // 处理下拉框打开状态变化
    const handleDropdownVisibleChange = (open: boolean) => {
        // 当下拉框打开时，重置搜索值，显示所有数据
        if (open) {
            setProjectSearchValue('');
        }
    };

    return (
        <Select
            placeholder="请选择项目名称"
            showSearch
            filterOption={false}
            onSearch={(searchValue) => debouncedSearch.run(searchValue)}
            options={filteredProjectList.map(item => ({
                label: item.name,
                value: item.id
            }))}
            value={value}
            onChange={handleChange}
            loading={loading || propLoading}
            disabled={!groupId}
            className={css.projectNameSelect}
            onDropdownVisibleChange={handleDropdownVisibleChange}
            {...restProps}
        />
    );
};

export default ProjectNameSelect;
