.required {
    position: relative;
    width: fit-content;

    &::after {
        content: '*';
        color: red;
        position: absolute;
        right: -12px;
        top: 0;
    }
}

.title {
    .subTitle {
        font-size: 12px;
        color: rgba(0, 0, 0, 0.45);
        margin-left: 8px;
    }
}

.tip {
    margin-left: 4px;
    cursor: pointer;
    color: rgba(0, 0, 0, 0.45);
}

.titleContainer {
    display: flex;
    align-items: center;
    max-width: 100%;

    .titleText {
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        max-width: 100%;
    }
}

.importKapiContentItem {
    margin-bottom: 24px;
    display: flex;
    flex-direction: column;

    &:last-child {
        margin-bottom: 0;
    }

    .importKapiContentItemTitle {
        margin-bottom: 8px;
    }
}

.importRepoContentItemTree {
    display: flex;
    padding: 4px 0px;
    flex-direction: column;
    align-items: flex-start;
    border-radius: 4px;
    border: 1px solid #EBEDF0;
    background: #FFF;
    padding: 12px 12px 0 12px;
    height: 290px;
    gap: 8px;

    .searchInput {
        margin-bottom: 8;
        width: 100%;
    }

    .importRepoContentItemTreeContent {
        width: 100%;
        height: 100%;
        overflow-y: auto;

        .importRepoContentItemTreeContentTree {
            height: 237px;

            :global {
                .ant-tree-node-content-wrapper {
                    width: calc(100% - 24px);
                    overflow: hidden;
                }

                .ant-tree-treenode {
                    width: 100%;
                }
            }
        }
    }
}