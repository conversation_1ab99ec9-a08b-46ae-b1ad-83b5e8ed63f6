import React, { useState, useImperativeHandle, forwardRef, Ref, useEffect, useContext, useCallback } from 'react';
import { Modal, message, Tree, Input, Button, Spin, Checkbox } from 'antd';
import {
    nsMockManageApiManageMainApiProgramImportKapiProjectItems as TreeApi,
    nsMockManageApiManageMainApiProgramImportKapiApi
} from '@/remote';
import css from './ImportKapiInterface.less';
// @ts-ignore
import classnames from 'classnames';
import { DirectorySelect } from '../DirectorySelect/DirectorySelect';
import { ApiManageProjectInfoContext, ApiManageProjectInfoContextType } from '../ProjectInfo/ProjectInfo';
import { KdevIconFont } from '@/business/commonComponents';
import { common_system_folder_addition, common_system_search } from '@kid/enterprise-icon/icon/output/icons';
import { RenderTitle } from '../ImportRepo/ImportRepo';
import DepartmentGroupSelect from '../DepartmentGroupSelect';
import ProjectNameSelect from '../ProjectNameSelect';


/**
 * 导入KAPI接口组件的引用接口，用于父组件调用内部方法
 */
export interface ImportKapiRef {
    getParams(): any;
    reset(): void;
    check(): boolean;
    open(): void;
    close(): void;
}

/**
 * 导入KAPI接口组件属性接口
 */
interface ImportKapiProps {
    onSuccess?: () => void;
    programId?: number;
}

/**
 * 导入KAPI接口组件
 * 用于选择KAPI接口并配置导入选项
 */
export const ImportKapi = forwardRef((props: ImportKapiProps, ref: Ref<ImportKapiRef>) => {
    const {
        projectId,
        openCreateCatalogModal
    } = useContext(ApiManageProjectInfoContext) as ApiManageProjectInfoContextType;
    const { onSuccess, programId: propProgramId } = props;
    const [isOpen, setIsOpen] = useState(false);
    const [importLoading, setImportLoading] = useState(false);
    const [treeLoading, setTreeLoading] = useState(false);

    // 表单数据状态
    const [targetDir, setTargetDir] = useState<number | undefined>(undefined);
    const [programId, setProgramId] = useState<number>(propProgramId || projectId || 0);
    const [groupId, setGroupId] = useState<number | null>(null);
    const [kapiProjectId, setKapiProjectId] = useState<string | null>(null);
    const [searchValue, setSearchValue] = useState('');
    const [treeData, setTreeData] = useState<TreeApi.IDataItem[]>([]);
    const [totalApiCount, setTotalApiCount] = useState<number>(0);
    // 迁移策略选项
    const [syncTestHistory, setSyncTestHistory] = useState(false);
    const [syncMockData, setSyncMockData] = useState(false);
    const [selectedApiCount, setSelectedApiCount] = useState<number>(0);
    const [apiNodeMap, setApiNodeMap] = useState<Record<string | number, boolean>>({});


    /**
     * 计算API节点的总数并记录所有API节点ID
     * 用于统计API节点数量和后续API节点选择判断
     */
    const countApiNodes = useCallback((nodes: TreeApi.IDataItem[]): number => {
        let count = 0;
        const apiMap: Record<string | number, boolean> = {};

        const traverse = (items: TreeApi.IDataItem[]) => {
            items.forEach(item => {
                if (item.type === 'API') {
                    count++;
                    apiMap[item.id] = true;
                }
                if (item.children && item.children.length > 0) {
                    traverse(item.children);
                }
            });
        };

        traverse(nodes);
        setApiNodeMap(apiMap);
        return count;
    }, []);


    const getAllExpandedKeys = useCallback((arr: TreeApi.IDataItem[]) => {
        const res: number[] = [];
        const loop = (nodes: TreeApi.IDataItem[]) => {
            nodes.forEach(item => {
                res.push(item.id);
                if (item.children) {
                    loop(item.children);
                }
            });
        };
        loop(arr);
        return res;
    }, []);



    function formatTreeData(data: TreeApi.IDataItem[]) {
        return data.map(item => {
            if (item.type === 'CATALOG') {
                return {
                    ...item,
                    id: 'CATALOG_' + item.id,
                    children: formatTreeData(item.children || [])
                }
            } else if (item.type === 'API') {
                return {
                    ...item,
                }
            }
            return item;
        })
    }

    useEffect(() => {
        if (kapiProjectId) {
            setTreeLoading(true);
            TreeApi.remote({
                projectId: Number(kapiProjectId),
            }).then(res => {
                // 这里需要根据实际API返回格式处理数据
                // 示例：将API数据转换为树形结构

                const data = formatTreeData(res);
                setTreeData(data);
                // 计算API总数
                const apiCount = countApiNodes(data);
                setExpandedKeys(getAllExpandedKeys(data));
                setTotalApiCount(apiCount);
            }).finally(() => {
                setTreeLoading(false);
            });
        }
    }, [kapiProjectId, groupId]);



    // 统计已选节点数量（兼容 antd Tree 的 checkedKeys 类型）
    const getCheckedCount = (keys: any) => {
        if (Array.isArray(keys)) return keys.length;
        if (keys && Array.isArray(keys.checked)) return keys.checked.length;
        return 0;
    };

    const [expandedKeys, setExpandedKeys] = useState<React.Key[]>([]);
    const [checkedKeys, setCheckedKeys] = useState<React.Key[]>([]);
    const [searchCheckedKeys, setSearchCheckedKeys] = useState<React.Key[]>([]);
    const [selectedKeys, setSelectedKeys] = useState<React.Key[]>([]);
    const [autoExpandParent, setAutoExpandParent] = useState<boolean>(true);

    // 当 propProgramId 变化时更新内部状态
    useEffect(() => {
        if (propProgramId) {
            setProgramId(propProgramId);
        }
    }, [propProgramId]);
    /**
     * 计算已选择的API节点数量
     * 通过检查节点ID是否在API节点映射表中来确定
     */
    const countSelectedApiNodes = useCallback((keys: React.Key[]) => {
        let count = 0;
        keys.forEach(key => {
            if (apiNodeMap[key]) {
                count++;
            }
        });
        return count;
    }, [apiNodeMap]);
    /**
     * 监听选中节点变化，更新已选择的API节点数量
     */
    useEffect(() => {
        const apiCount = countSelectedApiNodes(checkedKeys);
        setSelectedApiCount(apiCount);
    }, [checkedKeys, countSelectedApiNodes]);


    // 当搜索值变化时，更新搜索选中的节点
    useEffect(() => {
        setSearchCheckedKeys(checkedKeys);
    }, [searchValue]);

    /**
     * 获取表单参数
     */
    const getParams = () => {
        return {
            targetDir,
            groupId,
            projectId: kapiProjectId,
            checkedKeys
        };
    };

    /**
     * 重置表单
     */
    const reset = () => {
        setTargetDir(undefined);
        setGroupId(null);
        setKapiProjectId(null);
        setCheckedKeys([]);
        setSearchCheckedKeys([]);
        setSelectedKeys([]);
        setSearchValue('');
    };

    /**
     * 校验表单
     */
    const check = () => {
        if (!groupId) {
            message.error('请选择部门分组');
            return false;
        }
        if (!kapiProjectId) {
            message.error('请选择项目名称');
            return false;
        }
        if (checkedKeys.length === 0) {
            message.error('请至少选择一个接口');
            return false;
        }
        if (!targetDir) {
            message.error('请选择目标目录');
            return false;
        }
        return true;
    };

    /**
     * 处理目录选择变化
     */
    const handleDirectoryChange = (value: number | undefined) => {
        setTargetDir(value);
    };

    /**
     * 处理确认按钮点击
     * 校验表单并提交数据
     */
    const handleModalOk = async () => {
        if (!check()) {
            return;
        }

        setImportLoading(true);
        try {
            await nsMockManageApiManageMainApiProgramImportKapiApi.remote({
                apiIds: checkedKeys.filter(key => Number(key)).map(key => Number(key)),
                targetProgram: programId,
                targetCatalog: targetDir || 0,
                syncTestHistory: syncTestHistory,
                syncMockData: syncMockData
            });

            // 成功后重置表单并关闭弹窗
            reset();
            setIsOpen(false);
            onSuccess?.();
            message.success('开始导入，完成后通过KIM通知');
        } catch (error) {
            message.error('导入失败');
        } finally {
            setImportLoading(false);
        }
    };

    /**
     * 处理取消按钮点击
     * 关闭对话框并重置表单
     */
    const handleModalCancel = () => {
        setIsOpen(false);
        reset();
    };

    /**
     * 树节点展开处理
     */
    const onExpand = (expandedKeysValue: React.Key[]) => {
        setExpandedKeys(expandedKeysValue);
        setAutoExpandParent(false);
    };

    /**
     * 树节点选中处理
     */
    const onCheck = (checkedKeysValue: any, info: any) => {
        const key = info.node.key;
        if (searchValue) {
            // 搜索状态下，只同步当前操作的节点到 searchCheckedKeys
            let newSearchCheckedKeys = [...searchCheckedKeys];
            if (info.checked) {
                if (!newSearchCheckedKeys.includes(key)) {
                    newSearchCheckedKeys.push(key);
                    setCheckedKeys(newSearchCheckedKeys)
                }
            } else {
                newSearchCheckedKeys = newSearchCheckedKeys.filter(k => k !== key);
            }
            setSearchCheckedKeys(newSearchCheckedKeys);
        } else {
            // 非搜索状态，正常同步到 checkedKeys
            const newCheckedKeys = Array.isArray(checkedKeysValue)
                ? checkedKeysValue
                : checkedKeysValue.checked;
            setCheckedKeys(newCheckedKeys);
        }
    };

    /**
     * 树节点选择处理
     */
    const onSelect = (selectedKeysValue: React.Key[], _info: any) => {
        setSelectedKeys(selectedKeysValue);
    };

    /**
     * 递归过滤 treeData，返回包含匹配节点及其父节点的结构
     */
    const getSearchTreeData = (data: TreeApi.IDataItem[], search: string): TreeApi.IDataItem[] => {
        if (!search) return data;
        const match = (title: string, apiPath: string) =>
            title.toLowerCase().includes(search.toLowerCase()) || apiPath.toLowerCase().includes(search.toLowerCase());
        const loop = (nodes: TreeApi.IDataItem[]): TreeApi.IDataItem[] => {
            return nodes.map(node => {
                if (match(String(node.name), String(node.apiPath))) {
                    return { ...node };
                }
                if (node.children) {
                    const children = loop(node.children);
                    if (children.length) {
                        return { ...node, children };
                    }
                }
                return null;
            })
                .filter(Boolean) as TreeApi.IDataItem[];
        };
        return loop(data);
    };

    // 获取搜索过滤后的树数据
    const searchTreeData = getSearchTreeData(treeData, searchValue);

    /**
     * 暴露给父组件的方法
     */
    useImperativeHandle(ref, () => ({
        getParams,
        reset,
        check,
        open: () => setIsOpen(true),
        close: () => setIsOpen(false)
    }));

    /**
     * 渲染表单内容
     */
    const renderContent = () => {
        return (
            <div>
                <div className={css.importKapiContentItem}>
                    <span
                        className={classnames(css.required, css.importKapiContentItemTitle)}>部门分组</span>
                    <DepartmentGroupSelect
                        value={groupId}
                        onChange={setGroupId}
                    />
                </div>
                <div className={css.importKapiContentItem}>
                    <span
                        className={classnames(css.required, css.importKapiContentItemTitle)}>项目名称</span>
                    <ProjectNameSelect
                        value={kapiProjectId}
                        onChange={(value) => setKapiProjectId(value as string | null)}
                        groupId={groupId}
                    />
                </div>
                {kapiProjectId && (
                    <div className={css.importKapiContentItem}>
                        <span
                            className={classnames(css.required, css.importKapiContentItemTitle)}>
                            原接口选择（{selectedApiCount}/{totalApiCount}）
                        </span>
                        <div className={css.importRepoContentItemTree}>
                            <Input
                                placeholder="搜索接口名称"
                                value={searchValue}
                                onChange={e => setSearchValue(e.target.value)}
                                className={css.searchInput}
                                prefix={<KdevIconFont id={common_system_search} style={{ color: '#D5D6D9ff' }} />}
                            />
                            <div className={css.importRepoContentItemTreeContent}>
                                <Spin spinning={treeLoading} tip="数据加载中...">
                                    <Tree
                                        className={css.importRepoContentItemTreeContentTree}
                                        checkable
                                        onExpand={onExpand}
                                        expandedKeys={expandedKeys}
                                        autoExpandParent={autoExpandParent}
                                        onCheck={onCheck}
                                        checkedKeys={searchValue ? searchCheckedKeys : checkedKeys}
                                        onSelect={onSelect}
                                        selectedKeys={selectedKeys}
                                        treeData={searchValue ? searchTreeData : treeData}
                                        height={237}
                                        virtual={true}
                                        titleRender={(node: any) => {
                                            const typedNode = node as TreeApi.IDataItem;
                                            // 使用类型断言解决类型不兼容问题
                                            return <RenderTitle
                                                title={typedNode.name}
                                                node={{
                                                    ...typedNode,
                                                    parentId: 0,
                                                    // 确保children属性符合要求
                                                    children: typedNode.children as any
                                                } as any}
                                                expanded={expandedKeys.includes(typedNode.id)}
                                                filterText={searchValue}
                                            />;
                                        }}
                                        fieldNames={{
                                            title: 'name',
                                            key: 'id',
                                            children: 'children'
                                        }}
                                    />
                                </Spin>
                            </div>
                        </div>
                    </div>
                )}
                {/* <div className={css.importRepoContentItem}>
                    <span
                        className={classnames(css.required, css.importRepoContentItemTitle)}>迁移策略</span>
                    <div>
                        <Checkbox
                            checked={syncTestHistory}
                            onChange={e => setSyncTestHistory(e.target.checked)}
                        >
                            保留原接口测试数据
                        </Checkbox>
                        <Checkbox
                            checked={syncMockData}
                            onChange={e => setSyncMockData(e.target.checked)}
                        >
                            保留原接口 Mock 数据
                        </Checkbox>
                    </div>
                </div> */}
                <div className={css.importKapiContentItem}>
                    <span
                        className={classnames(css.required, css.importKapiContentItemTitle)}>目标目录</span>
                    <DirectorySelect
                        placeholder="请选择目录"
                        value={targetDir}
                        programId={programId}
                        onChange={handleDirectoryChange}
                        style={{ width: '100%' }}
                        dropdownFooter={
                            <div style={{ padding: '8px', textAlign: 'center' }}>
                                <Button
                                    type="text"
                                    icon={<KdevIconFont id={common_system_folder_addition} />}
                                    onClick={() => {
                                        openCreateCatalogModal(0, '');
                                    }}
                                    style={{ width: '100%' }}
                                >
                                    新建目录
                                </Button>
                            </div>
                        }
                    />
                </div>
            </div>
        );
    };

    return (
        <Modal
            title={<span className={css.title}>
                导入 KAPI 接口
            </span>}
            open={isOpen}
            onCancel={handleModalCancel}
            onOk={handleModalOk}
            okButtonProps={{ loading: importLoading }}
            width="640px"
        >
            {renderContent()}
        </Modal>
    );
});