import React, { useRef } from 'react';
import { Modal, message } from 'antd';
import { CreateProject, CreateProjectRef } from '../CreateProject/CreateProject';
import { nsMockManageApiManageMainApiProgram } from '@/remote';

interface ProjectModalProps {
    isOpen: boolean;
    onCancel: () => void;
    currentGroupId: number;
    onSuccess?: () => void;
}

export const ProjectModal: React.FC<ProjectModalProps> = ({
    isOpen,
    onCancel,
    currentGroupId,
    onSuccess
}) => {
    const [createProjectLoading, setCreateProjectLoading] = React.useState(false);
    const createProjectRef = useRef<CreateProjectRef>(null);

    const handleCreateModalOk = () => {
        if (!createProjectRef.current?.check()) {
            return;
        }

        const params = createProjectRef.current.getParams();
        params.bindProjects = params.bindProjects.map(item => ({
            projectId: item.projectId
        }));
        setCreateProjectLoading(true);
        if (params.name.length > 60) {
            message.error('项目名称过长');
            setCreateProjectLoading(false);
            return;
        }
        if (params.desc.length > 150) {
            message.error('项目描述过长');
            setCreateProjectLoading(false);
            return;
        }
        // 新建项目
        nsMockManageApiManageMainApiProgram.create({
            ...params,
            groupId: currentGroupId
        }).then(() => {
            message.success('创建项目成功');
            setCreateProjectLoading(false);
            onCancel();
            createProjectRef.current?.reset();
            onSuccess?.();
        }).catch(err => {
            setCreateProjectLoading(false);
        });
    };

    const handleCreateModalCancel = () => {
        onCancel();
        createProjectRef.current?.reset();
    };

    return (
        <Modal
            title="新建项目"
            open={isOpen}
            onCancel={handleCreateModalCancel}
            onOk={handleCreateModalOk}
            okButtonProps={{ loading: createProjectLoading }}
            width="480px"
        >
            <CreateProject ref={createProjectRef} />
        </Modal>
    );
}; 