import React, { useState, useEffect, useRef } from 'react'
import { Input, Checkbox, Tooltip, Button, Popover } from 'antd'
import { SearchOutlined, PlusOutlined, CopyOutlined } from '@ant-design/icons'
import { RepoAvatar } from '@/business/repo/RepoAvatar'
import css from './SearchProject.less'
import { common_system_add, common_system_api, common_system_upanddownarrows, kdev_repositories } from '@kid/enterprise-icon/icon/output/icons'
import { KdevIconFont, KEmpty } from '@/business/commonComponents'
import { nsKdevRepoRecentVisitList, nsKdevRepoListAllWithLimit, nsMockManageApiManageMainApiProgramList, nsMockManageApiManageMainApiProgramSearch } from '@/remote'
import { debounce } from 'lodash';
import classnames from 'classnames';
import { ProjectModal } from './ProjectModal';
import { ImportKapiProject, ImportKapiProjectRef } from '../ImportKapiProjectModal/ImportKapiProjectModal';

// 仓库数据接口
type ProjectItem = nsMockManageApiManageMainApiProgramList.ResponseItem
const apiTagColorOptions = [
    { color: '#FA4E3E', bgColor: '#FFF4F0' },
    { color: '#EA3A9B', bgColor: '#FFEBF2' },
    { color: '#7735D3', bgColor: '#F6EBFF' },
    { color: '#326BFB', bgColor: '#F0F7FF' },
    { color: '#36CFA2', bgColor: '#E6FFF4' },
    { color: '#FADB14', bgColor: '#FEFFE6' },
    { color: '#FFAA00', bgColor: '#FFFBE6' },
    { color: '#19B2FF', bgColor: '#EBF5FF' },
    { color: '#A5DA21', bgColor: '#F4FFE6' }
];

export default function SearchProject(Props: {
    onChange: (projectId: number) => void,
    projectName: string,
    currentGroupId: number,
    currentProjectId: number
}) {
    const [projectList, setProjectList] = useState<ProjectItem[]>([]);
    const [searchKey, setSearchKey] = useState<string>('');
    const [open, setOpen] = useState<boolean>(false);
    const [isModalOpen, setIsModalOpen] = useState<boolean>(false);
    const importKapiRef = useRef<ImportKapiProjectRef>(null);

    // 创建防抖的搜索函数
    const debouncedSearch = debounce((key: string) => {
        if (!Props.currentGroupId) { return }
        if (key) {
            nsMockManageApiManageMainApiProgramSearch.remote({
                pageNumber: 1,
                pageSize: 9999,
                filters: [
                    {
                        type: 'term',
                        field: 'groupId',
                        value: +Props.currentGroupId
                    },
                    {
                        type: 'fuzzy',
                        field: 'name',
                        value: key
                    }
                ]
            }).then((res) => {
                setProjectList(res.items)
            });
        } else {
            nsMockManageApiManageMainApiProgramList.remote({
                groupId: Props.currentGroupId, pageNumber: 1,
                pageSize: 99999,
            }).then((res) => {
                setProjectList(res.items)
            })
        }

    }, 300);

    useEffect(() => {
        debouncedSearch(searchKey);
        // 清理防抖函数
        return () => {
            debouncedSearch.cancel();
        };
    }, [searchKey, Props.currentGroupId]);

    const handleCreateProject = (e: React.MouseEvent) => {
        e.stopPropagation(); // 阻止事件冒泡
        setIsModalOpen(true);
        setOpen(false);
    };

    // 高亮项目名称中匹配的搜索关键词
    const highlightMatch = (text: string, keyword: string) => {
        if (!keyword.trim()) {
            return <span>{text}</span>;
        }

        const parts = text.split(new RegExp(`(${keyword})`, 'gi'));

        return (
            <span>
                {parts.map((part, index) =>
                    part.toLowerCase() === keyword.toLowerCase() ?
                        <span key={index} style={{ color: '#1890ff' }}>{part}</span> :
                        <span key={index}>{part}</span>
                )}
            </span>
        );
    };

    const addRepoPopoverContent = () => {
        return (
            <div className={css.addRepoPopoverContent}>
                {/* 搜索框 */}
                <div className={css.searchBox}>
                    <Input
                        className={css.searchInput}
                        placeholder="搜索项目关键词"
                        prefix={<SearchOutlined style={{ color: '#D5D6D9' }} />}
                        value={searchKey}
                        onChange={(e) => setSearchKey(e.target.value)}
                        allowClear
                    />
                </div>

                {/* 项目列表 */}
                <div className={css.repoList}>
                    {projectList.length === 0 && <div className={css.noData}>
                        <KEmpty />
                    </div>}
                    {projectList.map((repo, i) => (
                        <div key={repo.id} className={classnames(
                            css.repoItem,
                            repo.id === Props.currentProjectId ? css.active : ''
                        )}
                            onClick={() => {
                                Props.onChange(repo.id)
                                setOpen(false);
                                setSearchKey('');
                            }}>
                            <div className={css.projectIcon} style={{ backgroundColor: apiTagColorOptions[i % apiTagColorOptions.length].bgColor }}>
                                <KdevIconFont id={common_system_api} style={{ fontSize: 16, color: apiTagColorOptions[i % apiTagColorOptions.length].color }} />
                            </div>
                            <div className={css.projectName}>
                                {highlightMatch(repo.name, searchKey)}
                            </div>
                        </div>
                    ))}
                </div>
                {/* 底部 */}
                <div className={css.bottom}>
                    <div className={css.addProject} onClick={handleCreateProject}>
                        <KdevIconFont id={common_system_add} style={{ color: '#898A8Cff' }} />
                        <span>新建项目</span>
                    </div>
                    {/* <div className={css.line}></div>
                    <div className={css.import} onClick={() => {
                        importKapiRef.current?.open();
                        setOpen(false)
                    }}>
                        <KdevIconFont id={common_system_import} style={{ color: '#575859' }} />
                        <span>导入 KAPI 项目</span>
                    </div>
                    <ImportKapiProject
                        ref={importKapiRef}
                        currentGroupId={Props.currentGroupId}
                    /> */}
                </div>
            </div >
        )
    }

    return (
        <div className={css.selectRepoListContainer}>
            {/* 项目名称 */}
            <Popover
                content={addRepoPopoverContent}
                title={null}
                trigger="click"
                placement="bottomLeft"
                overlayClassName={css.menuOverlay}
                destroyTooltipOnHide
                open={open}
                onOpenChange={(v) => {
                    if (!v) {
                        setSearchKey('')
                    }
                    setOpen(v)
                }}
                overlayInnerStyle={{
                    width: 320
                }}
            >
                <div className={css.projectName1}>
                    <div className={css.newInterfaceIconBox}>
                        <KdevIconFont id={common_system_api} />
                    </div>
                    <div style={{
                        flex: 1,
                        overflow: 'hidden',
                        textOverflow: 'ellipsis',
                        whiteSpace: 'nowrap',
                        minWidth: 0,
                        fontSize: 14,
                    }}>
                        {Props.projectName}
                    </div>
                    <div className={classnames(css.arrow, open ? css.open : '')} style={{ flexShrink: 0 }}>
                        <KdevIconFont id={common_system_upanddownarrows} style={{ color: '#898A8C' }} />
                    </div>
                </div>
            </Popover>

            <ProjectModal
                isOpen={isModalOpen}
                onCancel={() => setIsModalOpen(false)}
                currentGroupId={Props.currentGroupId}
                onSuccess={() => {
                    debouncedSearch(searchKey);
                }}
            />
        </div>
    )
}
