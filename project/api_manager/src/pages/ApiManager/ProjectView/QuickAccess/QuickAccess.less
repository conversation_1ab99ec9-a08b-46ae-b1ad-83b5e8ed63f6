.additional {
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 100%;
    line-height: 1.6;

    .header {
        display: flex;
        align-items: center;
        width: 100%;
        padding: 8px 4px 4px;
        font-size: 12px;
        line-height: 18px;

        span {
            margin-right: 10px;
            color: #898A8C;
        }

        .line {
            flex: 1 0 0;
            height: 1px;
            background-color: #EBEDF0;
        }
    }

    .list {
        display: flex;
        flex-direction: column;
        align-items: center;
        width: 100%;

        .item {
            display: flex;
            align-items: center;
            width: 100%;
            height: 42px;
            border-radius: 4px;
            padding: 13px 16px;
            cursor: pointer;

            &:hover {
                background-color: #F5F7FA;

                // span {
                //     color: #326BFB;
                // }

                .close {
                    display: block;
                }
            }

            .projectIcon {
                display: flex;
                width: 16px;
                height: 16px;
                justify-content: center;
                align-items: center;
                margin-right: 12px;
                border-radius: 2px;

                &.color0 {
                    background: #FFEBF2ff;
                    color: #EA3A9Bff;
                }

                &.color1 {
                    background: #F0F7FFff;
                    color: #326BFBff;
                }

                &.color2 {
                    background: #FFF4F0ff;
                    color: #FA4E3Eff;
                }

                &.color3 {
                    background: #F6EBFFff;
                    color: #7735D3ff;
                }

                &.color4 {
                    background: #E6FFF4ff;
                    color: #36CFA2ff;
                }

                &.color5 {
                    background: #FFFBE6ff;
                    color: #FFAA00ff;
                }
            }

            span {
                font-size: 14px;
                color: #252626;
                flex: 1 0 0;
                width: 0;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
            }

            .close {
                display: none;
                cursor: pointer;
            }
        }

        .item.actived {
            background-color: rgb(245, 246, 247);

            span {
                color: #326BFB;
            }
        }
    }
}