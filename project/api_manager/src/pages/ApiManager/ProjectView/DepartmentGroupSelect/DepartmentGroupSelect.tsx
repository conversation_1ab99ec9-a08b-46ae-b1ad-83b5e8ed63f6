import React, { useState, useEffect, useMemo } from 'react';
import { Select, SelectProps } from 'antd';
import { nsMockManageApiManageMainApiProgramImportKapiGroupList } from '@/remote';
import { useDebounceFn } from 'ahooks';
import css from './DepartmentGroupSelect.less';

/**
 * 部门分组选择器组件属性接口
 */
export interface DepartmentGroupSelectProps extends Omit<SelectProps, 'options'> {
    /**
     * 选中的部门分组ID
     */
    value?: number | null;
    /**
     * 部门分组变化回调
     */
    onChange?: (value: number | null) => void;
    /**
     * 是否显示加载状态
     */
    loading?: boolean;
}

/**
 * 部门分组选择器组件
 * 用于选择部门分组，支持搜索过滤
 */
export const DepartmentGroupSelect: React.FC<DepartmentGroupSelectProps> = (props) => {
    // 解构props，提取需要的属性
    const { value, onChange, loading: propLoading, ...restProps } = props;

    // 组件内部状态
    const [loading, setLoading] = useState<boolean>(propLoading || false);
    const [groupSearchValue, setGroupSearchValue] = useState<string>('');
    const [groupList, setGroupList] = useState<
        nsMockManageApiManageMainApiProgramImportKapiGroupList.IReturn_Item[]
    >([]);

    // 防抖搜索函数
    const debouncedSearch = useDebounceFn(
        (v: string) => {
            setGroupSearchValue(v);
        },
        { wait: 300 }
    );

    // 过滤部门分组列表
    const filteredGroupList = useMemo(() => {
        if (!groupSearchValue) return groupList;
        return groupList.filter(item =>
            item.name.toLowerCase().includes(groupSearchValue.toLowerCase())
        );
    }, [groupList, groupSearchValue]);

    // 获取部门分组列表
    useEffect(() => {
        setLoading(true);
        nsMockManageApiManageMainApiProgramImportKapiGroupList.remote()
            .then(res => {
                setGroupList(res);
                setLoading(false);
            })
            .catch(() => {
                setLoading(false);
            });
    }, []);

    // 处理选择变化
    const handleChange = (v: number | null) => {
        onChange?.(v);
    };

    // 处理下拉框打开状态变化
    const handleDropdownVisibleChange = (open: boolean) => {
        // 当下拉框打开时，重置搜索值，显示所有数据
        if (open) {
            setGroupSearchValue('');
        }
    };

    return (
        <Select
            placeholder="请选择部门分组"
            showSearch
            filterOption={false}
            onSearch={(searchValue) => debouncedSearch.run(searchValue)}
            options={filteredGroupList.map(item => ({
                label: item.name,
                value: item.id
            }))}
            value={value}
            onChange={handleChange}
            loading={loading || propLoading}
            className={css.departmentGroupSelect}
            onDropdownVisibleChange={handleDropdownVisibleChange}
            {...restProps}
        />
    );
};

export default DepartmentGroupSelect;
