@import '~@lynx/design-token/dist/less/token.less';

.projectInfo {
    height: 100%;
    display: flex;
    background: #fff;
}

.splitPane {
    :global {
        .cjjWdp {
            background-color: @color-border-table !important;
            opacity: 1 !important;
        }

        .fXAXjb {
            &:hover {
                cursor: default;
            }
        }

        .ciqkXk,
        .cqKmWU {
            background: #bbb !important;
            border-color: #fff;
        }
    }
}

.splitPaneResizer {
    .splitPane();

    :global {
        .cjjWdp {
            &:hover {
                background-color: @color-bg-brand !important;
                transition-duration: 0s;
            }
        }

        .ciqkXk,
        .cqKmWU {
            background: #bbb !important;
            border-color: #fff;
        }

        .fXAXjb {
            &:hover {
                cursor: col-resize;
            }
        }
    }
}

.hiddenLeftPaneContent {
    :global {
        .Resizer {
            cursor: default;
        }
    }
}

.rightPane {
    position: relative;
    height: 100%;
}