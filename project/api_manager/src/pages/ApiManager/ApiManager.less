@import '~@lynx/design-token/dist/less/token.less';

.entry {
    background-color: #fff;
    height: 100%;
    overflow: hidden;
    display: flex;
    flex-direction: column;

    .apiManageTabs {
        height: 48px;
        min-height: 48px;

        :global {
            .ant-tabs-tab-btn {
                font-weight: 600;
                font-size: 16px;
            }

            .ant-tabs-top>.ant-tabs-nav,
            .ant-tabs-bottom>.ant-tabs-nav,
            .ant-tabs-top>div>.ant-tabs-nav,
            .ant-tabs-bottom>div>.ant-tabs-nav {
                margin: 0 !important;
            }

            .ant-tabs-nav {
                height: 48px;
                padding: 0 16px;
                margin: 0 !important;
            }
        }
    }
}

.ApiManager {
    background-color: #fff;
    overflow: hidden;
    // margin-left: 2px;
    display: flex;
    flex-direction: column;
    height: 100%;

    :global {

        .ant-tree {
            padding-right: 6px;
        }

        .ant-tree .ant-tree-treenode {
            align-items: center;
            padding: 0;
        }

        .ant-tree .ant-tree-switcher {
            align-self: center !important;
        }

        .ant-tree .ant-tree-switcher .ant-tree-switcher-icon {
            font-size: 12px;
        }
    }

    .leftPaneContent {
        padding: 12px 16px 0;
        height: 100%;

        // overflow: hidden;
        .leftContent {
            overflow: hidden;
            height: 100%;
            display: flex;
            flex-direction: column;

            .title {
                display: flex;
                height: 32px;
                // justify-content: space-between;
                align-items: center;
                margin: 0 0 12px 0;
                color: #252626;
                // cursor: pointer;

                h1 {
                    font-size: 16px;
                    font-weight: 500;
                    margin: 0;
                    line-height: 32px;
                    flex: 0 0 64px;
                    margin-right: 8px;
                }

                p {
                    cursor: pointer;
                    color: #575859;

                    .icon {
                        color: #898A8C;
                        margin-right: 4px;
                    }
                }
            }

            .searchBox {
                // z-index: 100;
                display: flex;
                justify-content: space-between;
            }

            .empty {
                margin-top: 24px;
                text-align: center;
                color: #898A8C;
            }

            .repoDirectoryTreeBox {
                overflow: hidden;
                height: 100%;
            }

            :global {
                .ant-segmented-item-selected {
                    font-weight: 800;
                }

                .ant-segmented-item {
                    border-radius: 4px !important;
                }

                .ant-segmented-item-selected {
                    border-radius: 4px !important;
                }

                .ant-tree-treenode-selected {
                    font-weight: bold;
                }
            }
        }

    }

    .rightPane {
        position: relative;
    }
}

.repoDirectoryTree {
    height: 100%;
    overflow: auto;

    :global {
        .ant-tree-list-scrollbar {
            width: 6px !important;
            right: -6px !important;
        }

        .ant-tree-list-scrollbar-thumb {
            background-color: @color-bg-tertiary !important;
            border-radius: 6px !important;
        }

        .ant-tree-treenode {
            height: 38px;
            display: flex;
            align-items: center;
            border-radius: 4px;
            color: @color-text-primary;
        }

        .ant-tree-treenode::before {
            bottom: 0;
            border-radius: 4px;
            height: 38px;
        }

        .ant-tree-treenode:hover::before {
            background-color: @color-bg-basic-hover;
        }

        .ant-tree-node-content-wrapper.ant-tree-node-selected {
            color: @color-text-brand !important;
        }

        .ant-tree-title {
            display: flex;
            align-items: center;
            width: 100%;
            height: 100%;
        }

        .ant-tree-node-content-wrapper {
            overflow: hidden;
            padding: 0 0 0 4px;
            height: 100%;
        }

        .ant-tree-switcher {
            width: 16px !important;
            height: 32px;
            // line-height: 22px;
            display: flex;
            align-items: center;
            padding-left: 4px;
            margin-top: -1px;
        }

        .ant-tree-switcher .ant-tree-switcher-icon {
            font-size: 14px;
            color: @color-text-icon-secondary;
        }

        .ant-tree-indent-unit {
            width: 16px;
        }

        .ant-tree-treenode-selected {
            &::before {
                background-color: #F0F4FF !important;
            }

            .svg-common_system_folder_close_line,
            .svg-common_system_auto_import_folder {
                color: @color-bg-brand !important;
            }
        }
    }

    .treeNodeItem {
        display: flex;
        align-items: center;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
        width: 100%;
        box-sizing: border-box;
        position: relative;

        .treeNodeName {
            flex: 1;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            font-weight: 400 !important;
        }

        .followBtn {
            padding: 0 4px;
            height: 32px;
            line-height: 32px;
            font-size: 16px;
            color: @color-text-icon-secondary;
        }
    }
}

:global {
    .ant-tree-node-content-wrapper:hover::before {
        content: "";
    }
}

.moreResultsButton {
    color: #575859 !important;
    padding: 5px 0px;

    &:hover {
        color: #252626 !important;
    }
}

.searchDirectoryTree {
    height: 100%;
    overflow: auto;
    // padding-left: 12px;
    // padding-right: 8px;

    :global {
        .ant-tree-list-scrollbar {
            width: 6px !important;
            right: -6px !important;
        }

        .ant-tree-list {
            padding: 4px 0px 0px 0px;
        }

        .ant-tree-list-scrollbar-thumb {
            background-color: @color-bg-tertiary !important;
            border-radius: 6px !important;
        }

        .ant-tree-treenode {
            // height: 38px;
            // padding: 0 !important;
            display: flex;
            align-items: center;
            border-radius: 4px;
            color: @color-text-primary;
            padding-left: 12px;
            padding-right: 8px;
            padding-bottom: 0;
        }

        .ant-tree-treenode::before {
            bottom: 0;
            height: 32px;
        }

        .ant-tree-treenode:hover::before {
            background-color: @color-bg-basic-hover;
        }


        .ant-tree-node-content-wrapper.ant-tree-node-selected {
            color: @color-text-brand !important;
        }

        .ant-tree-title {
            display: flex;
            align-items: center;
            width: 100%;
            height: 100%;
        }

        .ant-tree-node-content-wrapper {
            overflow: hidden;
            padding: 0px 0px 0px 4px;
            height: 100%;
            line-height: 22px;
        }

        .ant-tree.ant-tree-directory .ant-tree-treenode {
            padding: 0;
        }

        .ant-tree-switcher {
            width: 16px !important;
            height: 32px;
            // line-height: 22px;
            display: flex;
            align-items: center;
            padding-left: 4px;
            padding-right: 10px;
            margin-top: -1px;
        }

        .ant-tree-switcher .ant-tree-switcher-icon {
            font-size: 14px;
            color: @color-text-icon-secondary;
        }

        .ant-tree-indent-unit {
            width: 16px;
        }

        .ant-tree-treenode-selected {
            &::before {
                display: none !important;
                background-color: #F0F4FF !important;
            }

            .svg-common_system_folder_close_line,
            .svg-common_system_auto_import_folder {
                color: @color-bg-brand !important;
            }

            // padding: 0 !important;
        }

        .ant-tree .ant-tree-treenode {
            padding: 0 !important;
        }
    }

    .treeNodeItem {
        display: flex;
        align-items: center;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
        width: 100%;
        box-sizing: border-box;
        position: relative;
        line-height: 22px;
        padding: 0;

        .treeNodeName {
            flex: 1;
            padding: 5px 0px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            font-weight: 400 !important;
        }

        .followBtn {
            padding: 0 4px;
            height: 32px;
            line-height: 32px;
            font-size: 16px;
            color: @color-text-icon-secondary;
        }
    }
}

.searchTreeResult {
    padding: 0px !important;
    width: 360px !important;
    min-width: 360px !important;
}