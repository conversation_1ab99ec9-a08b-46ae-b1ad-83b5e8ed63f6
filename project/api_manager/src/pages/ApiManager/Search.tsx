import { Tabs, message, Input, Tooltip, Button, TreeSelect, Spin } from 'antd';
import React, { useState, createContext, useContext, useEffect, useRef, useCallback, memo } from 'react';
import css from './ApiManager.less';
import { ERouter } from 'CONFIG';
import { getUrlSearch } from '@/index.config/tools';
import { IQuery, ApiManager } from '@/pages/ApiManager/ApiManager';
import { router } from 'libs/mvvm';
import store from '../../pages/httpApi/viewApi/checkRule/store';
import { GlobalSearchInput } from '@/business/topNavComps/GlobalSearchInput';
import { isKDevPageV2 } from 'kdev-fe-common/src/component/KDevPageLayout/KDevPageLayout';
import { ApiMgrSplitPane } from '@/pages/ApiManager/ApiMgrSplitPane';
import { RelevantToMe } from '@/pages/ApiManager/relevantToMe/RelevantToMe';
import { ApiView } from '@/pages/ApiManager/apiView/ApiView';
import { ApiViewM } from '@/pages/ApiManager/apiView/ApiViewM';
import { RelevantDirectoryTree } from './relevantToMe/RelevantDirectoryTree';
import { KdevIconFont, KEmpty } from '@/business/commonComponents';
import {
    nsApiManagerSearch, nsMockManageApiManageMainApiGlobalSearchListGET
} from '@/remote';
import { eNodeType } from './relevantToMe/RelevantToMeUtils';
import { IRelevantToMeContextProps, RelevantToMeContext } from './Header';
// import { KDEV_API_GROUP_PROJECT } from '@/appV2';

import { NewUserLandingPage } from './newUserLandingPage/NewUserLandingPage';
import { NewUserLandingPageM } from './newUserLandingPage/NewUserLandingPageM';
import { DirMenu } from './dirMenu/DirMenu';
import { DirMenuM } from './dirMenu/DirMenuM';
import { KDEV_API_GROUP_REPO, KDEV_API_SEARCH_GROUP } from './apiManager.config';
import SearchResultTree from './ApiMenu/searchResultTree';
import { LOCAL_STORAGE_KEYS } from '@/index.config/LOCAL_STORAGE_KEYS';
import { SearchApi } from '@/business/topNavComps/searchApi/SearchApi';
import { debounce } from 'lodash';
import { common_system_add } from '@kid/enterprise-icon/icon/output/icons';

const newUserLandingPageM = new NewUserLandingPageM();
const dirMenuM = new DirMenuM();

export function getKey(item: nsApiManagerSearch.IItem) {
    if (item.type === eNodeType.API) {
        return `A${item.apiId}`;
    }
    return `G${item.groupId}`;
}
function expandAll(list: Array<nsApiManagerSearch.IItem> = [], dep?: number) {
    const keys: Array<string> = [];
    let cur = 1;
    function Loop(arr) {
        if (dep && cur > dep) {
            return;
        }
        arr.forEach(item => {
            if (item.children && item.children.length > 0) {
                keys.push(getKey(item));
                Loop(item.children);
            }
        });
        cur++;
    }
    Loop(list);
    return keys;
}

const isExceedByteLimit = (key: string, minLength: number) => {
    // 对于中文字符长度，使用简单的汉字判断
    if (/[\u4e00-\u9fa5]/.test(key)) {
        return key.length < 2; // 中文字符要求最少2个字符
    }
    // 对于英文字符，要求最少3个字母
    return key.length < 3;
};

export const renderSearch = () => {
    const relevantToMeContext = useContext<IRelevantToMeContextProps>(RelevantToMeContext);
    const [globalSearchResults, setGlobalSearchResults] = useState<number>(0);
    const [searchVisible, setSearchVisible] = useState<boolean>(false);
    const [currentKeyword, setCurrentKeyword] = useState<string>('');
    const [searchApiKeyword, setSearchApiKeyword] = useState<string>('');
    const [searchLength, setSearchLength] = useState<number>(0);
    const [searchLoading, setSearchLoading] = useState<boolean>(false);
    const searchInputRef = useRef<any>(null);
    const selectedKey = (() => {
        if (relevantToMeContext.apiId) {
            return `A${relevantToMeContext.apiId}`;
        }
        if (relevantToMeContext.groupId) {
            return `G${relevantToMeContext.groupId}`;
        }
        return undefined;
    })();
    let title: string | undefined;
    if (!selectedKey) {
        title = '如需创建一级目录，请联系：xuning、yinwenjing03、liufei13';
    }

    const onSearchApi = async (key: string) => {
        try {
            setSearchLoading(true);
            const res = await nsMockManageApiManageMainApiGlobalSearchListGET.remote({
                key: key.trim(),
                pageNo: 1,
                pageSize: 10,
                apiIds: localStorage.getItem(LOCAL_STORAGE_KEYS.API_MGR_RECENT_VIEWS_API_IDS) || ''
            });
            setGlobalSearchResults(res.total);
        } catch {
        } finally {
            setSearchLoading(false);
        }
    };

    const handleSearch = debounce(async (key: string) => {
        setCurrentKeyword(key);
        if (isExceedByteLimit(key, 3) && key !== '') {
            message.warn('最少需要输入3个字符或者2个汉字');
            // return;
        }
        if (relevantToMeContext.apiType === 'all') {
            // 数据可能实时变化，不要拦截
            // if (key === relevantToMeContext.search) {
            //     return;
            // }
            relevantToMeContext.setSearch!(key);
            try {
                if (!!key) {
                    setSearchApiKeyword(key);
                    relevantToMeContext.setSearchLoading(true);
                    setSearchLoading(true);
                    const userLastGroup = window['search'] ?
                        JSON.parse(localStorage.getItem(KDEV_API_SEARCH_GROUP) || '{}') :
                        JSON.parse(localStorage.getItem(KDEV_API_GROUP_REPO) || '{}');
                    const { list } = await nsApiManagerSearch.remote({ key, currentGroupId: userLastGroup.groupId });
                    relevantToMeContext.setLoadLock(false);
                    relevantToMeContext.setSearchExpandedKeys(expandAll(list));
                    relevantToMeContext.setLoadLock(true);
                    setSearchLength(list.length);
                    relevantToMeContext.setSearchLoading(false);
                    relevantToMeContext.setSearchTreeData!(list);
                    setSearchLoading(false);
                    onSearchApi(key);
                } else {
                    setCurrentKeyword('');
                    setSearchLength(0);
                    setGlobalSearchResults(0);
                    const list = await relevantToMeContext.getSearchTreeDataInit();
                    relevantToMeContext.setTreeData(list);
                }
            } catch (e) { }
        } else {
            // key不为空的时候
            if (!isExceedByteLimit(key, 3)) {
                setSearchApiKeyword(key);
                onSearchApi(key);
                setSearchLoading(true);
                relevantToMeContext!.onSearchTreeData!(key)
                setSearchLoading(false);
                // relevantToMeContext!.searchWithMe!(key);
            } else {
                relevantToMeContext!.searchWithMe!(key);
            }
        }
    }, 1000)

    const renderMoreResultsButton = (onClick: () => void, results: number) => (
        <div
            style={{
                textAlign: 'center',
                borderTop: '1px solid #f0f0f0',
                borderRadius: '0px 0px 5px 5px',
            }}
        >
            <Button
                type="link"
                className={css.moreResultsButton}
                onClick={onClick}
            >
                更多结果({results})
            </Button>
        </div>
    );
    return (
        <div className={css.searchBox} style={{ marginBottom: 12 }}>
            <TreeSelect
                ref={searchInputRef}
                style={{ flex: 1, position: 'relative' }}
                placeholder="搜索目录/API"
                allowClear
                showSearch
                suffixIcon={null}
                onSearch={handleSearch}
                popupClassName={css.searchTreeResult}
                dropdownStyle={{
                    padding: 0,
                    zIndex: 100
                }}
                dropdownRender={(menu) => (
                    <>
                        {!isExceedByteLimit(currentKeyword, 3) && !relevantToMeContext.searchLoading && (
                            relevantToMeContext.apiType === 'withMe' ? (
                                <div>
                                    <div style={{
                                        overflowX: 'hidden',
                                        maxHeight: '440px',
                                    }}>
                                        <RelevantDirectoryTree
                                            isSearch={true}
                                            currentKeyword={searchApiKeyword}
                                            hideCollectAndMore={true}
                                        />
                                    </div>
                                    {renderMoreResultsButton(() => {
                                        setSearchVisible(true);
                                    }, globalSearchResults)}
                                </div>
                            ) : (
                                <div>
                                    {searchLength > 0 ? (
                                        <SearchResultTree />
                                    ) : (
                                        <KEmpty
                                            image="NOMAL_SIMPLE_SEARCH"
                                            description="本团队目录下，暂无数据"
                                            className={css.kEmpty}
                                        />
                                    )}
                                    {renderMoreResultsButton(() => {
                                        setSearchVisible(true);
                                    }, globalSearchResults)}
                                </div>
                            )
                        )}
                    </>

                )}
            />
            <SearchApi
                hideSearchApi={true}
                visible={searchVisible}
                onVisibleChange={setSearchVisible}
                keyword={searchApiKeyword}
                onKeywordChange={(keyword) => {
                    setSearchApiKeyword(keyword);
                }}
            />
            <DirMenu model={dirMenuM} type="addBtn">
                <Button
                    type="primary"
                    style={{ flexShrink: 0, marginLeft: '10px' }}
                    icon={<KdevIconFont id={common_system_add} />}
                />
            </DirMenu>
        </div>
    );
};
