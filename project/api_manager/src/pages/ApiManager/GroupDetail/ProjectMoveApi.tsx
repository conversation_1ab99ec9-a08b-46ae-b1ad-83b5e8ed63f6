import React, { forwardRef, Ref, useEffect, useImperativeHandle, useState } from 'react';
import { message, Modal, TreeSelect } from 'antd';
import {
    nsApiManagerBatchMove,
    nsApiManagerSearch,
    nsMockManageApiManageMainApiProgramCatalogMove,
    nsMockManageApiManageMainApiProgramCatalogRootList,
    nsMockManageApiManageMainApiProgramCatalogSubList,
    nsMockManageApiManageMainApiProgramApiMove,
    nsMockManageApiManageMainCollectListGET
} from '@/remote';
import css from './GroupDetail.less';
import classNames from 'classnames';
import { RepoGroupSelect } from '@/business/httpApiComponents/RepoGroupSelect';



export interface IProjectMoveApiRef {
    onOpen(projectId: number, type: string, id: number, parentId: number): void;
}

interface IProps {
    onMoveCallback?(targetGroupKey: string, curNodeData: nsApiManagerBatchMove.IItem[]): void;
}

export const ProjectMoveApi = forwardRef((props: IProps, ref: Ref<IProjectMoveApiRef>) => {
    const [visible, setVisible] = useState<boolean>(false);
    const [catalogId, setCatalogId] = useState<number>();
    const [catalogList, setCatalogList] = useState<any[]>([]);
    const [selectedCatalogKey, setSelectedCatalogKey] = useState<string>('');
    const [type, setType] = useState<string>();
    const [id, setId] = useState<number>();
    const [parentId, setParentId] = useState<number>();

    const handleOk = async () => {
        if (!selectedCatalogKey) return;
        const targetId = +selectedCatalogKey!.split('-').pop()!;
        if (type === 'CATALOG') {
            await nsMockManageApiManageMainApiProgramCatalogMove.remote({
                catalogId: id!,
                targetParentId:targetId
            });
        } else {
            await nsMockManageApiManageMainApiProgramApiMove.remote({
                apiId: id!,
                originCatalogId: parentId!,
                targetCatalogId:targetId!
            });
        }
        message.success('移动成功');
        setVisible(false);
        props.onMoveCallback?.(selectedCatalogKey!, [{
            id: id!,
            type: type as unknown as nsMockManageApiManageMainCollectListGET.ENodeType,
        }]);
    };

    useEffect(() => {
        getCatalogList();
    }, [catalogId]);

    const onOpen = (projectId: number, typeArg: string, idArg: number, parentIdArg: number) => {
        setType(typeArg);
        setId(idArg);
        setParentId(parentIdArg);
        setCatalogId(projectId);
        setVisible(true);
    };

    useImperativeHandle(ref, () => ({
        onOpen
    }));

    // 递归查找节点
    const findNodeById = (list: any[], idArg: number): any => {
        for (const node of list) {
            if (node.id === idArg) return node;
            if (node.children) {
                const found = findNodeById(node.children, idArg);
                if (found) return found;
            }
        }
        return null;
    };

    const deepSearchCatalog = (list: any[], idArg?: number) => {
        for (const node of list) {
            if (node.id === idArg) return node;
            if (node.children) {
                const found = deepSearchCatalog(node.children, idArg);
                if (found) return found;
            }
        }
        return null;
    }

    const getCatalogList = async () => {
        if (!catalogId) return;
        try {
            const res = await nsMockManageApiManageMainApiProgramCatalogRootList.remote({
                programId: catalogId
            });
            const formattedList = formatCatalogList(res);
            setCatalogList(formattedList);
        } catch (error) {
            console.error('获取目录列表失败:', error);
            // message.error('获取目录列表失败');
        }
    };

    const formatCatalogList = (list: any[], baseName: string = '-') => {
        return list.filter(item => item.type === 'CATALOG').map(item => ({
            ...item,
            key: (item.type || 'CATALOG') + baseName + item.id,
            value: (item.type || 'CATALOG') + baseName + item.id,
            title: item.name,
            label: item.name,
            isLeaf: item.type === 'API',
            hasChild: item.hasChild === undefined ? true : item.hasChild,
            selectable: item.type === 'CATALOG', // 只允许选择目录节点
            children: item.children ? formatCatalogList(item.children, baseName + item.id + '-') : []
        }));
    };

    return <Modal
        title="移动"
        visible={visible}
        okButtonProps={{ disabled: !selectedCatalogKey }}
        onOk={handleOk}
        onCancel={() => setVisible(false)}
        destroyOnClose
    >
        <div>
            <div className={classNames(css.title, css.require)}>目录选择</div>
            <TreeSelect
                className={css.catalogSelect}
                treeData={catalogList}
                value={selectedCatalogKey}
                onChange={(value) => {
                    setSelectedCatalogKey(value ||'');
                }}
                placeholder="请选择目录"
                style={{ width: '100%' }}
                loadData={async (node) => {
                    const { id: idArg } = node;
                    try {
                        const res = await nsMockManageApiManageMainApiProgramCatalogSubList.remote({
                            id: parseInt(idArg, 10)
                        });
                        res.forEach(item => {
                            item.type = item.type.toUpperCase() as 'CATALOG' | 'API';
                        });
                        const baseKey = '-' + node.key.toString().split('-').slice(1).join('-') + '-';
                        const newList = [...catalogList];
                        const target = findNodeById(newList, parseInt(idArg, 10));
                        if (target) {
                            target.children = formatCatalogList(res, baseKey);
                            setCatalogList(newList);
                        }
                    } catch (error) {
                        message.error('加载子目录失败');
                    }
                }}
                treeDefaultExpandAll={false}
                showSearch
                allowClear
                treeNodeFilterProp="title"
            />
        </div>
    </Modal>;
});
