// 接口目录（全部接口&与我相关）
import React, { useState, useEffect } from 'react';
import { Header } from '@/pages/ApiManager/Header';
import { getUrlSearch, pushKey } from '@/index.config/tools';
import { ApiManageProjectView } from '@/pages/ApiManager/ProjectView/ApiManageProjectView';
import { ERouter } from 'CONFIG';

/**
 * API管理-接口目录
 */

export enum ViewTypeMap {
    REPO_VIEW = 'repoView',
    PROJECT_VIEW = 'projectView'
}


export default function ApiMenuEntry() {

    // const [viewType, setViewType] = useState<ViewTypeMap>(ViewTypeMap.REPO_VIEW);

    // useEffect(() => {
    //     getViewType();
    // }, []);

    // document.addEventListener('viewTypeChange', ((e: any) => {
    //     pushKey({
    //         viewType: e.detail.viewType
    //     });
    //     getViewType();
    // }) as EventListener);

    // function getViewType() {
    //     const searchParams = getUrlSearch() as { viewType: string };
    //     const localViewType = localStorage.getItem('apiManagerViewType');

    //     // 如果URL中没有viewType参数，但localStorage中有
    //     if (!searchParams.viewType && localViewType) {
    //         pushKey({
    //             viewType: localViewType
    //         });
    //         searchParams.viewType = localViewType;
    //     }

    //     // 如果仍然没有viewType，使用默认值
    //     if (!searchParams.viewType) {
    //         searchParams.viewType = ViewTypeMap.REPO_VIEW;
    //     }

    //     if (searchParams.viewType === ViewTypeMap.PROJECT_VIEW) {
    //         setViewType(ViewTypeMap.PROJECT_VIEW);
    //     } else {
    //         setViewType(ViewTypeMap.REPO_VIEW);
    //     }
    //     localStorage.setItem('apiManagerViewType', searchParams.viewType);
    // }

    if (location.pathname === ERouter.API_MOCK_REPO_MGR) {
        localStorage.setItem('apiManagerViewType', ViewTypeMap.REPO_VIEW);
    } else {
        localStorage.setItem('apiManagerViewType', ViewTypeMap.PROJECT_VIEW);
    }


    return location.pathname === ERouter.API_MOCK_REPO_MGR ? <Header /> : <ApiManageProjectView />;
}
