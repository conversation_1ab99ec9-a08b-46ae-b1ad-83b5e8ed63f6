import React, { useState, createContext, useEffect, useContext, useRef } from 'react';
import css from './RequestInfo.less';
import * as ToolFun from '../tools';
import { ApiHttpProxyContextType, ApiHttpProxyContext, requestType } from '../ApiHttpProxy/ApiHttpProxy';
import { EMethod, MethodTag } from '@/business/httpApiComponents/MethodTag';
import PopoverEllipsis from '@/business/common/PopoverEllipsis';
import { ERouter } from '@/CONFIG';
import { KdevIconFont, AceEditor, KEmpty, FileUploader } from '@/business/commonComponents';
import { common_system_arrowshang, common_system_arrowxia, common_system_copy02, common_system_export, common_system_more } from '@kid/enterprise-icon/icon/output/icons';
import copy from 'copy-to-clipboard';
import { Button, Input, message, Popover, Radio, Segmented, Tooltip, Select } from 'antd';
import classNames from 'classnames';
import { beautifyStr<PERSON><PERSON>, isJsonString } from '@/index.config/tools';
import {
    nsKdevUser,
    nsKdevUserListGET,
    nsMockManageApiManageMainProxyCaptureV2CurlCopy,
    nsMockManageApiManageMainProxyCaptureV2CurlCopyKimCardLinkPost,
} from '@/remote'
import { ApiHttpProxyDrawerContext, ApiHttpProxyDrawerContextType } from '../DrawerCom/DrawerCom';
import { KDevParticipants } from '@/business/common/KDevParticipants';
import { SearchUser } from 'kdev-fe-common/src/shared/common/kdevSearch';
import { ECLICK_POINT, uploadClickEvent } from '@/index.config';
import { DirectorTypeMap } from '../AddRule/AddRule';
export interface PropsType {
    // url: string
}

function getBase64Pre(
    selectRequest: requestType
) {
    const contentType = selectRequest!.responseInfo.header.find(i => i.key === 'Content-Type')?.value;
    if (contentType?.includes('png')) { return 'data:image/png;base64,' }
    if (contentType?.includes('jpg')) { return 'data:image/jpeg;base64,' }
    if (contentType?.includes('jpeg')) { return 'data:image/jpeg;base64,' }
    if (contentType?.includes('gif')) { return 'data:image/gif;base64,' }
    if (contentType?.includes('bmp')) { return 'data:image/bmp;base64,' }
    if (contentType?.includes('webp')) { return 'data:image/webp;base64,' }
    if (contentType?.includes('svg')) { return 'data:image/svg+xml;base64,' }
    if (contentType?.includes('ico')) { return 'data:image/x-icon;base64,' }
    if (contentType?.includes('heic')) { return 'data:image/heic;base64,' }
    if (contentType?.includes('heif')) { return 'data:image/heif;base64,' }
    return ''
}
function isImage(
    type: string,
    radioValue: string,
    selectRequest: requestType
) {
    if (type === 'res' &&
        radioValue === 'body' &&
        selectRequest!.responseInfo!.header.find(i => i.key === 'Content-Type')?.value.startsWith('image')
    ) {
        return true
    }
    return false
}
function isFile(
    type: string,
    radioValue: string,
    selectRequest: requestType
) {
    if (type === 'res' &&
        radioValue === 'body' &&
        selectRequest!.responseInfo.bodyType === 'file'
    ) {
        return true
    }
    return false
}

const getUserList: (value: string) => Promise<nsKdevUser.IUser[]> = async (value) => {
    const params = {
        search: value.trim()
    };
    const respData = await nsKdevUserListGET.remote(params);
    return Promise.resolve(respData.list);
}

export function RequestInfo(props: PropsType) {
    const context = useContext<ApiHttpProxyContextType>(ApiHttpProxyContext);
    const drawerContext = useContext<ApiHttpProxyDrawerContextType>(ApiHttpProxyDrawerContext);
    const [reqWidth, setReqWidth] = useState<number>(50);

    const handleMouseMove = (e: MouseEvent) => {
        const container = document.querySelector(`.${css.other}`);
        if (!container) return;

        const containerRect = container.getBoundingClientRect();
        const percentage = ((e.clientX - containerRect.left) / containerRect.width) * 100;

        const newWidth = Math.min(Math.max(percentage, 20), 80);
        setReqWidth(newWidth);
    };

    const handleMouseUp = () => {
        document.removeEventListener('mousemove', handleMouseMove);
        document.removeEventListener('mouseup', handleMouseUp);
    };

    const startDragging = () => {
        document.addEventListener('mousemove', handleMouseMove);
        document.addEventListener('mouseup', handleMouseUp);
    };

    if (!context.selectRequest) {
        return <div
            style={{
                height: '100%',
                display: 'flex',
                justifyContent: 'center',
                alignItems: 'center',
                overflow: 'hidden'
            }}>
            <KEmpty image="NOMAL_SIMPLE_EMPTY"
                description={<span>暂无数据，请点击抓包日志数据进行查看</span>}></KEmpty>
        </div>
    }
    return (
        <div className={css.requestInfo}>
            <div className={css.header}>
                <div className={css.headerLeft}>
                    <MethodTag
                        method={context.selectRequest!.method as EMethod} style={{ fontSize: 14 }} />
                    {context.selectRequest!.httpCode && <span
                        className={css.httpCodeTag}
                        style={{ background: ToolFun.tagColor(+(context.selectRequest!.httpCode || '')) }}
                    >
                        {context.selectRequest!.httpCode}
                    </span>}
                    <PopoverEllipsis
                        title={`${context.selectRequest!.domain}${context.selectRequest!.url}${context.selectRequest!.queryString}`}
                        overlayStyle={{
                            maxWidth: 800
                        }}
                    >
                        <span className={css.textEllipsis}>
                            {
                                context.selectRequest!.apiId && <div>
                                    {context.selectRequest!.domain}
                                    <a
                                        target="_blank"
                                        href={`${ERouter.API_MOCK_REPO_MGR}?apiId=${context.selectRequest!.apiId}`}
                                    >
                                        {context.selectRequest!.url}
                                    </a>
                                    {context.selectRequest!.queryString}
                                </div>
                            }
                            {
                                !context.selectRequest!.apiId &&
                                `${context.selectRequest!.domain}${context.selectRequest!.url}${context.selectRequest!.queryString}`
                            }
                        </span>
                    </PopoverEllipsis>
                </div>
                <div className={css.headerRight}>
                    <Tooltip title={'复制url'}>
                        <span
                            className={classNames(css.copy, css.icon)}
                            onClick={() => {
                                copy(`${context.selectRequest!.domain}${context.selectRequest!.url}${context.selectRequest!.queryString}`);
                                message.success('复制成功');
                                uploadClickEvent({ record_type: ECLICK_POINT.COPY_URL });
                            }}
                        >
                            {/* <KdevIconFont id={common_system_copy02} style={{ color: '#326BFBff' }} /> */}
                            <span style={{ color: '#326BFBff' }}>copy</span>
                        </span>
                    </Tooltip>
                    <Tooltip title={'导出Curl'}>
                        <span
                            className={classNames(css.curl, css.icon)}
                            onClick={async () => {
                                const res = await nsMockManageApiManageMainProxyCaptureV2CurlCopy.remote({
                                    domain: context.selectRequest!.domain,
                                    url: context.selectRequest!.url,
                                    method: context.selectRequest!.method,
                                    requestInfo: context.selectRequest!.requestInfo,
                                })
                                copy(res);
                                message.success('复制cURL成功');
                                uploadClickEvent({ record_type: ECLICK_POINT.COPY_CURL });
                            }}
                        >
                            {/* <KdevIconFont id={common_system_export} style={{ color: '#326BFBff' }} /> */}
                            <span style={{ color: '#326BFBff' }}>cURL</span>
                        </span>
                    </Tooltip>
                    <Tooltip title={'分享cURL'}>
                        <span className={classNames(css.curl, css.icon)} onClick={() => {
                            nsMockManageApiManageMainProxyCaptureV2CurlCopyKimCardLinkPost
                                .remote({
                                    domain: context.selectRequest!.domain,
                                    url: context.selectRequest!.url,
                                    method: context.selectRequest!.method,
                                    requestInfo: context.selectRequest!.requestInfo,
                                }).then((res) => {
                                    copy(res)
                                    message.success('已复制cURL，请在kim中分享');
                                })
                            uploadClickEvent({ record_type: ECLICK_POINT.SHEAR_CURL });
                        }}>
                            <span style={{ color: '#326BFBff' }}>分享</span>
                        </span>
                    </Tooltip>
                    <span
                        className={classNames(css.more, css.icon)}
                    >
                        <Popover
                            overlayClassName={css.popover}
                            placement="bottomRight"
                            content={
                                <div className={css.ul}>
                                    <span className={css.li} onClick={() => {
                                        drawerContext.setIsAddRuleModalOpenFn(true);
                                        requestAnimationFrame(() => {
                                            drawerContext.addRuleRef.current?.set({
                                                matchKey: 'path',
                                                matchRule: 'prefix',
                                                matchValue: context.selectRequest!.url,
                                                behaviorList: [{
                                                    behaviorType: 'REQUEST_REDIRECT', // 行为类型 
                                                    directorType: DirectorTypeMap.URL
                                                }],
                                                ruleGroupId: context.currentRuleGroup!.id
                                            })
                                            uploadClickEvent({
                                                record_type: ECLICK_POINT.QUICK_RULE_ADD,
                                                quick_rule_type: 'request_redirect'
                                            });
                                        })
                                    }}>
                                        请求重定向
                                    </span>
                                </div>
                            }>
                            <span><KdevIconFont id={common_system_more} style={{ color: '#326BFBff' }} /></span>
                        </Popover>
                    </span>
                </div>
            </div>
            <div className={css.other}>
                <div className={css.req} style={{ width: `${reqWidth}%` }}>
                    <RenderContent type="req"></RenderContent>
                </div>
                <div className={css.borderBox} onMouseDown={startDragging}>
                    <div className={css.pointWarp}>
                        <div className={css.point}></div>
                        <div className={css.point}></div>
                        <div className={css.point}></div>
                    </div>
                </div>
                <div className={css.res} style={{ width: `${100 - reqWidth}%` }}>
                    {context.selectRequest!.responseInfo && <RenderContent type="res"></RenderContent>}
                </div>
            </div>
        </div>
    )
}

interface Props {
    type: 'req' | 'res'
}
function RenderContent(props: Props) {
    const context = useContext<ApiHttpProxyContextType>(ApiHttpProxyContext);
    const drawerContext = useContext<ApiHttpProxyDrawerContextType>(ApiHttpProxyDrawerContext);
    const requestRadio = Object.keys(context.selectRequest!.requestInfo).filter(item => item !== 'bodyType');
    const responseRadio = context.selectRequest!.responseInfo ?
        Object.keys(context.selectRequest!.responseInfo).filter(item => item !== 'bodyType') : []
    const [radioValue, setRadioValue] = useState<string>(requestRadio[0])
    const [showPopover, setShowPopover] = useState<boolean>(false)

    const aceEditorRef = useRef<AceEditor>(null);

    return <>
        <div
            className={css.contentTop}
        >
            <div className={css.contentTopSearchBox}>{props.type === 'req' ? 'Request' : 'Response'}</div>
            <Segmented
                // size="small"
                options={props.type === 'req' ? requestRadio : responseRadio}
                onChange={(e) => {
                    setRadioValue(e as string)
                }}
            />

            <Popover
                onOpenChange={(e) => {
                    setShowPopover(e);
                }}
                overlayClassName={css.popover}
                placement="bottomRight"
                content={
                    <div className={css.ul}>
                        {props.type === 'req' && <span className={css.li} onClick={() => {
                            drawerContext.setIsAddRuleModalOpenFn(true);
                            requestAnimationFrame(() => {
                                drawerContext.addRuleRef.current?.set({
                                    matchKey: 'path',
                                    matchRule: 'prefix',
                                    matchValue: context.selectRequest!.url,
                                    behaviorList: [{
                                        behaviorType: 'REQUEST_HEADER', // 行为类型 
                                        requestHeader: [
                                            {
                                                key: '',
                                                value: '',
                                                id: +new Date()
                                            }
                                        ]
                                    }]
                                })
                                uploadClickEvent({
                                    record_type: ECLICK_POINT.QUICK_RULE_ADD,
                                    quick_rule_type: 'request_header'
                                });
                            })
                        }}>
                            修改请求头
                        </span>}
                        {props.type === 'res' && <span className={css.li} onClick={() => {
                            drawerContext.setIsAddRuleModalOpenFn(true);
                            requestAnimationFrame(() => {
                                drawerContext.addRuleRef.current?.set({
                                    matchKey: 'path',
                                    matchRule: 'prefix',
                                    matchValue: context.selectRequest!.url,
                                    behaviorList: [{
                                        behaviorType: 'RESPONSE_HEADER', // 行为类型 
                                        responseHeader: [{
                                            key: '',
                                            value: '',
                                            id: +new Date()
                                        }]
                                    }]
                                })
                                uploadClickEvent({
                                    record_type: ECLICK_POINT.QUICK_RULE_ADD,
                                    quick_rule_type: 'response_header'
                                });
                            })
                        }}>
                            修改响应头
                        </span>}
                        {props.type === 'res' && <span className={css.li} onClick={() => {
                            const res = context.selectRequest!.responseInfo!.body
                            const body = isJsonString(res) ? beautifyStrJson(res) : res
                            drawerContext.setIsAddRuleModalOpenFn(true);
                            requestAnimationFrame(() => {
                                drawerContext.addRuleRef.current?.set({
                                    matchKey: 'path',
                                    matchRule: 'prefix',
                                    matchValue: context.selectRequest!.url,
                                    behaviorList: [{
                                        behaviorType: 'RESPONSE_MODIFY_POST_BODY', // 行为类型 
                                        responseBody: body
                                    }]
                                });
                                uploadClickEvent({
                                    record_type: ECLICK_POINT.QUICK_RULE_ADD,
                                    quick_rule_type: 'response_body'
                                });
                            })
                        }}>
                            修改响应体
                        </span>}
                    </div>
                }>
                <span className={css.more}
                    style={{ color: showPopover ? '#204DD4ff' : '#326BFBff' }}
                >
                    {props.type === 'req' ? '修改请求' : '修改返回'}
                    <KdevIconFont
                        style={{
                            marginLeft: '4px'
                        }}
                        id={showPopover ? common_system_arrowshang : common_system_arrowxia}
                    />
                </span>
            </Popover >
            {
                radioValue === 'body' && <>
                    <span className={css.expandAll} onClick={() => {
                        aceEditorRef.current?.expandAll()
                    }}>全部展开</span>
                    <span className={css.unExpandAll} onClick={() => {
                        aceEditorRef.current?.unExpandAll()
                    }}>全部收起</span>
                </>
            }

        </div >
        {
            <RenderContentDetail
                data={
                    props.type === 'req' ?
                        context.selectRequest!.requestInfo :
                        context.selectRequest!.responseInfo
                }
                selectRequest={context.selectRequest!}
                radioValue={radioValue}
                type={props.type}
                aceEditorRef={aceEditorRef}
            ></RenderContentDetail>
        }
    </>

}

interface RenderContentDetailType {
    data: any,
    radioValue: string,
    type: string,
    aceEditorRef: React.RefObject<AceEditor>,
    selectRequest: requestType
}

function RenderContentDetail(props: RenderContentDetailType) {
    const [clickIndex, setClickIndex] = useState<{
        header: number,
        query: number,
        heaformder: number,
    }>({
        header: -1,
        query: -1,
        heaformder: -1,
    })
    if (props.radioValue === 'body' && isImage(props.type, props.radioValue, props.selectRequest!)) {
        return (
            <div style={{ flex: '1 0 0', overflow: 'hidden' }}>
                <img
                    style={{
                        width: '100%',
                        height: '100%',
                        objectFit: 'contain'
                    }}
                    src={getBase64Pre(props.selectRequest!) + props.data.body} alt=""
                />
            </div>
        );
    } else if (props.radioValue === 'body' && isFile(props.type, props.radioValue, props.selectRequest!)) {
        return (
            <div style={{ flex: '1 0 0', overflow: 'hidden' }}>
                <FileUploader
                    fileName={JSON.parse(props.data.body).fileName}
                    fileUrl={JSON.parse(props.data.body).fileUrl}
                    readonly={true}
                />
            </div>
        );
    } else if (props.radioValue === 'body') {
        const aceValue = isJsonString(props.data.body) ? beautifyStrJson(props.data.body) : props.data.body;
        return (
            <div style={{ flex: '1 0 0', overflow: 'hidden' }}>
                <AceEditor
                    ref={props.aceEditorRef}
                    theme="xcode"
                    width="100%"
                    style={{ height: '100%', overflow: 'auto' }}
                    showPrintMargin={false}
                    readOnly
                    value={aceValue || ''}
                />
            </div>
        );
    }
    return (
        <div className={css.contentBottom}>
            {
                props.data[props.radioValue]?.map((item, index) => (
                    <div
                        key={index}
                        className={
                            classNames(
                                css.row,
                                clickIndex[props.radioValue] === index && css.rowClick
                            )
                        }
                        onClick={() => {
                            setClickIndex({
                                ...clickIndex,
                                [props.radioValue]: index
                            })
                        }}
                    >
                        <div className={css.contentBottomLeft}>
                            {item.key}：
                        </div>
                        <div className={css.contentBottomRight}>
                            {item.value}
                        </div>
                    </div>
                ))
            }
        </div>
    );
}