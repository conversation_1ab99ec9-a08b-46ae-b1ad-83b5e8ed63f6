.ruleLinkProxy {
    width: 100%;
    padding: 12px 20px;
    box-sizing: border-box;
    display: flex;
    justify-content: space-between;
    border-bottom: 1px solid #EBEDF0;

    .link {
        display: flex;
        gap: 16px;
        align-items: center;

        :global {
            .ant-select {
                border-radius: 4px;
            }

            .ant-select-selector {
                padding: 0 4px;
                display: flex;
                align-items: center;
                justify-content: space-between;

                &::before {
                    content: '场景';
                    display: flex;
                    height: 24px;
                    padding: 3px 8px;
                    justify-content: center;
                    align-items: center;
                    border-radius: 4px;
                    background: #fff;
                    color: #575859ff;
                    font-size: 14px;
                    margin-right: 12px;
                    font-weight: 400;
                }
            }

            .ant-select-item-option-selected {
                background: #F5F7FAff !important;
            }

            .ant-select-selection-item {
                font-weight: 500;
            }
        }

        .title {
            color: #252626;
            font-size: 16px;
            font-weight: 500;
        }
    }

    .options {
        display: flex;
        gap: 8px;
    }
}

.linkPop {
    width: 328px;

    .proxyAddress {
        display: flex;
        padding: 5px 8px;
        justify-content: center;
        align-items: center;
        gap: 10px;
        align-self: stretch;
        border-radius: 4px;
        background: var(---bg_tag, #F5F7FA);

        .proxyAddressUrl {
            flex: 1 0 0;
            width: 0;
            white-space: pre-wrap;
            word-break: break-all;
        }

        .proxyAddressCopy {
            cursor: pointer;
        }
    }
}

.tip {
    color: #252626ff;
}

.linkDescribe {
    color: #326BFBff
}

.proxyAddressPopup {
    .proxyAddressItem {
        display: flex;
        justify-content: space-between;
        align-items: center;
        width: 100%;

        >span {
            flex: 1;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            margin-right: 8px;
        }

        &:hover {

            .editDelete {
                display: block;
            }
        }

        .editDelete {
            display: none;
        }
    }
}