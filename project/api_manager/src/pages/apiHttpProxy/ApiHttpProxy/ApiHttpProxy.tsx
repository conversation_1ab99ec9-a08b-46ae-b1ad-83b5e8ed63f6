import React, { useState, createContext, useEffect, useCallback, useMemo, useRef } from 'react';
import css from './ApiHttpProxy.less';
import AppDomainfilter from '../AppDomainfilter/AppDomainfilter';
import UrlFilter from '../UrlFilter/UrlFilter';
import RequestList from '../RequestList/RequestList';
import LinkProxy from '../LinkProxy/LinkProxy';
import {
    nsMockManageApiManageMainProxyCaptureV2AddressList,
    nsMockManageApiManageMainProxyCaptureV2ScanCode,
    nsMockManageApiManageMainProxyCaptureV2SettingStartUp,
    nsMockManageApiManageMainProxyCaptureV2Start,
    nsMockManageApiManageMainProxyCaptureV2Stop,
    nsMockManageApiManageMainProxyCaptureTypeList,
    nsMockManageApiManageMainProxyCaptureV2RuleGroupSearch,
    nsMockManageApiManageMainProxyCaptureV2RuleGroupDetail,
    nsMockManageApiManageMainProxyCaptureV2RuleList,
    nsMockManageApiManageMainProxyCaptureV2RuleGroupList,
    nsMockManageApiManageMainProxySaveNamePOST,
    nsMockManageApiManageMainProxyRemoveGET,
    nsMockManageApiManageMainProxySavePOST,
    nsMockManageApiManageMainProxyCaptureV2ScanCodeWifi,
    nsMockManageApiManageMainProxyCaptureV2RuleGroupEnable
} from '@/remote'
import { LinkSdk } from '@im/link-sdk-ws';
import { uint8ArrayToText } from '@im/im-common-utils';
import { message, Modal, notification } from 'antd';
import { DrawerCom } from '../DrawerCom/DrawerCom';
import { debounce } from 'lodash';
import { RuleGroupInfo } from '../RuleGroupInfo/RuleGroupInfo';
import { RequestInfo } from '../RequestInfo/RequestInfo';
import { ERouter } from '@/CONFIG';
import RuleLinkProxy from '../RuleLinkProxy/RuleLinkProxy';
import { AddAdress, AddAdressRef } from '../AddAdress/AddAdress';
import { DeleteAdress, DeleteAdressRef } from '../DeleteAdress/DeleteAdress';
import { KdevIconFont } from '@/business/commonComponents';
import {
    common_system_notice_mian, common_system_xiaojiantouyou, common_system_xiaojiantouzuo
} from '@kid/enterprise-icon/icon/output/icons';
import SplitPane from 'react-split-pane';
import Pane from 'react-split-pane/lib/Pane';
import { useLocalStorageState } from 'ahooks';
import classNames from 'classnames';

const linkAddress_staging = ['wss://im-link-staging.corp.kuaishou.com/ws'];
const linkAddress_prod = [
    'wss://klink-newproduct-ws1.kuaishouzt.com',
    'wss://klink-newproduct-ws2.kuaishouzt.com',
    'wss://klink-newproduct-ws3.kuaishouzt.com',
];

const RuleGroupStatusMap = {
    add: '新建',
    edit: '编辑',
}

const ALL_TYPE = '全部';

const WifiMap = {};

export interface requestType {
    apiId: number | null,
    domain: string,
    hitRuleKey: null,
    hitRuleList: { key: string, value: string }[],
    httpCode: string | null,
    id: string,
    logTime: number,
    method: string,
    queryString: string,
    requestInfo: {
        body: string,
        bodyType: string,
        header: { key: string, value: string }[],
        form: { key: string, value: string }[],
        query: { key: string, value: string }[] | null,
    }
    responseInfo: {
        body: string,
        bodyType: string,
        header: { key: string, value: string }[],
        query: { key: string, value: string }[] | null,
    } | null
    requestType: string,
    respTime: string,
    ruleType: number,
    url: string
}

export interface linkInfoType {
    deviceName: string
}

export enum LinkWay {
    NAVITE = 'NAVITE',
    LOCAL = 'LOCAL',
    WIFI = 'WIFI',
}

export interface ApiHttpProxyContextType {
    requestList: requestType[];
    setRequestList: (arr: requestType[]) => void;

    filterRequestList: requestType[];
    setFilterRequestList: (val: requestType[]) => void;

    domainList: domainType[];
    setDomainList: (arr: domainType[]) => void;

    selectedDomain: string[];
    setSelectedDomain: (domain: string[]) => void;

    urlFilterText: string;
    setUrlFilterText: (text: string) => void;

    isCapture: boolean;
    setIsCapture: (value: boolean) => void;

    linkInfo: linkInfoType;
    setLinkInfo: (value: linkInfoType) => void;

    linkWay: LinkWay;
    setLinkWay: (value: LinkWay) => void;

    ruleLinkWay: LinkWay;
    setRuleLinkWay: (value: LinkWay) => void;

    qrCode: string;
    setQrCode: (value: string) => void;

    startUp: boolean;
    setStartUp: (value: boolean) => void;

    proxyAddress: string;
    setProxyAddress: (value: string) => void;

    filterRequestByRule: string;
    setFilterRequestByRule: (value: string) => void;

    proxyAddressList: nsMockManageApiManageMainProxyCaptureV2AddressList.RuleGroupType[];
    getAddressList: () => void;

    usedProxyAddress: nsMockManageApiManageMainProxyCaptureV2AddressList.RuleGroupType | null;
    setUsedProxyAddress: (v: nsMockManageApiManageMainProxyCaptureV2AddressList.RuleGroupType | null) => void;

    allTypeList: string[];
    setAllTypeList: (v: string[]) => void;

    selectTypeList: string[];
    setSelectTypeList: (v: string[]) => void;
    getRuleGroupList: () => Promise<any>;

    requestTypeList: string[];

    selectRequestTypeList: string[];
    setSelectRequestTypeList: (v: string[]) => void;

    responseCodeList: string[];
    selectResponseCodeList: string[];
    setSelectResponseCodeList: (v: string[]) => void;

    selectRequestId: string;
    setSelectRequestId: (v: string) => void;

    selectRequest: requestType | null;
    setSelectRequest: (v: requestType) => void;

    ruleGroupList: nsMockManageApiManageMainProxyCaptureV2RuleGroupSearch.RuleGroupType[];
    setRuleGroupList: (v: nsMockManageApiManageMainProxyCaptureV2RuleGroupSearch.RuleGroupType[]) => void;

    currentRuleGroup: nsMockManageApiManageMainProxyCaptureV2RuleGroupSearch.RuleGroupType | null;
    setCurrentRuleGroup: (v: nsMockManageApiManageMainProxyCaptureV2RuleGroupSearch.RuleGroupType | null) => void;
    resetCurrentRuleGroup: (v: number) => void;

    drawerHeight: number;
    setDrawerHeight: (v: number) => void;

    loadingCurrentRuleGroup: boolean;
    setLoadingCurrentRuleGroup: (v: boolean) => void;

    ruleList: nsMockManageApiManageMainProxyCaptureV2RuleList.RuleType[];
    setRuleList: (v: nsMockManageApiManageMainProxyCaptureV2RuleList.RuleType[]) => void;
    getRuleList: () => void;

    addRuleGroupId: number;
    setAddRuleGroupId: (v: number) => void;

    drawerComTabValue: string;
    setDrawerComTabValue: (v: string) => void;

    isCaptureLoading: boolean;
    setIsCaptureLoading: (v: boolean) => void;

    drawerTabsItems: { label: any, key: string, children: any }[];
    setDrawerTabsItems: (v: { label: any, key: string, children: any }[]) => void;

    isChangeDrawerHeight: boolean;
    setIsChangeDrawerHeight: (v: boolean) => void;

    setIsAddAdressModalOpen: (v: boolean) => void;
    setEditAdressStatus: (v: 'add' | 'edit') => void;
    addAdressRef: React.RefObject<AddAdressRef>;
    deleteAdressRef: React.RefObject<DeleteAdressRef>;
    setIsDeleteAdressModalOpen: (v: boolean) => void;
    pacUrlQrCode: string;
    pacUrl: string;
    getWifiProxy: () => void;

    url: string;

    moreFilterCount: number;
    linkState: string;
}

export const ApiHttpProxyContext = createContext<ApiHttpProxyContextType>({});
export const LAST_SELECT_RULE_GROUP_ID = 'lastSelectRuleGroupId';

export interface domainType {
    domain: string,
    icon: string
}

export interface PropsType {
    url: string
}

const requestMap = {};
export function ApiHttpProxy(props: PropsType) {
    const [isCollapse, setIsCollapse] = useState(false);
    const [leftPaneSize, setLeftPaneSize] = useLocalStorageState<string>('api_http_proxy_leftpane_size', {
        defaultValue: '250px'
    });
    const [allowResize, setAllowResize] = useState<boolean>(true);

    const linkSdk = useMemo(() => {
        const isProd = props.url.indexOf('corp.kuaishou') > -1;
        const linkAddress = isProd ? linkAddress_prod : linkAddress_staging;
        return new LinkSdk({
            linkAddress: linkAddress,   // 具体根据上面提供的环境填入
            kpf: 'PC_WEB',              // 是一个平台公参，详细可参考 "接入前准备" 章节
            kpn: 'Kdev-API'             // 产品标识
        });
    }, []);
    const [linkState, setLinkState] = useState<string>('disconnected');
    const stateChange = (...state: any) => {
        if (state[0] === 'connected') {
            setLinkState('connected')
        } else if (state[0] === 'disconnected') {
            setLinkState('disconnected')
        } else if (state[0] === 'connecting') {
            setLinkState('connecting')
        }
    }
    useEffect(() => {
        linkSdk.on('stateUpdate', stateChange);
        return () => {
            linkSdk.off('stateUpdate', stateChange);
        }
    }, [linkSdk])
    // 抓取的全部请求
    const [requestList, setRequestList] = useState<requestType[]>([]);
    // 筛选的全部请求
    const [filterRequestList, setFilterRequestList] = useState<requestType[]>([]);
    // 全部domain
    const [domainList, setDomainList] = useState<domainType[]>([]);
    // 选中的domain
    const [selectedDomain, setSelectedDomain] = useState<string[]>([]);
    // 选中的domain
    const [urlFilterText, setUrlFilterText] = useState<string>('');
    // 选中的domain
    const [isCapture, setIsCapture] = useState<boolean>(false);
    // 选中的domain
    const [isCaptureLoading, setIsCaptureLoading] = useState<boolean>(false);
    const [isChangeDrawerHeight, setIsChangeDrawerHeight] = useState<boolean>(false);
    // 选中的domain
    const [linkInfo, setLinkInfo] = useState<linkInfoType>({
        deviceName: ''
    });
    // 选中的domain
    const [linkWay, setLinkWay] = useState<LinkWay>(LinkWay.NAVITE);
    // 选中的domain
    const [ruleLinkWay, setRuleLinkWay] = useState<LinkWay>(LinkWay.LOCAL);
    // 选中的domain
    const [qrCode, setQrCode] = useState<string>('');
    // 选中的domain
    const [startUp, setStartUp] = useState<boolean>(true);
    // 选中的domain
    const [proxyAddress, setProxyAddress] = useState<string>('');
    // 选中的domain
    const [filterRequestByRule, setFilterRequestByRule] = useState<string>('all');
    // 代理地址列表
    const [proxyAddressList, setProxyAddressList] = useState<
        nsMockManageApiManageMainProxyCaptureV2AddressList.RuleGroupType[]
    >([]);
    // 正在使用的代理地址
    const [usedProxyAddress, setUsedProxyAddress] = useState<
        nsMockManageApiManageMainProxyCaptureV2AddressList.RuleGroupType | null
    >(null);
    const [allTypeList, setAllTypeList] = useState<string[]>([]);
    const [selectTypeList, setSelectTypeList] = useState<string[]>([]);
    const requestTypeList = ['GET', 'POST', 'HEAD', 'PUT', 'DELETE', 'OPTIONS', 'TRACE', 'CONNECT']
    const [selectRequestTypeList, setSelectRequestTypeList] = useState<string[]>([]);

    const responseCodeList = ['2xx', '3xx', '4xx', '5xx']
    const [selectResponseCodeList, setSelectResponseCodeList] = useState<string[]>([]);
    const [selectRequestId, setSelectRequestId] = useState<string>('');
    const [selectRequest, setSelectRequest] = useState<requestType | null>(null);
    const [ruleGroupList, setRuleGroupList] = useState<
        nsMockManageApiManageMainProxyCaptureV2RuleGroupSearch.RuleGroupType[]
    >([]);
    const [ruleList, setRuleList] = useState<
        nsMockManageApiManageMainProxyCaptureV2RuleList.RuleType[]
    >([]);
    const [currentRuleGroup, setCurrentRuleGroup] = useState<
        nsMockManageApiManageMainProxyCaptureV2RuleGroupSearch.RuleGroupType | null
    >(null);

    const [drawerHeight, setDrawerHeight] = useState<number>(450);
    const [loadingCurrentRuleGroup, setLoadingCurrentRuleGroup] = useState<boolean>(false);
    const [addRuleGroupId, setAddRuleGroupId] = useState<number>(-1);
    const [moreFilterCount, setMoreFilterCount] = useState<number>(0);

    const [drawerComTabValue, setDrawerComTabValue] = useState<string>('ruleGroup');

    const [drawerTabsItems, setDrawerTabsItems] = useState<{ label: any, key: string, children: any }[]>([
        {
            label: <span>
                规则组
                {/* <KdevIconFont id={common_system_closesmall}></KdevIconFont> */}
            </span>,
            key: 'ruleGroup',
            children: <RuleGroupInfo></RuleGroupInfo>
        },
        {
            label: <span>
                日志详情
                {/* <KdevIconFont id={common_system_closesmall}></KdevIconFont> */}
            </span>,
            key: 'requestInfo',
            children: <RequestInfo></RequestInfo>
        }
    ]);

    const [isAddAdressModalOpen, setIsAddAdressModalOpen] = useState<boolean>(false);
    const [editAdressStatus, setEditAdressStatus] = useState<'add' | 'edit'>('add');
    const [pacUrlQrCode, setPacUrlQrCode] = useState<string>('');
    const [pacUrl, setPacUrl] = useState<string>('');
    const addAdressRef = useRef<AddAdressRef>(null);
    const deleteAdressRef = useRef<DeleteAdressRef>(null);

    const [isDeleteAdressModalOpen, setIsDeleteAdressModalOpen] = useState<boolean>(false);

    useEffect(() => {
        setSelectRequest(requestList.find((request) => request.id === selectRequestId) as requestType);
    }, [selectRequestId])

    useEffect(() => {
        let count = 0;
        if (selectRequestTypeList.length) {
            count++;
        }
        if (selectResponseCodeList.length) {
            count++;
        }
        if (filterRequestByRule === 'hit') {
            count++;
        }
        setMoreFilterCount(count)
    }, [selectRequestTypeList, selectResponseCodeList, filterRequestByRule])

    // 缓存过滤条件
    // useEffect(() => {
    //     requestAnimationFrame(() => {
    //         const store = {
    //             selectRequestTypeList, selectResponseCodeList, filterRequestByRule, selectTypeList
    //         }
    //         localStorage.setItem('kdev_proxy_filter', JSON.stringify(store));
    //     })
    // }, [selectRequestTypeList, selectResponseCodeList, filterRequestByRule, selectTypeList])


    const filterRequest = () => {
        let res: requestType[] = requestList;
        if (selectedDomain.length) {
            res = res.filter((request) => {
                return selectedDomain.includes(request.domain)
            });
        }
        if (urlFilterText) {
            res = res.filter((request) => {
                return request.url.includes(urlFilterText)
                    || request.queryString.includes(urlFilterText)
                    || request.domain.includes(urlFilterText)
            });
        }
        if (selectTypeList.length) {
            res = res.filter((request) => {
                return selectTypeList.includes(request.requestType)
            });
        }
        if (selectRequestTypeList.length) {
            res = res.filter((request) => {
                return selectRequestTypeList.includes(request.method)
            });
        }
        if (selectResponseCodeList.length) {
            res = res.filter((request) => {
                for (const code of selectResponseCodeList) {
                    if ((request.httpCode || '').startsWith(code.slice(0, 1))) {
                        return true
                    }
                }
                return false
            });
        }
        if (filterRequestByRule === 'hit') {
            res = res.filter((request) => {
                return request.hitRuleList.length
            });
        }

        // const domainListTemp = [...new Set(res.map(i => i.domain))];
        // setDomainList(domainListTemp.map(i => ({
        //     domain: i,
        //     icon: `${i}/favicon.ico`
        // })))
        setFilterRequestList([...res])
    }

    useEffect(() => {
        filterRequest()
    }, [
        requestList
    ]);

    const filterRequestdebounce = useCallback(debounce(filterRequest, 200), [
        selectTypeList,
        selectedDomain,
        urlFilterText,
        selectRequestTypeList,
        selectResponseCodeList,
        filterRequestByRule])

    useEffect(() => {
        filterRequestdebounce()
    }, [
        selectTypeList,
        selectedDomain,
        urlFilterText,
        selectRequestTypeList,
        selectResponseCodeList,
        filterRequestByRule
    ]);

    const getRuleGroupList = () => {
        return nsMockManageApiManageMainProxyCaptureV2RuleGroupList.remote({
            proxyId: usedProxyAddress!.id
        }).then((res) => {
            setRuleGroupList(res);
        })
    }

    useEffect(() => {
        if (!usedProxyAddress) return;
        getRuleGroupList();
    }, [usedProxyAddress])

    const getAddressList = () => {
        return nsMockManageApiManageMainProxyCaptureV2AddressList.remote({}).then((res) => {
            const usedProxyAddressId = localStorage.getItem('kdevProxyAddressId') || res.list[0].id;
            const lastProxyAddress = res.list.find(i => i.id === +usedProxyAddressId);
            setUsedProxyAddress(lastProxyAddress || res.list[0]);
            setProxyAddress((lastProxyAddress || res.list[0]).address)
            setProxyAddressList(res.list);
        });
    }

    // 切换环境后刷新规则组列表
    useEffect(() => {
        if (!usedProxyAddress) return;
        if (!proxyAddressList.length) return;
        if (!ruleGroupList.length) return;
        // 有开启的规则组
        if (usedProxyAddress.enableRuleGroupId !== currentRuleGroup?.id) {
            let id = usedProxyAddress.enableRuleGroupId;
            if (!id) {
                id = ruleGroupList.find(i => i.id === currentRuleGroup!.id)
                    ? currentRuleGroup!.id : ruleGroupList[0]?.id
            }
            resetCurrentRuleGroup(id);
        }
        const lastSelectRuleGroupId = localStorage.getItem(LAST_SELECT_RULE_GROUP_ID);
        // 如果存在上一个选中的规则组，则选中
        if (lastSelectRuleGroupId && ruleGroupList.some(i => i.id === +lastSelectRuleGroupId)) {
            resetCurrentRuleGroup(+lastSelectRuleGroupId);
        } else if (!usedProxyAddress.enableRuleGroupId) { // 没有开启的规则组，打开第一个
            resetCurrentRuleGroup(ruleGroupList[0]?.id);
        }

        // 新建后，选择新建的
        if (addRuleGroupId !== -1) {
            resetCurrentRuleGroup(addRuleGroupId);
            setAddRuleGroupId(-1)
        }
    }, [usedProxyAddress, proxyAddressList, ruleGroupList])

    const getRuleList = () => {
        nsMockManageApiManageMainProxyCaptureV2RuleList.remote({
            ruleGroupId: currentRuleGroup!.id
        }).then((res) => {
            setRuleList(res.map((i, index) => ({
                ...i,
                order: index
            })));
        })
    }

    // 切换规则组后，刷新规则列表
    useEffect(() => {
        if (currentRuleGroup) {
            getRuleList();
        }
    }, [currentRuleGroup])

    // 初始化
    useEffect(() => {
        // 获取代理地址列表
        getAddressList();
        // 获取请求文件类型
        nsMockManageApiManageMainProxyCaptureTypeList.remote({}).then(res => {
            setAllTypeList([ALL_TYPE, ...res.list]);
            const temp: string[] = []
            if (res.list.includes('Fetch/XHR')) {
                temp.push('Fetch/XHR')
            }
            if (res.list.includes('Other')) {
                temp.push('Other')
            }
            setSelectTypeList(temp)
        }).catch(err => {
            setAllTypeList([])
        });
    }, []);



    const resetCurrentRuleGroup = useCallback(debounce((id: number) => {
        setLoadingCurrentRuleGroup(true)
        if (!id) {
            setCurrentRuleGroup(null)
            setLoadingCurrentRuleGroup(false)
            return
        }
        nsMockManageApiManageMainProxyCaptureV2RuleGroupDetail.remote({
            id
        }).then((res) => {
            setCurrentRuleGroup(res)
            setLoadingCurrentRuleGroup(false)
        })
    }, 100), [])

    // ws回调
    const payload = useCallback((uint8Array) => {
        const data: requestType = JSON.parse(uint8ArrayToText(uint8Array.payloadData));
        if (data) {
            console.info(data);
            const handleRequestData = () => {
                setRequestList(prevRequestList => {
                    const newRequestList: requestType[] = [...prevRequestList, data];
                    requestMap[data.id] = data;
                    if (newRequestList.length > 10000) {
                        const deleteItem = newRequestList.shift();
                        delete requestMap[deleteItem!.id]
                    }
                    setDomainList((temp) => {
                        if (!temp.some(i => i.domain === data.domain)) {
                            return [...temp, {
                                domain: data.domain,
                                icon: `${data.domain}/favicon.ico`
                            }]
                        }
                        return temp
                    })
                    return newRequestList;
                });
            };

            if (data.responseInfo) {
                if (requestMap[data.id]) {
                    requestMap[data.id].responseInfo = data.responseInfo;
                    requestMap[data.id].httpCode = data.httpCode;
                    requestMap[data.id].respTime = data.respTime;
                    setRequestList(prevRequestList => {
                        setDomainList((temp) => {
                            if (!temp.some(i => i.domain === data.domain)) {
                                return [...temp, {
                                    domain: data.domain,
                                    icon: `${data.domain}/favicon.ico`
                                }]
                            }
                            return temp
                        })
                        return [...prevRequestList]
                    });
                } else {
                    handleRequestData();
                }
            } else {
                if (!requestMap[data.id]) {
                    handleRequestData();
                }
            }
        }
    }, [])

    // 抓包开关改变后，断开或者链接ws
    useEffect(() => {
        if (!usedProxyAddress) return;
        setIsCaptureLoading(true)
        if (isCapture) {
            if (!ruleGroupList.some(i => i.id === usedProxyAddress.enableRuleGroupId)) {
                nsMockManageApiManageMainProxyCaptureV2RuleGroupEnable.remote({
                    proxyId: usedProxyAddress!.id,
                    id: currentRuleGroup!.id,
                    enable: 1
                }).then(() => {
                    getAddressList();
                    message.success('规则所对应的「规则组」未启用，已为您自动开启');
                })
            }
            nsMockManageApiManageMainProxyCaptureV2Start.remote({
                proxyId: usedProxyAddress.id
            }).then((res) => {
                message.success('开始抓包');
                if (linkSdk.linkState !== 'connected') {
                    linkSdk.connect({
                        userId: res.mainProxyCaptureId,
                        serviceToken: res.tokenValue,
                        ssecurity: res.ssecurity
                    });
                }

                // 处理ws的消息
                linkSdk.on('message', payload);

                setIsCaptureLoading(false);
            })
        } else {
            nsMockManageApiManageMainProxyCaptureV2Stop.remote({
                proxyId: usedProxyAddress.id
            }).then((res) => {
                message.success('停止抓包');
                linkSdk.disconnect();
                // 取消监听
                linkSdk.off('message', payload);
                setIsCaptureLoading(false);
            })
        }
    }, [isCapture]);

    const getWifiProxy = () => {
        if (!usedProxyAddress) return;
        if (WifiMap[usedProxyAddress.id]) {
            setPacUrlQrCode(WifiMap[usedProxyAddress.id].pacUrlQrCode);
            setPacUrl(WifiMap[usedProxyAddress.id].pacUrl);
            return;
        }
        nsMockManageApiManageMainProxyCaptureV2ScanCodeWifi.remote({
            proxyId: usedProxyAddress.id
        }).then((res) => {
            setPacUrlQrCode(res.pacUrlQrCode);
            setPacUrl(res.pacUrl);
            WifiMap[usedProxyAddress.id] = res;
        })
    }
    // 正在使用的 代理地址 或者 冷启动开关 变化后，更新qrcode
    useEffect(() => {
        if (!usedProxyAddress) {
            return;
        }
        nsMockManageApiManageMainProxyCaptureV2ScanCode.remote({
            proxyId: usedProxyAddress.id
        }).then((res) => {
            setQrCode(res.qrCode);
            setProxyAddress(`https://koasproxy.${props.url.includes('corp') ? 'corp' : 'staging'}.kuaishou.com/proxy/${res.proxyKey}`)
            setStartUp(Boolean(res.startUp));
        })
        if (linkWay === LinkWay.WIFI || ruleLinkWay === LinkWay.WIFI) {
            getWifiProxy();
        }
    }, [usedProxyAddress, startUp])

    useEffect(() => {
        setAllowResize(!isCollapse);
    }, [isCollapse]);

    return (
        <ApiHttpProxyContext.Provider
            value={{
                requestList, setRequestList,
                filterRequestList, setFilterRequestList,
                domainList, setDomainList,
                selectedDomain, setSelectedDomain,
                urlFilterText, setUrlFilterText,
                isCapture, setIsCapture,
                linkInfo, setLinkInfo,
                linkWay, setLinkWay,
                ruleLinkWay, setRuleLinkWay,
                qrCode, setQrCode,
                startUp, setStartUp,
                proxyAddress, setProxyAddress,
                filterRequestByRule, setFilterRequestByRule,
                url: props.url,
                proxyAddressList, getAddressList,
                usedProxyAddress, setUsedProxyAddress,
                allTypeList, setAllTypeList,
                selectTypeList, setSelectTypeList, getRuleGroupList,
                requestTypeList,
                selectRequestTypeList, setSelectRequestTypeList,
                responseCodeList,
                selectResponseCodeList, setSelectResponseCodeList,
                selectRequestId, setSelectRequestId,
                selectRequest, setSelectRequest,
                ruleGroupList, setRuleGroupList,
                currentRuleGroup, setCurrentRuleGroup, resetCurrentRuleGroup,
                drawerHeight, setDrawerHeight,
                loadingCurrentRuleGroup, setLoadingCurrentRuleGroup,
                ruleList, setRuleList, getRuleList,
                addRuleGroupId, setAddRuleGroupId,
                drawerComTabValue, setDrawerComTabValue,
                isCaptureLoading, setIsCaptureLoading,
                drawerTabsItems, setDrawerTabsItems,
                isChangeDrawerHeight, setIsChangeDrawerHeight,
                setIsAddAdressModalOpen,
                setEditAdressStatus,
                addAdressRef,
                deleteAdressRef,
                setIsDeleteAdressModalOpen,
                pacUrlQrCode,
                pacUrl,
                getWifiProxy,
                moreFilterCount,
                linkState,
            }}
        >
            {
                location.pathname === (ERouter.API_MOCK_HTTP_PROXY) ? <div className={css.apiHttpProxy}>
                    <div className={css.top}>
                        <LinkProxy />
                    </div>
                    <div className={css.main}>
                        <SplitPane
                            onChange={(sizes) => setLeftPaneSize(sizes[0])}
                            allowResize={allowResize}
                            className={
                                allowResize
                                    ? !isCollapse
                                        ? css.splitPaneResizer
                                        : classNames(css.splitPane)
                                    : css.splitPane
                            }
                        >
                            <Pane
                                size={isCollapse ? '14px' : leftPaneSize}
                                minSize={isCollapse ? '14px' : '200px'}
                                maxSize="400px"
                                style={{ visibility: isCollapse ? 'hidden' : 'visible' }}
                            >
                                {isCollapse ? null : <AppDomainfilter />}
                            </Pane>
                            <Pane className={css.rightPane}>
                                <div
                                    className={css.resizerIconBox}
                                    onClick={() => setIsCollapse(!isCollapse)}
                                    id="isCollapseBtn"
                                    style={{
                                        display: 'block',
                                        textIndent: !isCollapse ? -1 : 0
                                    }}
                                >
                                    <KdevIconFont
                                        color="#898A8C"
                                        id={isCollapse ? common_system_xiaojiantouyou : common_system_xiaojiantouzuo}
                                    />
                                </div>
                                <UrlFilter />
                                <RequestList />
                            </Pane>
                        </SplitPane>
                        <DrawerCom />
                    </div>
                </div> : null
            }
            {
                location.pathname === (ERouter.API_MOCK_HTTP_PROXY_RULE) ? <div className={css.apiHttpProxy}>
                    <div className={css.top}>
                        <RuleLinkProxy />
                    </div>
                    <div className={css.main}>
                        <DrawerCom />
                    </div>
                </div > : null
            }

            {/* 场景弹框 */}
            <Modal
                width="480px"
                title={RuleGroupStatusMap[editAdressStatus] + '场景'}
                open={isAddAdressModalOpen}
                onCancel={() => {
                    setIsAddAdressModalOpen(false);
                    setEditAdressStatus('add');
                }}
                onOk={() => {
                    if (addAdressRef.current?.check()) {
                        const params = addAdressRef.current?.getParams();
                        if (editAdressStatus === 'edit') {
                            nsMockManageApiManageMainProxySaveNamePOST.remote({
                                id: params.id,
                                name: params.name
                            }).then((res) => {
                                setIsAddAdressModalOpen(false);
                                setEditAdressStatus('add');
                                addAdressRef.current?.reset();
                                getAddressList();
                                message.success('编辑成功');
                            })
                        } else {
                            nsMockManageApiManageMainProxySavePOST.remote({
                                name: params.name,
                                ruleGroupList: []
                            }).then((res) => {
                                setIsAddAdressModalOpen(false);
                                setEditAdressStatus('add');
                                addAdressRef.current?.reset();
                                getAddressList();
                                message.success('新建成功');
                            })
                        }
                    }
                }}
            >
                <AddAdress ref={addAdressRef} />
            </Modal>

            {/* 删除场景弹框 */}
            <Modal
                width="480px"
                title={<span>
                    <KdevIconFont
                        style={{ color: '#FFAA00ff', marginRight: 8, fontSize: 17 }}
                        id={common_system_notice_mian}>
                    </KdevIconFont>
                    确认删除场景
                </span>}
                open={isDeleteAdressModalOpen}
                onCancel={() => {
                    setIsDeleteAdressModalOpen(false);
                }}
                onOk={() => {
                    nsMockManageApiManageMainProxyRemoveGET.remote({
                        id: deleteAdressRef.current?.getParams().id
                    }).then((res) => {
                        setIsDeleteAdressModalOpen(false);
                        getAddressList();
                    })
                }}
            >
                <DeleteAdress ref={deleteAdressRef} />
            </Modal>
        </ApiHttpProxyContext.Provider>
    )
}
