import React from 'react';
import { withKDevPageError } from '@kdev-fe-common/common/errorHandler';
import zhCN from 'antd/es/locale/zh_CN';
import { ConfigProvider, Empty } from 'antd';
import { filterRouter, hideHeader, isShowSubMenu } from './routerConfig';
import css from './app.less';
import { ERouter } from 'CONFIG';
import {
    GLOBAL,
    team,
} from '@/business/global';
import { observer } from 'mobx-react';
import { Bind } from 'lodash-decorators';
import { DefaultEmptyIcon } from '@/business/commonIcon';
import {
    nsMockManageKoasApiManageNoticeGetNoticeGet,
    nsMockManageKoasApiManageNoticeCloseNoticeGet,
} from '@/remote';
import { kconfStore, weblogSendImmediatelyCustom } from '@/index.config';
import { RenderDepartment } from './RenderDepartment';

import {
    KDevPageLayout,
    KDevPageHeaderRightSpace,
    HeaderUserCenter,
    HeaderKimOnCall,
    KDevPageSider,
    KDevPageTopMenu,
    HelpDocument,
    HeaderGlobalSearch,
} from '@kdev-fe-common/component/KDevPageLayout/KDevPageLayout';
import { router } from 'kdev-fe-common/src/shared/libs';
import { getUrlSearch } from '@/index.config/tools'
import { Route, Switch } from 'react-router';
import { AsyncLoader } from 'libs/a-component/AsyncLoader';
import { QuickAccess } from './pages/ApiManager/ProjectView/QuickAccess/QuickAccess';
import { EGray_Key, storeGrayFeatures } from '@kdev-fe-common/store';
import { KdevIconFont, KEmpty } from '@/business/commonComponents';
import { common_system_right_arrow_surface } from '@kid/enterprise-icon/icon/output/icons';
import SearchProject from './pages/ApiManager/ProjectView/SearchProject/SearchProject';


interface IState {
    noticMsg: string;
    noticeUrl: string;
    hideNavMenu: boolean;
    projectId: number;
    currentGroupId: number;
    projectInfo: any;
}

declare global {
    interface Window {
        search: boolean;
    }
}




const isNeedLayout = (): boolean => {
    const notNeedLayout = [
        ERouter.API_MOCK_REPO_API_DOCS,
        ERouter.API_MOCK_REPO_API_DOCS_SHOW,
        ERouter.API_MOCK_REPO_API_DOCS_ENTRY,
    ]
    return notNeedLayout.includes(location.pathname as ERouter);
}

@observer
class AppTaxiOps extends React.Component<any, IState> {
    private unListenRouter: any;
    public state: IState = {
        noticMsg: '',
        noticeUrl: '',
        hideNavMenu: false,
        projectId: 0,
        currentGroupId: 0,
        projectInfo: null
    };
    constructor(props: any) {
        super(props);
        const url = getUrlSearch() as { search: string };
        window.search = Boolean(url.search);
    }



    @Bind
    private async changeHideNavMenu(info): Promise<void> {
        this.setState({
            hideNavMenu: info.detail.hideNavMenu
        })
    }

    @Bind
    private async projectIdChange(info): Promise<void> {
        this.setState({
            ...this.state,
            projectId: info.detail.projectId,
            currentGroupId: info.detail.currentGroupId,
            projectInfo: info.detail.projectInfo
        })
    }

    public async componentDidMount(): Promise<void> {
        this.getNotic();

        setTimeout(() => {
            weblogSendImmediatelyCustom('SHOW_EVENT', { id: 'new_kdev_nav_v2' })
        }, 2000)

        this.unListenRouter = router.routerProps.history.listen(() => {
            // 需要重新计算 顶部 和 侧边导航
            this.setState({});
        });
        document.addEventListener('changeHideNavMenu', this.changeHideNavMenu);
        document.addEventListener('projectIdChange', this.projectIdChange);
        const url = getUrlSearch() as { hideNavMenu: string };
        if (url.hideNavMenu === 'true') {
            this.setState({
                hideNavMenu: true,
            });
        }
    }

    public componentWillUnmount(): void {
        this.unListenRouter?.()
        document.removeEventListener('changeHideNavMenu', this.changeHideNavMenu);
        document.removeEventListener('projectIdChange', this.projectIdChange);
    }

    @Bind
    private async getNotic(): Promise<void> {
        try {
            const result = await nsMockManageKoasApiManageNoticeGetNoticeGet.remote();
            this.setState({ noticMsg: result?.notice, noticeUrl: result?.url });
        } catch { }
    }



    @Bind
    private async onCloseNotice(): Promise<void> {
        try {
            await nsMockManageKoasApiManageNoticeCloseNoticeGet.remote();
        } catch { }
    }




    private renderHeaderRight() {
        return (
            <KDevPageHeaderRightSpace>
                <HeaderGlobalSearch />
                {/*oncall*/}
                <HeaderKimOnCall
                    userName={team.model.userName}
                    filterPath={(path) => path === '/web/api-mock'}
                />
                {/* 帮助文档 */}
                <HelpDocument
                    localList={[
                        {
                            id: 1,
                            name: '帮助文档',
                            url: 'https://docs.corp.kuaishou.com/k/home/<USER>/fcADxcc0Q0eTLrIwJoEB3OcQC',
                            iconUrl:
                                'https://h1.static.yximgs.com/udata/pkg/QA-SERVEREE-FE-OUT/kdev_help_v2_icon.svg',
                        },
                    ]}
                />
                {/*用户中心*/}
                <HeaderUserCenter
                    photo={team.model.photo}
                    chineseName={team.model.chineseName}
                />
            </KDevPageHeaderRightSpace>
        );
    }


    @Bind
    renderQuickAccess() {
        return <QuickAccess />
    }

    @Bind
    private renderSider() {
        const routerConfig =
            location.pathname !== ERouter.API_MOCK_PROXY &&
            location.pathname !== ERouter.API_MOCK_REPO_API_VERSION_DIFF &&
            filterRouter();
        if (!routerConfig) {
            return null;
        }
        return <KDevPageSider
            routeSetting={routerConfig}
            // menuHeader={this.renderDepartment}
            additional={this.renderQuickAccess}
            menuHeader={() => {
                return isShowSubMenu() ? <>
                    <div className={css.backProjectList} onClick={() => {
                        router.push(ERouter.API_MOCK_REPO_MGR_PROJECT)
                    }}>
                        <KdevIconFont id={common_system_right_arrow_surface} style={{
                            marginRight: '4px',
                            transform: 'rotate(180deg)'
                        }} />
                        <span>返回项目列表</span>
                    </div>
                    <div className={css.projectName}>
                        <SearchProject
                            projectName={this.state.projectInfo?.name || ''}
                            onChange={(id: number) => {
                                document.dispatchEvent(new CustomEvent('projectIdChange1', {
                                    detail: id
                                }));
                                // setProjectId(id);
                                // pushKey({
                                //     projectId: id
                                // });
                                // router.push(ERouter.API_MOCK_REPO_MGR_PROJECT_INFO, { projectId: id, viewType: 'projectView' });
                                // setSelectedNode({ id: 0, type: null, key: null });
                            }}
                            currentGroupId={this.state.currentGroupId}
                            currentProjectId={this.state.projectId}
                        />
                    </div>
                </> : <></>
            }}
        />;
    }

    @Bind
    private renderEmptyDepartment(): React.ReactNode {
        return (
            <Empty
                image={<DefaultEmptyIcon />}
                description="请在左上角选择部门"
                className={css.defaultEmpty}
            />
        );
    }

    private renderNoLayout = () => {
        const proNotFound = () => import('@pages/gateway/NotFoundPage');
        return <Switch>
            {
                GLOBAL.loaded()
                    ? filterRouter().renderRoutes()
                    : this.renderEmptyDepartment()
            }
            <Route
                path="*"
                render={
                    // 路由全部不匹配：页面不存在
                    () => (
                        <AsyncLoader
                            compoLazy={proNotFound}
                            fallback={<div />}
                        />
                    )
                }
            />
        </Switch>
    }

    public renderContent(): React.ReactNode {

        const center = (
            <KDevPageTopMenu
                getKeyByPathName={() => {
                    return '/web/api-mock';
                }}
            />
        );

        const noLayout = <this.renderNoLayout />;

        return isNeedLayout() ? noLayout : (
            <KDevPageLayout
                header={hideHeader() || this.state.hideNavMenu ? null : {
                    right: this.renderHeaderRight(),
                    center: center,
                }}
                slider={this.state.hideNavMenu ? null : this.renderSider()}
                className={css.app}
            >
                {noLayout}
            </KDevPageLayout>
        );
    }

    public render(): React.ReactNode {
        return (
            <ConfigProvider locale={zhCN} renderEmpty={() => <KEmpty image="NOMAL_SIMPLE_SEARCH_2" />}>
                {kconfStore.isLoaded && this.renderContent()}
            </ConfigProvider>
        );
    }
}

export default withKDevPageError(AppTaxiOps);
