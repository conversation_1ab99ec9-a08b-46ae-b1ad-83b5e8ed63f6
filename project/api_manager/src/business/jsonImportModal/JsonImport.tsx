import { <PERSON><PERSON>ie<PERSON> } from 'libs';
import React from 'react';
import { JsonImportM } from './JsonImportM';
import { observer } from 'mobx-react';
import { Modal, Button } from 'antd';
import css from './JsonImport.less';
// import { jsonHint } from './configure';
import { AsyncAceEditor } from '@/business/commonComponents';

@observer
export class JsonImport extends AView<JsonImportM> {

    public render(): React.ReactNode {
        const model = this.model;
        return (
            <Modal
                title={ 'JSON导入' }
                visible={ model.visible }
                onCancel={ model.onCloseJsonImportModal }
                width={ 700 }
                onOk={ model.onSaveJson }
                confirmLoading={ model.saveLoading }
                className={ css.jsonImportModal }
            >
                <Button onClick={ model.onPrettyJson } className={ css.pretty }>Pretty</Button>
                <AsyncAceEditor
                    width={ '100%' }
                    height={ '100%' }
                    showPrintMargin={ false }
                    highlightActiveLine={true}
                    value={ model.jsonStr }
                    onChange={ model.onChangeJsonStr }
                    // placeholder={jsonHint}
                    commands={[{
                        name: 'find',
                        bindKey: { win: 'Ctrl-F', mac: 'Command-F' },
                        exec: () => {
                            return false;
                        }
                    }]}
                />
            </Modal>
        );
    }
}
