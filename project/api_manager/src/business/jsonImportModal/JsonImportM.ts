import { AViewModel } from 'libs';
import { observable, action, runInAction } from 'mobx';
import {
    nsMockManageKoasApiManageHttpApiGetRequestByJsonPost, nsMockManageKoasApiManageHttpApiGetResponseByJsonPost
} from '@/remote';
import { message } from 'antd';
// import { reqDataEnum } from '../enum';
import JSONbig from 'json-bigint';
import { beautifyStrJson, isJsonString, JSONObject } from '@/index.config/tools';

export class JsonImportM extends AViewModel {
    @observable public visible: boolean = false;
    @observable public jsonStr: string = '';
    @observable protected type: string = '';
    @observable public saveLoading: boolean = false;
    @observable public apiId: number = 0;

    public onSaveJsonCallBack?(type: string, data, example: string): void;
    public onCloseModalCallBack?(): void;

    // 加载model
    @action
    public init(type: string, jsonStr: string, apiId: number) {
        this.type = type;
        this.jsonStr = jsonStr;
        this.apiId = apiId;
        this.visible = true;
    }

    @action.bound
    public onPrettyJson() {
        this.jsonStr = beautifyStrJson(this.jsonStr);
    }

    @action.bound
    public onChangeJsonStr(value: string): void {
        this.jsonStr = value;
    }

    @action.bound
    public onCloseJsonImportModal(): void {
        this.jsonStr = '';
        this.visible = false;
        this.onCloseModalCallBack && this.onCloseModalCallBack();
    }

    @action.bound
    public checkParam(): boolean {
        if (isJsonString(this.jsonStr)) {
            return false;
        }
        return true;
        // if (this.jsonStr.trim()[0] === '{') {
        //     try {
        //         JSON.parse(this.jsonStr);
        //         return false;
        //     } catch (e) {
        //         return true;
        //     }
        // } else {
        //     return true;
        // }
    }

    @action.bound
    public onSaveJson(): void {
        if (this.jsonStr === '') {
            this.onCloseJsonImportModal();
            return;
        }
        if (this.checkParam()) {
            message.warn('请输入正确的json格式数据');
            return;
        }
        if (this.type) {
            this.getRequestByJson();
        } else {
            this.getResponseByJson();
        }
    }

    @action.bound
    public async getRequestByJson() {
        this.saveLoading = true;
        try {
            const params = {
                in: this.type,
                jsonMap: JSONObject(this.jsonStr),
                arDocId: this.apiId
            };
            const result = await nsMockManageKoasApiManageHttpApiGetRequestByJsonPost.remote(params);
            runInAction(() => {
                this.onSaveJsonCallBack && this.onSaveJsonCallBack(this.type, result.parameters, this.jsonStr);
                this.saveLoading = false;
                this.onCloseJsonImportModal();
            });
        } catch (e) {
            runInAction(() => {
                this.saveLoading = false;
            });
        }
    }

    @action.bound
    public async getResponseByJson(): Promise<void> {
        this.saveLoading = true;
        try {
            const params = {
                jsonMap: JSONObject(this.jsonStr),
                arDocId: this.apiId
            };
            const result = await nsMockManageKoasApiManageHttpApiGetResponseByJsonPost.remote(params);
            runInAction(() => {
                this.onSaveJsonCallBack && this.onSaveJsonCallBack(this.type, result.body, this.jsonStr);
                this.saveLoading = false;
                this.onCloseJsonImportModal();
            });
        } catch (e) {
            runInAction(() => {
                this.saveLoading = false;
            });
        }
    }
}
