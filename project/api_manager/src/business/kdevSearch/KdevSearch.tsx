import React from 'react';
import css from './index.less';
import classNames from 'classnames';
import { Input } from 'antd';
import { KdevIconFont } from '@/business/commonComponents';
import { common_system_search } from '@kid/enterprise-icon/icon/output/icons';
import { InputProps } from 'antd/lib/input';

interface IProps extends InputProps {
    inputRef?: React.Ref<Input>; // 即ref
}
export class KdevSearch extends React.Component<IProps, any> {

    public render() {
        const { inputRef, ...inputProps } = this.props;
        return (
            <Input.Search
                {...inputProps}
                prefix={
                    <KdevIconFont id={common_system_search} className={css.kdevSearchIcon} />
                }
                ref={inputRef}
                className={classNames(css.kdevSearch, this.props.className)}
            />
        );
    }
}
