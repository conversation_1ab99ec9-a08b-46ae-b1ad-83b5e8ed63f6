import React, {
    createContext,
    useCallback,
    useContext,
    useEffect,
    useRef,
    useState
} from 'react';
import {
    nsApiCommentsList,
    nsApiCommentsDelete,
    nsApiCommentsSave,
    nsApiCommentsResolve,
} from '@/remote';
import {
    common_system_comment,
    common_system_guanbisamll,
    common_system_correct,
    common_system_delete02,
    common_system_edit,
} from '@kid/enterprise-icon/icon/output/icons';
import { KdevIconFont } from '@/business/commonComponents';
import {
    Button,
    Divider,
    Tooltip,
    Space,
    message,
    Modal
} from 'antd';
import { AvatarList } from '@/business/AvatarList/AvatarList';
import moment from 'moment/moment';
import { ApiCommentEditor } from '@/business/markdownEditor/ApiCommentEditor';
import { MarkdownViewer } from '@biz/comps/markdownViewer';
import css from './Commit.less';
import classNames from 'classnames';
import { team } from '@/business/global';
import { EventEmitter2 } from 'eventemitter2';
import PopoverEllipsis from '@/business/common/PopoverEllipsis';
import { useDebounceFn } from 'ahooks';
import { eachNode } from '@libs/utils';

// 评论可滚动容器id，多处使用，保证数据一致
export const commentContentScrollContanier = 'comment_content_scroll_contanier';
export const overDomId = 'over_dom';

type ProviderProps<T extends (...args: any) => any> = {
    children: React.ReactNode;
} & Parameters<T>;
function createContainer<T extends (params?: any) => any>(useHook: T) {
    const Context = createContext<ReturnType<T> | null>(null);

    // 用来提供数据
    function Provider({ children, ...other }: ProviderProps<T>) {
        const state: ReturnType<T> = useHook(other);
        return (
            <Context.Provider value={state}>
                {children}
            </Context.Provider>
        );
    }

    // 消费数据
    function useContainer() {
        const value = useContext(Context) as ReturnType<T>;
        return value;
    }

    return [
        Provider,
        useContainer,
    ];
}

export enum CommentType {
    requestHeader = 'requestHeader',
    requestRest = 'requestRest',
    requestQuery = 'requestQuery',
    requestBody = 'requestBody',
    responseHeader = 'responseHeader',
    responseBody = 'responseBody',
}
type Comment = nsApiCommentsList.Comment & { realPath: string };
type CommentList = {
    [K in CommentType]: Record<string, Array<Comment>>;
};
function getInitCommentList(): CommentList {
    return {
        [CommentType.requestHeader]: {},
        [CommentType.requestRest]: {},
        [CommentType.requestQuery]: {},
        [CommentType.requestBody]: {},
        [CommentType.responseHeader]: {},
        [CommentType.responseBody]: {},
    };
}
function useComments(
    props: {
        apiId?: number;
        version: number;
        disabled?: boolean;
        getApiParamsList?(): any;
    }
) {
    const { apiId, version } = props;

    /**
     * 在某些场景下， 我们需要评论不可用or不展示
     * 比如api新增、curl新增的场景下  不显示
     */
    const [disabled, setDisabled] = useState<boolean>(props.disabled || false);
    /**
     * 根据apiId、version请求全部的评论（来自后端）
     */
    const [list, setList] = useState<Array<nsApiCommentsList.Comment>>([]);
    /**
     * 当前活跃的path  eg:<EMAIL>
     */
    const [active, setActive] = useState<string>();
    /**
     * 抽屉是否可见
     */
    const [visible, setVisible] = useState<boolean>(false);

    const eventEmitterRef = useRef(new EventEmitter2());
    const commentListConRef = useRef<HTMLDivElement>(null);
    const [commentWrapOverHeight, setCommentWrapOverHeight] = useState<number>(0);
    const [paddingTop, setPaddingTop] = useState<number>();
    const [paddingBottom, setPaddingBottom] = useState<number>();

    /**
     * 按照CommentType类型进行区分后在不同的表格里面显示
     */
    const commentList: CommentList = (() => {
        const res: CommentList = getInitCommentList();
        list?.forEach(item => {
            Object.values(CommentType).forEach(ty => {
                if (item.path.startsWith(ty)) {
                    const path = item.path.replace(getStaff(ty), '');
                    if (!res[ty][path]) {
                        res[ty][path] = [];
                    }
                    res[ty][path].push({
                        ...item,
                        realPath: path,
                    });
                }
            });
        });
        return res;
    })();

    /**
     * 关闭时，清空数据
     */
    useEffect(() => {
        if (!visible) {
            setActive(undefined);
            setList(list.filter(item => !!item.commentId) || []);
        }
    }, [visible]);
    /**
     * 初始化请求数据
     */
    useEffect(() => {
        if (apiId && version) {
            setActive(undefined);
            setPaddingTop(0);
            setPaddingBottom(0);
            setCommentWrapOverHeight(0);
            fetchCommentsList();
        }
    }, [apiId, version]);

    // 获取参数的path值
    const getPath = (item) => {
        let path: string = item.N;
        let parentItem = item.parent;
        while (parentItem) {
            path = `${parentItem.N}.${path}`;
            parentItem = parentItem.parent;
        }
        return path;
    };

    /**
     * 添加一个新的comment, 用于用户点击新增；不请求后端接口
     */
    const addNewComment = (comment: Pick<nsApiCommentsList.Comment, 'path' | 'comment' | 'markdown'>) => {
        if (props.getApiParamsList) {
            const userInfo = team.getUserInfo();
            const newCommentList: any[] = [];
            const {
                request: {
                    header: requestHeader,
                    query: requestQuery,
                    rest: requestRest,
                    body: requestBody
                },
                response: {
                    header: responseHeader,
                    body: responseBody
                }
            } = props.getApiParamsList();
            const apiParamsInfo = {
                requestHeader,
                requestQuery,
                requestRest,
                requestBody,
                responseHeader,
                responseBody
            };
            // 生成新的评论列表
            Object.values(CommentType).forEach(ty => {

                eachNode(apiParamsInfo[ty], item => {
                    const path = getPath(item);
                    // 只保留一个编辑评论状态
                    commentList[ty][path] && newCommentList.push(...commentList[ty][path].filter(it => it.commentId));
                    if (comment?.path?.startsWith(ty) && comment.path.split('@')[1] === path) {
                        newCommentList.push({
                            ...comment,
                            commentUser: {
                                id: userInfo.userId,
                                name: userInfo.chineseName,
                                username: userInfo.userName,
                                email: userInfo.email,
                                photo: userInfo.photo,
                            },
                            createTime: +new Date(),
                        });
                    }
                    return false;
                });
            });
            setList(newCommentList);
        }
    };
    /**
     * 删除Comment, 用于取消按钮
     */
    const deleteComment = (comment: nsApiCommentsList.Comment) => {
        let newCommentList = [...list];
        /**
         * 因为新增的没有commentId， 可以使用createTime作为Id 删除
         */
        newCommentList = newCommentList.filter(item => {
            return !(item.commentId === comment.commentId && item.createTime === comment.createTime);
        });
        setList(newCommentList);
    };

    const resolveComment = async (path: string) => {
        const newList = [...list].filter(it => it.path !== path);
        const { list: arr } = await nsApiCommentsList.remote({
            apiId: apiId!,
            version,
        });
        (arr || []).forEach(it => {
            const index = newList.findIndex(item => item.commentId === it.commentId);
            if (index > -1) {
                newList[index] = it;
            } else {
                newList.push(it);
            }
        });
        setList(newList || []);
    };

    const fetchCommentsList = async () => {
        if (!apiId) {
            return;
        }
        const { list: arr } = await nsApiCommentsList.remote({
            apiId,
            version,
        });
        setList(arr || []);
    };
    const updateCommentsList = fetchCommentsList;

    // 获取要滚动到的位置
    const getShouldScorllTop = () => {
        const commentIconDom = document.getElementById(`icon$${active}`);
        return commentIconDom ? commentIconDom.getBoundingClientRect().top - 9 : undefined;
    };

    const getCommentWrapOverHeight = () => {
        const commentContanierDomRect = document.getElementById('commentContanier')?.getBoundingClientRect();
        const commentListConRect = commentListConRef.current?.getBoundingClientRect();
        if (commentContanierDomRect && commentListConRect) {
            return commentContanierDomRect.height - commentListConRect.height - (paddingTop || 0);
        }
        return 0;
    };

    const scrollCommentContent = useDebounceFn(() => {
        if (!active) { return; }
        const activeDom = document.getElementById(active);
        const commentIconDomScrollTop = getShouldScorllTop() || 0;
        if (activeDom && Math.abs(commentIconDomScrollTop - activeDom.getBoundingClientRect().top) > 3) {
            if (commentIconDomScrollTop > activeDom.getBoundingClientRect().top) {
                setPaddingTop(commentIconDomScrollTop - activeDom.getBoundingClientRect().top + (paddingTop || 0));
            } else {
                setPaddingBottom(
                    activeDom.getBoundingClientRect().top
                    - commentIconDomScrollTop
                    + (paddingBottom || 0)
                );
            }
            scrollCommentToIconPos.run(false);
        }
    }, { wait: 100 });

    // 当选中发生变化将选中对应的评论滚动到视口内，并与选中内容对齐
    const scrollCommentToIconPos = useDebounceFn((isScrollComment: boolean = true) => {
        if (active && visible) {
            setCommentWrapOverHeight(getCommentWrapOverHeight());
            const activeDom = document.getElementById(`${active}`);
            const commentContanierDom = document.getElementById('commentContanier');
            const commentIconDomScrollTop = getShouldScorllTop();
            if (activeDom && commentContanierDom && commentIconDomScrollTop) {
                commentContanierDom.scrollBy({
                    top: activeDom.getBoundingClientRect().top - commentIconDomScrollTop
                });
                isScrollComment && scrollCommentContent.run();
            }
        }
    }, { wait: 100 });

    // 点击评论按钮触发事件
    const onHandleClickCommentIcon = (type: CommentType, realPath: string, isAddComment: boolean = true) => {
        // 数据结构弹窗下 commentType为空
        const commentMap = commentList[type] || {};
        const path = `${getStaff(type)}${realPath}`;
        const comments = !!path ? commentMap[path] : [];
        setVisible(true);
        setActive(path);
        /**
         * 不存在未提交的comment
         */
        if (!comments || comments.every(it => !!it.commentId)) {
            isAddComment && addNewComment({
                path,
                comment: '',
                markdown: '',
            });
            scrollCommentToIconPos.run(path);
        }
    };

    return {
        apiId,
        version,
        visible,
        setVisible,
        active,
        setActive,
        commentList,
        updateCommentsList,
        addNewComment,
        deleteComment,
        resolveComment,
        originList: list,
        setOriginList: setList,
        disabled,
        setDisabled,
        eventEmitter: eventEmitterRef.current,
        scrollCommentToIconPos,
        onHandleClickCommentIcon,
        paddingTop,
        setPaddingTop,
        paddingBottom,
        commentListConRef,
        commentWrapOverHeight
    };
}

type TCreateContainerReturn = [any, () => ReturnType<typeof useComments>];

export const [CommentProvider, useCommentContainer] = createContainer(useComments) as TCreateContainerReturn;

/**
 * 接口定义分 request-body/request-header/request-query/response-header/response-body;
 * 前端定义前缀
 */
export function getStaff(type: CommentType) {
    return type + '@';
}

interface CommentIconWrapProps {
    children: React.ReactNode;
    num?: number;
    realPath: string;
    commentType: CommentType;
    disabled?: boolean;
    hasChildren?: boolean;
    virtualTable: any;
    rowKey: string;
    isHoverRow?: boolean;
}
/**
 * 评论Icon
 */
export function CommentIconWrap(
    {
        children,
        num,
        realPath,
        commentType,
        disabled,
        hasChildren = false,
        virtualTable,
        rowKey,
        isHoverRow
    }: CommentIconWrapProps) {
    const { onHandleClickCommentIcon } = useCommentContainer();
    if (disabled) {
        return children as any as React.ReactElement;
    }
    return <div
        style={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between',
            width: hasChildren ? 'calc(100% - 24px)' : '100%'
        }}
        id={`icon$${getStaff(commentType)}${realPath}`}
    >
        <div style={{
            flex: 1,
            overflow: 'hidden',
            textOverflow: 'ellipsis',
            whiteSpace: 'nowrap',
        }}>{children}</div>
        <div
            onClick={() => onHandleClickCommentIcon(commentType, realPath)}
            style={{
                flex: 0,
                marginLeft: '4px',
                marginRight: '12px',
                cursor: 'pointer'
            }}
        >
            {
                num === 0
                    ? (isHoverRow ? <Tooltip title={'评论'}>
                        <span>
                            <KdevIconFont
                                id={common_system_comment}
                                className={css.commentIcon}
                            />
                        </span>
                    </Tooltip> : null)
                    : <div style={{
                        background: '#FFC200',
                        color: '#fff',
                        width: '20px',
                        textAlign: 'center',
                        borderRadius: '3px',
                        lineHeight: '16px',
                    }}>{num}</div>
            }
        </div>
    </div>;
}
interface Props {
    style?: React.CSSProperties;
}

export function CommentDraw(props: Props) {
    const {
        apiId,
        version,
        visible,
        setVisible,
        active,
        setActive,
        commentList,
        addNewComment,
        updateCommentsList,
        originList,
        resolveComment,
        eventEmitter,
        paddingTop,
        paddingBottom,
        commentListConRef,
        commentWrapOverHeight
    } = useCommentContainer();

    /**
     * 此处要使用display: none做显隐
     * 初始化加载过慢会导致定位评论滚动不顺畅，影响用户体验
     */
    // if (!visible) {
    //     return null;
    // }
    const mun = originList?.filter(it => !!it.commentId).length;
    return <div
        className={css.draw}
        style={{
            display: visible ? 'flex' : 'none',
            ...props.style,
        }}
    >
        <div className={css.commentTitle}>
            <b style={{ fontSize: '16px' }}>
                评论（{mun}）
            </b>
            <span className={css.closeBox} onClick={() => setVisible(false)}>
                <KdevIconFont id={common_system_guanbisamll} />
            </span>
        </div>
        <div
            className={css.commentListWrap}
            id="commentContanier"
            style={{ paddingTop, paddingBottom }}
        >
            <div className={css.commentList} ref={commentListConRef}>
                {
                    Object.values(CommentType).map(typeItem => Object.keys(commentList[typeItem]).map(key => {
                        const item = commentList[typeItem][key];
                        const title = key.split('.').reverse()[0];
                        return <Card key={item[0].path} title={title} item={item} typeItem={typeItem} />;
                    }))
                }
            </div>
            <div style={{ height: commentWrapOverHeight }} />
        </div>
    </div>;
}

function Card(props: {
    title: string;
    item: Array<Comment>;
    typeItem: CommentType;
}) {
    const {
        title,
        item,
        typeItem,
    } = props;

    const {
        active,
        apiId,
        version,
        setVisible,
        setActive,
        addNewComment,
        resolveComment,
        eventEmitter,
    } = useCommentContainer();
    const [show, setShow] = useState<boolean>(true);
    // const commentCardRef = useRef<HTMLDivElement>(null);
    const handleResolve = async () => {
        await nsApiCommentsResolve.remote({
            list: item.map(it => it.commentId).filter(Boolean)
        });
        resolveComment(item[0].path);
        message.success('操作成功！');
        setVisible(false);
    };

    const handlePublish = (key, path) => {
        eventEmitter.emit(key, path);
    };

    return <div
        onMouseEnter={() => setShow(true)}
        onMouseLeave={() => setShow(false)}
        className={classNames(
            css.card,
            {
                [css.active]: active === item[0].path
            },
            active === item[0].path && 'comment_active'
        )}
        onClick={() => {
            setActive(item[0].path);
            handlePublish(typeItem, item[0].realPath);
        }}
        id={item[0].path}
    >
        <div
            style={{
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'space-between',
                lineHeight: '22px',
                height: '22px'
            }}
        >
            <PopoverEllipsis title={title}>
                <div style={{
                    color: '#898A8C',
                    borderLeft: '3px solid #D5D6D9',
                    paddingLeft: '8px',
                    flex: 1,
                    overflow: 'hidden',
                    textOverflow: 'ellipsis',
                    whiteSpace: 'nowrap',
                }}>{title}</div>
            </PopoverEllipsis>
            {
                show && item.filter(it => !!it.commentId).length > 0 && <div>
                    <Divider type={'vertical'} />
                    <Button
                        type="text"
                        className={css.iconBtn}
                        onClick={() => handleResolve()}
                        icon={
                            <Tooltip title="标记为已解决并隐藏" placement="topRight">
                                <span>
                                    <KdevIconFont id={common_system_correct} />
                                </span>
                            </Tooltip>
                        }
                    ></Button>
                </div>
            }
        </div>
        {
            item.map((comment, index) => <Commit comment={comment} key={comment.commentId || comment.createTime} />)
        }
        {
            !item.some(it => !it.commentId) &&
            <div
                className={css.addCommentBtn}
                onClick={() => addNewComment({
                    path: item[0].path,
                    comment: '',
                    markdown: '',
                })}
            >
                {show && '添加评论...'}
            </div>
        }
    </div>;
}

function Commit(props) {
    const comment = props.comment;
    const [isEdit, setIsEdit] = useState(!comment.commentId);
    const [mdEditor, setMdEditor] = useState(null);
    const [show, setShow] = useState<boolean>(false);
    const userInfo = team.getUserInfo();
    const {
        apiId,
        version,
        deleteComment,
        addNewComment,
        updateCommentsList,
        originList,
        setOriginList,
    } = useCommentContainer();
    const initMdEditor = (editor) => {
        setMdEditor(editor);
        editor.setValue(comment.comment);
    };

    const handleDeleteComment = async () => {
        Modal.confirm({
            title: '确认删除',
            onOk: async () => {
                try {
                    await nsApiCommentsDelete.remote({
                        commentId: comment.commentId,
                    });
                    message.success('删除成功！');
                    updateCommentsList();
                } catch (e) {

                }
            }
        });
    };

    const handleSubmit = async () => {
        try {
            // @ts-ignore
            const value = mdEditor.cm.doc.getValue();
            if (!value) {
                message.error('请填写评论!');
                return;
            }
            await nsApiCommentsSave.remote({
                commentId: comment.commentId || -1,  // 为-1时新增评论信息
                apiId: apiId || -1,
                version: version,
                comment: value,
                path: comment.path,
            });
            message.success('评论成功！');
            setIsEdit(false);
            updateCommentsList();
        } catch (e) { }
    };

    return <div className={css.mdEdit}>
        <div
            onMouseEnter={() => setShow(true)}
            onMouseLeave={() => setShow(false)}
        >
            <div className={css.userInfoWrap}>
                <div className={css.userInfo}>
                    <AvatarList
                        reviewers={[{ ...comment.commentUser, avatarUrl: comment.commentUser.photo }] || []}
                        kim
                        size={24}
                    />
                    <span style={{ marginLeft: '2px' }}>
                        {comment.commentUser.name}
                    </span>
                </div>
                {
                    show && !isEdit
                        ? <div className={css.operateWrap}>
                            {
                                userInfo.userName === comment.commentUser.username &&
                                <Button
                                    type="text"
                                    onClick={() => {
                                        setIsEdit(true);
                                    }}
                                    className={css.iconBtn}
                                    icon={
                                        <Tooltip title={'编辑'}>
                                            <span><KdevIconFont id={common_system_edit} /></span>
                                        </Tooltip>
                                    }
                                />
                            }
                            <Button
                                type="text"
                                onClick={() => {
                                    if (originList.find(it => it.path === comment.path && !it.commentId)) {
                                        const newList = [...originList];
                                        setOriginList([
                                            ...newList.filter(it => !(it.path === comment.path && !it.commentId)),
                                            {
                                                path: comment.path,
                                                comment: `@${comment.commentUser.username} `,
                                                markdown: `[ @${comment.commentUser.name} ](kim://username?username=${comment.commentUser.username})`,
                                                commentUser: {
                                                    id: userInfo.userId,
                                                    name: userInfo.chineseName,
                                                    username: userInfo.userName,
                                                    email: userInfo.email,
                                                    photo: userInfo.photo,
                                                },
                                                createTime: +new Date(),
                                            }
                                        ] as any);
                                    } else {
                                        addNewComment({
                                            path: comment.path,
                                            comment: `@${comment.commentUser.username} `,
                                            markdown: `[ @${comment.commentUser.name} ](kim://username?username=${comment.commentUser.username})`,
                                        });
                                    }
                                }}
                                className={css.iconBtn}
                                icon={
                                    <Tooltip title={'回复'}>
                                        <span><KdevIconFont id={common_system_comment} /></span>
                                    </Tooltip>
                                }
                            />
                            {
                                userInfo.userName === comment.commentUser.username &&
                                <Button
                                    type="text"
                                    onClick={handleDeleteComment}
                                    className={css.iconBtn}
                                    icon={
                                        <Tooltip title={'删除'}>
                                            <span><KdevIconFont id={common_system_delete02} /></span>
                                        </Tooltip>
                                    }
                                />
                            }
                        </div>
                        : (!!comment.commentId && <span style={{ color: '#575859' }}>{moment(comment.updateTime).format('YYYY-MM-DD')}</span>)
                }
            </div>
            <div
                style={{ marginTop: '8px' }}
                className={classNames({
                    [css.editorView]: !isEdit,
                    [css.editorEdit]: isEdit
                })}
            >
                {isEdit
                    ? <ApiCommentEditor
                        conf={{
                            onload: (editor) => {
                                if (!editor) {
                                    return;
                                }
                                initMdEditor(editor);
                            }
                        }}
                        placeholder="请输入"
                        delay={0}
                    />
                    : <MarkdownViewer key={comment.markdown} content={comment.markdown} />
                }
            </div>
        </div>
        {
            isEdit && <div style={{
                display: 'flex',
                justifyContent: 'flex-end',
                marginTop: '8px'
            }}>
                <Space>
                    <Button
                        size={'small'}
                        onClick={() => {
                            if (comment.commentId) {
                                setIsEdit(false);
                            } else {
                                deleteComment(comment);
                            }
                        }}
                    >取消</Button>
                    <Button
                        size={'small'}
                        type={'primary'}
                        onClick={handleSubmit}
                    >评论</Button>
                </Space>
            </div>
        }
    </div>;
}

// export const CommentDraw = React.forwardRef(InnerCommentDraw);
/**
 *  固定在右侧的图钉，用于查看所有的评论
 */
export function CommentAffix() {
    const {
        setVisible,
        setActive,
        visible,
        disabled,
    } = useCommentContainer();
    const handleClick = () => {
        setVisible(true);
        setActive(undefined);
    };
    /**
     * 抽屉出现的时候， 固定的全局图钉隐藏
     */
    if (visible) {
        return null;
    }
    if (disabled) {
        return null;
    }
    return <div
        style={{
            position: 'fixed',
            right: '0',
            background: '#fff',
            top: '400px',
            lineHeight: '32px',
            width: '45px',
            paddingLeft: '16px',
            border: '1px solid #EBEDF0',
            borderTopLeftRadius: '25px',
            boxSizing: 'border-box',
            borderBottomLeftRadius: '25px',
            cursor: 'pointer',
        }}
        onClick={handleClick}
    >
        <KdevIconFont
            id={common_system_comment}
            style={{
                color: '#898A8C',
                fontSize: '20px',
                position: 'relative',
                left: '-4px',
                top: '2px'
            }} />
    </div>;
}
function getCommitNum(commentList: {
    [K in CommentType]: Record<string, Array<Comment>>
}) {
    return Object.values(commentList).reduce((total, item) => (total + Object.keys(item).length), 0);
}
