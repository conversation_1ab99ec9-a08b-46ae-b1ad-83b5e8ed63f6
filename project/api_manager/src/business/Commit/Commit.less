.editorView {
  :global {
    .editormd-html-preview {
      padding: 0 !important;
    }

    .cm-link {
      color: rgb(0, 0, 0) !important;
      text-decoration: none;
    }
  }
}

:global {
  .cm-link {
    color: rgb(0, 0, 0) !important;
    text-decoration: none !important;
  }
}

.editorEdit {
  :global {
    .CodeMirror-activeline-background {
      background: #fff;
    }

    .CodeMirror-scroll {
      margin-bottom: -34px;
    }

    // 修改输入框样式
    .editormd {
      border-radius: 4px;

      &:has(.CodeMirror-focused) {
        border: 1px solid #326BFB !important;
      }
    }
  }
}

.card {
  border: 1px solid #EBEDF0;
  padding: 16px;
  border-radius: 4px;
  cursor: pointer;

  .addCommentBtn {
    height: 24px;
    margin-top: 8px;
    color: #575859;

    &:hover {
      cursor: pointer;
    }
  }

  .iconBtn {
    color: #898A8C;
    padding: 0;
    font-size: 16px;
    height: 22px;
    width: 22px;
    margin-right: -4px;

    &:hover {
      color: #326BFB;
    }
  }

  .mdEdit {
    margin-top: 16px;

    .userInfoWrap {
      display: flex;
      align-items: center;
      justify-content: space-between;
      height: 24px;

      .userInfo {
        display: flex;
        align-items: center;
        height: 24px;
      }

      .operateWrap {
        display: flex;
        align-items: center;
        gap: 16px;
      }
    }
  }
}

.draw {
  position: fixed;
  background-color: #FFFFFF;
  width: 320px;
  right: 0;
  padding: 16px;
  top: 96px;
  height: calc(100% - 96px);
  overflow: auto;
  flex-direction: column;
  z-index: 1;

  &:before {
    content: "";
    position: absolute;
    top: 0;
    left: -16px;
    bottom: 0;
    width: 10px;
    box-shadow: -2px 0px 10px rgba(0, 0, 0, 0.5);
  }
}

.active {
  border-top: 3px solid #FFAA00;
}

.commentIcon {
  color: #898A8C !important;
  font-size: 20px;

  &:hover {
    color: #326BFB !important;
  }
}

.commentTitle {
  display: flex;
  align-items: center;
  height: 24px;
  line-height: 24px;

  .closeBox {
    margin-left: auto;
    font-size: 20px;
    color: #898A8C;

    &:hover {
      cursor: pointer;
    }
  }
}

.commentListWrap {
  flex: 1;
  overflow: hidden auto;
  margin-top: 16px;

  .commentList {
    display: flex;
    flex-direction: column;
    gap: 16px;
  }
}

:global {
  ._comment_active {
    td {
      background: #FFFBE6 !important;
    }
  }
}