import React from 'react';
import { observer } from 'mobx-react';
import { AView, AViewModel } from '@libs/mvvm';
import { Popover, Select, Avatar } from 'antd';
import css from './CreateMaintainer.less';
import { observable, action, runInAction } from 'mobx';
import { nsKdevUserListGET } from '@/remote';
import Bind from 'lodash-decorators/bind';
import debounce from 'lodash-decorators/debounce';

const { Option } = Select;

export class CreateMaintainerModel extends AViewModel {
    @observable public peopleList: nsKdevUserListGET.IUser[] = [];
    @observable public loadingList: boolean = false;
    @observable public visible: boolean = false;
    @observable public userInfo: nsKdevUserListGET.IUser = {
        id: 0,
        photo: '',
        username: '',
        name: '',
        email: '',
        avatarUrl: ''
    };
    @observable public username = '';

    public timeout?: number;

    /**
     * 调用搜索接口
     */
    @debounce(300)
    private async getUserList(value: string) {
        try {
            const result = await nsKdevUserListGET.remote({
                search: value
            });
            runInAction(() => {
                this.peopleList = result.list || [];
            });
        } catch (e) {
        }
    }

    @action.bound
    public handleSearch(value: string) {
        this.getUserList(value);
    }

    @action
    public handleChange(value: string) {
        this.username = value;
        const index = this.peopleList.findIndex(ele => ele.username === value);
        (index > -1) && (this.userInfo = this.peopleList[index]);
        this.handleVisibleChange();
    }

    /**
     * 关闭popover
     */
    @action
    public handleVisibleChange() {
        this.visible = !this.visible;
        if (this.visible) {
            this.username = '';
            this.peopleList = [];
        }
    }
}

interface IPropsInfo {
    handelSearchSelect: any;
    btnNode: React.ReactNode;
}

@observer
export class CreateMaintainer extends AView<CreateMaintainerModel, IPropsInfo> {

    @Bind
    private handleChange(value: string) {
        this.model.handleChange(value);
        this.props.handelSearchSelect(this.model.userInfo);
    }

    @Bind
    private handleVisibleChange() {
        this.model.handleVisibleChange();
    }

    @Bind
    private btn(e) {
        e.stopPropagation();
    }

    public contentNode(): React.ReactNode {
        return (
            <div onClick={this.btn}>
                <Select
                    placeholder={'输入关键字搜索'}
                    value={this.model.username}
                    style={{ width: '200px' }}
                    optionLabelProp="lable"
                    showSearch
                    filterOption={false}
                    onSearch={this.model.handleSearch}
                    onChange={this.handleChange}
                >
                    {
                        this.model.peopleList.map((ele, index) => {
                            return <Option key={ele.username} lable={ele.name} value={ele.username}>
                                <div className={css.searPeopleOption}>
                                    <Avatar src={ele.photo}>{ele.name[0]}</Avatar>
                                    <div className={css.info}>
                                        <p style={{ fontWeight: 'bold' }}>{ele.name}</p>
                                        <p>{ele.username}</p>
                                    </div>
                                </div>
                            </Option>;
                        })
                    }
                </Select>
            </div>
        );
    }

    public render(): React.ReactNode {
        return (
            <Popover
                placement="bottomLeft"
                content={this.contentNode()}
                trigger="click"
                visible={this.model.visible}
                onVisibleChange={this.handleVisibleChange}>
                <span onClick={this.btn}>{this.props.btnNode}</span>
            </Popover>
        );
    }
}
