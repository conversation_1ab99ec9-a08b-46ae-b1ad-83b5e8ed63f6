.moveInPorjectModal {
    .requiredLabel {
        margin-bottom: 8px;
    }

    .requiredLabel::after {
        content: ' *';
        color: #ff4d4f;
    }

    .treeSelect {
        width: 100%;
        margin-bottom: 24px;
    }

    .createProjectBtn {
        display: flex;
        margin-top: 2px;
    }

    .branchSelect {
        width: 100%;
    }

    .projectTree {
        border: 1px solid #d9d9d9;
        border-radius: 4px;
        margin: 0;
        padding: 12px 0;

        .titleWrap {
            display: flex;

            .editProjectName {
                flex: 1;
            }

            .editCloseBtn {
                color: #ff4d4f;
                font-size: 18px;
            }

            .editOkBtn {
                color: #31bf30;
                font-size: 18px;
            }
        }

        :global {
            .ant-empty-description {
                opacity: .5;
            }

            .ant-tree-node-content-wrapper {
                display: flex;
                height: 32px;
                align-items: center;
            }

            .ant-tree-title {
                width: 100%;
            }
        }
    }
}