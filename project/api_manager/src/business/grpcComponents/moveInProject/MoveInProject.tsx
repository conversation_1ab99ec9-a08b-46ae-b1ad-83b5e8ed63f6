import React from 'react';
import { AView, bindObserver } from '@libs/mvvm';
import { observer } from 'mobx-react';
import { MoveInProjectM } from './MoveInProjectM';
import { Modal, TreeSelect, Input, Tree, Empty, Button, Select } from 'antd';
import css from './MoveInProject.less';
import { TreeSelect_onDropdownVisibleChange } from '@libs/utils';
import { Bind } from 'lodash-decorators';
import { SearchEmptyIcon } from '@/business/commonIcon';
import { KdevIconFont } from '@/business/commonComponents';

const { DirectoryTree } = Tree;
const { Option } = Select;

const Input_projectName = bindObserver(Input, 'projectName');

@observer
export class MoveInProject extends AView<MoveInProjectM> {

    @Bind
    private titleRender(nodeData): React.ReactNode {
        const model = this.model;
        if (nodeData.key === '0') {
            return (
                <div className={css.titleWrap}>
                    <Input_projectName
                        className={css.editProjectName}
                        placeholder="请填写项目名称"
                        model={model}
                    />
                    <Button
                        icon={<KdevIconFont id="#iconclose" className={css.editCloseBtn} />}
                        type="link"
                        onClick={model.onCloseEditProject}
                    />
                    <Button
                        icon={<KdevIconFont id="#iconcheck-line" className={css.editSaveBtn} />}
                        type="link"
                        onClick={model.createProject}
                        loading={model.saveProjectLoading}
                    />
                </div>
            );
        }
        return (
            <div>{nodeData.projectName}</div>
        );
    }

    @Bind
    private renderProjectInfo(): React.ReactNode {
        const model = this.model;
        return (
            <>
                <div className={css.requiredLabel}>选择项目</div>
                {
                    model.projectList?.length ?
                        <DirectoryTree
                            treeData={model.projectList}
                            titleRender={this.titleRender}
                            className={css.projectTree}
                            height={240}
                            icon={<KdevIconFont id="#iconxiangmukuzixiangmuyusuanxiadashenqingbiao" />}
                            onSelect={model.onSelectProject}
                            selectedKeys={[model.projectId]}
                            blockNode
                        /> :
                        <Empty className={css.projectTree} image={<SearchEmptyIcon />} />
                }
                <a className={css.createProjectBtn} onClick={model.addProjectRow}>新建项目</a>
            </>
        );
    }

    // @Bind
    // private renderBranch(): React.ReactChild {
    //     const model = this.model;
    //     return (
    //         <>
    //             <div className={css.requiredLabel}>选择分支</div>
    //             <Select value={model.branchName} onSelect={model.onSelectBranch} className={css.branchSelect}>
    //                 {
    //                     model.branchNameList.map(item => (
    //                         <Option key={item} value={item}>{item}</Option>
    //                     ))
    //                 }
    //             </Select>
    //         </>
    //     );
    // }

    public render() {
        const model = this.model;
        return (
            <Modal
                visible={model.visible}
                title="移入项目"
                className={css.moveInPorjectModal}
                onCancel={model.onCloseMoveInProjectModal}
                onOk={model.checkParams}
            >
                <div className={css.requiredLabel}>选择部门</div>
                <TreeSelect
                    className={css.treeSelect}
                    placeholder={'请选择部门'}
                    treeData={model.departmentList}
                    onDropdownVisibleChange={TreeSelect_onDropdownVisibleChange}
                    showSearch
                    treeNodeFilterProp="title"
                    onSelect={model.onSelectDepartment}
                    value={model.departmentId || undefined}
                />
                {this.renderProjectInfo()}
                {/* {this.renderBranch()} */}
            </Modal>
        );
    }
}
