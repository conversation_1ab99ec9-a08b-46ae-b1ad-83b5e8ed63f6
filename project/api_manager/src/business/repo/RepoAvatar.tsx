import React from 'react';
import css from './index.less';
import { Avatar } from 'antd';
import classNames from 'classnames';

const color = [
    '#327DFF14',
    '#7735D314',
    '#36CFA214',
    '#FFA11414'
];

function AvatarColorRandom(str) {
    const idx = str.length % 4; // 根据name长度取背景色值
    return color[idx];
}

interface IProps {
    name: string;
    avatarUrl?: string;
    size?: 'large' | 'small' | 'default' | number;
    className?: string;
    fontSize?: number;
}

export class RepoAvatar extends React.Component<IProps, any> {
    public render(): React.ReactElement {
        return (
            <Avatar
                src={this.props.avatarUrl}
                shape={'square'}
                size={this.props.size || 48}
                className={classNames(css.repoAvatar, this.props.className)}
                style={{
                    backgroundColor: AvatarColorRandom(this.props.name || ''),
                    fontSize: this.props.fontSize || 18
                }}
            >
                {this.props.name?.substr(0, 1).toUpperCase() || ''}
            </Avatar>
        );
    }
}
