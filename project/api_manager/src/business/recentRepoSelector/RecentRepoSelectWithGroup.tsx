import React from 'react';
import { RecentRepoSelector } from './RecentRepoSelector';
import {
    nsKdevRepoListAll, nsKdevRepoListAllWithLimit, nsKdevRepoRecentVisitList
} from '@/remote';
import Bind from 'lodash-decorators/bind';

interface IProps {
    value: number | undefined;
    groupId?: string;
    onChange: (value: nsKdevRepoListAll.IList_item | null) => void;
    placeholder?: string;
    suffixIcon?: React.ReactNode;
    allowClear?: boolean;
    className?: string;
    afterInit?: (value: nsKdevRepoListAll.IList_item | null) => void; // 初始化结束后的回调函数
    disabled?: boolean;
    path?: string;
    style?: React.CSSProperties;
    getPopupContainer?: (triggerNode: HTMLElement) => HTMLElement;
    repoName?: string | undefined;
    defaultFill?: boolean; // 默认为true，初始化时是否默认填充
    featureId?: number; // 是否需要返回范围限制为某feature视图内的常用仓库
    useStateRepoName?: boolean;
    status?: '' | 'warning' | 'error' | undefined;
}
/**
 * 与原组件 RepoSelectWithGroup（分支视图右上方的仓库选择组件） 逻辑类似
 * 区别：
 * 1、默认选择框与普通的select样式一样
 * 2、groupId改为可选参数，如果没有则调用 /api/kdev/repo/listAllWithLimit 接口获取我的仓库列表
 * 3、默认填充常用仓库中的Top1，如果常用仓库无数据，则展示我的仓库列表，不默认填充
 * 4、增加手动埋点@todo
 * 5、可通过defaultFill 判断初始化时是否默认填充
 * 此期仅修改迭代视图和feature视图中的相关仓库选择 
 * https://docs.corp.kuaishou.com/k/home/<USER>/fcADxv7o2HNXrHxDwUaV4cc0R
 * https://team.corp.kuaishou.com/task/T2607370
 */
export class RecentRepoSelectWithGroup extends React.Component<IProps, any> {

    /**
     * @description 如果没有groupId 则调用 /api/kdev/repo/listAllWithLimit 接口获取我的仓库列表
     */
    @Bind
    public async getRepoList(keyword): Promise<Array<nsKdevRepoListAll.IList_item>> {
        try {
            const params = {
                groupId: this.props.groupId ? +this.props.groupId : undefined,
                search: keyword.trim(),
            };
            const respData = await nsKdevRepoListAllWithLimit.remote(params);

            return Promise.resolve(respData.list);
        } catch (e) {
            return Promise.resolve([]);
        }
    }

    @Bind
    public async getRecentIngroup(): Promise<Array<nsKdevRepoListAll.IList_item>> {
        try {
            let params = {
                groupId: this.props.groupId,
            };
            if (this.props.featureId) {
                params = Object.assign(params, { featureId: this.props.featureId });
            }
            const respData = await nsKdevRepoRecentVisitList.remote(params);
            return Promise.resolve(respData.list);
        } catch (e) {
            return Promise.resolve([]);
        }
    }

    public render(): React.ReactElement {
        return <RecentRepoSelector
            value={this.props.value}
            onSearch={this.getRepoList}
            onGetRecent={this.getRecentIngroup}
            onChange={this.props.onChange}
            placeholder={this.props.placeholder}
            suffixIcon={this.props.suffixIcon}
            allowClear={this.props.allowClear}
            className={this.props.className}
            afterInit={this.props.afterInit}
            disabled={this.props.disabled}
            style={this.props.style}
            getPopupContainer={this.props.getPopupContainer}
            repoName={this.props.repoName}
            groupId={this.props.groupId}
            defaultFill={this.props.defaultFill}
            useStateRepoName={this.props.useStateRepoName}
            status={this.props.status}
        />;
    }
}
