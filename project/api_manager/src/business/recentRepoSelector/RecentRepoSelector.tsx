import React from 'react';
import css from './RecentRepoSelector.less';
import { Input, Popover, Spin, Button, Select, Tooltip, Empty } from 'antd';
import { RepoAvatar } from '@/business/repo/RepoAvatar';
// import {KDevEmpty, EmptyMap} from '@/business/empty';
import { nsKdevRepoDetailListGet, nsKdevRepoListAll } from '@/remote';
import { searchHighLight } from '@/business/common/searchHighLight';
import Bind from 'lodash-decorators/bind';
import classNames from 'classnames';
import { Debounce } from 'lodash-decorators';
import { SearchEmptyIcon } from '@/business/commonIcon';
import { KdevIconFont, KEmpty } from '../commonComponents';
import { common_system_search } from '@kid/enterprise-icon/icon/output/icons';

interface IProps {
    value: number | undefined;
    onSearch: (keyword: string) => Promise<Array<nsKdevRepoListAll.IList_item>>;
    onGetRecent: () => Promise<Array<nsKdevRepoListAll.IList_item>>;
    onChange: (value: nsKdevRepoListAll.IList_item | null) => void;
    placeholder?: string;
    suffixIcon?: React.ReactNode;
    allowClear?: boolean;
    className?: string;
    afterInit?: (value: nsKdevRepoListAll.IList_item | null) => void; // 初始化结束后的回调函数
    disabled?: boolean;
    style?: React.CSSProperties;
    getPopupContainer?: (triggerNode: HTMLElement) => HTMLElement;
    repoName?: string | undefined;
    path?: string | undefined;
    groupId?: string;
    defaultFill?: boolean; // 默认为true，初始化时是否默认填充
    useStateRepoName?: boolean;
    status?: '' | 'warning' | 'error' | undefined;
}

interface IState {
    visible: boolean;
    loading: boolean;
    mouseEnter: boolean;
    keyword: string;
    repoList: Array<nsKdevRepoListAll.IList_item>;
    repoName: string;
    showRecent: boolean; // 展示“常用仓库”信息
    repoPath: string;
}

export class RecentRepoSelector extends React.Component<IProps, IState> {
    private readonly inputRef;
    public state: IState = {
        visible: false,
        loading: false,
        mouseEnter: false,
        keyword: '',
        repoList: [],
        repoName: '',
        showRecent: false,
        repoPath: ''
    };

    constructor(props) {
        super(props);
        this.inputRef = React.createRef();
    }

    public componentDidMount(): void {
        this.loadInfo();
    }

    /**
     * @description 当外部groupId变化，默认选中常用仓库中第一个，若为空数组则不填充
     */
    public componentDidUpdate(prevProps: Readonly<IProps>): void {
        // if (String(prevProps.groupId) !== String(this.props.groupId)) {
        //     this.props.onGetRecent().then((respData) => {
        //         this.setState({
        //             repoName: respData[0]?.repoName || ''
        //         });
        //         this.props.onChange( respData[0] || null);
        //     });
        // }
    }

    @Debounce(300)
    private async getSearchRepoList(keyword) {
        if (keyword !== this.state.keyword) {
            return;
        }
        const respData = await this.props.onSearch(keyword);
        this.setState({
            repoList: respData,
            loading: false,
            showRecent: false,
        });
    }

    @Debounce(300)
    private async getRecentRepoList() {
        const respData = await this.props.onGetRecent();
        this.setState({
            repoList: respData,
            loading: false,
            showRecent: respData.length !== 0,
        });
    }

    /**
     * 打开选择面板
     * @param visible
     */
    @Bind
    private handleVisibleChange(visible) {
        this.setState({
            visible
        });
        if (visible) {
            setTimeout(() => {
                this.inputRef.current?.focus();
            }, 100);
            this.setState({
                keyword: '',
                repoList: [],
                loading: true,
            });
            this.props.onGetRecent().then((respData) => {
                if (!respData || respData.length === 0) {
                    this.getSearchRepoList('');
                } else {
                    this.setState({
                        repoList: respData,
                        loading: false,
                        showRecent: true,
                    });
                }
            });
        }
    }

    /**
     * 选中
     * @param repoItem
     */
    @Bind
    private onSelect(repoItem) {
        this.props.onChange(repoItem);
        this.setState({
            visible: false,
            repoName: repoItem.repoName,
            repoPath: repoItem.path,
        });
    }

    /**
     * 修改关键字，触发搜索
     * @param keyword
     */
    private handleChange(keyword) {
        this.setState({
            keyword,
            loading: true
        });
        if (keyword) {
            this.getSearchRepoList(keyword);
        } else {
            this.getRecentRepoList();
        }
    }

    /**
     * 清空
     * @param e
     */
    @Bind
    private handleClear(e?) {
        e?.stopPropagation();
        this.props.onChange(null);
        this.setState({
            repoName: '',
        });
    }

    private async loadInfo() {
        this.setState({
            loading: true
        });

        // 如果有值的话，调取接口获取name;否则，如果显示默认填充，则显示常用仓库列表返回的第一个仓库
        if (this.props.value) {
            // const respData = await nsKdevRepoDetailListGet.remote({repoIds: String(this.props.value)});
            // const ele = respData.list.find(item => String(item.repoId) === String(this.props.value));
            // if (ele) {
            //     const result = await this.props.onSearch(ele.name ?? '');
            //     const repo = result.find(item => item.repoId === this.props.value);
            //     this.setState({
            //         repoName: repo?.repoName || '',
            //         repoPath: repo?.path || '',
            //     }, () => {
            //         this.props.afterInit?.(repo || null);
            //     });
            // } else {
            //     this.props.afterInit?.(null);
            // }
            this.setState({
                repoName: this.props.repoName || '',
                repoPath: this.props.path || '',
            });

        } else {
            this.props.onGetRecent().then((respData) => {
                // const repo = this.props.defaultFill === false ? null : (respData[0] || null);
                // this.setState({
                //     repoName: repo?.repoName || '',
                //     repoPath: repo?.path || '',
                // }, () => {
                //     this.props.afterInit?.(repo);
                // });
            });
        }
    }

    private renderRepoList(): React.ReactElement {
        return (
            <div className={css.repoListContent}>
                {
                    !this.state.keyword && this.state.showRecent &&
                    <p className={css.title}>常用仓库</p>
                }
                <Spin spinning={this.state.loading}>
                    <div className={css.repoList}>
                        {
                            this.state.repoList.map(item => {
                                return (
                                    <div
                                        className={css.item}
                                        key={item.repoId}
                                        onClick={() => this.onSelect(item)}
                                    >
                                        <RepoAvatar
                                            name={item.repoName}
                                            size={36}
                                            className={css.avatar}
                                        />
                                        <div className={css.repoName}>
                                            <p className={css.path}
                                                dangerouslySetInnerHTML={{
                                                    __html: searchHighLight(item.repoName, this.state.keyword)
                                                }}
                                            />
                                            <p className={css.desc}
                                                dangerouslySetInnerHTML={{
                                                    __html: searchHighLight(item.path, this.state.keyword)
                                                }}
                                            />
                                        </div>
                                    </div>
                                );
                            })
                        }
                    </div>
                    {
                        this.state.repoList.length === 0 &&
                        <div className={css.emptyBox}>
                            {/* <Empty
                                className={css.empty}
                                image={<SearchEmptyIcon />}
                                description={'暂无数据～'}
                            /> */}
                            <KEmpty image="NOMAL_SIMPLE_SEARCH_2" />
                            {/* <KDevEmpty
                                info={this.state.keyword
                                    ? EmptyMap.NO_DATA_FIND
                                    : EmptyMap.COMMON_EMPTY
                                }
                            /> */}
                        </div>
                    }
                </Spin>
            </div>
        );
    }

    public render(): React.ReactElement {
        return <Popover
            overlayClassName={css.topPopoverContent}
            destroyTooltipOnHide={true}
            placement={'bottomLeft'}
            title={null}
            trigger="click"
            visible={!this.props.disabled && this.state.visible}
            onVisibleChange={this.handleVisibleChange}
            getPopupContainer={this.props.getPopupContainer}
            content={
                <div className={css.right}>
                    <div className={css.inputBox}>
                        <Input
                            autoFocus
                            prefix={<KdevIconFont className={css.icon} id={common_system_search} style={{ color: '#D5D6D9' }} />}
                            allowClear={true}
                            value={this.state.keyword}
                            onChange={(e) => this.handleChange(e.target.value)}
                            ref={this.inputRef}
                        />
                    </div>
                    {
                        this.renderRepoList()
                    }
                </div>
            }
        >
            <Tooltip title={this.state.repoPath || this.props.repoName}>
                <Select
                    className={classNames(this.props.className)}
                    placeholder={this.props.placeholder}
                    value={this.props.useStateRepoName === false ?
                        this.props.repoName || undefined
                        : this.props.repoName || this.state.repoName || undefined}
                    open={false}
                    style={this.props.style || { width: '100%' }}
                    disabled={this.props.disabled}
                    allowClear={this.props.allowClear}
                    onClear={this.handleClear}
                    status={this.props.status}
                />
            </Tooltip>
        </Popover>;
    }
}
