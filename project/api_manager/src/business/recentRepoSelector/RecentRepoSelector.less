.topPopoverContent {
  color: #252626;

  .right {
    flex: 1;
    overflow: hidden;
    display: flex;
    flex-direction: column;

    .inputBox {
      padding: 12px 12px 16px 12px;
    }

    .repoListContent {
      flex: 1;
      overflow: auto;

      .title {
        margin: 0;
        padding: 0 12px 8px 12px;
      }

      .repoList {
        margin-bottom: 12px;

        .item {
          padding: 8px 15px;
          display: flex;
          align-items: center;
          cursor: pointer;

          .avatar {
            font-size: 16px;
          }

          .repoName {
            flex: 1;
            overflow: hidden;
            margin-left: 8px;
            word-break: break-all;

            .path {
              color: #252626;
              font-weight: bold;
              line-height: 22px;
              margin: 0;
              overflow: hidden;
              white-space: nowrap;
              text-overflow: ellipsis;
            }

            .desc {
              font-size: 12px;
              color: #898a8c;
              line-height: 18px;
              margin: 0;
              //overflow: hidden;
              //white-space: nowrap;
              //text-overflow: ellipsis;
            }
          }

          &:hover {
            background: #f5f7fa;
          }
        }
      }

      .emptyBox {
        margin-top: 40px;
      }
    }
  }

  :global {
    .ant-popover-inner-content {
      width: 470px;
      height: 300px;
      padding: 0;
      display: flex;
    }

    .ant-popover-arrow {
      border-color: transparent !important;
    }

    .kdev-antd-popover-inner-content {
      width: 470px;
      height: 300px;
      padding: 0;
      display: flex;
    }

    .kdev-antd-popover-arrow {
      border-color: transparent !important;
    }
  }
}

.selectorBox {
  width: 200px;
  height: 32px;
  display: flex;
  align-items: center;
  border-radius: 4px;
  padding: 0 12px;
  justify-content: space-between;
  background-color: #f5f7fa;

  .text {
    flex: 1;
    font-weight: 700;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }

  .placeholder {
    color: #bbbdbf;
  }

  .downIcon {
    font-size: 12px;
    color: rgba(0, 0, 0, .3);
  }

  .clearBtnIcon {
    width: 14px;
    cursor: pointer;
    color: rgba(0, 0, 0, .25);

    &:hover {
      color: rgba(0, 0, 0, .45);
    }
  }
}