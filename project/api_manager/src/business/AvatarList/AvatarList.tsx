import React from 'react';
import { Avatar, Col, Dropdown, Menu, Popover, Row, Tag, Tooltip } from 'antd';
import css from './AvatarList.less';
import { EllipsisOutlined } from '@ant-design/icons';
import { RealDOM } from '@libs/utils';
import { KDevAvatar } from '@kdev-fe-common/common/KDevAvatar';
import { ListWithSearch } from './ListWithSearch';

function onClickItem(e, item, kim) {
    if (kim) {
        window.location.href = `kim://username?username=${item.username}`;
    }
    e?.stopPropagation?.();
}

export function showReviewers(reviewers, kim) {
    return <Menu className={css.popReviewer}>
        {reviewers?.map(item => {
            return (
                <Menu.Item key={item?.username} onClick={(e) => onClickItem(e, item, kim)}>
                    <Row align="middle" justify={'space-between'}>
                        <Col>
                            <KDevAvatar src={item?.avatarUrl}
                                className={css.reviewerListAvatar}
                                username={item?.name} />
                            {item?.name}
                        </Col>
                        {
                            kim &&
                            <Col>
                                <img src={'https://cdnfile.corp.kuaishou.com/kc/files/a/QA-SERVEREE-FE-IN/kim.png'}
                                    style={{ height: '18px', marginLeft: '6px' }} />
                            </Col>
                        }
                    </Row>
                </Menu.Item>
            );
        })}
    </Menu>;
}

function renderPopoverName(ele, kim) {
    return (
        kim ?
            <a className={css.hoverKim} href={`kim://username?username=${ele.username}`}
                onClick={(e) => e.stopPropagation()}
            >
                <img src={'https://cdnfile.corp.kuaishou.com/kc/files/a/QA-SERVEREE-FE-IN/kim.png'}
                    style={{ height: '18px', marginRight: '6px' }} />
                {ele.name + ' ' + ele.username}
            </a>
            : <span>{ele.name + ' ' + ele.username}</span>
    );
}

interface IProps_AvatarList {
    reviewers: any[];
    max?: number;
    kim?: boolean;
    showSearch?: boolean; // 是否在dropdown中显示搜索框，默认为false
    popupContainerInBody?: boolean; // Dropdown是否挂载在body上，默认为false
    showNameIfSingle?: boolean; // 是否在用户数为1时显示用户名，默认为false
    getPopupContainer?: (triggerNode: HTMLElement) => HTMLElement;
    size?: number;
}

/**
 * 带头像的用户列表(可展示本地搜索框)
 * @param props
 * @constructor
 */
export function AvatarList(props: IProps_AvatarList) {
    const domOut = new RealDOM();
    const max = props.max || 3;
    if (!props.reviewers) {
        return null;
    }
    return (
        <span ref={domOut.setDom} className={css.avatarBox}>
            <Avatar.Group
                maxStyle={{ color: '#f56a00', backgroundColor: '#fde3cf' }}
                maxPopoverPlacement={'bottom'}
                className={css.avatarGroup}
                size={props.size || 32}
            >
                {
                    props.reviewers.length <= max ?
                        props.reviewers.map(item => {
                            const isShowName = props.showNameIfSingle && props.reviewers.length === 1;
                            return (
                                <Popover
                                    autoAdjustOverflow={false}
                                    content={() => renderPopoverName(item, props.kim)}
                                    key={item.username}
                                    placement="topRight"
                                    arrowPointAtCenter={true}
                                // getPopupContainer={() => domOut.dom as HTMLDivElement}
                                >
                                    <span className={isShowName ? css.hoverShade : ''}>
                                        <KDevAvatar src={item.avatarUrl} username={item.name} size={props.size || 32} />
                                        {
                                            isShowName && <span className={css.avatarName}>{item.name}</span>
                                        }
                                    </span>
                                </Popover>
                            );
                        })
                        :
                        <>
                            {
                                props.reviewers.slice(0, max - 1).map(item => {
                                    return (
                                        <Popover
                                            autoAdjustOverflow={false}
                                            content={() => renderPopoverName(item, props.kim)}
                                            key={item.username}
                                            placement="topRight"
                                        // getPopupContainer={() => domOut.dom as HTMLDivElement}
                                        >
                                            <KDevAvatar
                                                src={item.avatarUrl}
                                                username={item.name}
                                                size={props.size || 32}
                                            />
                                        </Popover>
                                    );
                                })
                            }
                            <Dropdown
                                overlay={props.showSearch
                                    ? <ListWithSearch reviewers={props.reviewers} kim={props.kim} />
                                    : showReviewers(props.reviewers, props.kim)}
                                placement="bottomCenter"
                                arrow
                                // getPopupContainer={ props.getPopupContainer ? props.getPopupContainer
                                //     : () => (props.popupContainerInBody ? document.body : domOut.dom as HTMLDivElement) }
                                // @ts-ignore
                                destroyPopupOnHide
                            >
                                <Avatar
                                    className={css.ellipIcon}
                                    icon={<EllipsisOutlined />}
                                    size={props.size || 32}
                                    style={{ lineHeight: props.size ? `${props.size - 2}px` : '32px' }}
                                />
                            </Dropdown>
                        </>
                }
            </Avatar.Group>
        </span>
    );

}
