.avatarGroup {
  display: inline-block;

  .ellipIcon {
    background-color: #f5f7fa !important; // 三个点的背景色不被平台整体的头像背景色
    color: #898a8c;
    font-size: 24px;
    transition: all 0.3s;
    border: 2px solid #ffffff;
  }

  .ellipIcon:hover {
    background-color: #CCD8FF;
    color: #5990FF;

  }

  .ellipIconActive {
    background-color: #CCD8FF;
    color: #5990FF;

  }

  .hoverShade {
    border-radius: 15px;
    margin-left: -14px;
    padding: 0 7px 0 14px;
    display: inline-block;
    height: 30px;
    line-height: 30px;

    .avatarName {
      padding: 0 8px;
    }

    &:hover {
      background-color: #ebefff;
    }
  }
}

.popReviewer {
  padding: 8px 0;
  max-height: 286px;
  overflow: auto;
  min-width: 168px;

  :global {
    .ant-dropdown-menu-item {
      padding: 8px 16px;
    }

    .ant-dropdown-menu-item:hover {
      background: #f5f7fa;
    }
  }

  .reviewerListAvatar {
    // display: inline-block;
    width: 36px;
    height: 36px;
    margin-right: 8px;
    line-height: 36px;

  }

}

.hoverKim {
  color: #252626;
}

.hoverKim:hover {
  color: #327DFF;
}

.avatarBox {
  :global {
    .ant-popover-inner-content {
      white-space: nowrap;
    }
  }
}

.withSearchDropdown {
  padding: 8px 0;
  background-color: #ffffff;
  border-radius: 2px;
  -webkit-box-shadow: 0 3px 6px -4px rgb(0 0 0 / 12%), 0 6px 16px 0 rgb(0 0 0 / 8%), 0 9px 28px 8px rgb(0 0 0 / 5%);
  box-shadow: 0 3px 6px -4px rgba(0, 0, 0, .12), 0 6px 16px 0 rgba(0, 0, 0, .08), 0 9px 28px 8px rgba(0, 0, 0, .05);

  .input {
    padding: 0 8px 8px;
  }
}