import React from 'react';
import { Input } from 'antd';
import css from './AvatarList.less';
import { Bind } from 'lodash-decorators';
import { showReviewers } from './AvatarList';
import { RealDOM } from '@libs/utils';

interface IProps {
    reviewers: any[];
    kim?: boolean;
}
/**
 * 带头像可搜索的用户下拉列表
 * @param props
 * @constructor
 */
export class ListWithSearch extends React.Component<IProps, any> {
    private domSearchInput = new RealDOM<HTMLElement>();
    public state = {
        keyword: '',
    };

    @Bind
    public onChangeKeyword(e) {
        this.setState({
            keyword: e.target.value,
        });
    }

    public componentDidMount(): void {
        setTimeout(() => {
            this.domSearchInput.dom && this.domSearchInput.dom.focus(); // 搜索框聚焦
        }, 300);
    }

    public render() {
        if (!this.props.reviewers.length) {
            return null;
        }
        const filtered = this.props.reviewers.filter(item => (item?.username || '').search(this.state.keyword) !== -1 || (item?.name || '').search(this.state.keyword) !== -1); // -
        return (
            <div className={css.withSearchDropdown}>
                <div className={css.input}>
                    <Input.Search
                        ref={this.domSearchInput.setDom}
                        allowClear
                        value={this.state.keyword}
                        onChange={this.onChangeKeyword}
                    />
                </div>
                <div className={css.menuList}>
                    {showReviewers(filtered, this.props.kim)}
                </div>
            </div>
        );
    }
}
