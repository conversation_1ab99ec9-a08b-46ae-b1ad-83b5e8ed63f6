import React, { useState } from 'react';
import { nsWorkbenchBranchDetailTeamListGet } from '@/remote';
import { teamLevelMap, teamStatusMap, teamTypeIconMap } from './TeamMap';
import css from './TeamItem.less';
import { KdevTag } from '../common/KdevTag';
import { Space, Tooltip } from 'antd';
import classNames from 'classnames';
import { KdevIconFont } from '@/business/common/KdevIconFont';
import { kdev_commonlyused_teamequirements } from '@kid/enterprise-icon/icon/output/icons';
import AsyncLoader from '@libs/a-component/AsyncLoader';

const AsyncTeamDetailModal: any = () => import('@team/taskdetailmodel');

interface IProps extends IProps_Origin {
    children?: any;
    disabled?: boolean;
    style?: React.CSSProperties;
}
interface IProps_Origin {
    taskId: string;
    visible?: boolean;
    onClose?: any;
    zIndex?: number;
    forWorkbench?: boolean; // 提供给工作台的组件 关闭sandbox
}

export const AsyncTaskDetailModal = (props: IProps_Origin) => {
    let env: 'pre' | 'test' | 'staging' | 'prod' = 'test';
    if (location.origin.indexOf('kdev.corp.kuaishou.com') > -1) {
        env = 'prod';
    } else if (location.origin.indexOf('kdev-pre.corp.kuaishou.com') > -1
        || location.origin.indexOf('kdev-prt.corp.kuaishou.com') > -1) {
        env = 'pre';
    } else if (location.origin.indexOf('kdev-test.staging.kuaishou.com') > -1) {
        env = 'staging';
    }

    return (
        <AsyncLoader
            compoLazy={AsyncTeamDetailModal}
            compoProps={{
                ...props,
                env: env,
                loaderOptions: {
                    useSandbox: props.forWorkbench ? false : true
                },
                zIndex: props.zIndex || 1000
            }}
            fallback={<div />}
        />
    );
};

export const TeamDetailModal = (props: IProps) => {
    const [visible, setVisible] = useState(false);
    const handleClick = (e?) => {
        if (props.disabled) {
            return;
        }
        setVisible(!visible);
        e?.stopPropagation();
    };

    return (
        <>
            <span onClick={handleClick} className={css.teamText} style={props.style}>
                {props?.children}
            </span>
            <div style={{ width: 0 }}>
                {
                    visible && <AsyncTaskDetailModal {...props} onClose={handleClick} visible={visible} />
                }
            </div>
        </>
    );
};
