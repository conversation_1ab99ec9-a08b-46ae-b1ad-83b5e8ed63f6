import React, { MouseEvent, useRef } from 'react';
import css from './ApiTeams.less';
import { Popover, Tooltip } from 'antd';
import { nsWorkbenchBranchDetailTeamListGet } from '@/remote';
import { KdevIconFont } from '@/business/commonComponents';
import {
    kdev_commonlyused_teamequirements, kdev_commonlyused_teamsubtask2,
    kdev_commonlyused_teamdefect, common_system_brokenlink
} from '@kid/enterprise-icon/icon/output/icons';

const teamTypeIconMap = {
    demand: {
        icon: kdev_commonlyused_teamequirements,
        text: '产品需求',
        iconColor: '#326bfb'
    },
    child_task: {
        icon: kdev_commonlyused_teamsubtask2,
        text: '子任务',
        iconColor: ''
    },
    bug: {
        icon: kdev_commonlyused_teamdefect,
        text: '线上缺陷',
        iconColor: '#FCA10D'
    },
};

interface IRemoveTeamArg extends Object {
    teamInfo: nsWorkbenchBranchDetailTeamListGet.IReturnItem;
}

interface IApiTeamProps extends nsWorkbenchBranchDetailTeamListGet.IReturnItem {
    onRemove?({ teamInfo, ...arg }: IRemoveTeamArg, e: MouseEvent<HTMLSpanElement>): void;
}

function ApiTeam({ onRemove, ...props }: IApiTeamProps) {
    return (
        <div className={css.apiTeam}>
            <KdevIconFont
                id={teamTypeIconMap[props?.taskType]?.icon || teamTypeIconMap['demand']?.icon}
                color={teamTypeIconMap[props?.taskType]?.iconColor || teamTypeIconMap['demand']?.iconColor}
                style={{ fontSize: '16px' }}
            />
            <Tooltip title={props.name || props?.title}>
                <a
                    className={css.teamName}
                    href={props.url} // 此跳转不可使用router跳转，版本diff业务组件无router
                    target="_blank"
                >
                    {props.name || props?.title}
                </a>
            </Tooltip>
            {
                onRemove &&
                <Tooltip title="解除绑定">
                    <span className={css.brokenlinkBtn} onClick={(e) => onRemove({ teamInfo: props }, e)}>
                        <KdevIconFont id={common_system_brokenlink} />
                    </span>
                </Tooltip>
            }
        </div>
    );
}

interface IShowTeamsProps {
    teams: nsWorkbenchBranchDetailTeamListGet.IReturnItem[];
    onRemove?({ teamInfo, ...arg }: IRemoveTeamArg, e: MouseEvent<HTMLSpanElement>): void;
}

function ShowTeams(props: IShowTeamsProps) {
    if (Array.isArray(props.teams) && props.teams.length > 0) {
        return (
            <div className={css.showTeamList}>
                {props.teams.map(item => <ApiTeam {...item} key={item?.taskId} onRemove={props.onRemove} />)}
            </div>
        );
    }
    return null;
}

interface IApiTeamsProps {
    teams: nsWorkbenchBranchDetailTeamListGet.IReturnItem[];
    onRemove?({ teamInfo, ...arg }: IRemoveTeamArg, e: MouseEvent<HTMLSpanElement>): void;
    style: React.CSSProperties;
}

function ApiTeams(props: IApiTeamsProps) {
    const apiTeamsRef = useRef<HTMLDivElement>(null);

    if (Array.isArray(props.teams) && props.teams.length > 0) {
        const moreTeams = props.teams.slice(1);
        return (
            <div className={css.apiTeams} ref={apiTeamsRef} style={{
                ...props.style
            }}>
                <ApiTeam {...props.teams[0]} onRemove={props.onRemove} />
                {
                    props.teams.length > 1 &&
                    <Popover
                        content={ShowTeams({ teams: moreTeams, onRemove: props.onRemove })}
                        getPopupContainer={() => apiTeamsRef.current || document.body}
                        placement="bottom"
                    >
                        <div className={css.teamNum}>+{props.teams.length - 1}</div>
                    </Popover>
                }
            </div>
        );
    }
    return null;
}

export { ApiTeams, ShowTeams };
