.team {
  display: inline-flex;
  align-items: center;
  justify-content: space-between;
  //height: 36px;
  background-color: #F5F7FA;
  padding: 8px 6px;
  border-radius: 4px;
  overflow: hidden;
  .teamTypeName {
    margin: 0 8px;
    min-width: 80px;
  }
  .priorityName {
    margin: 0 8px;
  }
  .teamName {
    display: inline-block;
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap;
    flex: 1;
  }
  .rightBox{
    width: 36px;
    text-align: right;
    margin-left: 12px;
  }
  .rightLevel {
    font-size: 12px;
    color: #FFFFFF;
    width: 24px;
    height: 20px;
    text-align: center;
    line-height: 20px;
    border-radius: 4px;
    display: inline-block;
  }
  .delBtn{
    display: none;
  }
}
.deletable{
  &:hover{
    .rightLevel {
      display: none;
    }
    .delBtn{
      display: inline-block;
    }
  }
}
.teamTitle {
  font-size: 18px;
  font-weight: 500;
  height: 45px;
}
.teamText {
  height: 22px;
}
