import React from 'react';
import {
    IMarkdownEditorCustomProps,
    MarkdownEditorCustom,
    IMarkdownEditorCustomPropsPlugin,
    MentionPlugin,
    History_Participants
} from '@biz/comps';
import css from './ApiCommentEditor.less';
import classNames from 'classnames';
import {
    nsKdevUserListGET,
    nsApiCommentsUserList
} from '@/remote';
import { Bind } from 'lodash-decorators/bind';
import { displayHTML } from '@libs/utils';
import { team } from '@/business/global';
interface ApiCommentEditorProps extends IMarkdownEditorCustomProps {
    wrapperClassName?: string;
}

export class ApiCommentEditor extends React.Component<ApiCommentEditorProps, any> {
    private editor: IPlainObject = {};
    private convertedContent = '';
    public mdDom = React.createRef<HTMLDivElement>();
    private readonly markdownConf = {
        autoHeight: true,
        lineNumbers: false,
        watch: false, // 关闭实时预览
        saveHTMLToTextarea: true,
        placeholder: this.props.placeholder || '',
        atLink: false, // 关闭编辑器对@的解析功能
        toolbar: '',
    };

    private readonly PLUGINS: IMarkdownEditorCustomPropsPlugin[] = [
        {
            plugin: MentionPlugin,
            pluginConf: {
                getMentions: this.getMentions,
            },
        },
    ];

    public state = {
        converting: false,
        previewHTML: '',
    };

    constructor(p, c) {
        super(p, c);
        Object.assign(this.markdownConf, {
            // tslint:disable-next-line:only-arrow-functions
            onload: (editor) => {
                this.editor = editor;
                this.editor.getSaveHTML = this.getSaveHTML;
                this.editor.getSaveMarkdown = this.getSaveMarkdown;
                this.editor.existPreview = this.openEditor;
                this.props?.conf?.onload?.call(editor, editor);
                this.editor.editor.append('<div style="clear: both"></div>');
                this.editor.preview.css({ display: 'none' }); // 不用编辑器自带的预览
            },
            // tslint:disable-next-line:only-arrow-functions
            onwatch: () => {
                this.editor.codeMirror.css({ display: 'none' });
            },
            // tslint:disable-next-line:only-arrow-functions
            onunwatch: () => {
                this.editor.codeMirror.css({ display: 'block' });
            }
        });
    }

    // private async searchParticipants(keywords: string) {
    //     const name = keywords.replace(/\'/g, '');
    //     const params = {
    //         search: name,
    //     };
    //     try {
    //         const { list } = await nsKdevUserListGET.remote(params);
    //         return list;
    //     } catch (e) {
    //         console.error(e);
    //     }
    // }

    private async searchParticipants(keywords: string) {
        const name = keywords.replace(/\'/g, '');
        const storage = localStorage.getItem(History_Participants);
        const arr = storage ? JSON.parse(storage) : [team.getUserInfo().userName];

        const params = {
            key: name,
            localUser: arr,
        };
        try {
            const { list } = await nsApiCommentsUserList.remote(params);
            return list || [];
        } catch (e) {
            console.error(e);
        }
    }

    @Bind
    private getMentions(keywords) {
        return this.searchParticipants(keywords);
    }

    @Bind
    private openEditor() {
        this.editor.unwatch?.();
        this.editor.showToolbar?.();
        // 此处需要清空，以防后续preview出错
        this.mdDom.current && (this.mdDom.current.innerHTML = '');
    }

    @Bind
    private async getSaveHTML() {
        const el = this.mdDom.current;
        if (!el) {
            return '';
        }
        try {
            await displayHTML(
                el,
                {
                    atLink: false, // 关闭编辑器对@的解析功能
                    emoji: false,
                    tex: false,
                },
                this.editor.getValue(),
            );
        } catch (e) {
            console.error(e);
        }
        return el.children[0]?.innerHTML || '';
    }

    @Bind
    private getSaveMarkdown() {
        return this.editor.getMarkdown();
    }

    @Bind
    private clickEditorWrapper() {
        this.editor?.focus?.();
    }

    public render() {
        const { conf, wrapperClassName, ...props } = this.props;
        // conf 不起作用，仅 onload 会被触发
        return (
            <div
                className={classNames(wrapperClassName, css.ApiCommentEditor)}
            >
                <MarkdownEditorCustom
                    plugins={this.PLUGINS}
                    conf={this.markdownConf}
                    {...props}
                />
                <div ref={this.mdDom} style={{ display: 'none' }} />
            </div>
        );
    }
}
