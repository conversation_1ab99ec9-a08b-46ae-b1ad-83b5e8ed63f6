import { observable, runInAction } from 'mobx';
import { Bind } from 'lodash-decorators';
import { nsMockManageKoasApiManageHttpApiQueryCaseOperationTypePost } from '@/remote';

interface ICaseOperateTypeList {
    label: string;
    value: string;
}

// 仅用于editJsonTable文件下组件
export class EditJsonStore {
    @observable protected caseOperateTypeList: ICaseOperateTypeList[] = [];

    constructor() {
        this.init();
    }

    private init(): void {
        // 解决异步问题，临时方案
        const api_mgr_caseOperateTypeList = localStorage.getItem('api_mgr_caseOperateTypeList');
        if (api_mgr_caseOperateTypeList) {
            try {
                this.caseOperateTypeList = JSON.parse(api_mgr_caseOperateTypeList);
            } catch {}
        }
        this.queryCaseOperateType();
    }

    @Bind
    private async queryCaseOperateType(): Promise<void> {
        try {
            const result = await nsMockManageKoasApiManageHttpApiQueryCaseOperationTypePost.remote();
            runInAction(() => {
                this.caseOperateTypeList = result?.operationTypeList || [];
                localStorage.setItem('api_mgr_caseOperateTypeList', JSON.stringify(this.caseOperateTypeList));
            });
        } catch {
        }
    }
}
