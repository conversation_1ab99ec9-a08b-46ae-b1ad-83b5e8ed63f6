.multiDimensionalWrap {
  .table {
    .type {
      width: 94px;
    }

    .caseOperateType{
      width: 100%;
    }

    .deleteBtn {
      margin-right: 8px;
    }

    .addChildRowBtn {
      margin-right: 8px;
    }

    :global {
      .ant-table .ant-table-container .ant-table-tbody .ant-table-row .ant-table-cell:nth-child(1) {
        display: flex;
        align-items: center;
        height: 100%;
      }
    }
  }

  .addRowBtn {
    margin-top: 16px;
    margin-right: 16px;
  }
}
