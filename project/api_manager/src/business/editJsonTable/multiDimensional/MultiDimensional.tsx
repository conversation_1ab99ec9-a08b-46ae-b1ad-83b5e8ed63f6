import { AView } from 'libs';
import React from 'react';
import { MultiDimensionalM } from './MultiDimensionalM';
import { inject, observer } from 'mobx-react';
import { Input, Button, Table, Select, Checkbox, Modal, Tooltip, InputNumber } from 'antd';
import css from './MultiDimensional.less';
import { PlusCircleOutlined } from '@ant-design/icons';
import { Bind } from 'lodash-decorators';
import { KdevIconFont } from '@/business/commonComponents/KdevIconFont';
import { DataStructureModal } from '../dataStructureModal/DataStructureModal';

const { Option } = Select;

interface IProps {
    tableBottom?: React.ReactElement | React.ReactNode;
    valueTitle?: string;
    editJsonStore?: any;
    tableTitleKey?: string[];
    disabled?: {
        [key: string]: boolean;
    };
}

@inject('editJsonStore')
@observer
export class MultiDimensional extends AView<MultiDimensionalM, IProps> {

    @Bind
    public componentDidMount(): void {
        if (this.props.tableTitleKey) {
            this.model.getTableScrollX(this.props.tableTitleKey, this.column);
        }
    }

    private column: any[] = [
        {
            title: '名称',
            key: 'name',
            width: 300,
            fixed: 'left',
            render: this.renderName
        },
        {
            title: '类型',
            key: 'type',
            width: 120,
            render: this.renderType
        },
        {
            title: '是否必填',
            key: 'required',
            width: 100,
            render: this.renderRequired
        },
        {
            title: this.props.valueTitle || '参数值',
            key: 'value',
            width: 120,
            render: this.renderValue
        },
        {
            title: '备注',
            key: 'description',
            width: 200,
            render: this.renderDescription
        },
        {
            title: '参数取值范围',
            key: 'valueScope',
            width: 120,
            render: this.renderValueScope
        },
        {
            title: '参数取值最小值',
            key: 'valueMinLength',
            width: 130,
            render: this.renderValueMinLength
        },
        {
            title: '参数取值最大值',
            key: 'valueMaxLength',
            width: 130,
            render: this.renderValueMaxLength
        },
        {
            title: '用例生成操作类型',
            key: 'operationType',
            width: 150,
            render: this.renderCaseOperteType
        },
        {
            title: '操作',
            key: 'operate',
            width: 150,
            fixed: 'right',
            render: this.renderOperate
        }
    ];

    @Bind
    public columns(): any[] {
        const props = this.props;
        if (props.tableTitleKey) {
            const column = this.column.filter(item => props.tableTitleKey?.includes(item.key));
            return column;
        }
        return this.column;
    }

    @Bind
    protected renderName(record): React.ReactNode {
        let disabledName: boolean = false;
        if (record.key === '0' && this.model.rootName) {
            disabledName = true;
        }
        return (
            <Input
                value={record.name}
                onChange={(e) => {
                    this.model.onChangeInputVal(e, record, 'name');
                }}
                disabled={this.model.disabledName(record) || disabledName || this.props?.disabled?.name}
            />
        );
    }

    @Bind
    protected renderType(record): React.ReactNode {
        return (
            <Select
                value={record.type}
                className={css.type}
                onSelect={(val) => this.model.onSelectVal(val, record, 'type')}
                disabled={this.props?.disabled?.type}
            >
                <Option value={'object'}>object</Option>
                <Option value={'array'}>array</Option>
                <Option value={'number'}>number</Option>
                <Option value={'string'}>string</Option>
                <Option value={'boolean'}>boolean</Option>
            </Select>
        );
    }

    @Bind
    protected renderRequired(record): React.ReactNode {
        return (
            <Checkbox
                checked={record.required}
                onChange={(e) => {
                    this.model.onChangeCheckboxChecked(e, record);
                }}
                disabled={this.props.disabled?.required}
            >是</Checkbox>
        );
    }

    @Bind
    private renderValue(record): React.ReactNode {
        return (
            <Input
                value={record.value}
                onChange={e => {
                    this.model.onChangeInputVal(e, record, 'value');
                }}
                disabled={record.type === 'array' || record.type === 'object' || this.props?.disabled?.value}
            />
        );
    }

    @Bind
    private renderValueScope(record): React.ReactNode {
        return (
            <Input
                value={record.valueScope || undefined}
                disabled={record.type === 'object' || record.type === 'array'}
                placeholder={'如：[1,2,3]'}
                onChange={e => {
                    this.model.onChangeInputVal(e, record, 'valueScope');
                }}
            />
        );
    }

    @Bind
    private renderValueMaxLength(record): React.ReactNode {
        return (
            <InputNumber
                value={record.valueMaxLength}
                disabled={record.type === 'object' || record.type === 'array'}
                onChange={val => {
                    this.model.onChangeInputNumVal(val, record, 'valueMaxLength');
                }}
            />
        );
    }

    @Bind
    private renderValueMinLength(record): React.ReactNode {
        return (
            <InputNumber
                value={record.valueMinLength}
                disabled={record.type === 'object' || record.type === 'array'}
                onChange={val => {
                    this.model.onChangeInputNumVal(val, record, 'valueMinLength');
                }}
            />
        );
    }

    @Bind
    private renderCaseOperteType(record): React.ReactNode {
        const caseOperateTypeList = this.props.editJsonStore?.caseOperateTypeList || [];
        if (!record.operationType && caseOperateTypeList?.length) {
            record.operationType = caseOperateTypeList[0].value;
        }
        return (
            <Select
                placeholder="请选择"
                value={record.operationType}
                options={caseOperateTypeList}
                className={css.caseOperateType}
                disabled={record.type === 'object' || record.type === 'array'}
                // allowClear
                onSelect={(val) => this.model.onSelectVal(val, record, 'operationType')}
            // onClear={() => this.model.onSelectVal('', record, 'operationType')}
            />
        );
    }

    @Bind
    protected renderDescription(record): React.ReactNode {
        return (
            <Input
                value={record.description}
                onChange={(e) => {
                    this.model.onChangeInputVal(e, record, 'description');
                }}
                disabled={this.props?.disabled?.description}
            />
        );
    }

    @Bind
    protected renderOperate(record): React.ReactNode {
        const model = this.model;
        return (
            <>
                <Button
                    icon={<KdevIconFont id={'#iconyanse'} />}
                    className={css.deleteBtn}
                    onClick={() => this.onDeleteRow(record)}
                    disabled={this.props?.disabled?.operate}
                />
                {
                    (record.type === 'object' || record.type === 'array') &&
                    <Tooltip title={'添加子节点'}>
                        <Button
                            icon={<KdevIconFont id={'#icontianjiaziziduan'} />}
                            className={css.addChildRowBtn}
                            onClick={() => model.onAddRow(record)}
                            disabled={this.props?.disabled?.operate}
                        />
                    </Tooltip>
                }
                <Tooltip title={'选择已有数据结构'}>
                    <Button
                        icon={<KdevIconFont id={'#iconadd-circle'} />}
                        onClick={() => model.onOpenReferenceDataModal(record)}
                        disabled={this.props?.disabled?.operate}
                    />
                </Tooltip>
            </>
        );
    }

    @Bind
    protected onDeleteRow(record) {
        if (record.children && record.children.length) {
            Modal.confirm({
                content: record.name + '该参数下有子集删除将不可恢复，确认删除？',
                onOk: () => this.model.onDeleteRow(record)
            });
        } else {
            this.model.onDeleteRow(record);
        }
    }

    @Bind
    protected renderAddRowBtn(): React.ReactNode {
        const model = this.model;
        if (model.list.length < 1) {
            return (
                <Button
                    icon={<PlusCircleOutlined />}
                    type={'dashed'}
                    className={css.addRowBtn}
                    onClick={() => model.onAddRow(null)}
                    disabled={this.props?.disabled?.ruleType}
                >添加一行</Button>
            );
        }
    }

    public render(): React.ReactNode {
        const model = this.model;
        return (
            <div
                className={css.multiDimensionalWrap}
            >
                <Table
                    className={css.table}
                    columns={this.columns()}
                    dataSource={model.list}
                    bordered
                    pagination={false}
                    rowKey={'key'}
                    defaultExpandAllRows
                    scroll={{
                        x: model.tableScrollX
                    }}
                    expandable={{
                        expandedRowKeys: model.expandedRowKeys,
                        onExpand: model.onExpandRowKeys
                    }}
                />
                <div>
                    {this.renderAddRowBtn()}
                    {this.props.tableBottom}
                </div>
                <DataStructureModal model={model.dataStructureModalModel} />
            </div>
        );
    }
}
