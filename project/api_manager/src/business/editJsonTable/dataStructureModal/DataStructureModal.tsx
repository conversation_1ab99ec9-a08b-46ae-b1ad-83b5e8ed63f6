import { AView } from 'libs';
import React from 'react';
import { DataStructureModalModel } from './DataStructureModalModel';
import { observer } from 'mobx-react';
import { Table, Input, Modal } from 'antd';
import css from './DataStructureModal.less';
import Bind from 'lodash-decorators/bind';
import moment from 'moment';
import { bindObserver } from '@libs/mvvm';

const { Search } = Input;

const Search_modelName = bindObserver(Search, 'modelName');

@observer
export class DataStructureModal extends AView<DataStructureModalModel> {

    @Bind
    public dataStructureColumns(model): any[] {
        const columns = [
            {
                title: '名称',
                dataIndex: 'fullClassName',
                key: 'fullClassName',
            },
            {
                title: '描述',
                dataIndex: 'description',
                key: 'description',
            },
            {
                title: '类型',
                dataIndex: '',
                key: '',
                render: record => 'json'
            },
            {
                title: '创建者',
                dataIndex: 'createUser',
                key: 'createUser',
            },
            {
                title: '最后更新时间',
                dataIndex: 'createTime',
                key: 'createTime',
                render: text => moment(text).format('YYYY-MM-DD HH:mm:ss')
            }
        ];
        return columns;
    }

    public render(): React.ReactNode {
        const model = this.model;
        return (
            <Modal
                title={ '数据结构' }
                className={ css.dataStructureModalWrap }
                visible={ model.visible }
                width={ 1000 }
                onCancel={ model.onCloseDataStructureModal }
                onOk={ model.queryById }
            >
                <div className={ css.dataStructureModalTop }>
                    <Search_modelName
                        className={ css.modelName }
                        model={ model }
                        placeholder={ '支持数据结构名搜索' }
                        onSearch={ () => model.onChangePageInfo(1) }
                    />
                </div>
                <Table
                    className={ css.dataStructureListTable }
                    columns={ this.dataStructureColumns(model) }
                    bordered
                    dataSource={ model.modelList }
                    rowKey={ 'id' }
                    pagination={ {
                        showTotal: total => `共 ${ total } 条`,
                        current: model.pageIndex,
                        // pageSize: model.pageSize,
                        showSizeChanger: false,
                        total: model.total,
                        onChange: model.onChangePageInfo,
                    } }
                    rowSelection={{
                        selectedRowKeys: model.selectedRowKeys,
                        type: 'radio',
                        onChange: model.onSelectRow
                    }}
                />
            </Modal>
        );
    }
}
