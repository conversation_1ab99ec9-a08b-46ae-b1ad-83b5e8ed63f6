import { AViewModel } from 'libs';
import { observable, action, runInAction } from 'mobx';
import {
    nsMockManageKoasApiManageModelQueryByIdGet,
    nsMockManageKoasApiManageModelQueryByModuleIdGet
} from '@/remote';
import { message } from 'antd';

export class DataStructureModalModel extends AViewModel {
    @observable protected moduleId: number = 0;
    @observable public visible: boolean = false;
    @observable public modelList: any[] = [];
    @observable public pageIndex: number = 1;
    @observable public modelName: string = '';
    @observable public total: number = 0;
    @observable public selectedRowKeys: number[] = [];

    public onSaveSelectDataStructureCallback?(list): void;

    @action
    public initLoading(moduleId: number) {
        this.moduleId = moduleId;
        this.visible = true;
        this.queryByModuleId();
    }

    @action.bound
    protected initData() {
        this.moduleId = 0;
        this.modelList = [];
        this.modelName = '';
        this.pageIndex = 1;
        this.total = 0;
        this.selectedRowKeys = [];
    }

    // 取消新建编辑数据结构回调
    // @action.bound
    // public onSaveSelectDataStructureCallback(type: string) {
    //     if (type === 'save') {
    //         this.queryByModuleId();
    //     }
    // }

    // 关闭弹框
    @action.bound
    public onCloseDataStructureModal() {
        this.initData();
        this.visible = false;
    }

    // 选中节点
    @action.bound
    public onSelectRow(selectedRowKeys) {
        this.selectedRowKeys = selectedRowKeys;
    }

    @action.bound
    public onChangePageInfo(pageIndex) {
        this.pageIndex = pageIndex;
        this.queryByModuleId();
    }

    // 获取数据结构列表
    @action.bound
    protected async queryByModuleId() {
        try {
            const params = {
                moduleId: this.moduleId,
                modelName: this.modelName,
                pageIndex: this.pageIndex
            };
            const result = await nsMockManageKoasApiManageModelQueryByModuleIdGet.remote(params);
            runInAction(() => {
                this.modelList = result.modelList;
                this.total = result.total;
            });
        } catch (e) {
        }
    }

    // 获取数据结构详情
    @action.bound
    public async queryById() {
        if (!this.selectedRowKeys.length) {
            message.warn('请选择要引用的数据结构');
            return;
        }
        try {
            const params = {
                id: this.selectedRowKeys[0]
            };
            const result = await nsMockManageKoasApiManageModelQueryByIdGet.remote(params);
            runInAction(() => {
                if (result.children && result.children.length && this.onSaveSelectDataStructureCallback) {
                    this.onSaveSelectDataStructureCallback(result.children);
                    this.onCloseDataStructureModal();
                }
            });
        } catch (e) {
        }
    }
}
