import { action, observable } from 'mobx';
import ParticipantListModel from '@/business/participant/participantList/ParticipantListModel';
function getSelectionCoords(win) {
    win = win || window;
    const doc = win.document;
    let sel = doc.selection;
    let range;
    let rects;
    let rect;
    let x = 0;
    let y = 0;
    if (sel) {
        if (sel.type !== 'Control') {
            const _range = sel.createRange();
            _range.collapse(true);
            x = _range.boundingLeft;
            y = _range.boundingTop;
        }
    } else if (win.getSelection) {
        sel = win.getSelection();
        if (sel.rangeCount) {
            range = sel.getRangeAt(0).cloneRange();
            if (range.getClientRects) {
                range.collapse(true);
                rects = range.getClientRects();
                if (rects.length > 0) {
                    rect = rects[0];
                }
                // 光标在行首时，rect为undefined
                if (rect) {
                    x = rect.left;
                    y = rect.top;
                }
            }
            // Fall back to inserting a temporary element
            if ((x === 0 && y === 0) || rect === undefined) {
                const span = doc.createElement('span');
                if (span.getClientRects) {
                    // Ensure span has dimensions and position by
                    // adding a zero-width space character
                    span.appendChild(doc.createTextNode('\u200b'));
                    range.insertNode(span);
                    rect = span.getClientRects()[0];
                    x = rect.left;
                    y = rect.top;
                    const spanParent = span.parentNode;
                    spanParent.removeChild(span);

                    // Glue any broken text nodes back together
                    spanParent.normalize();
                }
            }
        }
    }
    return { x, y };
}
function getInnerText(str) {
    return str.replace(/<[^<>]+>/g, '').replace(/&nbsp;/g, ' ');
}
const AT_TAG_STRING_START = '<span style="color: #1890ff">@';
const AT_TAG_STRING_END = '</span>';
export class Participant {
    private selection: any = document.getSelection();
    private currentInputAnchor: number = 0;
    private currentInputFocus: number = 0;
    private textArea: any = null;
    private onParticipantsSelected: any = null;
    public participantListModel = new ParticipantListModel();
    public nameMap: { [name: string]: string } = {};
    @observable public commentValue: string = '';
    @observable public popPosition = {
        top: 0,
        left: 0
    };
    public initParticipant() {
        const self = this;
        this.selection = document.getSelection();
        this.participantListModel.setFields({
            onSelected(username) {
                try {
                    const selection = self.selection;
                    selection.removeAllRanges();
                    selection.setBaseAndExtent(
                        self.textArea,
                        self.currentInputAnchor - 1,
                        self.textArea,
                        self.currentInputFocus
                    );
                    const endIndex = self.commentValue.slice(-7) === '<p></p>' ? 7 : 4;
                    const text = self.commentValue.slice(0, -endIndex);
                    let test = selection.focusNode.wholeText.slice(self.currentInputAnchor - 1);
                    test = test.replace(/\s/, '&nbsp;') + '$';

                    self.commentValue = '<p>' + text.replace(new RegExp(test), `${AT_TAG_STRING_START}${username}${AT_TAG_STRING_END}&nbsp;`) + '</p>';
                    self.currentInputAnchor = 0;
                    self.onParticipantsSelected = () => {
                        selection.collapseToEnd();
                    };
                } catch (e) {
                    console.error(e);
                }
            }
        });
    }
    public resetRange() {
        const range = this.selection.getRangeAt(0);
        while (range) {
            range.selectNode(this.selection.focusNode);
            range.deleteContents();
            if (this.selection.focusNode.tagName === 'DIV') {
                this.selection.focusNode.innerHTML = '<p><br /></p>';
                break;
            }
        }
    }
    public setCommentParticipants() {
        this.onParticipantsSelected && (this.onParticipantsSelected(), this.onParticipantsSelected = null);
        this.textArea = this.selection.focusNode;
        if (this.commentValue.length <= 7) {
            this.participantListModel.onSearchValueChange();
            return;
        }
        const nodeText: string = this.textArea.wholeText;
        if (!nodeText) {
            return;
        }
        const index = this.selection.focusOffset;
        if (this.currentInputAnchor > 0 && nodeText[index - 1] !== '@') {
            if (/\s/.test(nodeText[index - 1]) || !nodeText.includes('@')) {
                this.currentInputAnchor = 0;
                this.currentInputFocus = 0;
                this.participantListModel.onSearchValueChange();
                return;
            }
            this.currentInputFocus = index;
            this.participantListModel.onSearchValueChange(nodeText.slice(this.currentInputAnchor));
            const position = getSelectionCoords(window);
            this.popPosition.left = position.x;
            this.popPosition.top = position.y;
        } else {
            if (nodeText[index - 1] === '@' && !nodeText[index]) {
                // first @
                this.currentInputAnchor = index;
                this.currentInputFocus = index;
                this.participantListModel.onSearchValueChange('');
                // todo: 在中间输入
                const position = getSelectionCoords(window);
                this.popPosition.left = position.x;
                this.popPosition.top = position.y;
            }
            if (/\s/.test(nodeText[index - 1])) {
                this.currentInputAnchor = 0;
                this.currentInputFocus = 0;
                this.participantListModel.onSearchValueChange();
            }
        }
    }
    @action.bound
    public onRichTextChange(val) {
        const oldValStr = getInnerText(this.commentValue);
        const newValStr = getInnerText(val);
        this.commentValue = newValStr.length < oldValStr.length ? this.onTextDelete(val) : val;
        this.setCommentParticipants();
    }
    public onTextDelete(originStr: string): string {
        const value = this.selection.focusNode.nodeValue;
        if (
            value && value[0] === '@' &&
            this.currentInputAnchor === 0 &&
            this.selection.focusNode.parentNode.nodeName === 'SPAN'
        ) {
            const range = this.selection.getRangeAt(0);
            range.selectNode(this.selection.focusNode);
            range.deleteContents();
            const range1 = this.selection.getRangeAt(0);
            range1.selectNode(this.selection.focusNode);
            range1.deleteContents();
            if (this.selection.focusNode.tagName === 'P') {
                if (this.selection.focusNode.innerHTML.length === 0) {
                    return '<p><br /></p>';
                } else {
                    return `<p>${this.selection.focusNode.innerHTML}</p>`;
                }
            } else if (this.selection.focusNode.tagName === 'DIV') {
                if (this.selection.focusNode.innerHTML.length === 0) {
                    return '<p><br /></p>';
                }
            }
            return originStr;
        }
        return originStr;
    }
    @action.bound
    public handleCommentChange(val) {
        this.commentValue = val;
    }

    public getCommentBody(): string {
        const self = this;
        const str =
            this.commentValue.replace(
                /<span style="color: #1890ff">@(\p{Unified_Ideograph})+<\/span>/ug,
                (s) => {
                    const chineseName = s.slice(
                        AT_TAG_STRING_START.length,
                        -AT_TAG_STRING_END.length);
                    const name =
                        self.participantListModel.chineseNameMap[chineseName] ||
                        self.nameMap[chineseName] ||
                        '';
                    if (name) {
                        return `#{${name},${chineseName}}#`;
                    } else {
                        return s;
                    }
                });
        return str;
    }
}
