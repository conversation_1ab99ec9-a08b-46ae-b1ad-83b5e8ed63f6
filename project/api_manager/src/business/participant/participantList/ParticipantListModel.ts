import { AViewModel } from '@libs/mvvm';
import { observable, action, runInAction, computed } from 'mobx';
import { nsMockPermissionGetUserListGet } from '@/remote';

interface IUserInfo {
    userName: string;
    userId: number;
    photo: string;
    chineseName: string;
    email: string;
}

export default class ParticipantListModel extends AViewModel {
    @observable public data: nsMockPermissionGetUserListGet.IReturn_getUserList[] = [];
    @observable public selected: number = -1;
    @observable public searchValue: string = '';
    @observable public visible: boolean = false;
    @observable public loading: boolean = false;
    public userSelected = 0;
    public chineseNameMap = {};
    public userNameMap = {};
    public userInfo: IUserInfo = {
        userId: -1,
        userName: '',
        chineseName: '',
        email: '<EMAIL>',
        photo: 'http://localhost:3000/uploads/user/avatar/1/cd8.jpeg'
    };
    public onSelected?: (username: string) => void;

    @computed
    public get usernameList() {
        return this.data.map((user) => user.username);
    }

    @action
    public getNameMap() {
        this.data.forEach((user) => {
            this.chineseNameMap[user.name] = user.username;
            this.userNameMap[user.username] = user.name;
        });
    }

    @action
    public async getParticipantList(username = '') {
        try {
            const params = {
                username
            };
            const { list } = await nsMockPermissionGetUserListGet.remote(params);
            runInAction(() => {
                this.handleDataChange(list);
                this.getNameMap();
            });
        } catch (e) {
        }
    }

    @action
    public onSearchValueChange(value?: string) {
        if (!value || value.length <= 3) {
            this.handleDataChange([]);
            return;
        }
        this.handleSearchValueChange(value);
        this.selected = 0;
        this.userSelected = 0;
        this.getParticipantList(value);
    }

    @action
    public handleDataChange(data) {
        this.data = data;
    }

    @action.bound
    public handleSelectChange(value) {
        this.onSelectedChange(value);
        this.onSelected && this.onSelected(this.userNameMap[this.usernameList[this.selected]] || '');
        this.handleSearchValueChange('');
        this.handleDataChange([]);
    }

    @action.bound
    public onSelectedChange(value) {
        runInAction(() => {
            this.selected = value;
        });
    }

    @action
    public handleSearchValueChange(val) {
        runInAction(() => {
            this.searchValue = val;
        });
    }
}
