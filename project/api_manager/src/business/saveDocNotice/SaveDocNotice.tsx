import React from 'react';
import { AView, AViewModel, bindObserver } from 'libs/mvvm';
import { action, observable, runInAction } from 'mobx';
import { observer } from 'mobx-react';
import { Bind } from 'lodash-decorators';
import { Modal, Input, Checkbox, message } from 'antd';
import css from './SaveDocNotice.less';
import { KDevParticipants } from '@/business/commonComponents/KDevParticipants';
import { nsMockManageKoasApiManageHttpApiUpdatePost } from '@/remote';

const { TextArea } = Input;

const TextArea_remarkWhenUpdate = bindObserver(TextArea, 'remarkWhenUpdate');

interface IParticipants {
    username: string;
    name: string;
    avatarUrl: string;
}

interface IProps {
}

export class SaveDocNoticeM extends AViewModel {
    @observable public visible: boolean = false;
    @observable public remarkWhenUpdate: string = '';
    @observable public sendKimWhenUpdate: boolean = false;
    @observable public participants: IParticipants[] = [];
    @observable public saveLoading: boolean = false;

    public onSaveDocCallbck?(): void;
    public getParams?(): void;

    @action.bound
    public onOpenSaveDocNotice(participants: IParticipants[]) {
        this.visible = true;
        this.onChangeParticipants(participants);
    }

    @action.bound
    public onCloseSaveDocNotice() {
        this.visible = false;
        this.initData();
    }

    // @action.bound
    // public onSaveDoc(): void {
    //     this.onSaveDocCallbck
    //         && this.onSaveDocCallbck();
    //     this.initData();
    // }

    @action
    private initData(): void {
        this.visible = false;
        this.remarkWhenUpdate = '';
        this.sendKimWhenUpdate = false;
        this.participants = [];
    }

    @action.bound
    public onChangeParticipants(participants: IParticipants[]): void {
        this.participants = participants;
    }

    @action.bound
    public onChangeSendKimWhenUpdate(e): void {
        this.sendKimWhenUpdate = e.target.checked;
    }

    // 编辑API
    @Bind
    public async update() {
        runInAction(() => this.saveLoading = true);
        try {
            const cooperList: string[] = this.participants.map(item => item.username);
            const updateParams: object = {
                cooperList,
                remarkWhenUpdate: this.remarkWhenUpdate,
                sendKimWhenUpdate: this.sendKimWhenUpdate
            };
            const params = this.getParams && this.getParams() as any;
            params.baseInfo = {
                ...params.baseInfo,
                ...updateParams
            };
            await nsMockManageKoasApiManageHttpApiUpdatePost.remote(params);
            runInAction(() => {
                this.saveLoading = false;
                this.onSaveDocCallbck
                    && this.onSaveDocCallbck();
                this.initData();
            });
        } catch (e) {
            runInAction(() => this.saveLoading = false);
        }
    }
}

@observer
export class SaveDocNotice extends AView<SaveDocNoticeM, IProps> {
    public render(): React.ReactNode {
        const model = this.model;
        return (
            <Modal
                title={'保存更新'}
                visible={model.visible}
                onCancel={model.onCloseSaveDocNotice}
                className={css.saveDocNoticeModal}
                wrapClassName={css.saveDocNoticeModalWrap}
                onOk={model.update}
                okButtonProps={{
                    loading: model.saveLoading
                }}
                maskClosable={false}
            >
                <div className={css.rowLabel}>更新描述（选填）</div>
                <TextArea_remarkWhenUpdate model={model} />
                <div className={css.rowLabel}>
                    <Checkbox
                        checked={model.sendKimWhenUpdate}
                        onChange={model.onChangeSendKimWhenUpdate}
                    >
                        通过Kim向研发参与人发送消息
                    </Checkbox>
                </div>
                <div className={css.participantsRow}>
                    <span className={css.participantsRowLabel}>研发参与人：</span>
                    <KDevParticipants
                        value={model.participants}
                        onChange={model.onChangeParticipants}
                    />
                </div>
            </Modal>
        );
    }
}
