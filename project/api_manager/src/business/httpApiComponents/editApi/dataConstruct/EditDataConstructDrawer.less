.editDataConstructDrawer {
    :global {
        .ant-drawer-header {
            padding: 16px;
            border-bottom: none;
            font-weight: 600;
        }

        .ant-drawer-close {
            padding: 16px;
            position: absolute;
            right: 0;
        }

        .ant-drawer-body {
            padding: 0 16px;
        }

        .ant-drawer-footer {
            padding: 16px;
        }
    }

    .row {
        margin-top: 16px;
        display: flex;
        flex-direction: column;

        &:nth-of-type(1) {
            margin-top: 0;
        }

        >label {
            padding: 8px 0;
            color: #575859;

            &[aria-required=true]::after {
                content: ' *';
                color: #FA4E3E;
            }
        }
    }

    .footer {
        display: flex;
        gap: 8px;
    }
}