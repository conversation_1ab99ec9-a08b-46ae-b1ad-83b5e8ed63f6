import React, { useEffect, useState } from 'react';
import { <PERSON><PERSON>, Toolt<PERSON> } from 'antd';
import { KdevIconFont } from '@/business/commonComponents';
import { common_system_revise, common_system_delete02 } from '@kid/enterprise-icon/icon/output/icons';
import css from './DataConstructTableTitle.less';
import { IRefInfo, EApiParam, ERefInfo } from '@/business/httpApiComponents/editApi/apiParamsInfo/formatParamsTools';
import { KdevTag } from '@/business/common/KdevTag';
import PopoverEllipsis from '@/business/common/PopoverEllipsis';
import { IRecord } from '@/business/httpApiComponents/editApi/apiParamsInfo/EditParamTable';

interface IDataConstructTableTitleArgProps {
    refId?: string;
    data?: IRecord;
    onRemove?: (data?: IRecord) => void;
    onEdit?: (data?: IRecord) => void;
}

function DataConstructTableTitle({ data, ...props }: IDataConstructTableTitleArgProps) {
    const [refInfo, setRefInfo] = useState<IRefInfo | object>(data ? (data[EApiParam.REF_INFO] || {}) : {});

    useEffect(() => {
        const refDetail = data ? (data[EApiParam.REF_INFO] || {}) : {};
        setRefInfo(refDetail);
    }, [data]);

    if (!props.refId) {
        return null;
    }

    return (
        <div className={css.dataConstructTableTitle}>
            <label>类名</label>
            <Tooltip title={refInfo[ERefInfo.NAME]}>
                <span className={css.displayName}>{refInfo[ERefInfo.DISPLAY_NAME]}</span>
            </Tooltip>
            <label>仓库</label>
            <PopoverEllipsis title={refInfo[ERefInfo.PROJECT_NAME]}>
                <span className={css.projectName}>{refInfo[ERefInfo.PROJECT_NAME]}</span>
            </PopoverEllipsis>
            <label>版本</label>
            <span>
                {
                    refInfo[ERefInfo.VERSION] &&
                    <KdevTag
                        content={refInfo[ERefInfo.VERSION] === '1' ? '线上版' : '开发版'}
                        type={refInfo[ERefInfo.VERSION] === '1' ? 'success' : 'primary'}
                    />
                }
            </span>
            {
                props.onEdit &&
                <Tooltip title="编辑数据结构">
                    <Button
                        icon={<KdevIconFont id={common_system_revise} />}
                        type="link"
                        style={{ marginLeft: 16 }}
                        onClick={() => {
                            props.onEdit && props.onEdit(data);
                        }}
                    />
                </Tooltip>
            }
            {
                props.onRemove &&
                <Tooltip title="移除数据结构">
                    <Button
                        icon={<KdevIconFont id={common_system_delete02} />}
                        type="link"
                        onClick={() => {
                            props.onRemove && props.onRemove(data);
                        }}
                    />
                </Tooltip>
            }
        </div>
    );
}

export { DataConstructTableTitle };
