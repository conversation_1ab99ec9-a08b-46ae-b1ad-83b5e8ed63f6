import React, { useState, forwardRef, Ref, useImperativeHandle, useRef, useCallback } from 'react';
import { Button, Drawer, Input, Spin, message } from 'antd';
import css from './EditDataConstructDrawer.less';
import {
    EditParamTable, IEditParamTableRef, IRecord
} from '@/business/httpApiComponents/editApi/apiParamsInfo/EditParamTable';
import { ProjectSelect } from '@/business/httpApiComponents/projectSelect/ProjectSelect';
import {
    nsMockManageApiManageMainStructureV1CreatePOST, nsMockManageApiManageMainStructureV1EditPOST,
    nsMockManageApiManageMainStructureInfoGET, nsHttpApiInterface
} from '@/remote';
import { formatEditParam, formatDataConstructParam } from '@/business/httpApiComponents/editApi/apiParamsInfo/formatParamsTools';
import { KdevIconFont } from '@/business/commonComponents';
import { common_system_closesmall } from '@kid/enterprise-icon/icon/output/icons';

interface IEditDataConstructDrawerProps { }

interface IEditDataConstructDrawerQuery {
    projectId?: number;
    id?: string;
    onSaveCallback?(body: IRecord[], data?: any): void;
}

interface IEditDataConstructDrawerRef {
    showDrawer(query: IEditDataConstructDrawerQuery, data?: any): void;
}

const EditDataConstructDrawer = forwardRef((
    props: IEditDataConstructDrawerProps,
    ref: Ref<IEditDataConstructDrawerRef>
) => {
    const [visible, setVisible] = useState<boolean>(false);
    const [id, setId] = useState<string>('');
    const [name, setName] = useState<string>('');
    // const [fullyQualifiedName, setFullyQualifiedName] = useState<string>('');
    const [description, setDescription] = useState<string>('');
    const [projectId, setProjectId] = useState<number>();
    const [body, setBody] = useState<IRecord[]>([]);
    const editParamTableRef = useRef<IEditParamTableRef>(null);
    const [loading, setLoading] = useState<boolean>(false);
    const [cacheQuery, setCacheQuery] = useState<any>(null);
    const [cacheData, setCacheData] = useState<any>();

    const showDrawer = (query: IEditDataConstructDrawerQuery, data?: any) => {
        setId(query.id || '');
        setProjectId(query.projectId);
        setCacheData(data);
        setVisible(true);
        setCacheQuery(query);
        if (query.id) {
            getDataConstructInfo(query.id);
        }
    };

    const clearData = () => {
        setId('');
        setName('');
        setDescription('');
        setProjectId(undefined);
        setBody([]);
        setVisible(false);
    };

    const createDataConstruct = async (editBody: nsHttpApiInterface.IApiParam[]) => {
        setLoading(true);
        try {
            const params = {
                name,
                description,
                projectId: projectId || -1,
                body: editBody
            };
            const res = await nsMockManageApiManageMainStructureV1CreatePOST.remote(params);
            setLoading(false);
            message.success({
                content: '新建成功',
                style: { marginTop: -100 }
            });
            return res;
        } catch {
            setLoading(false);
        }
    };

    const editDataConstruct = async (editBody: nsHttpApiInterface.IApiParam[]) => {
        setLoading(true);
        try {
            const params = {
                id,
                name,
                description,
                projectId: projectId || -1,
                body: editBody
            };
            const res = await nsMockManageApiManageMainStructureV1EditPOST.remote(params);
            setLoading(false);
            message.success({
                content: '保存成功',
                style: { marginTop: -100 }
            });
            return id;
        } catch {
            setLoading(false);
            return id;
        }
    };

    // 获取详情
    const getDataConstructInfo = async (newId: string) => {
        setLoading(true);
        try {
            const res = await nsMockManageApiManageMainStructureInfoGET.remote({ id: newId });
            setId(res.id);
            setName(res.name);
            // setFullyQualifiedName(res.fullyQualifiedName);
            setDescription(res.description);
            setProjectId(res.projectId);
            const { params } = formatEditParam(res.body);
            setBody(params);
            setLoading(false);
            return res;
        } catch {
            setLoading(false);
            return null;
        }
    };

    const onSave = async () => {
        if (!name) {
            message.warn('请填写类名');
            return;
        }
        // if (!description) {
        //     message.warn('请填写描述');
        //     return;
        // }
        if (!projectId || projectId < 1) {
            message.warn('请选择仓库');
            return;
        }
        const editBody = editParamTableRef.current?.getParams() || [];
        if (editBody.length < 1) {
            message.warn('请填写数据结构');
            return;
        }
        const newId = id ? await editDataConstruct(editBody) : await createDataConstruct(editBody);
        // 保存后处理逻辑
        if (newId) {
            const res = await getDataConstructInfo(newId);
            if (res) {
                const { params } = formatEditParam(res.body);
                const newBody = formatDataConstructParam(params || [], {
                    Id: res.id,
                    V: res.version,
                    T: new Date().getTime(),
                    N: res.name,
                    Dn: res.displayName,
                    Pn: res.projectName
                });
                cacheQuery.onSaveCallback && cacheQuery.onSaveCallback(newBody, cacheData);
                clearData();
            }
        }
    };

    useImperativeHandle(ref, () => ({
        showDrawer
    }));

    return (
        <Drawer
            title={<b>新建数据结构</b>}
            width={1100}
            className={css.editDataConstructDrawer}
            open={visible}
            onClose={() => clearData()}
            footer={
                <div className={css.footer}>
                    <Button type="primary" onClick={onSave}>确定</Button>
                    <Button onClick={() => clearData()}>取消</Button>
                </div>
            }
            closeIcon={<KdevIconFont id={common_system_closesmall} style={{ fontSize: 21 }} />}
            destroyOnClose
        >
            <Spin spinning={loading}>
                <div className={css.row}>
                    <label aria-required>类名</label>
                    <Input
                        placeholder="请输入"
                        value={name}
                        onChange={(e) => setName(e.target.value)}
                    />
                </div>
                <div className={css.row}>
                    <label>描述</label>
                    <Input
                        placeholder="请输入"
                        value={description}
                        onChange={(e) => setDescription(e.target.value)}
                    />
                </div>
                <div className={css.row}>
                    <label aria-required>所属仓库</label>
                    <ProjectSelect
                        value={projectId}
                        onChange={(val: number) => setProjectId(val)}
                    />
                </div>
                <div className={css.row}>
                    <label aria-required>数据结构</label>
                    <EditParamTable
                        type="body"
                        params={body}
                        ref={editParamTableRef}
                        isEditDataConstruct={false}
                    />
                </div>
            </Spin>
        </Drawer>
    );
});

export { EditDataConstructDrawer, IEditDataConstructDrawerRef };
