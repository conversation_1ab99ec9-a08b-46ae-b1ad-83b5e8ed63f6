import React, { MouseEvent, useRef, useState, Ref, useEffect } from 'react';
import { Popover, Button, Input, Table, Tooltip } from 'antd';
import css from './DataConstructPopover.less';
import { nsMockManageApiManageMainStructureSearchGET, nsMockManageApiManageMainStructureInfoGET } from '@/remote';
import PopoverEllipsis from '@/business/common/PopoverEllipsis';
import { IRecord } from '@/business/httpApiComponents/editApi/apiParamsInfo/EditParamTable';
import { formatEditParam, formatDataConstructParam } from '@/business/httpApiComponents/editApi/apiParamsInfo/formatParamsTools';
import { SearchInput } from '@/business/commonComponents/antdCom';
import { useDebounceFn } from 'ahooks';
import { KdevTag } from '@/business/common/KdevTag';

interface IDataConstruct extends nsMockManageApiManageMainStructureSearchGET.IReturn { }

interface IDataConstructPopoverProps {
    projectId?: number;
    groupId: number; // 所在分组
    onCreateDataConstruct?(): void;
    onSelectedDataConstruct?(body: IRecord[]): void;
    data?: IRecord;
}

function DataConstructPopover(props: IDataConstructPopoverProps) {
    const [visible, setVisible] = useState<boolean>(false);
    const [dataSource, setDataSource] = useState<IDataConstruct[]>([]);
    const [search, setSearch] = useState<string>('');
    const [loading, setLoading] = useState<boolean>(false);
    const searchClassNameInputRef = useRef<Input>(null);

    const structureSearch = async () => {
        setLoading(true);
        try {
            const res = await nsMockManageApiManageMainStructureSearchGET.remote({
                search,
                projectId: props.projectId,
                groupId: props.groupId
            });
            setDataSource(res);
            setLoading(false);
        } catch {
            setLoading(false);
        }
    };

    const getDataConstructInfo = async (id: string): Promise<IRecord[]> => {
        setLoading(true);
        try {
            const res = await nsMockManageApiManageMainStructureInfoGET.remote({ id });
            setLoading(false);
            return formatDataConstructParam(formatEditParam(res.body).params || [], {
                Id: res.id,
                V: res.version,
                T: new Date().getTime(),
                N: res.name,
                Dn: res.displayName,
                Pn: res.projectName
            });
        } catch {
            setLoading(false);
            return [];
        }
    };

    const onSearchDataConstructDebounce = useDebounceFn(() => {
        structureSearch();
    }, { wait: 300 });

    // 搜索框自动聚焦
    const searchClassNameInputDebounceFocus = useDebounceFn(() => {
        searchClassNameInputRef.current && searchClassNameInputRef.current.focus();
    }, { wait: 300 });

    // Popover显示/隐藏
    const onVisibleChange = (bool: boolean) => {
        setVisible(bool);
        if (bool) {
            structureSearch();
            searchClassNameInputDebounceFocus.run();
        } else {
            setSearch('');
        }
    };

    // 创建数据结构
    const onCreateDataConstruct = (e: MouseEvent<HTMLElement>) => {
        setVisible(false);
        props.onCreateDataConstruct && props.onCreateDataConstruct();
    };

    // 选中数据结构
    const onClickRow = async (record: IDataConstruct) => {
        const body = await getDataConstructInfo(record.id);
        setVisible(false);
        props.onSelectedDataConstruct && props.onSelectedDataConstruct(body);
    };
    const renderText = (text: string) => {
        return (
            <PopoverEllipsis title={text}>
                <div className={css.textStyle}>{text}</div>
            </PopoverEllipsis>
        );
    };

    const renderName = (record: IDataConstruct) => {
        const name = record.displayName;
        return (
            <Tooltip title={record.name} placement="topLeft">
                <div className={css.textStyle} dangerouslySetInnerHTML={{
                    __html: name.replace(
                        search,
                        '<span style="color: #326BFB">' + search + '</span>'
                    )
                }} />
            </Tooltip>
        );
    };

    const columns = [
        {
            title: '类名',
            key: 'displayName',
            width: 182,
            ellipsis: true,
            // dataIndex: 'displayName',
            render: renderName
        },
        {
            title: '仓库',
            key: 'projectName',
            width: 160,
            ellipsis: true,
            dataIndex: 'projectName',
            render: renderText
        },
        {
            title: '描述',
            key: 'description',
            width: 146,
            ellipsis: true,
            dataIndex: 'description',
            render: renderText
        },
        {
            title: '版本',
            key: 'version',
            width: 80,
            dataIndex: 'versionName',
            render: (text: string, record: IDataConstruct) => {
                return <KdevTag content={text} type={record.version === '1' ? 'success' : 'primary'} />;
            }
        }
    ];

    const renderPopoverContent = () => {
        return (
            <div className={css.popoverContentWrap}>
                <div className={css.popoverContentTitle}>
                    <SearchInput
                        value={search}
                        onChange={(e) => {
                            setSearch(e.target.value);
                            onSearchDataConstructDebounce.run();
                        }}
                        // onPressEnter={structureSearch}
                        autoFocus
                        placeholder="请输入类名"
                        inputRef={searchClassNameInputRef}
                    />
                    {
                        props.onCreateDataConstruct &&
                        <Button onClick={onCreateDataConstruct}>新建数据结构</Button>
                    }
                </div>
                <Table
                    rowKey="id"
                    dataSource={dataSource}
                    columns={columns}
                    pagination={false}
                    loading={loading}
                    scroll={{ y: 400 }}
                    locale={{
                        emptyText: <div className={css.emptyText}>输入关键字查找数据结构</div>
                    }}
                    tableLayout="fixed"
                    onRow={(record) => ({
                        onClick: () => onClickRow(record)
                    })}
                    className={css.dataConstructPopoverTable}
                />
            </div>
        );
    };

    return (
        <Popover
            placement="bottom"
            trigger="click"
            content={renderPopoverContent()}
            onVisibleChange={onVisibleChange}
            visible={visible}
        // getPopupContainer={() => dataConstructBtn?.current || document.body}
        >
            <Button type="link" size="small">引用数据结构</Button>
        </Popover>
    );
}

export { DataConstructPopover };
