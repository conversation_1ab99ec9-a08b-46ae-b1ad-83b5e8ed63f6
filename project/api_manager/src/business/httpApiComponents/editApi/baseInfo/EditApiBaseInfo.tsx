import React, { useEffect, useState, forwardRef, useImperativeHandle, Ref } from 'react';
import { KdevTitle } from '@/business/commonComponents';
import classNames from 'classnames';
import css from './EditApiBaseInfo.less';
import { Input, message, Col, Row, Select, TreeSelect } from 'antd';
import { ApiMethodSelect } from './ApiMethodSelect';
import { RepoGroupSelect } from '@/business/httpApiComponents/RepoGroupSelect';
import {
    nsHttpApiInterface,
    nsMockManageApiManageMainVersionChangeStatePOST,
    nsMockManageApiManageMainRepoTemplateNewInfo,
    nsMockManageApiManageMainTagSavePOST,
    nsMockManageApiManageMainGroupMatchProject,
    nsMockManageApiManageMainApiProgramCatalogRootList,
    nsMockManageApiManageMainApiProgramCatalogSubList
} from '@/remote';
import { IUserInfo, formatLoginUser } from '@/business/owner/Owner';
import ReactQuill from 'react-quill';
import 'react-quill/dist/quill.snow.css';
import { ESearchScene, TeamRelateButton, ApiTeams } from '@/business/teamComp';
import { KDevParticipants } from '@/business/common/KDevParticipants';
import { RecentRepoSelectWithGroup } from '@/business/recentRepoSelector/RecentRepoSelectWithGroup';
import { SelectTag } from '@/pages/groupSetting/tagMgr/selectTag/SelectTag';
import { KDEV_API_GROUP_PROJECT } from '@/pages/ApiManager/apiManager.config';
import { getUrlSearch, pushKey } from '@/index.config/tools';

interface IProps {
    apiId?: number;
    className?: string;
    isTemplate?: boolean;
    apiBaseInfo?: nsHttpApiInterface.IApiBaseInfo;
    components?: Array<nsMockManageApiManageMainRepoTemplateNewInfo.Component>;
    setComponents: (components: Array<nsMockManageApiManageMainRepoTemplateNewInfo.Component>) => void;
    onChangeProjectCallback?(projectId?: number): void;
    setParentPathFun?(path: string): void;
    requiredList?: number[];
    getTabIsRequired?: (groupId?: number) => void;
    projectId?: number;
}

enum RequiredMap {
    'tag' = 1,
}

interface ITemplateParams {
    path: string;
    desc: string;
}

interface IEditApiBaseInfoRef {
    getParams(): nsHttpApiInterface.IApiBaseInfo;
    getTemplateParams(): ITemplateParams;
    checkRequiredParams(): boolean;
}
const formatTeams = (teams) => {
    const newTeams: any[] = [];
    teams.forEach(item => {
        newTeams.push({
            title: item.name || item.title,
            taskId: item.taskId,
            taskType: item.taskType,
            status: item.status,
            url: item.url,
        });
    });
    return newTeams;
};

const EditApiBaseInfo = forwardRef((props: IProps, ref: Ref<IEditApiBaseInfoRef>) => {
    const [moduleName, setModuleName] = useState<string>('');
    const [name, setName] = useState<string>('');
    const [schema, setSchema] = useState<string>('GET');
    const [path, setPath] = useState<string>('');
    const [groupId, setGroupId] = useState<number>(-1);
    const [state, setState] = useState<nsMockManageApiManageMainVersionChangeStatePOST.TStatus>('待开发');
    const [admin, setAdmin] = useState<Array<IUserInfo>>();
    const [version, setVersion] = useState<number>(1);
    const [description, setDescription] = useState<string>('');
    const [repoName, setRepoName] = useState<string>();
    const [pathRepoName, setPathRepoName] = useState<string>();
    const [projectIdInBaseInfo, setProjectIdInBaseInfo] = useState<number>();
    const { components = [], setComponents } = props;
    const [tags, setTags] = useState<nsMockManageApiManageMainTagSavePOST.IReturn[]>([]);
    const nameMaxLength = 100;
    const [catalogList, setCatalogList] = useState<any[]>([]);
    const param = getUrlSearch(['projectCatalogId', 'projectId']) as { projectCatalogId: number, projectId: number };
    const [selectedCatalogId, setSelectedCatalogId] = useState<string>('');
    const [expandedKeys, setExpandedKeys] = useState<string[]>([]);


    const getTemplateParams = (): ITemplateParams => {
        return { path, desc: description };
    };

    const getParams = (): nsHttpApiInterface.IApiBaseInfo => {
        const obj: any = {
            name,
            schema,
            path,
            groupId,
            state,
            version,
            desc: description,
            apiId: props.apiId || -1,
            admin: admin || [],
            repoName,
            pathRepoName,
            projectId: projectIdInBaseInfo,
            tagIdList: tags.map(item => item.id),
            catalogId: +(selectedCatalogId?.split('-').pop())
        };
        if (!!moduleName) {
            obj.moduleName = moduleName;
        }
        return obj;
    };
    const getComponentsValue = () => {
        return components;
    };

    // 校验必填参数 true-通过 false-未通过
    const checkRequiredParams = (): boolean => {
        if (!name) {
            message.warning('请填写 API 名称～');
            return false;
        }
        if (!schema) {  // 接口类型
            message.warning('请选择接口类型～');
            return false;
        }
        if (!path) {
            message.warning('请填写 URL 地址～');
            return false;
        }
        // 当hideGroup为true时，跳过分组校验
        if (!props.projectId && (!groupId || groupId < 1)) {
            message.warning('请选择分组～');
            return false;
        }
        if (!props.projectId && (!projectIdInBaseInfo || projectIdInBaseInfo < 1)) {
            message.warning('请选择仓库～');
            return false;
        }
        if (!admin || admin.length === 0) {
            message.warning('请选择负责人～');
            return false;
        }
        if (props.projectId && (!selectedCatalogId || selectedCatalogId < 1)) {
            message.warning('请选择目录～');
            return false;
        }
        return true;
    };

    useImperativeHandle(ref, () => ({
        getParams,
        getTemplateParams,
        checkRequiredParams,
        getComponentsValue,
    }));

    function onMultiSelect(increRows, ids, compsIndex) {
        const newComponents = [...components];
        const allList = newComponents[compsIndex].value.concat(increRows).reduce((pre: any[], current) => {
            const existingItem = pre.find(item => item.taskId === current.taskId);
            if (!existingItem) {
                pre.push(current);
            }
            return pre;
        }, []).filter(item => ids.indexOf(item.taskId) > -1);
        newComponents[compsIndex].value = formatTeams(allList);
        setComponents(newComponents || []);
    }

    // 移除team
    const onRemoveTeam = ({ teamInfo }) => {
        const newComponents = [...components];
        if (newComponents[0].id === 'team') {
            const index = newComponents[0]?.value?.findIndex(item => item.taskId === teamInfo.taskId);
            index > -1 && (newComponents[0]?.value?.splice(index, 1));
            setComponents(newComponents || []);
        }
    };

    useEffect(() => {
        const apiBaseInfo = props?.apiBaseInfo || {};
        setModuleName(apiBaseInfo['moduleName'] || '');
        setName(apiBaseInfo['name'] || '');
        setSchema(apiBaseInfo['schema'] || 'GET');
        setPath(apiBaseInfo['path'] || '');
        setGroupId(apiBaseInfo['groupId'] || -1);
        setState(apiBaseInfo['state'] || '待开发');
        setVersion(apiBaseInfo['version'] || 1);
        setTags(apiBaseInfo['tagList'] || []);
        // setTeams(apiBaseInfo['teams'] || []);
        setDescription(apiBaseInfo['desc'] || '');
        setRepoName(apiBaseInfo['repoName']);
        setPathRepoName(apiBaseInfo['pathRepoName']);
        setProjectIdInBaseInfo(apiBaseInfo['projectId']);
        setSelectedCatalogId(apiBaseInfo['catalogId'] || '');
        getCatalogList(apiBaseInfo['catalogId'] || param.projectCatalogId || undefined);
        if (!props.isTemplate) {
            (async () => {
                if (apiBaseInfo['admin'] && apiBaseInfo['admin'].length > 0) {
                    setAdmin(apiBaseInfo['admin']);
                } else {
                    const loginUser = await formatLoginUser();
                    setAdmin([loginUser]);
                }
            })();
        }
    }, [props.apiBaseInfo]);

    useEffect(() => {
        // 组件多处引用，setParentPathFun参数可能不存在
        props.setParentPathFun?.(path);
    }, [path]);

    const selectRepo = (val: number) => {
        const params = {
            groupId: val > -1 ? val : JSON.parse(localStorage.getItem(KDEV_API_GROUP_PROJECT) || '{}').groupId,
        };
        nsMockManageApiManageMainGroupMatchProject.remote(params).then((respData) => {
            if (respData) {
                setRepoName(respData?.repoName);
                setProjectIdInBaseInfo(respData?.projectId);
                setPathRepoName(respData?.path);
                props.onChangeProjectCallback?.(respData?.projectId);
            } else {
                setRepoName(undefined);
                setProjectIdInBaseInfo(undefined);
                setPathRepoName(undefined);
                props.onChangeProjectCallback?.(undefined);
            }
        })
    }

    useEffect(() => {
        selectRepo();
    }, []);

    const renderComponents = () => {
        return <>
            {
                props.components?.map((item, index) => {
                    if (item.enable !== 1) {
                        return null;
                    }
                    if (item.component === 'team') {
                        return <Col span={12} key={item.id}>
                            <div className={css.row}>
                                <label aria-required={!!item.required}>{item.name}</label>
                                <div className={css.teamRelateButtonBox}>
                                    <TeamRelateButton
                                        multiple
                                        searchScene={ESearchScene.normal}
                                        searchParams={{}}
                                        selectedTaskIds={item.value.map(it => it.taskId)}
                                        onMultiSelect={(increRows, ids) => onMultiSelect(increRows, ids, index)}
                                        disabled={props.isTemplate}
                                        btnText="关联Team任务"
                                    />
                                </div>
                            </div>
                            {
                                item.value?.length > 0 &&
                                <div className={css.apiTeamsBox}>
                                    <ApiTeams teams={item.value} onRemove={onRemoveTeam} />
                                </div>
                            }
                        </Col>;
                    }
                    if (item.component === 'select') {
                        return <Col span={12} key={item.id}>
                            <div className={css.row}>
                                <label aria-required={!!item.required}>{item.name}</label>
                                <Select
                                    style={{ width: '100%' }}
                                    value={item.value}
                                    onChange={(value) => {
                                        const newComponents = [...components];
                                        newComponents[index].value = value;
                                        setComponents(newComponents);
                                    }}
                                >
                                    {item.params?.map(it =>
                                        <Select.Option key={it} value={it}>{it}</Select.Option>
                                    )}
                                </Select>
                            </div>
                        </Col>;
                    }
                })
            }
        </>;
    };

    const modules = {
        // 方式1: 可以是简单的一维数组配置
        // toolbar: ["bold", "italic", "underline", "strike", "blockquote"]
        // 方式2: 可以配置二维数组，进行多个选项的配置
        // 或者针对某一个配置项的key值，进行配置
        toolbar: [
            // 默认的
            [{ header: [1, 2, 3, false] }],
            ['bold', 'italic', 'underline', 'link'],
            [{ list: 'ordered' }, { list: 'bullet' }],
            // ["clean"]
            // 掘金的富文本编辑器
            // 'bold',
            // 'italic',
            // 'underline',
            // { header: 1 },
            // { header: 2 },
            // 'blockquote',
            ['code-block']
            // 'code',
            // 'link',
            // { list: 'ordered' },
            // { list: 'bullet' },
            // 'image'
        ]
        // 方式3: 可以自己指定工具栏的容器
        // toolbar: "#rq-toolbar"
    };

    // 格式化目录列表数据
    const formatCatalogList = (list: any[], baseName: string = '-') => {
        return list.map(item => ({
            ...item,
            key: (item.type || 'CATALOG') + baseName + item.id,
            value: (item.type || 'CATALOG') + baseName + item.id,
            title: item.name,
            label: item.name,
            isLeaf: item.type === 'API',
            hasChild: item.hasChild === undefined ? true : item.hasChild,
            selectable: item.type === 'CATALOG', // 只允许选择目录节点
            children: item.children ? formatCatalogList(item.children, baseName + item.id + '-') : []
        }));
    };

    const deepSearchCatalog = (list: any[], id?: number) => {
        for (const node of list) {
            if (node.id === id) return node;
            if (node.children) {
                const found = deepSearchCatalog(node.children, id);
                if (found) return found;
            }
        }
        return null;
    }


    // 获取根目录列表
    const getCatalogList = async (locationId?: number) => {
        if (!props.projectId) return;
        try {
            const res = await nsMockManageApiManageMainApiProgramCatalogRootList.remote({
                programId: props.projectId,
                locationId: locationId || undefined,
                locationType: 'CATALOG'
            });
            const formattedList = formatCatalogList(res);
            setCatalogList(formattedList);
            setSelectedCatalogId(deepSearchCatalog(formattedList, locationId).key);
        } catch (error) {
            console.error('获取目录列表失败:', error);
            // message.error('获取目录列表失败');
        }
    };

    // 递归查找节点
    const findNodeById = (list: any[], id: number): any => {
        for (const node of list) {
            if (node.id === id) return node;
            if (node.children) {
                const found = findNodeById(node.children, id);
                if (found) return found;
            }
        }
        return null;
    };

    // useEffect(() => {
    //     if (props.projectCatalogId) {
    //         getCatalogList();
    //     }
    // }, [props.projectCatalogId]);

    return (
        <div className={classNames(css.editApiBaseInfo, props.className)}>
            <KdevTitle
                text="基本信息"
                size="small"
                className={css.title}
            />
            <Row gutter={[24, 20]} style={{ marginBottom: 16 }}>
                <Col span={12}>
                    <div className={css.row}>
                        <label aria-required={!props.isTemplate}>名称</label>
                        <Input
                            style={{ width: '100%' }}
                            className={css.inputGroup}
                            value={name}
                            onChange={(e) => {
                                if (e.target.value.length > nameMaxLength) {
                                    return;
                                }
                                setName(e.target.value);
                            }}
                            placeholder="请输入 API 名称"
                            suffix={<span style={{ color: '#D5D6D9' }}>{`${name.length} / ${nameMaxLength}`}</span>}
                        />
                    </div>
                </Col>
                <Col span={12}>
                    <div className={css.row}>
                        <label aria-required={!props.isTemplate}>方法</label>
                        <Input
                            style={{ width: '100%' }}
                            className={css.inputGroup}
                            value={path}
                            onChange={(e) => setPath(e.target.value)}
                            addonBefore={
                                <ApiMethodSelect
                                    className={css.inputGroupAddon}
                                    value={schema || undefined}
                                    onChange={(val) => setSchema(val)}
                                />
                            }
                            placeholder="请输入 URL 地址"
                        />
                    </div>
                </Col>
                {!props.projectId && (
                    <Col span={12}>
                        <div className={css.row}>
                            <label aria-required={!props.isTemplate}>分组</label>
                            <RepoGroupSelect
                                getAllGroup={props.apiId ? true : false}
                                className={css.repoGroupSelect}
                                disabled={props.isTemplate}
                                value={groupId > -1 ? groupId : undefined}
                                onChange={(val) => {
                                    selectRepo(val);
                                    setGroupId(val);
                                    if (props.getTabIsRequired instanceof Function) {
                                        props.getTabIsRequired(val);
                                    }
                                }}
                            />
                        </div>
                    </Col>
                )}
                <Col span={12}>
                    <div className={css.row}>
                        <label aria-required={props.projectId ? false : !props.isTemplate}>仓库</label>
                        <RecentRepoSelectWithGroup
                            // groupId={  '1272'}
                            value={projectIdInBaseInfo}
                            repoName={repoName}
                            path={pathRepoName}
                            allowClear={true}
                            onChange={(reco) => {
                                setRepoName(reco?.repoName);
                                setProjectIdInBaseInfo(reco?.projectId);
                                setPathRepoName(reco?.path);
                                props.onChangeProjectCallback?.(reco?.projectId);
                            }} />
                    </div>
                </Col>

                <Col span={12}>
                    <div className={css.row}>
                        <label aria-required={!props.isTemplate}>负责人</label>
                        <div className={css.height32px}>
                            <KDevParticipants
                                value={(admin?.map(item => ({ ...item, avatarUrl: item.photo }))) as any}
                                disabled={props.isTemplate}
                                onChange={(list) => setAdmin(list)}
                            />
                        </div>
                    </div>
                </Col>
                <Col span={12}>
                    <div className={css.row}>
                        <label>版本</label>
                        <div className={css.height32px}>{`Version ${version}`}</div>
                    </div>
                </Col>
                {renderComponents()}
                <Col span={12}>
                    <div className={css.row}>
                        <label aria-required={props.requiredList?.includes(RequiredMap.tag)}>标签</label>
                        <div className={css.tagWrap}>
                            <SelectTag
                                value={tags}
                                onChange={(val) => setTags(val)}
                                groupId={groupId}
                                projectId={param.projectId}
                            />
                        </div>
                    </div>
                </Col>
                {props.projectId ? (
                    <Col span={12}>
                        <div className={css.row}>
                            <label aria-required={true}>目录</label>
                            <TreeSelect
                                className={css.catalogSelect}
                                treeData={catalogList}
                                value={selectedCatalogId}
                                onChange={(value) => {
                                    setSelectedCatalogId(value || '');
                                }}
                                placeholder="请选择目录"
                                style={{ width: '100%' }}
                                loadData={async (node) => {
                                    const { id } = node;
                                    try {
                                        const res = await nsMockManageApiManageMainApiProgramCatalogSubList.remote({
                                            id: parseInt(id, 10)
                                        });
                                        res.forEach(item => {
                                            item.type = item.type.toUpperCase() as 'CATALOG' | 'API';
                                        });
                                        const baseKey = '-' + node.key.toString().split('-').slice(1).join('-') + '-';
                                        const newList = [...catalogList];
                                        const target = findNodeById(newList, parseInt(id, 10));
                                        if (target) {
                                            target.children = formatCatalogList(res, baseKey);
                                            setCatalogList(newList);
                                        }
                                    } catch (error) {
                                        message.error('加载子目录失败');
                                    }
                                }}
                                treeDefaultExpandAll={false}
                                showSearch
                                allowClear
                                treeNodeFilterProp="title"
                            />
                        </div>
                    </Col>
                ) : null}
            </Row>
            <div className={classNames(css.row, css.rowAlignItemsStart)}>
                <label>详细说明</label>
                <div className={css.descBox}>
                    <ReactQuill
                        modules={modules}
                        className={[css.reactQuill, 'ql-editor'].join(' ')}
                        value={description}
                        onChange={(val) => setDescription(val)}
                    />
                </div>
            </div>
        </div>
    );
});

export { EditApiBaseInfo, IEditApiBaseInfoRef };
