@import '~@lynx/design-token/dist/less/token.less';

.editApiBaseInfo {
    .title {
        font-weight: 600;
        margin-bottom: 16px;

        &::before {
            border-radius: 1.5px;
        }
    }

    .row {
        display: flex;
        align-items: center;

        >label {
            width: 68px;
            min-width: 68px;
            margin-right: 16px;
            text-align: right;
            font-weight: 400;
            color: @color-text-secondary;

            &::after {
                content: ':';
            }

            &[aria-required=true]::before {
                content: '* ';
                color: #f5222d;
            }
        }

        .width144px {
            width: 144px;
        }

        .height32px {
            height: 32px;
            line-height: 32px;
        }

        .inputGroup {
            width: 583px;

            .inputGroupAddon {
                .width144px();

            }

            .input {
                flex: 1;
            }
        }

        .apiStatusRadioGroup {
            height: 32px;
            display: flex;
            align-items: center;
        }

        .repoGroupSelect {
            //.width144px();
            width: 100%;
        }

        .teamRelateButtonBox {
            height: 32px;
            display: flex;
            align-items: center;
        }

        .tagWrap {
            height: 32px;
            display: flex;
            align-items: center;
            overflow: hidden;
            width: 100%;
        }
    }

    .apiTeamsBox {
        margin-left: 84px;
    }

    .rowAlignItemsStart {
        align-items: start;

        .descBox {
            width: 100%;
            height: 196px;

            .reactQuill {
                padding: 0;

                :global {
                    .ql-toolbar {
                        border-radius: 4px 4px 0 0;
                    }

                    .ql-container {
                        border-radius: 0 0 4px 4px;
                        height: calc(100% - 42px);
                    }

                    .ql-picker-label,
                    .ql-picker-options {
                        border-radius: 4px;
                    }
                }
            }
        }
    }

    :global {
        .ant-input-group-addon {
            background-color: transparent;
            text-align: left;
        }

        .ant-input-group-addon:first-child:has(.ant-select-disabled) {
            background-color: #f5f5f5;
        }

        .ant-input-group-addon:first-child {
            border-radius: 4px 0 0 4px;
        }

        .ant-select-selection-placeholder {
            color: rgba(0, 0, 0, 0.75);
        }

        // 以下是富文本样式
        .ql-snow .ql-tooltip {
            z-index: 1;
        }

        .ql-toolbar.ql-snow,
        .ql-container.ql-snow {
            border-color: #D0D3D6;
        }

        .ql-snow .ql-stroke {
            stroke: #646A73;
        }

        .ql-snow .ql-fill,
        .ql-snow .ql-stroke.ql-fill {
            fill: #646A73;
        }

        .ql-snow.ql-toolbar button:hover,
        .ql-snow .ql-toolbar button:hover,
        .ql-snow.ql-toolbar button:focus,
        .ql-snow .ql-toolbar button:focus,
        .ql-snow.ql-toolbar button.ql-active,
        .ql-snow .ql-toolbar button.ql-active,
        .ql-snow.ql-toolbar .ql-picker-label:hover,
        .ql-snow .ql-toolbar .ql-picker-label:hover,
        .ql-snow.ql-toolbar .ql-picker-label.ql-active,
        .ql-snow .ql-toolbar .ql-picker-label.ql-active,
        .ql-snow.ql-toolbar .ql-picker-item:hover,
        .ql-snow .ql-toolbar .ql-picker-item:hover,
        .ql-snow.ql-toolbar .ql-picker-item.ql-selected,
        .ql-snow .ql-toolbar .ql-picker-item.ql-selected {
            color: #3370FF;
        }

        .ql-snow.ql-toolbar button:hover .ql-stroke,
        .ql-snow .ql-toolbar button:hover .ql-stroke,
        .ql-snow.ql-toolbar button:focus .ql-stroke,
        .ql-snow .ql-toolbar button:focus .ql-stroke,
        .ql-snow.ql-toolbar button.ql-active .ql-stroke,
        .ql-snow .ql-toolbar button.ql-active .ql-stroke,
        .ql-snow.ql-toolbar .ql-picker-label:hover .ql-stroke,
        .ql-snow .ql-toolbar .ql-picker-label:hover .ql-stroke,
        .ql-snow.ql-toolbar .ql-picker-label.ql-active .ql-stroke,
        .ql-snow .ql-toolbar .ql-picker-label.ql-active .ql-stroke,
        .ql-snow.ql-toolbar .ql-picker-item:hover .ql-stroke,
        .ql-snow .ql-toolbar .ql-picker-item:hover .ql-stroke,
        .ql-snow.ql-toolbar .ql-picker-item.ql-selected .ql-stroke,
        .ql-snow .ql-toolbar .ql-picker-item.ql-selected .ql-stroke,
        .ql-snow.ql-toolbar button:hover .ql-stroke-miter,
        .ql-snow .ql-toolbar button:hover .ql-stroke-miter,
        .ql-snow.ql-toolbar button:focus .ql-stroke-miter,
        .ql-snow .ql-toolbar button:focus .ql-stroke-miter,
        .ql-snow.ql-toolbar button.ql-active .ql-stroke-miter,
        .ql-snow .ql-toolbar button.ql-active .ql-stroke-miter,
        .ql-snow.ql-toolbar .ql-picker-label:hover .ql-stroke-miter,
        .ql-snow .ql-toolbar .ql-picker-label:hover .ql-stroke-miter,
        .ql-snow.ql-toolbar .ql-picker-label.ql-active .ql-stroke-miter,
        .ql-snow .ql-toolbar .ql-picker-label.ql-active .ql-stroke-miter,
        .ql-snow.ql-toolbar .ql-picker-item:hover .ql-stroke-miter,
        .ql-snow .ql-toolbar .ql-picker-item:hover .ql-stroke-miter,
        .ql-snow.ql-toolbar .ql-picker-item.ql-selected .ql-stroke-miter,
        .ql-snow .ql-toolbar .ql-picker-item.ql-selected .ql-stroke-miter {
            stroke: #3370FF;
        }

        .ql-snow.ql-toolbar button:hover .ql-fill,
        .ql-snow .ql-toolbar button:hover .ql-fill,
        .ql-snow.ql-toolbar button:focus .ql-fill,
        .ql-snow .ql-toolbar button:focus .ql-fill,
        .ql-snow.ql-toolbar button.ql-active .ql-fill,
        .ql-snow .ql-toolbar button.ql-active .ql-fill,
        .ql-snow.ql-toolbar .ql-picker-label:hover .ql-fill,
        .ql-snow .ql-toolbar .ql-picker-label:hover .ql-fill,
        .ql-snow.ql-toolbar .ql-picker-label.ql-active .ql-fill,
        .ql-snow .ql-toolbar .ql-picker-label.ql-active .ql-fill,
        .ql-snow.ql-toolbar .ql-picker-item:hover .ql-fill,
        .ql-snow .ql-toolbar .ql-picker-item:hover .ql-fill,
        .ql-snow.ql-toolbar .ql-picker-item.ql-selected .ql-fill,
        .ql-snow .ql-toolbar .ql-picker-item.ql-selected .ql-fill,
        .ql-snow.ql-toolbar button:hover .ql-stroke.ql-fill,
        .ql-snow .ql-toolbar button:hover .ql-stroke.ql-fill,
        .ql-snow.ql-toolbar button:focus .ql-stroke.ql-fill,
        .ql-snow .ql-toolbar button:focus .ql-stroke.ql-fill,
        .ql-snow.ql-toolbar button.ql-active .ql-stroke.ql-fill,
        .ql-snow .ql-toolbar button.ql-active .ql-stroke.ql-fill,
        .ql-snow.ql-toolbar .ql-picker-label:hover .ql-stroke.ql-fill,
        .ql-snow .ql-toolbar .ql-picker-label:hover .ql-stroke.ql-fill,
        .ql-snow.ql-toolbar .ql-picker-label.ql-active .ql-stroke.ql-fill,
        .ql-snow .ql-toolbar .ql-picker-label.ql-active .ql-stroke.ql-fill,
        .ql-snow.ql-toolbar .ql-picker-item:hover .ql-stroke.ql-fill,
        .ql-snow .ql-toolbar .ql-picker-item:hover .ql-stroke.ql-fill,
        .ql-snow.ql-toolbar .ql-picker-item.ql-selected .ql-stroke.ql-fill,
        .ql-snow .ql-toolbar .ql-picker-item.ql-selected .ql-stroke.ql-fill {
            fill: #3370FF;
        }
    }
}

.catalogSelect {
    width: 100%;

    :global {
        .ant-select-selector {
            min-height: 32px;
        }

        .ant-select-selection-placeholder {
            line-height: 30px;
        }

        .ant-select-selection-search-input {
            height: 30px;
        }
    }
}