import React, { useEffect, useState } from 'react';
import { Select } from 'antd';
import { SelectProps } from 'antd/lib/select';
import { nsMockManageApiManageMainApiSchemaListGET } from '@/remote';

interface IOption {
    value: string;
    label: string;
}

export enum EApiMethod {
    GET = 'GET',
    POST = 'POST',
    PUT = 'PUT',
    DELETE = 'DELETE',
    PATCH = 'PATCH',
    HEAD = 'HEAD'
}

function ApiMethodSelect(props: SelectProps<string>) {
    const [apiMethods, setApiMethods] = useState<IOption[]>([
        { value: 'GET', label: 'GET' },
        { value: 'POST', label: 'POST' },
        { value: 'PUT', label: 'PUT' },
        { value: 'DELETE', label: 'DELETE' },
        { value: 'PATCH', label: 'PATCH' },
        { value: 'HEAD', label: 'HEAD' },
        // { value: 'OPTIONS', label: 'OPTIONS' }
    ]);
    const [loading, setLoading] = useState(false);

    const getApiMethods = async () => {
        setLoading(true);
        try {
            const res = await nsMockManageApiManageMainApiSchemaListGET.remote();
            setApiMethods(nsMockManageApiManageMainApiSchemaListGET.formatSchemaList(res.schemaList));
            setLoading(false);
        } catch {
            setLoading(false);
        }
    };

    useEffect(() => {
        // getApiMethods();
    }, []);

    return (
        <Select
            options={apiMethods}
            placeholder="请选择请求方法"
            loading={loading}
            {...props}
        />
    );
}

export { ApiMethodSelect };
