import { AViewModel } from 'libs';
import { observable, action } from 'mobx';
import { JSONbigStringify, formatKey1, JSONbigParse } from '@/index.config/tools';
import { message } from 'antd';
import { JsonImportM } from '@/business/jsonImportModal/JsonImportM';
import { MultiDimensionalM } from '@/business/editJsonTable/multiDimensional/MultiDimensionalM';
import { OneDimensionalM } from '@/business/editJsonTable/oneDimensional/OneDimensionalM';

export class ResponseParamsM extends AViewModel {
    @observable public moduleId: number = 0;
    @observable public docId: number = 0;

    @observable public resBodyExample: string = '';
    @observable public responseType: number = 2; // 1：返回头部 2：返回结果

    public jsonImportM = new JsonImportM();
    public resBodyMultiDimensionalM = new MultiDimensionalM();
    public resHeaderOneDimensionalM = new OneDimensionalM();

    // 加载model
    @action
    public init(response, moduleId: number, docId: number) {
        this.moduleId = moduleId;
        this.docId = docId;
        this.initResponse(response);
        this.resBodyMultiDimensionalM.init('ResponseRoot', this.moduleId);
    }

    // 初始化数据
    @action.bound
    public initData() {
        this.moduleId = 0;
        this.responseType = 2;
        this.resBodyExample = '';
        this.resHeaderOneDimensionalM.initData();
        this.resBodyMultiDimensionalM.initData();
    }

    @action.bound
    public onOpenJsonImportModal(): void {
        this.jsonImportM.init('', this.resBodyExample, this.docId);
        this.jsonImportM.onSaveJsonCallBack = this.onSaveJsonCallBack;
    }

    @action.bound
    public onSaveJsonCallBack(type: string, data, expamle: string) {
        this.formatResBody(data);
        this.resBodyExample = expamle;
    }

    // 处理返回参数
    @action.bound
    protected initResponse(response) {
        if (response) {
            const headers = response?.headers || {};
            const body = response?.body || {};
            const example = body?.example || '';
            this.resBodyExample = JSONbigStringify(example) || '';
            this.formatResHeaderList(headers);
            this.formatResBody(body);
        }
    }

    // 处理返回header参数
    @action
    protected formatResHeaderList(headerObj) {
        const headerList: Array<any> = [];
        const headerKeys = Object.keys(headerObj);
        headerKeys.length && headerKeys.map(item => {
            headerList.push({
                name: item,
                type: headerObj[item]?.type || '',
                required: headerObj[item]?.required || false,
                description: headerObj[item]?.description || '',
                // example: headerObj[item]?.example || ''
                value: headerObj[item]?.value
            });
        });
        const list = formatKey1(headerList)[0];
        this.resHeaderOneDimensionalM.setList(list);
    }

    // 处理返回body参数
    @action.bound
    protected formatResBody(body): void {
        if (body.model && body.model.length) {
            body['children'] = body.model;
        }
        if (body['name'] || body['model']) {
            const [list, keys] = formatKey1([body]);
            const newKeys = keys.filter(item => item.split('-').length < 3).splice(0, 10);
            this.resBodyMultiDimensionalM.setListAndExpanedKeys(list, newKeys);
        }
    }

    @action.bound
    public expandAllKeys(boo: boolean, type: string): void {
        this[type].onExpandAllKeys(boo);
    }

    @action.bound
    public getResponse() {
        const headers = {};
        this.resHeaderOneDimensionalM.getList().forEach(item => {
            headers[item.name] = {
                type: item['type'],
                // example: item['example'],
                required: item['required'],
                description: item['description']
            };
        });
        const responseBodyList = this.resBodyMultiDimensionalM.getList();
        return {
            description: '',
            headers,
            body: {
                name: responseBodyList[0]?.name || '',
                type: responseBodyList[0]?.type || '',
                required: responseBodyList[0]?.required || false,
                example: JSONbigParse(this.resBodyExample) as any,
                model: responseBodyList[0]?.children || []
            }
        };
    }

    // 校验参数
    @action.bound
    public checkParams() {
        if (this.resBodyMultiDimensionalM.checkParams()) {
            return true;
        }
        const responseBodyList = this.resBodyMultiDimensionalM.getList();
        if (responseBodyList.length && responseBodyList[0].type !== 'object') {
            message.warn('返回结果最外层参数只能为"object"类型');
            return true;
        }
        return false;
    }

    @action.bound
    public getExpandedKeys(): string[] {
        return this.resBodyMultiDimensionalM.expandedRowKeys;
    }
}
