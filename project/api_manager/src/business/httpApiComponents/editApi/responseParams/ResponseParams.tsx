import { AView } from 'libs';
import React from 'react';
import { observer } from 'mobx-react';
import { Collapse, Radio, Button } from 'antd';
import {Bind} from 'lodash-decorators';
import { ResponseParamsM } from './ResponseParamsM';
import css from './ResponseParams.less';
import { bindObserver } from '@libs/mvvm';
import { JsonImport } from '@/business/jsonImportModal/JsonImport';
import { MultiDimensional } from '@/business/editJsonTable/multiDimensional/MultiDimensional';
import { OneDimensional } from '@/business/editJsonTable/oneDimensional/OneDimensional';
import { responseType } from './enum';

const { Panel } = Collapse;
const RadioGroup_responseType = bindObserver(Radio.Group, 'responseType');

interface IProps {
    apiGatherM?: any;
}

@observer
export class ResponseParams extends AView<ResponseParamsM, IProps> {

    @Bind
    public componentWillUnmount(): void {
        this.model.initData();
    }

    @Bind
    public componentDidMount(): void {
        // this.model.init();
    }

    @Bind
    private renderJsonImportBtn(type: string): React.ReactNode {
        const model = this.model;
        if (type === 'res' && model.responseType !== 2) {
            return;
        }
        return (
            <>
                <Button
                    type={ 'primary' }
                    onClick={ model.onOpenJsonImportModal }
                    className={ css.jsonImportBtn }
                >
                    JSON导入
                </Button>
                <span className={ css.jsonTootip }>使用json导入会覆盖原有是否必填、备注项的值</span>
                <JsonImport model={model.jsonImportM} />
            </>
        );
    }

    public render(): React.ReactNode {
        const model = this.model;
        return (
            <Collapse ghost className={ css.responseCollapse } defaultActiveKey={ '1' }>
                <Panel header={ '返回数据' } key={ '1' }>
                    <RadioGroup_responseType
                        model={ model }
                        className={ css.margBottom16px }
                        options={ responseType }
                        optionType={ 'button' }
                    />
                    {
                        model.responseType === 2 &&
                        <span className={ css.expandBtn }>
                            <a onClick={ () => model.expandAllKeys(true, 'resBodyMultiDimensionalM') }>
                                全部展开
                            </a>/
                            <a onClick={ () => model.expandAllKeys(false, 'resBodyMultiDimensionalM') }>
                                全部收起
                            </a>
                        </span>
                    }
                    {
                        model.responseType === 2 ?
                            <MultiDimensional
                                model={ model.resBodyMultiDimensionalM }
                                tableBottom={ this.renderJsonImportBtn('res') }
                            /> :
                            <OneDimensional model={ model.resHeaderOneDimensionalM } />
                    }
                </Panel>
            </Collapse>
        );
    }
}
