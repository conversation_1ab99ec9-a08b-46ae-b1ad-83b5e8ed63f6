import { IRecord } from './EditParamTable';
import { nsHttpApiInterface, nsMockManageApiManageMainApiParamTypeListGET } from '@/remote';
import { v4 as uuidv4 } from 'uuid';

// 字段名变换枚举值定义
const ERefInfo = nsHttpApiInterface.ERefInfo;
const EApiParam = nsHttpApiInterface.EApiParam;
const EParamType = nsMockManageApiManageMainApiParamTypeListGET.EParamType;

interface IRefInfo extends nsHttpApiInterface.IRefInfo { }

// 查找数据所在层级的index
function findIndexDataConstruct(key: string, data: IRecord[]) {
    let index = -1;
    data.forEach((item, idx) => {
        if (item.key === key) {
            index = idx;
            return;
        } else if (index < 0 && item.children && item.children.length) {
            index = findIndexDataConstruct(key, item.children);
        }
    });
    return index;
}

// 删除数据结构返回新数据
function removeDataConstruct(keys: string[], data: IRecord[]) {
    if (!keys.length) {
        return data;
    }
    const newData: IRecord[] = [];
    data.forEach(item => {
        const index = keys.indexOf(item.key);
        if (index > -1) {
            keys.splice(index, 1);
        } else {
            const obj = { ...item };
            if (item.children && item.children.length) {
                const children = removeDataConstruct(keys, item.children);
                obj.children = children.length ? children : undefined;
            }
            newData.push(obj);
        }
    });
    return newData;
}

// 重新生成引用数据结构
let countTime = 0;
function cloneDataConstruct(body: IRecord[], parent?: IRecord) {
    const newData: IRecord[] = [];
    const refCreateTime = new Date().getTime();
    body.forEach(item => {
        const obj: IRecord = {
            ...item,
            key: uuidv4(),
            parent
        };
        if (!parent) {
            const itemRefInfo = item[EApiParam.REF_INFO] || {};
            obj[EApiParam.REF_INFO] = {
                Id: itemRefInfo[ERefInfo.ID] || '',
                V: itemRefInfo[ERefInfo.VERSION] || '',
                T: refCreateTime + countTime,
                N: itemRefInfo[ERefInfo.NAME] || '',
                Dn: itemRefInfo[ERefInfo.DISPLAY_NAME] || '',
                Pn: itemRefInfo[ERefInfo.PROJECT_NAME] || ''
            };
        }
        if (item.children && item.children.length) {
            const newChildren = cloneDataConstruct(item.children, obj);
            obj.children = newChildren.length ? newChildren : undefined;
        }
        newData.push(obj);
    });
    return newData;
}

// 替换数据结构
function replaceDataConstruct(data: IRecord[], body: IRecord[], refInfo: IRefInfo) {
    const insetRefKeys: string[] = [];
    const newData: IRecord[] = [];
    data.forEach(item => {
        const itemRefInfo = item[EApiParam.REF_INFO];
        if (itemRefInfo && refInfo[ERefInfo.ID] === itemRefInfo[ERefInfo.ID]) {
            if (!insetRefKeys.includes(getRefKey(itemRefInfo))) {
                insetRefKeys.push(getRefKey(itemRefInfo));
                const newBody = cloneDataConstruct(body);
                countTime += 1;
                newData.push(...newBody);
            }
        } else {
            const obj = { ...item };
            if (item.children && item.children.length) {
                const children = replaceDataConstruct(item.children, body, refInfo);
                obj.children = children.length ? children : undefined;
            }
            newData.push(obj);
        }
    });
    return newData;
}

// 标记引用数据结构
function formatDataConstructParam(params: IRecord[], refInfo: IRefInfo) {
    params.map(item => {
        item[EApiParam.REF_INFO] = { ...item[EApiParam.REF_INFO], ...refInfo };
    });
    return params;
}

// 格式化参数成为编辑数据
function formatEditParam(
    params: nsHttpApiInterface.IApiParam[],
    parent?: IRecord,
    cParamsItem?: IRecord
): {
    params: IRecord[],
    expandedAllkeys: string[],
    changeParams: IRecord[]
} {
    let expandedKeys: string[] = [];
    const cParams: IRecord[] = [];
    for (const item of params) {
        item['key'] = uuidv4();
        item['parent'] = parent || null;
        item['children'] = (item['children'] && item['children'].length > 0)
            ? item['children']
            : undefined;
        if (item.children && item.children.length > 0) {
            const obj: IRecord = {
                ...item,
                parent: cParamsItem
            } as IRecord;
            const { expandedAllkeys, changeParams } = formatEditParam(item.children, item as IRecord, cParamsItem);
            expandedKeys = [...expandedKeys, ...expandedAllkeys, item['key']];
            if (item['isAdd'] || item['isRemove'] || item['changeItem']) {
                obj.children = changeParams;
                cParams.push(obj);
            }
        } else {
            if (item['isAdd'] || item['isRemove'] || item['changeItem']) {
                const obj: IRecord = {
                    ...item,
                    parent: cParamsItem
                } as IRecord;
                cParams.push(obj);
            }
        }
    }
    return {
        params: params as IRecord[],
        expandedAllkeys: expandedKeys,
        changeParams: cParams as IRecord[]
    };
}

// 处理成后端想要的数据格式
function getFormatParams(params: IRecord[]): nsHttpApiInterface.IApiParam[] {
    const res: nsHttpApiInterface.IApiParam[] = [];
    for (const item of params) {
        if (item[EApiParam.NAME]) {
            const obj: nsHttpApiInterface.IApiParam = {
                N: item[EApiParam.NAME],
                T: item[EApiParam.TYPE],
                R: item[EApiParam.REQUIRED],
                D: item[EApiParam.DESC],
                V: item[EApiParam.VALUE],
                C: item[EApiParam.CHECK],
                ACL: item[EApiParam.ACL]
            };
            if (item[EApiParam.REF_INFO]) {
                obj[EApiParam.REF_INFO] = item[EApiParam.REF_INFO];
            }
            if (item.children && item.children.length > 0) {
                obj.children = getFormatParams(item.children);
            }
            res.push(obj);
        }
    }
    return res;
}

function createParam(parentRecord?: IRecord): IRecord {
    return {
        N: '',
        T: EParamType.string, // 3-string
        R: 1, // 1-必填
        D: '',
        V: '',
        ACL:[],
        key: uuidv4(),
        parent: parentRecord
    };
}

interface IContext {
    parent?: string;
    groups: Array<string>;
    pathList: Array<string>;
    deep: number;
}

function deepTraverse(list: Array<IRecord>, cb: (data: IRecord, context: IContext) => void, context: IContext) {
    list?.forEach(item => {
        cb(item, context);
        if (item.children) {
            const itemRefInfo = item[EApiParam.REF_INFO];
            const childContext = {
                parent: item.key,
                pathList: context.pathList ? [...context.pathList, item.N] : [],
                groups: itemRefInfo ? [...context.groups, getRefKey(itemRefInfo)] : [...context.groups],
                deep: context.deep + 1,
            };
            deepTraverse(item.children, cb, childContext);
        }
    });
}

function hasAncestorWithClass(element, className) {
    while (element) {
        if (element.classList && element.classList.contains(className)) {
            return true;
        }
        element = element.parentNode;
    }
    return false;
}

// 获取引用数据结构唯一值
function getRefKey(refInfo: Required<IRecord>['Rf']): string {
    return `${refInfo[ERefInfo.ID]}--${refInfo[ERefInfo.CREATE_TIME]}`;
}

// 校验同级不能出现重复key
function checkEditParams(arr: Array<IRecord>): IRecord | undefined {
    const map = new Map();
    let record: IRecord | undefined;
    arr.forEach(item => {
        if (record) {
            return;
        }
        if (map.has(item[EApiParam.NAME])) {
            record = item;
            return;
        } else if (item.children && item.children.length) {
            const it = checkEditParams(item.children);
            if (it) {
                record = it;
                return;
            }
        }
        item[EApiParam.NAME] && map.set(item[EApiParam.NAME], item);
    });
    return record || undefined;
}

export {
    IRefInfo, ERefInfo, EApiParam, EParamType, getRefKey, checkEditParams,
    findIndexDataConstruct, formatEditParam, deepTraverse, hasAncestorWithClass, getFormatParams,
    createParam, formatDataConstructParam, removeDataConstruct, replaceDataConstruct
};
