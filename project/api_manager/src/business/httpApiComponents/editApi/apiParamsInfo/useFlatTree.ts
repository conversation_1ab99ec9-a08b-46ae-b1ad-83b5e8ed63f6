import React, { useRef, useMemo } from 'react';
import {
    IRecord,
    RecordAddition,
} from '@/business/httpApiComponents/editApi/apiParamsInfo/EditParamTable';
import {
    deepTraverse,
    EApiParam,
    getRefKey,
} from '@/business/httpApiComponents/editApi/apiParamsInfo/formatParamsTools';
import { commentContentScrollContanier, overDomId } from '@/business/Commit/Commit';

export function useFlatTree({ list }: { list: Array<IRecord> }) {
    /**
     * 背景色从1开始循环  共有8个
     */
    const constructNO = useRef<number>(0);
    /**
     * 背景色从1开始循环  共有8个
     */
    const constructColor = useRef<Record<string, number>>({});

    const dataSourceMap = useMemo(() => {
        /**
         * 每条数据的key到节点的对应关系
         */
        const keyToNode: Record<string, RecordAddition> = {}; // dataSourceMap
        /**
         * 颜色到节点的对应关系
         * 一种颜色对应一个group
         * 颜色是随机的
         */
        const colorToNode: Record<number, Array<IRecord>> = {}; // colorRelatedRow
        /**
         * 数据结构到节点的对应关系
         * 1个数据结构里面包含多个节点
         * group的id是前端生成的； 会存在添加相同的group；故id为 `${id}-${createTime}`;
         * group: 数据结构；数据模型；
         */
        const groupToNode: Record<string, Array<IRecord>> = {}; // groupRelatedRow

        /**
         * path到节点的对应关系
         * path以.分割
         */
        const pathToNode: Record<string, IRecord> = {}; // dataSourceMap
        /**
         * 第一层拥有子节点的元素
         */
        const firstFloorNodeKey: Array<string> = [];
        /**
         * 第二层拥有子节点的元素
         */
        const secondFloorNodeKey: Array<string> = [];

        deepTraverse(
            list,
            (item, context) => {
                const path = [...context?.pathList, item.N].join('.');
                pathToNode[path] = item;
                if (context.deep === 0 && item.children && item.children.length > 0) {
                    firstFloorNodeKey.push(item.key);
                }
                if (context.deep === 1 && item.children && item.children.length > 0) {
                    secondFloorNodeKey.push(item.key);
                }
                const groups = [...(context?.groups || [])] || [];
                const itemRefInfo = item[EApiParam.REF_INFO];
                if (itemRefInfo) {
                    const key = getRefKey(itemRefInfo);
                    groups.push(key);
                    if (!groupToNode[key]) {
                        groupToNode[key] = [item];
                    } else {
                        groupToNode[key].push(item);
                    }

                }
                let colorNo = groups[0] ? constructColor.current[groups[0]] : undefined;
                if (groups[0] && !constructColor.current[groups[0]]) {
                    colorNo = constructNO.current + 1;
                    constructColor.current[groups[0]] = colorNo;
                    constructNO.current = colorNo;
                }
                if (colorNo) {
                    if (!colorToNode[colorNo]) {
                        colorToNode[colorNo] = [item];
                    } else {
                        colorToNode[colorNo].push(item);
                    }
                }
                keyToNode[item.key] = {
                    ...item,
                    // @ts-ignore
                    parent: context?.parent,
                    groups: groups,
                    colorNo: colorNo,
                    path,
                };
            },
            {
                parent: undefined,
                groups: [],
                pathList: [],
                deep: 0,
            }
        );
        return {
            keyToNode,
            colorToNode,
            groupToNode,
            pathToNode,
            pathList: Object.keys(pathToNode),
            firstFloorNodeKey,
            secondFloorNodeKey,
        };
    }, [list]);

    return {
        constructNO,
        constructColor,
        dataSourceMap,
    };
}

export function useExpand({
    dataSourceMap,
    virtualTableRef,
    scrollCommentToIconPos
}: any) {
    const { rowVisualizer } = virtualTableRef.current || {};
    const [expanded, setExpanded] = React.useState<boolean | Record<string, boolean>>(true);
    const [viewPaddingBottom, setViewPaddingBottom] = React.useState<number>(0);

    /**
     * 当第1层的目录都被折叠后， expanded为false
     */
    const expandedWithComputer = (() => {
        if (typeof expanded === 'boolean') {
            return expanded;
        }
        return !dataSourceMap.firstFloorNodeKey.every(it => !expanded[it]);
    })();

    // 获取评论信息的位置
    const commentActiveScorllTop = () => {
        const commentActiveDom = document.querySelector('.comment_active');
        return commentActiveDom ? commentActiveDom.getBoundingClientRect().top : undefined;
    };

    // 获取当前表格所在容器的位置信息
    const getTableContainerRefRect = () => {
        return virtualTableRef.current?.tableContainerRef?.current?.getBoundingClientRect();
    };

    // 获取点击当前选中dom
    const getSearchCurrentSelectedDom = (key: string) => {
        return document.querySelector(`.key_${key}`);
    };

    // 搜索时定位到该元素
    const handleExpandAndScrollTop = (key: string) => {
        // 根据key获取所在虚拟表格数据中的下标
        const index = virtualTableRef.current?.rows.findIndex(it => it.id === key);
        try {
            // 使用scrollToIndex滚动位置计算有偏差，采用scrollToOffset自己计算应该滚动的距离
            // virtualTableRef.current?.rowVisualizer.scrollToIndex(index);
            const commentActiveDomTop = commentActiveScorllTop();
            const tableContainerRefRect = getTableContainerRefRect();
            // 偏移距离计算行高45px
            if (
                commentActiveDomTop
                && commentActiveDomTop > tableContainerRefRect.top
                && commentActiveDomTop < tableContainerRefRect.bottom
            ) {
                rowVisualizer.scrollToOffset((index * 45) + (tableContainerRefRect.top - commentActiveDomTop));
            } else {
                rowVisualizer.scrollToOffset(index * 45);
            }
        } catch (e) { }
    };

    // 将搜索到的内容加css样式
    const highlightSearchRow = (key: string) => {
        getSearchCurrentSelectedDom(key)?.classList.add('search-item');
    };

    // 内容区域滚动到评论位置
    const contentContainerSrcollPos = (key: string) => {
        const currentSearchSelectedDom = getSearchCurrentSelectedDom(key);
        if (currentSearchSelectedDom) {
            const commentActiveDomTop = commentActiveScorllTop();
            const currentSearchSelectedTop = currentSearchSelectedDom.getBoundingClientRect().top;
            if (commentActiveDomTop && currentSearchSelectedTop) {
                document.getElementById(commentContentScrollContanier)?.scrollBy({
                    top: currentSearchSelectedTop - commentActiveDomTop
                });
            }
        }
    };

    // 内容区域无法滚动到评论位置处理
    const contentContainerSrcollPosDiff = (key: string) => {
        const currentDomTop = getSearchCurrentSelectedDom(key)?.getBoundingClientRect().top || 0;
        const commentActiveDomTop = commentActiveScorllTop();
        // 评论被删除则不产生滚动
        if (commentActiveDomTop !== undefined) {
            if (currentDomTop - commentActiveDomTop > 3) {
                const compotedViewPaddingBottom = currentDomTop - commentActiveDomTop + viewPaddingBottom;
                setViewPaddingBottom(compotedViewPaddingBottom);
                const overDivDom = document.getElementById(overDomId);
                if (overDivDom) {
                    const overDivHeight = document.body.getBoundingClientRect().height
                        - overDivDom.getBoundingClientRect()?.top;
                    overDivDom?.setAttribute('style', `height: ${overDivHeight + compotedViewPaddingBottom}px`);
                }
                document.getElementById(commentContentScrollContanier)?.scrollBy({
                    top: currentDomTop - commentActiveDomTop
                });
            } else if (commentActiveDomTop - currentDomTop > 3) {
                scrollCommentToIconPos?.run();
            }
        }
    };

    const handleSelectInSearch = (val: string) => {
        if (!val) {
            return;
        }
        setExpanded(true);
        // 当前选中参数key
        const key = dataSourceMap?.pathToNode?.[val]?.key;
        if (key) {
            handleExpandAndScrollTop(key);

            // 定位到该元素后，给其加css高亮样式
            setTimeout(() => {
                highlightSearchRow(key);
            }, 100);

            // 2s后去除高亮样式
            setTimeout(() => {
                try {
                    getSearchCurrentSelectedDom(key)?.classList.remove('search-item');
                } catch (e) { }
            }, 2000);
        }
    };

    const handleScrollContentContainer = (val: string) => {
        // 当前选中参数key
        const key = dataSourceMap?.pathToNode?.[val]?.key;
        setTimeout(() => {
            contentContainerSrcollPos(key);
        }, 100);
        setTimeout(() => {
            contentContainerSrcollPosDiff(key);
        }, 200);
    };

    return {
        expanded,
        setExpanded,
        expandedWithComputer,
        handleSelectInSearch,
        handleScrollContentContainer
    };
}
