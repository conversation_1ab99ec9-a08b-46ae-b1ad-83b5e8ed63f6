/*
 * @Author: han<PERSON><PERSON>
 * @Date: 2024-04-19 13:48:39
 * @LastEditors: hanweiqi
 * @LastEditTime: 2024-09-19 09:47:38
 * @Description: 请填写简介
 */
import React from 'react';
import { Radio } from 'antd';
import { RadioProps } from 'antd/lib/radio';
import { nsHttpApiInterface } from 'src/remote';
import css from './ReqRadioGroupTab.less';

enum EReqRadioType {
    HEADER = 'header',
    QUERY = 'query',
    BODY = 'body',
    ACCOUNT_NUMBER = 'accountNumber'
}

interface IReqRadioGroupTabProps extends RadioProps {
    filterReqRadioType?: EReqRadioType[];
    comType?: string;
    requestParams?: nsHttpApiInterface.IApiRequest;
}

const requestParamsTabOptions = (
    requestParams?: nsHttpApiInterface.IApiRequest,
    filterReqRadioType?: EReqRadioType[],
    comType?: string
) => {
    const headerCount = getConfigCount(requestParams?.header);
    const queryCount = getConfigCount(requestParams?.rest) + getConfigCount(requestParams?.query);
    const options: Array<any> = [
        { 
            label: (<div className={css.title}>
                <span className={css.titleText}>请求头部</span>
                {headerCount ? <span className={css.count}>{headerCount}</span> : null}
            </div>),
            value: EReqRadioType.HEADER
        },
        {
            label: (<div className={css.title}>
                <span className={css.titleText}>请求体</span>
            </div>),
            value: EReqRadioType.BODY
        },
        {
            label: (<div className={css.title}>
                <span className={css.titleText}>参数</span>
                {queryCount ? <span className={css.count}>{queryCount}</span> : null}
            </div>),
            value: EReqRadioType.QUERY
        },
    ];
    const account = {
        label: (
            <div className={css.title}>
                <span className={css.titleText}>账号</span>
            </div>
        ),
        value: EReqRadioType.ACCOUNT_NUMBER
    };
    if (comType === 'apiTest') {
        options.push(account);
    }
    if (filterReqRadioType) {
        return options.filter(item => !filterReqRadioType.includes(item.value));
    }
    return options;
};

function ReqRadioGroupTab({filterReqRadioType, ...props}: IReqRadioGroupTabProps) {
    return (
        <Radio.Group
            options={requestParamsTabOptions(props.requestParams, filterReqRadioType, props.comType)}
            optionType="button"
            {...props}
        />
    );
}

export function getConfigCount(config?: nsHttpApiInterface.IApiParam[]): number {
    if (!config) {
        return 0;
    }
    let count = 0;
    count += config.filter(item => item.N).length;
    for (const item of config) {
        if (item.children) {
            count += getConfigCount(item.children);
        }
    }
    return count;
}

export { ReqRadioGroupTab, EReqRadioType };
