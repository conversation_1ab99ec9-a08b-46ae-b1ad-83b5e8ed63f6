import React, { useRef, forwardRef, useImperativeHandle, Ref, useState, useEffect } from 'react';
import { Radio, message, Segmented } from 'antd';
import { KdevTitle, JsonAceEditor } from '@/business/commonComponents';
import classNames from 'classnames';
import css from './EditApiParamsInfo.less';
import { nsHttpApiInterface, nsMockManageApiManageMainApiJson5CommentParserPOST, nsMockManageApiManageMainApiJson5ParamPrettyPOST } from '@/remote';
import { EditParamTable, IEditParamTableRef, IRecord } from './EditParamTable';
import { ImportJsonModal } from './ImportJson';
import { EApiParam } from './formatParamsTools';
import { BodyTypeRadioTab, EBodyTypeRadioTabType } from './BodyTypeRadioTab';
import { Ace } from 'ace-builds';
import * as commonFun from '@/commonFun';
import json5 from 'json5';

enum EBodyJsonViewTypeEnum {
    JSON = 'JSON',
    FORMAT = 'FORMAT',
}
interface IProps {
    // projectId: number;
    className?: string;
    responseParams?: nsHttpApiInterface.IApiResponse;
    setApiResponse?: (value: any) => void;
    json5Body?: string;
}

interface IEditApiResponseRef {
    getParams(): nsHttpApiInterface.IApiResponse;
    checkParams(): boolean;
    getSourceParams(): nsHttpApiInterface.IApiResponse;
    replaceJsonData(): Promise<boolean>;
    bodyViewType: EBodyJsonViewTypeEnum;
    setResponseJson: (value: string) => void;
}

enum EResRadioType {
    HEADER = 'header',
    QUERY = 'query',
    BODY = 'body'
}

type TResRadioType = EResRadioType.HEADER | EResRadioType.BODY;

const responseParamsTabOptions = [
    { label: '返回头部', value: EResRadioType.HEADER },
    { label: '返回体', value: EResRadioType.BODY }
];

const EditApiResponse = forwardRef((props: IProps, ref: Ref<IEditApiResponseRef>) => {
    const [resRadioType, setResRadioType] = useState<TResRadioType>(EResRadioType.BODY);
    const [bodyType, setBodyType] = useState<nsHttpApiInterface.EResBodyFormat>(nsHttpApiInterface.EResBodyFormat.JSON);
    const [body, setBody] = useState<nsHttpApiInterface.IApiParam[]>([]);
    const [errorLine, setErrorLine] = useState<Ace.Annotation[]>([]);
    const [bodyJsonViewType, setBodyJsonViewType] = useState<string>(EBodyJsonViewTypeEnum.JSON);
    const [responseJson, setResponseJson] = useState<string>('');


    const headerEditParamTableRef = useRef<IEditParamTableRef>(null);
    const bodyEditParamTableRef = useRef<IEditParamTableRef>(null);

    const getParams = (): nsHttpApiInterface.IApiResponse => {
        return {
            header: headerEditParamTableRef.current?.getParams() || [],
            body: bodyEditParamTableRef.current?.getParams() || [],
            bodyType,
            json5Body: responseJson || '',
            bodyJsonViewType: bodyJsonViewType as EBodyJsonViewTypeEnum
        };
    };

    const getSourceParams = () => {
        return {
            header: headerEditParamTableRef.current?.dataSource || [],
            body: bodyEditParamTableRef.current?.dataSource || [],
            bodyType
        };
    };

    // 获取JSON
    const getDataSource = (): IRecord[] => {
        return bodyEditParamTableRef.current?.dataSource || [];
    };

    // 校验参数名是否重复
    const checkParams = (): boolean => {
        const headerRecord = headerEditParamTableRef.current?.checkParams();
        if (headerRecord) {
            message.warn(`返回头部「${headerRecord[EApiParam.NAME]}」参数重复，请修改后再次保存`);
            return true;
        }
        const bodyRecord = bodyEditParamTableRef.current?.checkParams();
        if (bodyRecord) {
            message.warn(`返回结果「${bodyRecord[EApiParam.NAME]}」参数重复，请修改后再次保存`);
            return true;
        }
        return false;
    };

    useImperativeHandle(ref, () => ({
        getParams,
        checkParams,
        getSourceParams,
        replaceJsonData,
        setResponseJson,
        bodyViewType: bodyJsonViewType as EBodyJsonViewTypeEnum
    }));

    // 同步json到格式化数据
    async function replaceJsonData() {
        let jsonStr
        try {
            // jsonStr = await commonFun.formatJsonHasComment(responseJson);
            jsonStr = await nsMockManageApiManageMainApiJson5ParamPrettyPOST.
                remote(responseJson || '{}');
            if (jsonStr.status === 200) {
                jsonStr = jsonStr.data.prettyJson5;
                setResponseJson(jsonStr);
                setErrorLine([]);
            } else {
                throw jsonStr;
            }
            if (!jsonStr) {
                jsonStr = '{}';
            }
            try {
                const dataSource = getDataSource()
                const clearACLData = dataSource.map(item => commonFun.clearACL(item))
                const updatedJsonAndFormat = commonFun.replaceJsonData(json5.parse(jsonStr), setBody, clearACLData);
                const newResponseJson = await nsMockManageApiManageMainApiJson5CommentParserPOST.remote(responseJson || '{}');
                if (newResponseJson.errno !== 200) {
                    throw new Error('json格式化失败');
                }
                const updatedBody = await commonFun.updateBody(newResponseJson, updatedJsonAndFormat);
                if (updatedBody) {
                    setBody(updatedBody)
                };
                return true;
            } catch (e) {
                message.warn('json格式化失败');
                return false;
            }
        } catch (e: any) {
            message.warn('请输入正确的响应体json格式');
            const err = JSON.parse(e.message);
            setErrorLine([{
                row: (e as any).data.errorLineNo - 1,
                text: e.message,
                type: 'error',
                column: 0
            }])
            // setErrorLine(commonFun.analyseFormatJsonError(e));
            return false;
        }
    }

    useEffect(() => {
        if (props.responseParams) {
            setBodyType(props.responseParams.bodyType || nsHttpApiInterface.EResBodyFormat.JSON);
            setBody(props.responseParams.body || []);
            if (props.responseParams?.body.length) {
                setResponseJson(props.json5Body || '')
                // const bodyJosn = commonFun.convertApiParamArrayToJson(props.responseParams?.body[0]);
                // setResponseJson(JSON.stringify(bodyJosn, null, 4))
            }
        }
    }, [props.responseParams]);

    const renderBodyFormat = () => {
        if (resRadioType === EResRadioType.BODY) {
            return (
                <BodyTypeRadioTab
                    type={EBodyTypeRadioTabType.RESPONSE}
                    value={bodyType}
                    onChange={(e) => setBodyType(e.target.value)}
                    // 使用json格式替代json导入
                    // renderJsonModal={() => (
                    //     <ImportJsonModal
                    //         getData={getDataSource}
                    //         setBody={(res) => {
                    //             props.setApiResponse && props.setApiResponse({
                    //                 ...props.responseParams,
                    //                 body: res
                    //             });
                    //             setBody(res);
                    //         }}
                    //         checkJsonError={true}
                    //     />
                    // )}
                    style={{ marginTop: '-8px' }}
                />
            );
        }
    };

    return (
        <div className={classNames(css.editApiResponse, props.className)}>
            <KdevTitle
                text="返回结果"
                size="small"
                className={css.title}
            />
            <Radio.Group
                options={responseParamsTabOptions}
                optionType="button"
                style={{ pointerEvents: 'auto' }}
                value={resRadioType}
                onChange={(e) => setResRadioType(e.target.value)}
                className={css.radioGroupType}
            />
            {renderBodyFormat()}
            <div className={css.row} style={{ display: resRadioType === EResRadioType.HEADER ? 'block' : 'none' }}>
                <EditParamTable
                    type="header"
                    params={props.responseParams?.header || []}
                    ref={headerEditParamTableRef}
                />
            </div>
            <div className={css.row} style={{ display: resRadioType === EResRadioType.BODY ? 'block' : 'none' }}>
                <div className={css.bodyJsonViewTypeRow}>
                    <Segmented
                        value={bodyJsonViewType}
                        style={{ borderRadius: 4 }}
                        options={[
                            {
                                label: 'JSON格式',
                                value: EBodyJsonViewTypeEnum.JSON
                            },
                            {
                                label: '结构化',
                                value: EBodyJsonViewTypeEnum.FORMAT
                            },
                        ]}
                        onChange={async (e) => {
                            if (e === EBodyJsonViewTypeEnum.FORMAT) {
                                try {
                                    // const jsonStr = await commonFun.formatJsonHasComment(responseJson);
                                    const jsonStr = await nsMockManageApiManageMainApiJson5ParamPrettyPOST.
                                        remote(responseJson || '{}');
                                    setResponseJson(jsonStr.data.prettyJson5);
                                    if (!await replaceJsonData()) {
                                        return
                                    };
                                    setBodyJsonViewType(e as EBodyJsonViewTypeEnum);
                                    setErrorLine([]);
                                    // commonFun.replaceJsonData(json5.parse(jsonStr), setBody, getDataSource());
                                } catch (e: any) {
                                    message.warn('请输入正确的json格式');
                                    const err = JSON.parse(e.message);
                                    setErrorLine([{
                                        row: err.errorLineNo - 1,
                                        column: 0,
                                        text: err.errorMsg,
                                        type: 'error'
                                    }])
                                    // setErrorLine(commonFun.analyseFormatJsonError(e));
                                    return;
                                }
                            } else {
                                const formattedJson = await commonFun.formatConvertToJson5(getDataSource());
                                setResponseJson(formattedJson.data);
                                setBodyJsonViewType(e as EBodyJsonViewTypeEnum)
                            }
                        }}
                    />
                </div>
                <JsonAceEditor
                    className={[css.jsonAceEditor, bodyJsonViewType === EBodyJsonViewTypeEnum.JSON ? '' : css.h0]}
                    json={responseJson}
                    setJsonResp={setResponseJson}
                    formatJsonStr={async () => {
                        try {
                            // const jsonStr = await commonFun.formatJsonHasComment(responseJson);
                            const jsonStr = await nsMockManageApiManageMainApiJson5ParamPrettyPOST.
                                remote(responseJson || '{}');
                            if (jsonStr.status === 200) {
                                setResponseJson(jsonStr.data.prettyJson5);
                                replaceJsonData();
                                setErrorLine([]);
                            } else {
                                throw jsonStr;
                            }
                        } catch (e) {
                            message.warn('请输入正确的json格式');
                            const err = JSON.parse(e.message);
                            setErrorLine([{
                                row: (e as any).data.errorLineNo - 1,
                                text: e.message,
                                type: 'error',
                                column: 0
                            }])
                            // setErrorLine(commonFun.analyseFormatJsonError(e));
                        }
                    }}
                    errorLine={errorLine}
                ></JsonAceEditor>
                <EditParamTable
                    style={{
                        display: bodyJsonViewType === EBodyJsonViewTypeEnum.FORMAT ? 'block' : 'none',
                        borderBottom: 'none'
                    }}
                    type="body"
                    params={body}
                    ref={bodyEditParamTableRef}
                    hasRoot
                    queryUpdate={async (newData) => {
                        const bodyJosn = commonFun.convertApiParamArrayToJson(newData[0]);
                        setResponseJson(JSON.stringify(bodyJosn, null, 4));
                    }}
                />
            </div>
        </div>
    );
});

export { EditApiResponse, IEditApiResponseRef };
