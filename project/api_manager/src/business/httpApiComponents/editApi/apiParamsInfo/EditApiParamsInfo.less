@import '~@lynx/design-token/dist/less/token.less';

.upload() {
    .ant-upload {
        height: 28px;
    }

    .ant-upload-list {
        position: relative;
        top: -28px;
    }

    .ant-upload-list-item {
        background: #F5F7FA;
        border: none !important;
        max-width: 369px !important;
        height: 28px !important;
        margin: 0 !important;

        &:hover {
            .ant-upload-list-item-info {
                background: transparent !important;
            }
        }
    }

    .ant-upload:has(+.ant-upload-list:has(.ant-upload-list-text-container)) {
        display: none !important;
    }
}

.editApiRequest,
.editApiResponse {
    .name {
        white-space: nowrap;
        text-overflow: ellipsis;
        overflow: hidden;
        line-height: 28px;
    }

    .title {
        font-weight: 600;
        margin-bottom: 16px;

        &::before {
            border-radius: 1.5px;
        }
    }

    .radioGroupType {
        margin-bottom: 16px;
    }

    .row {
        display: flex;
        margin-bottom: 24px;
        //overflow: hidden;

        .flex1 {
            flex: 1;
        }

        .bodyJsonViewTypeRow {
            box-sizing: border-box;
            padding: 0 12px;
            height: 52px;
            display: flex;
            align-items: center;
            border-top: 1px solid #EBEDF0;
            border-left: 1px solid #EBEDF0;
            border-right: 1px solid #EBEDF0;
            border-radius: 4px 4px 0 0;
        }

        .editParamTable {
            border: 1px solid #EBEDF0;
        }

        .jsonAceEditor {
            border-radius: 0 0 4px 4px !important;

            &.h0 {
                height: 0 !important;
                border: none !important;
            }
        }
    }

    :global {
        .upload()
    }
}

.editParamTable {
    flex: 1;
    //overflow: hidden;
    position: relative;

    .editTable {
        .expandIconBox {
            margin-inline-end: 8px;
            float: left;
            cursor: pointer;
            font-size: 16px;
            width: 16px;
            height: 28px;
            line-height: 28px;
            color: #898A8C;
        }

        .name {
            white-space: nowrap;
            text-overflow: ellipsis;
            overflow: hidden;
            line-height: 28px;
        }
    }

    :global {
        .ant-table-row-indent+.ant-table-row-expand-icon {
            margin-top: 6px;
        }

        .ant-input {
            height: 28px;
        }

        .ant-select-single:not(.ant-select-customize-input) .ant-select-selector {
            height: 28px;
        }

        .ant-select-single .ant-select-selector .ant-select-selection-item,
        .ant-select-single .ant-select-selector .ant-select-selection-placeholder {
            line-height: 26px;
        }

        // 表格样式重写
        .ant-table .ant-table-container .ant-table-tbody .ant-table-cell {
            padding: 8px 0 8px 12px;
        }

        .ant-table .ant-table-container .ant-table-tbody .ant-table-cell-fix-right {
            padding: 8px 0 8px 6px;
        }

        .upload()
    }

    .addBtnContainer {
        border-bottom: 1px solid #EBEDF0;
        padding: 12px;
    }

    .tdContainer {
        display: flex;
    }
}

.uploadBtn,
.expand {
    &:hover {
        color: #326BFB !important;
    }
}

:global {

    @BorderColors: #54D16D,
        #5C8FFF,
        #61C5FF,
        #FFBF29,
        #F757AA,
        #965AE0,
        #FF934F,
        #B7EB46,
        #F757AA;
    @BackgroundColor: #FAFFFA,
        #FAFCFF,
        #FAFCFF,
        #FFFDF0,
        #FFF7FA,
        #FBF6FF,
        #FFFBF6,
        #FAFFF3,
        #FFF7FA;
    @len: length(@BorderColors);

    .loop (@i) when (@i < @len) {
        @borderColor: extract(@BorderColors, @i);
        @background: extract(@BackgroundColor, @i);

        .row-construct-@{i} {
            td:nth-child(1) {
                border-left: 2px solid @borderColor;
            }
        }

        .row-background-@{i} {
            td {
                background: @background !important;
            }
        }

        .construct-header-@{i} {
            border-left: 2px solid @borderColor;
            background: @background !important;
            height: 47px;
            line-height: 47px;
        }

        .loop(@i + 1)
    }

    .loop (1);

    //.for(@data, @i: 1) when(@i =< length(@data)) {
    //    @borderColor: extract(@data, @i);
    //    @background: extract(@BackgroundColor, @i);
    //    .row-construct-@{i} {
    //        td:nth-child(1) {
    //            border-left: 2px solid @borderColor;
    //        }
    //        td {
    //            background: @background!important;
    //        }
    //    }
    //}
    //.for(@BorderColors);

    //.row-construct-1 {
    //    td:nth-child(1) {
    //        border-left: 2px solid #54D16D;
    //    }
    //    td {
    //        background: #FAFFFA!important;
    //    }
    //}
}

.editApiParamsInfo {
    :global {
        span:has(.ant-upload) {
            display: block !important;
            height: 28px;
        }
    }
}

.importJsonModal {
    height: 520px;
    top: 26.8%;

    :global {
        .ant-modal-body {
            height: calc(100% - 104px);
            padding-left: 16px;
            padding-right: 16px;
        }

        .ant-modal-content {
            height: 100%;
        }

        .ant-modal-footer {
            padding: 0 16px 12px;
        }
    }

    .importJsonModalTitle {
        display: flex;
        align-items: center;
        height: 24px;

        .prettifyBtn {
            margin-left: auto;
            margin-right: 32px;
        }
    }

    .jsonExample {
        font-size: 14px;
        color: #898A8C;
        margin-left: 12px;
        line-height: 22px;
    }

    .addDataBtn {
        line-height: 32px;
        float: left;
    }

    .aceEditorWrap {
        height: 100%;
        overflow: hidden;
        border: 1px solid #EBEDF0;
        border-radius: 4px;
        display: flex;
        flex-direction: column;

        :global {
            .ace-xcode .ace_gutter {
                background-color: #FAFCFF;
            }
        }

        .optionsLine {
            display: flex;
            flex-wrap: nowrap;
            align-items: center;
            height: 48px;
            padding-left: 12px;
            border-bottom: 1px solid #EBEDF0;
        }

        .optionWarp {
            height: 40px;
            margin-right: 16px;
            display: flex;
            flex-wrap: nowrap;
            align-items: center;
            cursor: pointer;
        }

        .option {}

        .icon {
            margin-right: 4px;
            color: #898A8C !important;
        }
    }
}