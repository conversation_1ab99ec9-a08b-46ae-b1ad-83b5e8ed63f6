import Fuse from 'fuse.js';
import React, { useState } from 'react';
import { Select, Tooltip } from 'antd';

function search(list: Array<string>, value) {
    console.time('fuzzySearch');
    const options = {
        // includeScore: true,
        shouldSort: true,
        findAllMatches: false,
        minMatchCharLength: 3, // 最小匹配字符长度
    };
    const fuse = new Fuse(list, options);

    const res = fuse.search(value);
    console.timeEnd('fuzzySearch');
    return res;
}

export function FuzzySearch(props: {
    list: Array<string>;
    handleSelectInSearch: (val: string) => void;
    style?: React.CSSProperties;
}) {
    const [value, setValue] = useState<string | undefined>();
    const [option, setOption] = useState<Array<{ value: string; label: string }>>([]);
    const onSearch = (val: string) => {
        const valTrim = val.trim();
        if (valTrim.length > 3) {
            return setOption(search(props.list, val).map(it => ({ value: it.item, label: it.item })));
        }
        return setOption([]);
    };
    return <Select
        style={{ ...(props.style || {}), width: '480px' }}
        showSearch
        value={value}
        placeholder="输入字段名进行搜索，字段直接用.分割；eg: data.status"
        filterOption={false}
        onChange={(val) => setValue(val)}
        onSearch={onSearch}
        onSelect={(val) => props.handleSelectInSearch(val)}
        allowClear
    // virtual={false}
    >
        {option.map(it => <Select.Option
            value={it.value}
            key={it.value}
        ><Tooltip title={it.label}><div>{it.label}</div></Tooltip></Select.Option>)}
    </Select>;
}
