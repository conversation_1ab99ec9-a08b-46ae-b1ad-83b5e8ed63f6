import React, { HTMLAttributes, useImperativeHandle, forwardRef, useRef, Ref, useEffect, useState, useCallback } from 'react';

import './VirtualTable.less';
import {
    ExpandedState,
    useReactTable,
    getCoreRowModel,
    getExpandedRowModel,
    ColumnDef,
    flexRender,
    HeaderContext,
    OnChangeFn,
    RowModel,
    Row,
    ColumnResizeMode,
    Header,
    ColumnPinningState
} from '@tanstack/react-table';
// @ts-ignore
import { Empty, Table } from 'antd';
import { useVirtual, VirtualItem } from 'react-virtual';
import { useScroll, useSize } from 'ahooks';

type ScrollAlignment = 'start' | 'center' | 'end' | 'auto';

interface ScrollToOptions {
    align: ScrollAlignment;
}

interface Props<T> {
    data: Array<T>;
    columns: ColumnDef<T, any>[];
    columnsCanResize?: boolean;
    tempVirtualTableId?: string;
    /**
     * 传入columns的每一项cell组件内，可以在cell调用外部函数
     */
    extraParamsInCell?: object;
    rowClassName?: (record: T) => string;
    getSubRows?: (record: T) => string | undefined;
    getRowId?: (record: T) => string;
    updateData?: () => void;
    columnPinning?: ColumnPinningState; // 固定列配置，目前只支持right，eg：{ left: ['column1', 'column2'], right: ['column3'] }
    handleMouseEnter?: (e: React.MouseEvent<HTMLTableRowElement>, record: T) => void;
    handleMouseLeave?: (e: React.MouseEvent<HTMLTableRowElement>, record: T) => void;
    expanded?: boolean | Record<string, boolean>;
    setExpanded?: OnChangeFn<ExpandedState>;
    height?: string;
    id?: number;
    emptyDom?: React.ReactNode;
}
export interface VirtualTableRef<T> {
    rows: Row<T>[];
    rowVisualizer: {
        virtualItems: VirtualItem[]
        totalSize: number
        scrollToOffset: (index: number, options?: ScrollToOptions) => void
        scrollToIndex: (index: number, options?: ScrollToOptions) => void
        measure: () => void
    };
    tableContainerRef: Ref<HTMLDivElement | null>;
}

function TableRow({ rows, virtualRow, props }) {
    const row = rows[virtualRow.index];
    const [isHover, setIsHover] = useState<boolean>(false);
    return (
        <tr
            key={row.id}
            onMouseEnter={e => {
                props.handleMouseEnter?.(e, row.original);
                setIsHover(true);
            }}
            onMouseLeave={e => {
                props.handleMouseLeave?.(e, row.original);
                setIsHover(false);
            }}
            className={props.rowClassName?.(row.original)}
        >
            {row.getVisibleCells().map(cell => {
                const sty: React.CSSProperties = cell?.column?.getIsPinned()
                    ? { position: 'sticky', right: 0, zIndex: 9 }
                    : {};
                return (
                    <td
                        key={cell.id}
                        style={sty}
                        className="__virtual-table-cell"
                    >
                        {flexRender(
                            cell.column.columnDef.cell,
                            {
                                ...cell.getContext(),
                                ...(props.extraParamsInCell || {}),
                                virtualRow: virtualRow,
                                isHoverRow: isHover
                            }
                        )}
                    </td>
                );
            })}
        </tr>
    );
}

function VirtualTableInner<T>(props: Props<T>, ref: Ref<VirtualTableRef<T>>) {
    const table = useReactTable<T>({
        data: props.data,
        columns: props.columns,
        // 列宽变换触发时机 实时变换-onChange & 拖拽结束变换-onEnd
        columnResizeMode: 'onEnd',
        state: {
            expanded: props.expanded as any,
            columnPinning: {
                left: [],
                ...props.columnPinning
            }
        },
        onExpandedChange: props.setExpanded,
        /**
         * 获取子元素，可以自定义子元素的属性名；
         */
        // @ts-ignore
        getSubRows: props.getSubRows ?? (row => row.children),
        getCoreRowModel: getCoreRowModel(),
        getExpandedRowModel: getExpandedRowModel(),
        debugTable: true,
        // @ts-ignore
        getRowId: props.getRowId ?? (originalRow => originalRow.key),
        // 挂载在table实例上
        meta: {
            /**
             * 当编辑后，提供callback，更新外部数据
             */
            updateData: (row, value) => {
                props.updateData?.();
            },
        }
    });
    const tableWrapRef = useRef<HTMLDivElement>(null);
    // 获取行数据
    const { rows } = table.getRowModel();

    const estimateSize = useCallback(() => 45, []);

    // body容器
    const tableContainerRef = React.useRef<HTMLDivElement>(null);
    const tableContainerRefScroll = useScroll(tableContainerRef);
    // const tableContainerRefSize = useSize(tableContainerRef);
    const rowVisualizer = useVirtual({
        parentRef: tableContainerRef,
        size: rows.length,
        overscan: 10,
        estimateSize: estimateSize
    });
    const { virtualItems: virtualRows, totalSize } = rowVisualizer;

    // 设置最外层 className
    const getTableClassName = () => {
        const className = '__virtual-table-tree';
        // if (tableContainerRefScroll?.left !== 0) {
        //     className += ' __virtual-table-has-fix-left'; // 左侧固定列
        // }
        // if (((tableContainerRefScroll?.left || 0) + (tableContainerRefSize?.width || 0)) < table.getTotalSize()) {
        //     className += ' __virtual-table-has-fix-right'; // 右侧固定列
        // }
        return className;
    };

    useImperativeHandle(ref, () => ({
        rowVisualizer,
        rows,
        tableContainerRef
    }), [rowVisualizer, rows]);

    // 根据外部column的size定义去计算列宽
    const colgroupNode = () => {
        const tableWidth = tableWrapRef.current?.getBoundingClientRect?.().width || 0;
        // 最后一列剩余宽度计算
        let lastSurplusWidth = tableWidth;
        return (
            <colgroup style={{ width: table.getTotalSize() }}>
                {
                    table.getAllColumns().map((item, index) => {
                        const columnSizing = table.getState().columnSizing;
                        const { size, minSize, maxSize } = item.columnDef;
                        const width = columnSizing[item.id] ?? size;
                        if (index < props.columns.length - 1) {
                            if (minSize && minSize > columnSizing[item.id]) {
                                lastSurplusWidth -= minSize;
                            } else {
                                lastSurplusWidth -= width;
                            }
                        }
                        return (
                            <col
                                key={item.id}
                                style={{
                                    width: (Object.keys(columnSizing).length && index === props.columns.length - 1)
                                        ? lastSurplusWidth > 0
                                            ? lastSurplusWidth
                                            : width
                                        : width,
                                    minWidth: minSize,
                                    maxWidth: maxSize
                                }}
                            />
                        );
                    })
                }
            </colgroup>
        );
    };

    // 渲染表格header th内容
    const renderTableHeaderTh = (header: Header<T, unknown>) => {
        const sty: React.CSSProperties = header?.column?.getIsPinned()
            ? { position: 'sticky', right: 0, zIndex: 10 }
            : {};
        return (
            <th
                className="__virtual-table-cell"
                key={header.id}
                style={sty}
                colSpan={header.colSpan}
            >
                {
                    !header.isPlaceholder &&
                    <div>
                        {flexRender(
                            header.column.columnDef.header,
                            header.getContext()
                        )}
                        {
                            props.columnsCanResize && (props.columns.length - 1) !== header.index &&
                            <div
                                className="__virtual-table-column-resizer"
                                onDoubleClick={() => header.column.resetSize()}
                                onMouseDown={header.getResizeHandler()}
                                onTouchStart={header.getResizeHandler()}
                                style={{
                                    transform: header.column.getIsResizing()
                                        ? `translateX(${table.getState().columnSizingInfo.deltaOffset ?? 0}px)`
                                        : '',
                                }}
                            />
                        }
                    </div>
                }
            </th>
        );
    };

    // 渲染表格header
    const renderTableHeader = () => {
        return (
            <thead className="__virtual-table-thead">
                {
                    table.options.columns &&
                    table.getHeaderGroups()?.map((headerGroup) => (
                        <tr key={headerGroup.id}>
                            {headerGroup.headers.map(renderTableHeaderTh)}
                        </tr>
                    ))
                }
            </thead>
        );
    };

    // 渲染表格body
    const renderTableBody = () => {
        // 计算虚拟滚动距离上下的padding值
        const paddingTop = virtualRows.length > 0 ? virtualRows?.[0]?.start || 0 : 0;
        const paddingBottom =
            virtualRows.length > 0
                ? totalSize - (virtualRows?.[virtualRows.length - 1]?.end || 0)
                : 0;
        return (
            <tbody className="__virtual-table-tbody">
                {paddingTop > 0 && (
                    <tr>
                        <td style={{ height: `${paddingTop}px` }} />
                    </tr>
                )}
                {
                    table.options.columns &&
                    virtualRows.map((virtualRow) => {
                        return (
                            <TableRow
                                key={virtualRow.key}
                                rows={rows}
                                virtualRow={virtualRow}
                                props={props}
                            />
                        );
                    })
                }
                {paddingBottom > 0 && (
                    <tr>
                        <td style={{ height: `${paddingBottom}px` }} />
                    </tr>
                )}
            </tbody>
        );
    };

    /**
     * 目前样式fork的antd的
     */
    return (
        <div className={getTableClassName()} ref={tableWrapRef} id={props.id?.toString()}>
            <div className="__virtual-table-header">
                <table style={{ tableLayout: 'fixed', marginLeft: `-${tableContainerRefScroll?.left}px` }}>
                    {colgroupNode()}
                    {renderTableHeader()}
                </table>
            </div>
            <div
                ref={tableContainerRef}
                className="__virtual-table-body"
                style={{ maxHeight: props.height || '650px', overflow: 'auto' }}
                id={props.tempVirtualTableId}
            >
                {
                    !table.options?.data?.length
                        ? props.emptyDom === undefined ? <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} /> : props.emptyDom
                        : <table style={{ tableLayout: 'fixed' }}>
                            {colgroupNode()}
                            {renderTableBody()}
                        </table>
                }
            </div>
        </div>
    );
}
export const VirtualTable = forwardRef(VirtualTableInner);
export function withVirtualTable<T = any>() {
    return forwardRef<VirtualTableRef<T>, Props<T>>(VirtualTableInner);
}
