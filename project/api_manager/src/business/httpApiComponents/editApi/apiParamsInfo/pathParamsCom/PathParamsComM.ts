/*
 * @Author: han<PERSON><PERSON>
 * @Date: 2024-06-16 13:29:16
 * @LastEditors: hanweiqi
 * @LastEditTime: 2024-09-24 11:49:18
 * @Description: Path 参数组件 Model
 */
import { action, observable } from 'mobx';

export class PathParamsComM {
    @observable public tableData: Array<any> = [];
    @observable public isShowPath: boolean = false;

    /**
     * FUNC_DES: 说明
     */
    @action.bound
    public setD(id: number, val: string): void {
        const findItem = this.tableData.find(item => item.I === id);
        if (findItem) {
            findItem.D = val;
            this.tableData = this.tableData.slice();
        }
    }

    /**
     * FUNC_DES: 参数值
     */
    @action.bound
    public setV(id: number, val: string): void {
        const findItem = this.tableData.find(item => item.I === id);
        if (findItem) {
            findItem.V = val;
            this.tableData = this.tableData.slice();
        }
    }

    /**
     * FUNC_DES: path 参数 展开 or 收起
     */
    @action.bound
    public setIsShowPath(): void {
        this.isShowPath = !this.isShowPath;
    }

    /**
     * FUNC_DES: 参数
     */
    @action.bound
    public params(): Array<any> {
        this.tableData?.map(item => delete item.I);
        return this.tableData;
    }

    /**
     * FUNC_DES: 回显参数
     */
    @action.bound
    public resDataToParams(data: Array<any>): void {
        const arr = data?.reduce((pre, cur) => {
            const obj = {
                N: cur.N,
                V: cur.V,
                D: cur.D,
                T: 3,
                R: 1,
            };
            pre.push(obj);
            return pre;
        }, []);
        if (arr.length === 0) {
            this.isShowPath = false;
        }
        this.tableData = arr;
        if (this.tableData?.length) {
            this.isShowPath = true;
        }
    }
}
