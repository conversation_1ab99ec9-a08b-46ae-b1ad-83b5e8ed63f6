/*
 * @Author: han<PERSON><PERSON>
 * @Date: 2024-06-16 13:28:54
 * @LastEditors: hanweiqi
 * @LastEditTime: 2024-09-24 11:58:17
 * @Description: Path 参数组件
 */
import React from 'react';
import { observer } from 'mobx-react';
import { Table, Input } from 'antd';
import { Bind } from 'lodash-decorators';
import { AView } from '@libs/mvvm';
import { common_system_arrowxia, common_system_jiantouyou } from '@kid/enterprise-icon/icon/output/icons';
import { KdevIconFont } from '@/business/commonComponents';
import { PathParamsComM } from './PathParamsComM';
import css from './PathParamsCom.less';

@observer
export class PathParamsCom extends AView<PathParamsComM> {

    private columns = (): Array<any> => [
        {
            title: '参数名',
            dataIndex: 'N'
        },
        {
            title: '类型',
            dataIndex: 'T',
            render: () => <span>[string]</span>
        },
        {
            title: '必填',
            dataIndex: 'R',
            render: () => <span>是</span>
        },
        {
            title: '说明',
            dataIndex: 'D',
            render: (val: string, record: any) => this.renderColumnsD(val, record)
        },
        {
            title: '参数值',
            dataIndex: 'V',
            render: (val: string, record: any) => this.renderColumnsV(val, record)
        }
    ]

    /**
     * FUNC_DES: Columns - 说明
     */
    @Bind()
    private renderColumnsD(val: string, record: any): React.ReactNode {
        const setD = (evt: React.ChangeEvent<HTMLInputElement>) => {
            this.model.setD(record.I, evt.target.value);
        };

        return (
            <Input
                placeholder="请填写参数说明"
                value={val}
                onChange={setD}
            />
        );
    }

    /**
     * FUNC_DES: Columns - 参数值
     */
    @Bind()
    private renderColumnsV(val: string, record: any): React.ReactNode {
        const setV = (evt: React.ChangeEvent<HTMLInputElement>) => {
            this.model.setV(record.I, evt.target.value);
        };

        return (
            <Input
                placeholder="请填写参数值"
                value={val}
                onChange={setV}
            />
        );
    }

    /**
     * FUNC_DES: 标题部分
     */
    @Bind()
    private renderTitle(): React.ReactNode {
        const { isShowPath, setIsShowPath } = this.model;
        const iconId = isShowPath ? common_system_arrowxia : common_system_jiantouyou;
        return (
            <div className={css.title}>
                <span>path 参数</span>
                <span
                    className={css.pathIcon}
                    onClick={setIsShowPath}
                >
                    <KdevIconFont id={iconId} />
                </span>
            </div>
        );
    }

    /**
     * FUNC_DES: 表格部分
     */
    @Bind()
    private renderTable(): React.ReactNode {
        this.model.tableData?.map((item, index) => item.I = index);
        return (
            <Table
                rowKey="I"
                columns={this.columns()}
                dataSource={this.model.tableData}
                pagination={false}
            />
        );
    }

    public render(): React.ReactNode {
        return (
            <div className={css.pathParamsCom}>
                {/* 标题部分 */}
                {this.renderTitle()}
                {/* 表格部分 */}
                {this.model.isShowPath && this.renderTable()}
            </div>
        );
    }
}
