:global {
  .search-item td {
    background-color: #FFFBE6 !important;
  }

  .__virtual-table-tree {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
    color: rgba(0, 0, 0, 0.85);
    font-variant: tabular-nums;
    line-height: 1.5715;
    list-style: none;
    font-feature-settings: 'tnum';
    position: relative;
    z-index: 0;
    clear: both;
    font-size: 14px;
    //background: #fff;
    border-radius: 2px;
    overflow: hidden;

    table {
      width: 100%;
      text-align: left;
      border-radius: 2px 2px 0 0;
      border-collapse: separate;
      border-spacing: 0;
    }

    thead>tr>th,
    tbody>tr>td {
      position: relative;
      overflow-wrap: break-word;
    }

    .__virtual-table-thead .__virtual-table-cell,
    .__virtual-table-cell {
      background: #F5F7FA;
      border-color: #F0F2F5;
      font-size: 14px;
      line-height: 22px;
      height: 22px;
      padding: 9px 12px;
      //font-weight: bold;
      color: #252626;
    }

    .__virtual-table-cell-sticky {
      position: sticky;
      z-index: 10;
      right: 0;
    }

    .__virtual-table-thead .__virtual-table-cell .__virtual-table-column-resizer {
      position: absolute;
      top: 0;
      right: 0;
      height: 100%;
      width: 5px;
      // background: #D5D6D9;
      cursor: col-resize;
      user-select: none;
      touch-action: none;
      z-index: 11;
      opacity: 0;
      border-right: 1px solid #D5D6D9;

      &:hover {
        border-right-color: #326BFB;
        border-right-width: 2px;
        height: 100vh;
      }
    }

    .__virtual-table-thead:hover {
      .__virtual-table-cell .__virtual-table-column-resizer {
        opacity: 1;
      }
    }

    // @media (hover: hover) {
    //   .__virtual-table-column-resizer {
    //     opacity: 0;
    //   }

    //   *:hover>.__virtual-table-column-resizer {
    //     opacity: 1;
    //   }
    // }

    table>thead>tr:first-child th:first-child {
      border-top-left-radius: 2px;
    }

    .virtual-table-thead>tr>th {
      border-bottom: 1px solid #EBEDF0;
      text-align: left;
    }

    .__virtual-table-body .__virtual-table-cell {
      padding: 8px 0 8px 12px;
      height: 22px;
      background-color: #FFFFFF;
    }

    .__virtual-table-tbody>tr {
      &:hover {
        >td {
          background: #fafafa;
        }
      }

      >td {
        border-bottom: 1px solid #EBEDF0;
        transition: background 0.3s;
        overflow-wrap: break-word;
        position: relative;
      }
    }

    .expand-icon-wrap {
      margin-inline-end: 8px;
      float: left;
      cursor: pointer;
      font-size: 16px;
      flex: none;
      width: 16px;
      height: 28px;
      line-height: 28px;
      color: #898A8C;
    }

    .rotate270 {
      transform: rotate(270deg);
    }
  }
}