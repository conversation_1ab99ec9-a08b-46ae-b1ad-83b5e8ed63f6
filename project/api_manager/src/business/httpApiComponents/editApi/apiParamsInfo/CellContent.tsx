import React, { useContext, useState, useRef, useEffect } from 'react';
import { KdevIconFont } from '@/business/commonComponents';
import {
    common_system_add,
    common_system_add03,
    common_system_arrowdownmian,
    common_system_delete02,
    common_system_upload
} from '@kid/enterprise-icon/icon/output/icons';

import { IRecord } from '@/business/httpApiComponents/editApi/apiParamsInfo/EditParamTable';
import { CellContext } from '@tanstack/table-core/src/core/cell';
import PopoverEllipsis from '@/business/common/PopoverEllipsis';
import css from '@/business/httpApiComponents/editApi/apiParamsInfo/EditApiParamsInfo.less';
import {
    ParamTypeSelect, RenderDescription,
    RenderName, RenderRequired, RenderValue,
} from '@/business/httpApiComponents/editApi/apiParamsInfo/EditParamTableColumns';
import {
    DataConstructPopover
} from '@/business/httpApiComponents/editApi/dataConstruct/DataConstructPopover';
import { EditApiContext, IEditApiContextProps } from '@/pages/httpApi/editApi/EditApi';
import { Button, Tooltip, Checkbox, Upload, message } from 'antd';
import type { CheckboxChangeEvent } from 'antd/es/checkbox';
import { EApiParam, EParamType } from '@/business/httpApiComponents/editApi/apiParamsInfo/formatParamsTools';
import {
    nsMockManageApiManageMainApiVerificationRuleGet,
} from '@/remote';

export function ExpandComps(props: {
    row: any;
    hasChildren: boolean;
    children: React.ReactNode;
}) {
    const { row } = props;
    if (!props.hasChildren) {
        return <>{props.children}</>;
    }
    return <div
        style={{
            // Since rows are flattened by default,we can use the row.depth property and paddingLeft to visually indicate the depth of the row
            paddingLeft: `${row.depth * 16}px`,
            display: 'flex'
        }}
    >
        {
            row.getCanExpand()
                ? <span onClick={() => {
                    // setExpand((pre) => !pre);
                    row.getToggleExpandedHandler()();
                }} className="expand-icon-wrap">
                    <KdevIconFont className={row.getIsExpanded() ? '' : 'rotate270'} id={common_system_arrowdownmian} />
                </span>
                : <span className="expand-icon-wrap" />
        }
        {
            props.children
        }
    </div>;
}
export function RenderNameComps(props: CellContext<IRecord, any>) {
    const {
        row,
        getValue,
        column: { id },
        table,
        dataSourceMap,
        focusKey,
        hasRoot,
        virtualRow,
        onAddParam,
        onChange
    } = props;
    const record = row.original;
    // const [expand, setExpand] = useState(row.getIsExpanded());
    const isGroup = !!dataSourceMap.keyToNode[record.key]?.groups?.length;
    const [isChange, setIsChange] = useState(false);
    // 判断是否禁用
    const getDisabled = () => {
        // 当表格type为body时，最外层的第一项name禁用编辑
        if (hasRoot && !record.parent && row.index === 0) {
            record[EApiParam.NAME] = 'root'; // 设置默认值
            return true;
        }
        // 当父级为array时，禁用name编辑
        if (record.parent) {
            return record.parent[EApiParam.TYPE] === EParamType.array;
        }
    };
    const handleMouseLeave = () => {
        if (isChange) {
            table.options.meta?.updateData();
            setIsChange(false);
        }
    };
    // 自动添加一行
    const autoAddRow = (nameValue: string) => {
        const notParamType = [EParamType.array, EParamType.object];  // object | array 类型不需要 自动增加一行
        const isAddParamType = !notParamType.includes(record[EApiParam.TYPE]);  // 根据类型判断是否可增加一行
        const isLastRow = hasRoot
            ? row.index === record.parent?.children.length - 1
            : row.index === table.options.data.length - 1;
        if (isLastRow && isAddParamType && nameValue) {
            // const newItemIndex: number = virtualRow.end + (row.subRows?.length || 0) * 45;
            onAddParam(row.index, record.parent);
        }
    };
    const hasChildren = dataSourceMap.firstFloorNodeKey.length > 0;
    return (
        <ExpandComps row={row} hasChildren={hasChildren}>
            {
                isGroup
                    ? <PopoverEllipsis title={record[EApiParam.NAME]}>
                        <div className={css.name}>{record[EApiParam.NAME]}</div>
                    </PopoverEllipsis>
                    : <RenderName
                        {...record}
                        rowKey={record.key}
                        index={row.index}
                        focusKey={focusKey}
                        handleMouseLeave={handleMouseLeave}
                        onChange={(e) => {
                            record[EApiParam.NAME] = e.target.value;
                            setIsChange(true);
                        }}
                        onInput={(e: any) => {
                            onChange && onChange();
                            autoAddRow(e.target.value);
                        }}
                        disabled={getDisabled()}
                    />
            }
        </ExpandComps>
    );
}

export function RenderTypeCell(props: CellContext<IRecord, any>, disabledType?: number[]) {
    const {
        row, getValue,
        column: { id },
        table,
        dataSourceMap,
        isEditDataConstruct,
        onOpenEditDataConstructDrawer,
        onSelectedDataConstruct,
        onChangeType,
        paramTypeMap
    } = props;
    const record = row.original;
    const editApiContext = useContext<IEditApiContextProps>(EditApiContext);
    if (!!dataSourceMap.keyToNode[record.key]?.groups?.length) {
        return `[${paramTypeMap?.get(record[EApiParam.TYPE])}]`;
    }
    const useDataConstruct = !record[EApiParam.NAME] && props.type === 'body';
    if (useDataConstruct) {
        return <DataConstructPopover
            projectId={editApiContext.projectId}
            groupId={editApiContext.groupId!}
            onCreateDataConstruct={
                isEditDataConstruct
                    ? () => onOpenEditDataConstructDrawer(record)
                    : undefined
            }
            onSelectedDataConstruct={(body) => onSelectedDataConstruct(body, record)}
            data={record}
        />;
    } else {
        return <ParamTypeSelect
            {...record}
            disabledType={disabledType}
            onChange={(val) => {
                onChangeType(val, record);
                table.options.meta?.updateData();
            }}
        />;
    }
}

export function RenderCheckCell(props: CellContext<IRecord, any>) {
    const {
        row, getValue,
        column: { id },
        table,
        dataSourceMap,
        isEditDataConstruct,
        onOpenEditDataConstructDrawer,
        onSelectedDataConstruct,
        onChangeType,
        paramTypeMap
    } = props;
    const record = row.original;
    return (<Checkbox
        checked={record[EApiParam.CHECK] !== false}
        onChange={(e: CheckboxChangeEvent) => {
            record[EApiParam.CHECK] = e.target.checked;
            table.options.meta?.updateData();
        }} defaultChecked={true} />)
}

export function RenderRequiredCell(props: CellContext<IRecord, any>) {
    const {
        row, getValue,
        column: { id },
        table,
        dataSourceMap,
    } = props;
    const [record, setRecord] = useState(row.original);
    useEffect(() => {
        setRecord(row.original)
    }, [props])
    if (!record[EApiParam.NAME]) {
        return null;
    }
    if (!!dataSourceMap.keyToNode[record.key]?.groups?.length) {
        return record[EApiParam.REQUIRED] ? '是' : '否';
    }
    return <RenderRequired
        {...record}
        onChange={(e) => {
            row.original[EApiParam.REQUIRED] = e.target.checked ? 1 : 0;
            setRecord({
                ...record,
                [EApiParam.REQUIRED]: e.target.checked ? 1 : 0
            })
        }}
    />;
}

export function RenderDescriptionCell(props: CellContext<IRecord, any>) {
    const {
        row, getValue,
        column: { id },
        table,
        dataSourceMap,
    } = props;
    const record = row.original;
    if (!record[EApiParam.NAME]) {
        return null;
    }
    if (!!dataSourceMap.keyToNode[record.key]?.groups?.length) {
        return <Tooltip title={record[EApiParam.DESC]}>
            <div style={{ overflow: 'hidden', textOverflow: 'ellipsis', whiteSpace: 'nowrap' }}>
                {record[EApiParam.DESC] || null}
            </div>
        </Tooltip>;
    }
    return <RenderDescription
        {...record}
        index={row.index}
        onChange={(e) => record[EApiParam.DESC] = e.target.value}
    />;
}

export function RenderValueCell(props: CellContext<IRecord, any>) {
    const uploadRef = useRef<any>(null);
    const {
        row, getValue,
        column: { id },
        table,
        dataSourceMap,
    } = props;
    const [isChange, setIsChange] = useState<boolean>(false);
    const record = row.original;

    const handleMouseLeave = () => {
        if (isChange) {
            table.options.meta?.updateData();
            setIsChange(false);
        }
    };

    if (!record[EApiParam.NAME]) {
        return null;
    }
    if (record[EApiParam.TYPE] === nsMockManageApiManageMainApiVerificationRuleGet.Type.file) {
        return (
            <Upload
                ref={uploadRef}
                name="file"
                beforeUpload={(file) => {
                    if (file.size > 1024 * 1000 * 200) {
                        message.warn('您选择的上传的文件过大，请选择200M以内文件！');
                        return false;
                    }
                    return true;
                }}
                action="/api/mock/manage/apiManage/main/api/upload/api/file"
                onChange={(info) => {
                    if (info.file.status === 'done') {
                        record[EApiParam.VALUE] = info.file.response.data.downUrl;
                        setIsChange(true);
                    }
                }}
                onRemove={() => {
                    record[EApiParam.VALUE] = '';
                    setIsChange(true);
                }}
                defaultFileList={record[EApiParam.VALUE] ? [{
                    uid: '1',
                    name: record[EApiParam.VALUE].split('/').pop() || '',
                    status: 'done',
                    url: record[EApiParam.VALUE],
                }] : []}
            >
                <Button size="small" icon={<KdevIconFont id={common_system_upload} />} >&nbsp;请选择文件</Button>
            </Upload >
        );
    }
    if (!!dataSourceMap.keyToNode[record.key]?.groups?.length) {
        return (
            <Tooltip title={record[EApiParam.VALUE]}>
                <div style={{ overflow: 'hidden', textOverflow: 'ellipsis', whiteSpace: 'nowrap' }}>
                    {record[EApiParam.VALUE] || null}
                </div>
            </Tooltip>
        );
    }
    return <RenderValue
        {...record}
        index={row.index}
        handleMouseLeave={handleMouseLeave}
        onChange={(e) => {
            record[EApiParam.VALUE] = e.target.value;
            setIsChange(true);
        }}
    />;
}

export function RenderOperateCell(props: CellContext<IRecord, any>) {
    const {
        row, getValue,
        column,
        table,
        dataSourceMap,
        onAddParam,
        type,
        onAddChildParam,
        onRemoveParam,
        hasRoot,
        virtualRow,
    } = props;
    const record = row.original;
    if (!!dataSourceMap.keyToNode[record.key]?.groups?.length) {
        return [];
    }
    // 判断是否能够移除以及添加同级操作
    const getAllowRemoveAndAdd = () => {
        // 当表格type为body时，最外层的第一项禁止移除以及添加同级操作
        if (hasRoot && !record.parent && row.index === 0) {
            return false;
        }
        return true;
    };
    return <div style={{ marginLeft: -6 }}>
        {
            getAllowRemoveAndAdd() &&
            <Tooltip title="向下插入一行">
                <Button
                    icon={<KdevIconFont id={common_system_add} />}
                    type="text"
                    size="small"
                    onClick={() => {
                        const newItemIndex: number = virtualRow.end + (row.subRows?.length || 0) * 45;
                        onAddParam(row.index, record.parent, newItemIndex);
                    }}
                />
            </Tooltip>
        }
        {
            type === 'body' &&
            <Tooltip title="添加子参数">
                <Button
                    icon={<KdevIconFont id={common_system_add03} />}
                    type="text"
                    size="small"
                    onClick={() => {
                        onAddChildParam(record);
                        row.toggleExpanded(true);
                    }}
                />
            </Tooltip>
        }
        {
            getAllowRemoveAndAdd() &&
            <Tooltip title="删除">
                <Button
                    icon={<KdevIconFont id={common_system_delete02} />}
                    type="text"
                    size="small"
                    onClick={() => {
                        onRemoveParam(row.index, record.parent)
                        table.options.meta?.updateData();
                    }}
                />
            </Tooltip>
        }
    </div>;
}
