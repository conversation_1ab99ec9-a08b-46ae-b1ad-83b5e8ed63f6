import React, { useEffect, useState, forwardRef, useImperativeHandle, Ref, useRef, useContext } from 'react';
import { KdevIconFont } from '@/business/commonComponents';
import classNames from 'classnames';
import css from './EditApiParamsInfo.less';
import { Button, Modal, Tooltip, Checkbox } from 'antd';
import {
    nsHttpApiInterface, nsMockManageApiManageMainStructureDevVersionPOST,
    nsMockManageApiManageMainApiParamTypeListGET
} from '@/remote';
import { common_system_add, common_system_upfold, common_system_underfold } from '@kid/enterprise-icon/icon/output/icons';
import { EditApiContext, IEditApiContextProps } from '@/pages/httpApi/editApi/EditApi';
import { DataConstructTableTitle } from '@/business/httpApiComponents//editApi/dataConstruct/DataConstructTableTitle';
import {
    hasAncestorWithClass, formatEditParam, getFormatParams, createParam, checkEditParams,
    findIndexDataConstruct, getRefKey, removeDataConstruct, IRefInfo, replaceDataConstruct, EApiParam, ERefInfo
} from './formatParamsTools';
import {
    EditDataConstructDrawer, IEditDataConstructDrawerRef
} from '@/business/httpApiComponents/editApi/dataConstruct/EditDataConstructDrawer';
import {
    // VirtualTable,
    VirtualTableRef,
    withVirtualTable
} from '@/business/httpApiComponents/editApi/apiParamsInfo/VirtualTable';
import { ColumnDef } from '@tanstack/react-table';
import {
    RenderNameComps,
    RenderTypeCell,
    RenderRequiredCell,
    RenderDescriptionCell,
    RenderValueCell,
    RenderOperateCell,
    RenderCheckCell
} from '@/business/httpApiComponents/editApi/apiParamsInfo/CellContent';
import { useGetState } from 'ahooks';
import { v4 as uuidv4 } from 'uuid';
import {
    useFlatTree,
    useExpand,
} from '@/business/httpApiComponents/editApi/apiParamsInfo/useFlatTree';
import { FuzzySearch } from './fuzzy-search';

const EParamType = nsMockManageApiManageMainApiParamTypeListGET.EParamType;
type TParamType = 'header' | 'query' | 'form' | 'body';

interface IProps {
    type: TParamType;
    params: nsHttpApiInterface.IApiParam[];
    isEditDataConstruct?: boolean;
    className?: string;
    hasRoot?: boolean;
    queryUpdate?(dataSource: IRecord[]): void;
    dataChange?: () => void;
    filterColumns?: { accessorKey: string }[],
    isCheckbox?: boolean;
    disabledType?: number[];
    style?: React.CSSProperties;
}

export interface IRecord extends nsHttpApiInterface.IApiParam {
    key?: string;
    parent?: IRecord;
    children?: IRecord[];
}

interface IEditParamTableRef {
    getParams(): nsHttpApiInterface.IApiParam[];
    checkParams(): IRecord | undefined; // 校验参数，有值代表有重复值，undefined代表无参数重复
    dataSource: IRecord[];
}

export interface RecordAddition extends IRecord {
    groups?: Array<string>;
    colorNo?: number;
    parent: RecordAddition;
    path: string;
}

const ConstructRelativeHeader = 'ConstructRelativeHeader';

const VirtualTable = withVirtualTable<IRecord>();
const EditParamTable = forwardRef(({ isEditDataConstruct = true, ...props }: IProps, ref: Ref<IEditParamTableRef>) => {
    const editApiContext = useContext<IEditApiContextProps>(EditApiContext);
    const editDataConstructDrawerRef = useRef<IEditDataConstructDrawerRef>(null);
    const [dataSource, setDataSource, getDataSource] = useGetState<IRecord[]>([]);
    const [expandedKeys, setExpandedKeys] = useState<string[]>([]);
    const [paramTypeMap, setParamTypeMap] = useState<Map<number, string>>();
    const [focusKey, setFocusKey] = useState<string>('');
    const [isRequired, setIsRequired] = useState<boolean>(true);
    const tempVirtualTableId = useRef(`Temp_Virtual_Table_Id_${uuidv4()}`);
    /**
     * 当前聚焦group id, 用于做group的浮窗展示
     */
    const [focusConstruct, setFocusConstruct] = useState<string | undefined>();
    const popoverRef = useRef<HTMLDivElement>(null);

    const virtualTableRef = useRef<VirtualTableRef<IRecord>>(null);

    const { dataSourceMap } = useFlatTree({ list: dataSource });

    let scrollTimeout;

    const getParams = () => {
        return getFormatParams(dataSource);
    };

    // 校验参数，有值代表有重复值，undefined代表无参数重复
    const checkParams = (): IRecord | undefined => {
        return checkEditParams(dataSource);
    };

    const createDevVersion = async (id: string): Promise<any> => {
        try {
            const res = await nsMockManageApiManageMainStructureDevVersionPOST.remote({ id });
            return res;
        } catch {
            return null;
        }
    };

    // 引用数据结构
    const onSelectedDataConstruct = (body: IRecord[], record: IRecord) => {
        const index = findIndexDataConstruct(record.key, dataSource);
        const parentRecord = (record as IRecord)?.parent;
        if (parentRecord) {
            parentRecord.children?.splice(index, 1, ...body);
        } else {
            dataSource.splice(index, 1, ...body);
        }
        setDataSource([...dataSource]);
    };

    // 点击编辑数据结构
    const onEditDataConstruct = (record?: IRecord) => {
        if (record) {
            const refInfo = record[EApiParam.REF_INFO];
            // 线上版本不可修改，想要修改生成开发版本
            if (refInfo && refInfo[ERefInfo.VERSION] === '1') {
                Modal.confirm({
                    title: '当前版本不可修改',
                    content: '当前版本为线上版本，不支持修改，如需修改请切换到开发版本，确定切换到开发版本进行修改吗？',
                    okText: '切换开发版本修改',
                    onOk: async () => {
                        const res = await createDevVersion(refInfo[ERefInfo.ID] || '');
                        if (res) {
                            record[EApiParam.REF_INFO] = {
                                Id: res.id,
                                V: res.version,
                                T: refInfo[ERefInfo.CREATE_TIME] || new Date().getTime(),
                                N: res.name,
                                Dn: res.displayName,
                                Pn: res.projectName
                            };
                            onOpenEditDataConstructDrawer(record, refInfo);
                        }
                    }
                });
            } else {
                onOpenEditDataConstructDrawer(record, refInfo);
            }
        }
    };

    // 打开新建编辑数据结构抽屉
    const onOpenEditDataConstructDrawer = (record: IRecord, refInfo?: IRefInfo) => {
        editDataConstructDrawerRef.current?.showDrawer({
            projectId: editApiContext.projectId && editApiContext.projectId > 0 ? editApiContext.projectId : undefined,
            id: refInfo ? refInfo[ERefInfo.ID] : '',
            // 数据结构保存后调用
            onSaveCallback: (body) => {
                if (refInfo) {
                    const newData = replaceDataConstruct(dataSource, body, refInfo);
                    setDataSource(newData);
                } else {
                    onSelectedDataConstruct(body, record);
                }
            }
        });
    };

    // 获取参数类型列表
    const getParamTypeList = async () => {
        try {
            const res = await nsMockManageApiManageMainApiParamTypeListGET.remotePromise();
            const map = new Map<number, string>();
            res?.paramTypes?.forEach(item => {
                map.set(item.code, item.name);
            });
            setParamTypeMap(map);
        } catch { }
    };

    useImperativeHandle(ref, () => ({
        getParams,
        checkParams,
        dataSource
    }));

    function newArrFun(data) {
        function traverseTree(tree) {
            tree.forEach(node => {
                Object.assign(node, { R: isRequired ? 1 : 0 });
                if (node.children?.length) {
                    traverseTree(node.children);
                }
            });
        }
        traverseTree(data);
    }

    useEffect(() => {
        newArrFun(dataSource);
        setDataSource([...dataSource]);
    }, [isRequired]);

    useEffect(() => {
        const { params, expandedAllkeys } = formatEditParam(props.params);
        setExpandedKeys(expandedAllkeys);
        if (props.hasRoot) {
            params.length ? setDataSource(params) : setDataSource([createParam()]);
        } else {
            setDataSource([...params, createParam()]);
        }
    }, [props.params]);

    // 添加同级参数
    const onAddParam = (index: number, parentRecord?: IRecord, newItemTop?: number) => {
        const newRecord: IRecord = createParam(parentRecord);
        if (!isRequired) {
            Object.assign(newRecord, { R: 0 });
        }
        if (typeof parentRecord === 'object' && parentRecord !== null) {
            const key = parentRecord.key;
            dataSourceMap.keyToNode[key!]?.children?.splice(index + 1, 0, newRecord);
            // parentRecord.children?.splice(index + 1, 0, newRecord);
        } else {
            dataSource.splice(index + 1, 0, newRecord);
        }
        setDataSource([...dataSource]);
        setFocusKey(newRecord.key!);
        // const top = document.getElementById(tempVirtualTableId.current)?.querySelector('table')?.offsetHeight;
        scrollTimeout = setTimeout(() => {
            if (typeof newItemTop === 'number') {
                scrollTo(newItemTop);
            } else {
                document.querySelector(`.key_${newRecord.key}`)?.scrollIntoView({ block: 'nearest' });
            }
        }, 0);
        props.dataChange && props.dataChange();
    };

    // 移除参数
    const onRemoveParam = (index: number, parentRecord?: IRecord) => {
        if (parentRecord) {
            /**
             * 先删除数据结构、再删除字段 失败
             */
            // parentRecord.children?.splice(index, 1);
            const key = parentRecord.key;
            dataSourceMap.keyToNode[key]?.children?.splice(index, 1);
        } else {
            dataSource.splice(index, 1);
        }
        setDataSource([...dataSource]);
        setTimeout(() => {
            props.dataChange && props.dataChange();
        }, 0);
    };

    // 移除引用数据结构
    const onRemoveDataConstruct = (refInfo?: IRefInfo) => {
        if (refInfo) {
            const records = dataSourceMap.groupToNode[getRefKey(refInfo)];
            const recordsKeys = records.map(item => item.key);
            const newData = removeDataConstruct(recordsKeys, dataSource);
            setDataSource(newData);
        }
    };

    // 添加子参数
    const onAddChildParam = (record: IRecord) => {
        if (record[EApiParam.TYPE] !== EParamType.array) {
            record[EApiParam.TYPE] = EParamType.object;
        }
        const newRecord: IRecord = createParam(record);
        if (!isRequired) {
            Object.assign(newRecord, { R: 0 });
        }
        record.children = [newRecord, ...(record?.children || [])];
        // 添加子参数默认展开当前行
        setExpandedKeys([...expandedKeys, record.key]);
        setDataSource([...dataSource]);
        setFocusKey(newRecord.key);
    };

    // 切换数据类型操作逻辑
    const onChangeType = (val: number, record: IRecord) => {
        record[EApiParam.TYPE] = val;
        // 切换type处理children
        if (props.type === 'body' && (val === EParamType.object || val === EParamType.array)) {
            !record.children && (record.children = [createParam(record)]);
            setExpandedKeys([...expandedKeys, record.key]);
        } else {
            record.children = undefined;
        }
        setDataSource([...dataSource]);
    };

    const onChange = () => {
        setTimeout(() => {
            props.dataChange && props.dataChange();
        }, 0);
    }

    useEffect(() => {
        getParamTypeList();
        return () => {
            clearTimeout(scrollTimeout);
        };
    }, []);

    const {
        expanded,
        setExpanded,
        expandedWithComputer,
        handleSelectInSearch,
        handleScrollContentContainer
    } = useExpand({
        dataSourceMap: dataSourceMap,
        virtualTableRef: virtualTableRef,
    });

    const columns = React.useMemo<(ColumnDef<IRecord, any> & { accessorKey: string })[]>(
        () => {
            const res = [
                {
                    header: () => <div style={{ display: 'flex', alignItems: 'center' }}>
                        <div>参数名</div>
                        {
                            dataSourceMap.firstFloorNodeKey.length > 0 &&
                            <Tooltip title={expandedWithComputer ? '收起全部' : '展开全部'}>
                                <Button
                                    type="text"
                                    size="small"
                                    onClick={() => setExpanded(pre => !pre)}
                                    style={{ color: '#898A8C', height: 22, padding: '2px 5px' }}
                                    icon={
                                        <KdevIconFont
                                            id={
                                                expandedWithComputer
                                                    ? common_system_upfold
                                                    : common_system_underfold
                                            }
                                        />
                                    }
                                />
                            </Tooltip>
                        }
                    </div>,
                    size: 480,
                    minSize: 480,
                    accessorKey: EApiParam.NAME,
                    cell: RenderNameComps as any
                },
                {
                    header: '类型',
                    size: 120,
                    minSize: 120,
                    accessorKey: EApiParam.TYPE,
                    cell: (arg) => {
                        return RenderTypeCell(arg, props.disabledType)
                    },
                },
                {
                    header: () => renderRequiredHeader(),
                    size: 70,
                    minSize: 80,
                    accessorKey: EApiParam.REQUIRED,
                    // dataIndex: 'type',
                    cell: RenderRequiredCell as any,
                },
                {
                    header: '说明',
                    size: 200,
                    minSize: 200,
                    accessorKey: EApiParam.DESC,
                    // dataIndex: 'type',
                    cell: RenderDescriptionCell as any
                },
                {
                    header: '参数值',
                    size: 148,
                    minSize: 148,
                    accessorKey: EApiParam.VALUE,
                    // dataIndex: 'type',
                    cell: RenderValueCell as any
                },
                {
                    header: '操作',
                    size: 104,
                    minSize: 104,
                    accessorKey: 'operate',
                    // dataIndex: 'type',
                    cell: RenderOperateCell as any,
                }
            ]
            if (props.isCheckbox) {
                res.unshift({
                    header: '',
                    size: 25,
                    minSize: 25,
                    accessorKey: EApiParam.CHECK,
                    cell: RenderCheckCell as any
                },)
            }
            return res
        },
        [dataSource, expandedWithComputer, isRequired]
    );

    let cloumnsFormat
    if (props.filterColumns) {
        const filterCloumns = props.filterColumns;
        cloumnsFormat = columns.filter((column) => {
            const index = filterCloumns.findIndex(item => item.accessorKey === column.accessorKey);
            return index >= 0
        })
        cloumnsFormat = cloumnsFormat.sort((a, b) => {
            const aIndex = filterCloumns.findIndex(item => item.accessorKey === a.accessorKey);
            const bIndex = filterCloumns.findIndex(item => item.accessorKey === b.accessorKey);
            return aIndex - bIndex
        })
    } else {
        cloumnsFormat = columns
    }

    const offset = focusConstruct ? (() => {
        const tableOffsetTop = popoverRef.current?.getBoundingClientRect().top || 0;
        const screenHeight = (document.getElementsByClassName(focusConstruct)?.[0] as any)
            ?.getBoundingClientRect().top || 0;
        return screenHeight - tableOffsetTop - 46;
    })() : 0;

    /**
     * FUNC_DES: 是否必填
     */
    const renderRequiredHeader = (): React.ReactElement => {
        return (
            <div>
                <Checkbox
                    checked={isRequired}
                    onChange={(e) => setIsRequired(e.target.checked)}
                >
                    必填
                </Checkbox>
            </div>
        );
    };

    const curConstruct = focusConstruct ? dataSourceMap.groupToNode[focusConstruct]?.[0] : undefined;
    const rowClassName = (record) => {
        const data: any = dataSourceMap.keyToNode[record.key];
        const classList: Array<string> = [`key_${record.key}`];
        if (data?.groups && data?.groups?.length > 0) {
            classList.push(data.groups[0]);
            const curData = dataSourceMap.keyToNode[record.key] as any;
            if (curData.colorNo) {
                /**
                 * 通过row-construct-1控制背景色
                 */
                classList.push(`row-construct-${curData.colorNo % 8 + 1}`);
            }
            if (data?.groups[0] === focusConstruct) {
                classList.push(`row-background-${curData.colorNo % 8 + 1}`);
            }
        }
        return classList.join(' ') || '';
    };
    /**
     * 使用useEffect导致虚拟滚动失效，暂时先用id代替
     */
    // 拿到curConstruct?.refInfo
    // 取唯一的keygetRefKey（curConstruct?.refInfo）
    // 取到哪些行是要删除的groupToNode   // []
    const scrollTo = (top: number) => {
        document.getElementById(tempVirtualTableId.current)?.scrollTo({ top, behavior: 'smooth' });
    };

    return (
        <div className={classNames(css.editParamTable, props.className)} style={props.style}>
            {dataSourceMap.pathList.length > 10 && <FuzzySearch
                style={{ position: 'absolute', right: '7px', top: '-38px' }}
                list={dataSourceMap.pathList}
                handleSelectInSearch={handleSelectInSearch}
            />}
            <div
                style={{ position: 'relative', zIndex: 10 }}
                className={ConstructRelativeHeader}
                ref={popoverRef}
                onMouseLeave={() => {
                    setFocusConstruct(undefined);
                }}
            >
                {
                    focusConstruct && <div
                        style={{
                            position: 'absolute',
                            width: '100%',
                            top: offset + 'px',
                        }}
                        className={
                            focusConstruct
                                ? `construct-header-${(dataSourceMap.keyToNode[curConstruct?.key || 0]?.colorNo || 0) % 8 + 1}`
                                : undefined
                        }
                    >
                        {
                            curConstruct &&
                            <DataConstructTableTitle
                                data={curConstruct}
                                refId={(curConstruct as any)[EApiParam.REF_INFO][ERefInfo.ID]}
                                onRemove={(record) =>
                                    onRemoveDataConstruct(
                                        record
                                            ? record[EApiParam.REF_INFO]
                                            : undefined
                                    )
                                }
                                onEdit={isEditDataConstruct ? onEditDataConstruct : undefined}
                            />
                        }
                    </div>
                }
            </div>
            <VirtualTable
                ref={virtualTableRef}
                data={dataSource}
                columns={cloumnsFormat}
                tempVirtualTableId={tempVirtualTableId.current}
                rowClassName={rowClassName}
                expanded={expanded}
                setExpanded={setExpanded as any}
                handleMouseEnter={(e, record) => {
                    const key = record.key;
                    const curData = dataSourceMap.keyToNode[key];
                    if (curData.groups?.[0]) {
                        setFocusConstruct(curData.groups[0]);
                    }
                }}
                columnPinning={{
                    right: ['operate']
                }}
                updateData={() => {
                    const newData = [...getDataSource()];
                    setDataSource((pre) => newData);
                    props?.queryUpdate && props?.queryUpdate(newData);
                    // props.dataChange && props.dataChange();
                }}
                handleMouseLeave={(event, record) => {
                    /**
                     * 若移到group的浮窗 不消失
                     */
                    if (event.relatedTarget && hasAncestorWithClass(event.relatedTarget, ConstructRelativeHeader)) {
                        return;
                    }
                    setFocusConstruct(undefined);
                }}
                extraParamsInCell={{
                    hasRoot: props.hasRoot,
                    type: props.type,
                    dataSourceMap,
                    isEditDataConstruct,
                    onOpenEditDataConstructDrawer,
                    onSelectedDataConstruct,
                    onChangeType,
                    onAddParam,
                    onAddChildParam,
                    onRemoveParam,
                    paramTypeMap,
                    onChange
                }}
            />
            {
                !props.hasRoot &&
                <div className={css.addBtnContainer}>
                    <Button
                        type="link"
                        size="small"
                        icon={<KdevIconFont id={common_system_add} style={{ marginRight: 4 }} />}
                        onClick={() => {
                            onAddParam(dataSource.length - 1, undefined, dataSource.length * 45);
                        }}
                    >
                        添加
                    </Button>
                </div>
            }
            <EditDataConstructDrawer ref={editDataConstructDrawerRef} />
        </div>
    );
});

export { EditParamTable, IRecord, IEditParamTableRef, formatEditParam };
