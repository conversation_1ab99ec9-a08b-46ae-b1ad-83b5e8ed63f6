import React from 'react';
import { Radio } from 'antd';
import { RadioProps } from 'antd/lib/radio';

enum EResRadioType {
    HEADER = 'header',
    BODY = 'body'
}

const requestParamsTabOptions = [
    { label: '返回头部', value: EResRadioType.HEADER },
    { label: '返回体', value: EResRadioType.BODY },
];

function ResRadioGroupTab(props: RadioProps) {
    return (
        <Radio.Group
            options={requestParamsTabOptions}
            optionType="button"
            {...props}
        />
    );
}

export { ResRadioGroupTab, EResRadioType };
