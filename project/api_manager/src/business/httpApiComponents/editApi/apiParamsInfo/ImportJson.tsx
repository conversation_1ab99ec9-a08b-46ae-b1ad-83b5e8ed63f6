import React, { useEffect, useState } from 'react';
import { But<PERSON>, Modal, message } from 'antd';
import { KdevIconFont, AceEditor } from '@/business/commonComponents';
import { common_system_import, common_system_copy02, common_system_remove, common_system_delete } from '@kid/enterprise-icon/icon/output/icons';
import css from './EditApiParamsInfo.less';
import { nsHttpApiInterface } from '@/remote';
import { isJsonString } from '@/index.config/tools';
import { EApiParam, EParamType } from '@/business/httpApiComponents/editApi/apiParamsInfo/formatParamsTools';
import { IRecord } from './EditParamTable';
import { CopyToClipboard } from 'react-copy-to-clipboard';
import JSON5 from 'json5';
import { Ace } from 'ace-builds';
interface IProps {
    getData?(): IRecord[];
    setBody?(body: nsHttpApiInterface.IApiParam[]): void;
    checkJsonError?: boolean; // 此参数为校验json格式的开关,当为true时,会在json错误的那一行的行号前,加一个错误icon
}

type TOperateType = 'replace' | 'insert' | 'update';

const regex = /\d+?:\d+?/g; // 用来匹配json5的报错,找到json的错误位置
interface IOperateEnum {
    replace: (json: object) => void;
    insert: (json: object) => void;
    update: (json: object) => void;
}
function ImportJsonModal(props: IProps) {
    const [visible, setVisible] = useState<boolean>(false);
    const [jsonStr, setJsonStr] = useState<string>('');
    const [rootValue, setRootValue] = useState<any>();
    const [data, setData] = useState<nsHttpApiInterface.IApiParam[]>([]);
    const [dataMap, setDataMap] = useState<Map<string, nsHttpApiInterface.IApiParam>>(new Map());
    const [errorLine, setErrorLine] = useState<Ace.Annotation[]>([]);
    const removeErrorLineIcon = () => {
        setErrorLine([])
    }

    const removeCommentary = (str) => {
        try {
            return JSON.stringify(JSON5.parse(str));
        } catch (e: any) {
            if (props.checkJsonError) {
                try {
                    const errorPositionArr = e.message.match(regex) || [];
                    const errorInfo = errorPositionArr.pop() || '0:0';
                    const rowNo = errorInfo.split(':')[0];
                    const columnNo = errorInfo.split(':')[0];
                    setErrorLine([{
                        row: +rowNo - 1,
                        column: +columnNo - 1,
                        text: 'json格式错误',
                        type: 'error'
                    }])
                } catch { }
            }
        }
    };
    // 填充当前数据
    const addCurrentData = () => {
        try {
            setJsonStr(JSON.stringify(rootValue, null, 4));
        } catch {
            setJsonStr(rootValue);
        }
    };

    useEffect(() => {
        // 回调函数
        addCurrentData();
    }, [rootValue]);

    const onOpenImportJsonModal = () => {
        setVisible(true);
        if (props.getData) {
            const dataSource = props.getData();
            setData(dataSource);
            setDataMap(getParamsMap(dataSource));
            if (dataSource[0]) {
                const jsonObj = convertApiParamArrayToJson(dataSource[0]);
                setRootValue(jsonObj);
            }
        }
    };

    const onCloseImportJsonModal = () => {
        setVisible(false);
        setJsonStr('');
    };

    // 全局替换
    const replaceJsonData = (jsonObj: any) => {
        const type = assertValueType(jsonObj);
        if ((type === EParamType.object || type === EParamType.array) && jsonObj !== null) {
            const arr = getApiParam(jsonObj, 'root', dataMap);
            props.setBody && props.setBody([arr]);
        } else {
            props.setBody && props.setBody([{
                [EApiParam.NAME]: 'root',
                [EApiParam.TYPE]: type,
                [EApiParam.VALUE]: `${jsonObj}`,
                [EApiParam.REQUIRED]: 1,
                [EApiParam.DESC]: ''
            }]);
        }
        onCloseImportJsonModal();
    };

    // 末端插入
    const insertJsonData = (jsonObj: object) => {
        if (data[0][EApiParam.TYPE] !== EParamType.array && data[0][EApiParam.TYPE] !== EParamType.object) {
            message.warning('root节点为基本数据类型，不支持在末端插入');
            return;
        }
        if (jsonObj === null && data[0][EApiParam.TYPE] !== EParamType.array) {
            message.warning('当前root类型不为array，不允许null在末端插入');
            return;
        }
        if (data[0]) {
            let arr: nsHttpApiInterface.IApiParam[] = [];
            if (jsonObj === null) {
                arr.push({
                    [EApiParam.NAME]: 'root',
                    [EApiParam.TYPE]: EParamType.object,
                    [EApiParam.VALUE]: `${jsonObj}`,
                    [EApiParam.REQUIRED]: 1,
                    [EApiParam.DESC]: ''
                });
            } else {
                arr = convertJSONToApiParamArray(jsonObj, dataMap);
            }
            if (data[0].children) {
                data[0].children.push(...arr);
            } else {
                data[0].children = arr;
            }
            props.setBody && props.setBody([...data]);
        }
        onCloseImportJsonModal();
    };

    const updateJsonData = (jsonObj: object) => {
        const arr = getApiParam(mergeJson(jsonObj, rootValue), 'root', dataMap);
        props.setBody && props.setBody([arr]);
        onCloseImportJsonModal();
    };

    const operateEnum: IOperateEnum = {
        replace: replaceJsonData,
        insert: insertJsonData,
        update: updateJsonData
    };

    const formatJsonStr = () => {
        if (jsonStr) {
            removeCommentary(jsonStr)
            if (!isJsonString(jsonStr)) {
                message.warning('请输入json格式的字符串');
            } else {
                try {
                    const jsonObj = JSON.parse(jsonStr);
                    setJsonStr(JSON.stringify(jsonObj, null, 4));
                } catch {
                    message.warning('请输入json格式的字符串');
                }
            }
        }
    };

    const formatJsonData = (type: TOperateType) => {
        if (jsonStr) {
            const formatJson = removeCommentary(jsonStr);
            if (!formatJson) {
                message.warning('请输入json格式的字符串');
                return;
            }
            if (!isJsonString(formatJson)) {
                message.warning('请输入json格式的字符串');
            } else {
                const jsonObj = JSON.parse(formatJson);
                operateEnum[type] && operateEnum[type](jsonObj);
            }
        } else {
            onCloseImportJsonModal();
        }
    };

    return (
        <>
            <Modal
                className={css.importJsonModal}
                width={880}
                title={
                    <div className={css.importJsonModalTitle}>
                        <b>导入JSON</b>
                        <span className={css.jsonExample}>
                            {`格式为 ${JSON.stringify({ key1: 'value1', key2: 'value2' })}`}
                        </span>
                    </div>
                }
                visible={visible}
                onCancel={onCloseImportJsonModal}
                footer={
                    <>
                        <Button onClick={onCloseImportJsonModal}>取消</Button>
                        <Button onClick={() => formatJsonData('replace')}>全局替换</Button>
                        <Button onClick={() => formatJsonData('insert')}>在末端插入</Button>
                        <Button type="primary" onClick={() => formatJsonData('update')}>增量更新</Button>
                    </>
                }
                maskClosable={false}
                destroyOnClose
            >
                <div className={css.aceEditorWrap}>
                    <div className={css.optionsLine}>
                        <CopyToClipboard text={jsonStr || ''}>
                            <div className={css.optionWarp} onClick={() => message.success('复制成功')}>
                                <KdevIconFont className={css.icon} id={common_system_copy02} />
                                <span className={css.option}>复制</span>
                            </div>
                        </CopyToClipboard>
                        <div className={css.optionWarp} onClick={formatJsonStr}>
                            <KdevIconFont className={css.icon} id={common_system_remove} />
                            <span className={css.option}>格式化</span>
                        </div>
                        <div className={css.optionWarp} onClick={() => {
                            setJsonStr('');
                        }}>
                            <KdevIconFont className={css.icon} id={common_system_delete} />
                            <span className={css.option}>清空</span>
                        </div>
                    </div>
                    <AceEditor
                        theme="xcode"
                        width="100%"
                        value={jsonStr}
                        onChange={(val) => {
                            setJsonStr(val);
                            if (props.checkJsonError) {
                                removeErrorLineIcon();
                            }
                        }}
                        showPrintMargin={false}
                        focus
                        annotations={errorLine}
                    />
                </div>
            </Modal>
            <Button
                className={css.uploadBtn}
                icon={<KdevIconFont id={common_system_import} style={{ marginRight: 4, color: '#326BFB' }} />}
                type="text"
                style={{ padding: '5px 8px', color: '#326BFB' }}
                onClick={onOpenImportJsonModal}
            >
                导入JSON
            </Button>
        </>
    );
}

/**
 * 断言类型
 * @param {any} value
 * @returns {number}
 */
function assertValueType(value: any): number {
    const type = typeof value;
    if (type === 'object') {
        if (Array.isArray(value)) {
            return EParamType.array;
        }
        return EParamType.object;
    } else if (type === 'number') {
        return EParamType.number;
    } else if (type === 'boolean') {
        return EParamType.boolean;
    }
    return EParamType.string;
}

export function convertApiParamArrayToJson(value: nsHttpApiInterface.IApiParam) {
    if (value[EApiParam.TYPE] === EParamType.object) {
        return convertApiParamArrayToJSON(value[EApiParam.CHILDREN] || [], value[EApiParam.VALUE]);
    } else if (value[EApiParam.TYPE] === EParamType.array) {
        return convertApiParamArrayToArray(value[EApiParam.CHILDREN] || []);
    } else if (value[EApiParam.TYPE] === EParamType.number || value[EApiParam.TYPE] === EParamType.int) {
        const num = Number(value[EApiParam.VALUE]);
        return isNaN(num) ? 0 : num;
    } else if (value[EApiParam.TYPE] === EParamType.boolean) {
        // 判断是否是字符串false，如果是则返回布尔类型false
        const isFalseStr = /^false$/gi.test((value[EApiParam.VALUE] || '').toString().trim());
        return isFalseStr ? false : Boolean(value[EApiParam.VALUE]);
    }
    return value[EApiParam.VALUE] ?? '';
}

// 数组转数组
function convertApiParamArrayToArray(arr: nsHttpApiInterface.IApiParam[]): any[] {
    const newArr: any[] = [];
    arr.forEach(item => {
        newArr.push(convertApiParamArrayToJson(item));
    });
    return newArr;
}

// 数组转json
export function convertApiParamArrayToJSON(arr: nsHttpApiInterface.IApiParam[], value): object {
    if (!arr.length && value === 'null') {
        return null
    }
    const obj = {};
    arr.forEach(item => {
        obj[item[EApiParam.NAME]] = convertApiParamArrayToJson(item);
    });
    return obj;
}

/**
 * 获取json描述信息
 * @param value
 * @param key
 * @returns
 */
function getApiParam(
    value: any,
    name: string,
    apiParamMap: Map<string, nsHttpApiInterface.IApiParam>,
    parentPath = ''
) {
    const T: number = assertValueType(value);
    let V = '';
    let children: nsHttpApiInterface.IApiParam[] | undefined;
    const currentPath = `${parentPath}/${name}`;
    if (T === EParamType.object && value !== null) {
        children = convertJSONToApiParamArray(value, apiParamMap, currentPath);
    } else if (T === EParamType.array) {
        children = convertArrayToApiParamArray(value, name, apiParamMap, currentPath);
    } else {
        V = `${value}`;
    }
    const oldObj = apiParamMap.get(currentPath);
    const obj: nsHttpApiInterface.IApiParam = {
        [EApiParam.NAME]: name,
        [EApiParam.TYPE]: T,
        [EApiParam.REQUIRED]: oldObj ? oldObj[EApiParam.REQUIRED] : 1,
        [EApiParam.DESC]: oldObj ? oldObj[EApiParam.DESC] : '',
        [EApiParam.VALUE]: V,
        [EApiParam.CHILDREN]: children
    };
    return obj;
}

function convertArrayToApiParamArray(
    arr: any[],
    parentKey: string,
    apiParamMap: Map<string, nsHttpApiInterface.IApiParam>,
    parentPath: string = ''
): nsHttpApiInterface.IApiParam[] {
    const newArr: nsHttpApiInterface.IApiParam[] = [];
    arr.forEach((item, index) => {
        const name = `${parentKey}[${index}]`;
        const obj = getApiParam(item, name, apiParamMap, `${parentPath}/${name}`);
        newArr.push(obj);
    });
    return newArr;
}

function convertJSONToApiParamArray(
    json: object,
    apiParamMap: Map<string, nsHttpApiInterface.IApiParam>,
    parentPath: string = ''
): nsHttpApiInterface.IApiParam[] {
    const arr: nsHttpApiInterface.IApiParam[] = [];
    const keys = Object.keys(json);
    keys.forEach(key => {
        const obj = getApiParam(json[key], key, apiParamMap, parentPath);
        arr.push(obj);
    });
    return arr;
}

// 存储未导入前数据 Map<string, nsHttpApiInterface.IApiParam>
function getParamsMap(arr: nsHttpApiInterface.IApiParam[], parentPath: string = '') {
    let newMap = new Map<string, nsHttpApiInterface.IApiParam>();
    arr.forEach(item => {
        const path = `${parentPath}/${item[EApiParam.NAME]}`;
        newMap.set(path, item);
        if (item.children && item.children.length) {
            newMap = new Map([...getParamsMap(item.children, path), ...newMap]);
        }
    });
    return newMap;
}

function getValueType(value: any) {
    const type = typeof value;
    if (Array.isArray(value)) {
        return 'array';
    }
    if (value === null) {
        return 'null';
    }
    if (type === 'bigint') {
        return 'string';
    }
    return type;
}

function mergeJsonObj(newObj: object, oldObj: object) {
    const obj = { ...oldObj, ...newObj };
    Object.keys(obj).forEach(key => {
        newObj[key] = mergeJson(newObj[key], oldObj[key]);
    });
    return obj;
}

function mergeJsonArr(newObj: Array<any>, oldObj: Array<any>) {
    // const arrLength = Math.max(newObj.length, oldObj.length);
    // const arr: any[] = [];
    // for (let i = 0; i < arrLength; i++) {
    //     arr.push(mergeJson(newObj[i], oldObj[i]));
    // }
    // return arr;
    return newObj.map((item, index) => {
        return mergeJson(item, oldObj[index]);
    });
}

// json 合并 array情况下暂做覆盖
function mergeJson(newJson: any, oldJson: any) {
    const newDataType = getValueType(newJson);
    const oldDataType = getValueType(oldJson);
    if (newDataType === 'object' && oldDataType === 'object') {
        return mergeJsonObj(newJson, oldJson);
    }
    // if (newDataType === 'array' && oldDataType === 'array') {
    //     return mergeJsonArr(newJson, oldJson);
    // }
    return newJson;
}

export { ImportJsonModal };