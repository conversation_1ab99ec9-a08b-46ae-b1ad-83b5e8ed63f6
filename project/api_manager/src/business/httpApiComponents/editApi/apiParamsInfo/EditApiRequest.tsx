import React, { useRef, forwardRef, useImperativeHandle, Ref, useState, useEffect } from 'react';
import { message, Select, Upload, Button, Segmented } from 'antd';
import { KdevTitle, KdevIconFont, JsonAceEditor } from '@/business/commonComponents';
import classNames from 'classnames';
import css from './EditApiParamsInfo.less';
import { Ace } from 'ace-builds';
import {
    nsHttpApiInterface, nsMockManageApiManageMainApiParamTypeListGET,
    nsMockManageApiManageMainApiTestAccountList,
    nsMockManageApiManageMainApiJson5CommentParserPOST,
    nsMockManageApiManageMainApiJson5ParamPrettyPOST
} from '@/remote';
import { EditParamTable, IEditParamTableRef, IRecord } from './EditParamTable';
import { ImportJsonModal } from './ImportJson';
import { EApiParam, EParamType } from './formatParamsTools';
import { ReqRadioGroupTab, EReqRadioType } from './ReqRadioGroupTab';
import { BodyTypeRadioTab, EBodyTypeRadioTabType } from './BodyTypeRadioTab';
import * as commonFun from '@/commonFun';
import { PathParamsCom } from './pathParamsCom/PathParamsCom';
import { PathParamsComM } from './pathParamsCom/PathParamsComM';
import * as QS from 'query-string';
import { UploadOutlined, InboxOutlined } from '@ant-design/icons';
import {
    common_system_upload
} from '@kid/enterprise-icon/icon/output/icons';
import json5 from 'json5';

const pathParamsComM = new PathParamsComM();

export function initApiRequest() {
    return {
        header: [],
        rest: [],
        query: [],
        body: [],
        bodyType: nsHttpApiInterface.EReqBodyFormat.JSON,
        json5Body: ''
    };
}
export function initBinary() {
    return [{
        N: 'root',
        T: 14,
        R: 1,
        V: ''
    }]
}

interface IProps {
    className?: string;
    requestParams?: nsHttpApiInterface.IApiRequest;
    kdevTitle?: React.ReactNode;
    filterReqRadioType?: EReqRadioType[];
    queryUpdate?(dataSource: IRecord[]): void;
    path: string;
    forceReqRadioType?: EReqRadioType; // 在编辑环境中,没有请求体tab,如果使用getDefaultReqRadioType选择默认tab会出问题,所以加一个强制选择tab的参数
    filterColumns?: { accessorKey: string }[];
    comType?: string;
    isCheckbox?: boolean;
    json5Body?: string;
}

export enum EBodyJsonViewTypeEnum {
    JSON = 'JSON',
    FORMAT = 'FORMAT',
}

interface IEditApiRequestRef {
    getParams(): nsHttpApiInterface.IApiRequest;
    checkParams(): boolean;
    getSourceParams(): nsHttpApiInterface.IApiRequest;
    getAccountInfo(): any;
    replaceJsonData(): Promise<boolean>;
    getDataSource(): () => IRecord[];
    setRequestJson: (value: string) => void;
    bodyViewType: EBodyJsonViewTypeEnum;
    format(): Promise<boolean>;
}

type TReqRadioType = EReqRadioType.HEADER | EReqRadioType.BODY | EReqRadioType.QUERY | EReqRadioType.ACCOUNT_NUMBER;
// 获取默认选中值
const getReqRadioTypeDefaultValue = (filterReqRadioType?: EReqRadioType[]) => {
    if (filterReqRadioType) {
        if (!filterReqRadioType.includes(EReqRadioType.HEADER)) {
            return EReqRadioType.HEADER;
        } else if (!filterReqRadioType.includes(EReqRadioType.QUERY)) {
            return EReqRadioType.QUERY;
        } else {
            return EReqRadioType.BODY;
        }
    }
    return EReqRadioType.BODY;
};


// 数组转数组
function convertApiParamArrayToArray(arr: nsHttpApiInterface.IApiParam[]): any[] {
    const newArr: any[] = [];
    arr.forEach(item => {
        newArr.push(convertApiParamArrayToJson(item));
    });
    return newArr;
}

// 数组转json
export function convertApiParamArrayToJSON(arr: nsHttpApiInterface.IApiParam[], value): object {
    if (!arr.length && value === 'null') {
        return null
    }
    const obj = {};
    arr.forEach(item => {
        obj[item[EApiParam.NAME]] = convertApiParamArrayToJson(item);
    });
    return obj;
}
export function convertApiParamArrayToJson(value: nsHttpApiInterface.IApiParam) {
    if (value[EApiParam.TYPE] === EParamType.object) {
        return convertApiParamArrayToJSON(value[EApiParam.CHILDREN] || [], value[EApiParam.VALUE]);
    } else if (value[EApiParam.TYPE] === EParamType.array) {
        return convertApiParamArrayToArray(value[EApiParam.CHILDREN] || []);
    } else if (value[EApiParam.TYPE] === EParamType.number || value[EApiParam.TYPE] === EParamType.int) {
        const num = Number(value[EApiParam.VALUE]);
        return isNaN(num) ? 0 : num;
    } else if (value[EApiParam.TYPE] === EParamType.boolean) {
        // 判断是否是字符串false，如果是则返回布尔类型false
        const isFalseStr = /^false$/gi.test((value[EApiParam.VALUE] || '').toString().trim());
        return isFalseStr ? false : Boolean(value[EApiParam.VALUE]);
    }
    return value[EApiParam.VALUE] ?? '';
}

export function getDefaultReqRadioType(requestParams: nsHttpApiInterface.IApiRequest) {
    const { header, query, body, bodyType } = requestParams;
    const isNotRoot = body?.[0]?.[EApiParam.TYPE] !== nsMockManageApiManageMainApiParamTypeListGET.EParamType.object;
    if ((bodyType === nsHttpApiInterface.EReqBodyFormat.JSON &&
        (isNotRoot || body?.[0]?.[EApiParam.CHILDREN]?.length))
        || (bodyType === nsHttpApiInterface.EReqBodyFormat.FORM_DATA && (body?.[0]?.N.trim() !== '') && body?.length)
        || (bodyType === nsHttpApiInterface.EReqBodyFormat.X_WWW_FORM_URLENCODED &&
            (body?.[0]?.N.trim() !== '') && body?.length)) {
        return EReqRadioType.BODY;
    } else if (query?.length) {
        return EReqRadioType.QUERY;
    } else if (header?.length) {
        return EReqRadioType.HEADER;
    } else {
        return EReqRadioType.BODY;
    }
}

const EditApiRequest = forwardRef((props: IProps, ref: Ref<IEditApiRequestRef>) => {
    const [reqRadioType, setReqRadioType] = useState<TReqRadioType>(
        getReqRadioTypeDefaultValue(props.filterReqRadioType)
    );
    const [bodyType, setBodyType] = useState<nsHttpApiInterface.EReqBodyFormat>(nsHttpApiInterface.EReqBodyFormat.JSON);
    const [form, setForm] = useState<nsHttpApiInterface.IApiParam[]>([]);
    const [requestJson, setRequestJson] = useState<string>('');
    const [bodyJsonViewType, setBodyJsonViewType] = useState<string>(EBodyJsonViewTypeEnum.JSON);
    const [errorLine, setErrorLine] = useState<Ace.Annotation[]>([]);

    const [body, setBody] = useState<nsHttpApiInterface.IApiParam[]>([]);
    const [accountList, setAccountList] = useState<Array<any>>([]);
    const [accountNumber, setAccountNumber] = useState<string>('');
    const [urlencoded, setUrlencoded] = useState<nsHttpApiInterface.IApiParam[]>([]);
    const [binary, setBinary] = useState<any>(initBinary());
    const headerEditParamTableRef = useRef<IEditParamTableRef>(null);
    const queryEditParamTableRef = useRef<IEditParamTableRef>(null);
    const formEditParamTableRef = useRef<IEditParamTableRef>(null);
    const bodyEditParamTableRef = useRef<IEditParamTableRef>(null);
    const urlencodedEditParamTableRef = useRef<IEditParamTableRef>(null);
    const [requestParams, setRequestParams] =
        useState<nsHttpApiInterface.IApiRequest | undefined>
            (props.requestParams);
    const getParams = (): nsHttpApiInterface.IApiRequest => {
        let bodyParams: nsHttpApiInterface.IApiParam[] = [];
        if (bodyType === nsHttpApiInterface.EReqBodyFormat.FORM_DATA) {
            bodyParams = formEditParamTableRef.current?.getParams() || [];
        } else if (bodyType === nsHttpApiInterface.EReqBodyFormat.X_WWW_FORM_URLENCODED) {
            bodyParams = urlencodedEditParamTableRef.current?.getParams() || [];
        } else if (bodyType === nsHttpApiInterface.EReqBodyFormat.BINARY) {
            bodyParams = binary;
        } else {
            bodyParams = bodyEditParamTableRef.current?.getParams() || [];
        }
        return {
            header: headerEditParamTableRef.current?.getParams() || [],
            query: queryEditParamTableRef.current?.getParams() || [],
            rest: pathParamsComM.params() || [],
            body: bodyParams,
            bodyType,
            json5Body: requestJson,
            bodyJsonViewType: bodyJsonViewType as EBodyJsonViewTypeEnum
        };
    };

    const getSourceParams = () => {
        return {
            header: headerEditParamTableRef.current?.dataSource || [],
            query: queryEditParamTableRef.current?.dataSource || [],
            rest: pathParamsComM.params() || [],
            body: bodyType === nsHttpApiInterface.EReqBodyFormat.FORM_DATA
                ? formEditParamTableRef.current?.dataSource || []
                : bodyEditParamTableRef.current?.dataSource || [],
            bodyType
        };
    };

    /**
     * FUNC_DES: 账号信息
     */
    const getAccountInfo = () => {
        if (accountNumber) {
            return accountList?.find(item => item.mobile === accountNumber) || {};
        }
        return {};
    }

    // 获取JSON
    const getDataSource = (): IRecord[] => {
        return bodyEditParamTableRef.current?.dataSource || [];
    };

    // 校验参数名是否重复
    const checkParams = (): boolean => {
        const headerRecord = headerEditParamTableRef.current?.checkParams();
        if (headerRecord) {
            message.warn(`请求头部「${headerRecord[EApiParam.NAME]}」参数重复，请修改后再次保存`);
            return true;
        }
        const bodyRecord = bodyType === nsHttpApiInterface.EReqBodyFormat.FORM_DATA
            ? formEditParamTableRef.current?.checkParams()
            : bodyEditParamTableRef.current?.checkParams();
        if (bodyRecord) {
            message.warn(`请求体「${bodyRecord[EApiParam.NAME]}」参数重复，请修改后再次保存`);
            return true;
        }
        const queryRecord = queryEditParamTableRef.current?.checkParams();
        if (queryRecord) {
            message.warn(`Query参数「${queryRecord[EApiParam.NAME]}」参数重复，请修改后再次保存`);
            return true;
        }
        return false;
    };

    const queryUpdate = (dataSource: IRecord[]) => {
        props.queryUpdate && props.queryUpdate(dataSource);
    };

    // 同步json到格式化数据
    async function replaceJsonData() {
        let jsonStr
        try {
            // jsonStr = await commonFun.formatJsonHasComment(requestJson)
            jsonStr = await nsMockManageApiManageMainApiJson5ParamPrettyPOST.
                remote(requestJson || '{}');
            if (jsonStr.status === 200) {
                jsonStr = jsonStr.data.prettyJson5;
                setRequestJson(jsonStr);
                setErrorLine([]);
            } else {
                throw jsonStr;
            }
            try {
                if (!jsonStr) {
                    jsonStr = '{}';
                }
                const dataSource = getDataSource()
                const clearACLData = dataSource.map(item => commonFun.clearACL(item))
                const updatedJsonAndFormat = await commonFun.replaceJsonData(json5.parse(jsonStr),
                    setBody, clearACLData);
                const newRequestJson = await nsMockManageApiManageMainApiJson5CommentParserPOST.remote(requestJson || '{}');
                const updatedBody = await commonFun.updateBody(newRequestJson, updatedJsonAndFormat);
                if (updatedBody) {
                    setBody(updatedBody)
                };
                return true;
            } catch (e) {
                message.warn('json格式化失败');
                return false;
            }
        } catch (e: any) {
            message.warn('请输入正确的请求体json格式');
            const err = JSON.parse(e.message);
            setErrorLine([{
                row: (e as any).data.errorLineNo - 1,
                text: e.message,
                type: 'error',
                column: 0
            }])
            //  setErrorLine(commonFun.analyseFormatJsonError(e));
            return false;
        }
    }

    useImperativeHandle(ref, () => ({
        getParams,
        checkParams,
        getSourceParams,
        getAccountInfo,
        replaceJsonData,
        setRequestJson,
        bodyViewType: bodyJsonViewType as EBodyJsonViewTypeEnum,
        format
    }));

    /**
     * FUNC_DES: 获取 apiDocTab
     */
    const getApiDocTab = () => {
        return QS.parse(location.search)?.apiDocTab || '';
    }

    useEffect(() => {
        if (props.requestParams) {
            setBodyType(props.requestParams?.bodyType || nsHttpApiInterface.EReqBodyFormat.JSON);
            setUrlencoded([]);
            setForm([]);
            if (props.requestParams?.bodyType === nsHttpApiInterface.EReqBodyFormat.FORM_DATA) {
                setForm(props.requestParams?.body || []);
            } else if (props.requestParams?.bodyType === nsHttpApiInterface.EReqBodyFormat.JSON) {
                setBody(props.requestParams?.body || []);
                if (props.requestParams?.body.length) {
                    // const bodyJosn = convertApiParamArrayToJson(props.requestParams?.body[0]);
                    // setRequestJson(JSON.stringify(bodyJosn, null, 4))
                    setRequestJson(props.json5Body || '')
                }
            } else if (props.requestParams?.bodyType === nsHttpApiInterface.EReqBodyFormat.X_WWW_FORM_URLENCODED) {
                setUrlencoded(props.requestParams?.body || []);
            } else if (props.requestParams?.bodyType === nsHttpApiInterface.EReqBodyFormat.BINARY) {
                setBinary(props.requestParams?.body || initBinary());
            }
            setRequestParams(props.requestParams);
        }
    }, [props.requestParams, props.json5Body]);

    useEffect(() => {
        if (props.requestParams?.body) {
            let newReqRadioType;
            if (props.forceReqRadioType) {
                newReqRadioType = props.forceReqRadioType;
            } else {
                newReqRadioType = getDefaultReqRadioType(props.requestParams);
            }
            setReqRadioType(newReqRadioType);
        }
    }, [props.requestParams]);

    /**
     * @description 解析 url 中 path 参数
     */
    useEffect(() => {
        const arr = commonFun.pathParamsRegexArrFun(props.path);
        const resDataToParamsArr = arr?.reduce((pre, cur) => {
            const findItem = props.requestParams?.rest?.find(item => item?.N === cur);
            const obj = {
                N: cur,
                V: findItem?.V || '',
                D: findItem?.D || '',
            };
            pre.push(obj);
            return pre;
        }, []);
        pathParamsComM.resDataToParams(resDataToParamsArr || []);
    }, [props.path]);

    /**
     * FUNC_DES: 获取可用账号列表
     */
    const fetchAccountList = async () => {
        try {
            const resData = await nsMockManageApiManageMainApiTestAccountList.remote({});
            resData && setAccountList(resData.list);
        } catch (e) { }
    }

    /**
     * FUNC_DES: reqRadioType, apiDocTab 值变化执行逻辑
     */
    useEffect(() => {
        const isReqRadioType = reqRadioType === EReqRadioType.ACCOUNT_NUMBER;
        const isGetApiDocTab = getApiDocTab() === 'test';
        if (isReqRadioType && isGetApiDocTab) {
            fetchAccountList();
        }
    }, [reqRadioType, getApiDocTab()]);

    /**
     * FUNC_DES: 获取 localStorage 中存储的账号信息
     */
    useEffect(() => {
        const localStorageInfo = localStorage.getItem('apiTest_accountNumber');
        setAccountNumber(localStorageInfo || '');
    }, []);

    const renderBodyFormat = () => {
        if (reqRadioType === 'body') {
            return (
                <BodyTypeRadioTab
                    type={EBodyTypeRadioTabType.REQUEST}
                    value={bodyType}
                    onChange={(e) => setBodyType(e.target.value)}
                    // 用json输入框代替导入json
                    // renderJsonModal={() => (
                    //     <ImportJsonModal
                    //         getData={getDataSource}
                    //         setBody={setBody}
                    //         checkJsonError={true}
                    //     />
                    // )}
                    style={{ marginTop: '-8px' }}
                />
            );
        }
    };

    /**
     * FUNC_DES: 选择账号 & 把账号信息存入 localStorage
     */
    const setAccountNumber_change = (val: string) => {
        setAccountNumber(val);
    }
    /**
     * FUNC_DES: 格式化
     */
    const format: () => Promise<boolean> = () => {
        return new Promise(async (res) => {
            try {
                // const jsonStr = await commonFun.formatJsonHasComment(requestJson);
                const jsonStr = await nsMockManageApiManageMainApiJson5ParamPrettyPOST.remote(requestJson || '{}');
                setRequestJson(jsonStr.data.prettyJson5);
                replaceJsonData();
                setErrorLine([]);
                setTimeout(() => res(true), 0)
            } catch (e: any) {
                message.warn('请输入正确的json格式');
                const err = JSON.parse(e.message);
                setErrorLine([{
                    row: err.errorLineNo - 1,
                    column: 0,
                    text: err.errorMsg,
                    type: 'error'
                }])
                res(false)
                // setErrorLine(commonFun.analyseFormatJsonError(e));
            }
        })
    }

    /**
     * FUNC_DES: 账号内容
     */
    const renderAccount = () => {
        if (reqRadioType !== EReqRadioType.ACCOUNT_NUMBER) {
            return;
        }
        return (
            <div>
                <div style={{ marginBottom: 8 }}>
                    <a
                        href="https://docs.corp.kuaishou.com/k/home/<USER>/fcAC_tHkCT67MmZppceABuEif"
                        target="_blank"
                    >
                        API测试账号信息使用指南
                    </a>
                </div>
                <Select
                    allowClear
                    placeholder="请选择账号"
                    style={{ width: 380 }}
                    value={accountNumber || undefined}
                    onChange={setAccountNumber_change}
                >
                    {
                        accountList?.map(item => (
                            <Select.Option
                                key={item.mobile}
                                value={item.mobile}
                            >
                                {item.mobile}（{item.platform}）
                            </Select.Option>
                        ))
                    }
                </Select>
            </div>
        );
    }
    return (
        <div className={classNames(css.editApiRequest, props.className)}>
            {
                props?.kdevTitle || <KdevTitle
                    text="请求参数"
                    size="small"
                    className={css.title}
                />
            }
            <ReqRadioGroupTab
                value={reqRadioType}
                style={{ pointerEvents: 'auto' }}
                onChange={(e) => {
                    setReqRadioType(e.target.value)
                }}
                className={css.radioGroupType}
                filterReqRadioType={props.filterReqRadioType}
                requestParams={requestParams}
                comType={props.comType}
            />
            {/* 账号内容 */}
            {renderAccount()}
            {renderBodyFormat()}
            <div className={css.row} style={{ display: reqRadioType === EReqRadioType.HEADER ? 'block' : 'none' }}>
                <EditParamTable
                    type="header"
                    params={props.requestParams?.header || []}
                    ref={headerEditParamTableRef}
                    dataChange={() => {
                        const data = headerEditParamTableRef.current?.getParams();
                        if (data) {
                            setRequestParams({
                                ...requestParams,
                                header: data
                            } as any)
                        }
                    }}
                    filterColumns={props.filterColumns}
                    isCheckbox={props.isCheckbox}
                    disabledType={[14]}
                />
            </div>
            <div
                className={css.row}
                style={{
                    display: (reqRadioType === EReqRadioType.BODY
                        && bodyType === nsHttpApiInterface.EReqBodyFormat.FORM_DATA)
                        ? 'block' : 'none'
                }}
            >
                <EditParamTable
                    type="form"
                    params={form || []}
                    ref={formEditParamTableRef}
                    filterColumns={props.filterColumns}
                    isCheckbox={props.isCheckbox}
                />
            </div>
            <div
                className={css.row}
                style={{
                    display: (reqRadioType === EReqRadioType.BODY
                        && bodyType === nsHttpApiInterface.EReqBodyFormat.JSON)
                        ? 'block' : 'none'
                }}
            >
                <div className={css.bodyJsonViewTypeRow}>
                    <Segmented
                        value={bodyJsonViewType}
                        style={{ borderRadius: 4 }}
                        options={[
                            {
                                label: 'JSON格式',
                                value: EBodyJsonViewTypeEnum.JSON
                            },
                            {
                                label: '结构化',
                                value: EBodyJsonViewTypeEnum.FORMAT
                            },
                        ]}
                        onChange={async (e) => {
                            if (e === EBodyJsonViewTypeEnum.FORMAT) {
                                try {
                                    // const jsonStr = await commonFun.formatJsonHasComment(requestJson);
                                    const jsonStr = await nsMockManageApiManageMainApiJson5ParamPrettyPOST.
                                        remote(requestJson || '{}');
                                    setRequestJson(jsonStr.data.prettyJson5);
                                    if (!await replaceJsonData()) {
                                        return
                                    }
                                    setBodyJsonViewType(e as EBodyJsonViewTypeEnum)
                                    setErrorLine([])
                                    // commonFun.replaceJsonData(json5.parse(jsonStr), setBody, getDataSource());
                                } catch (e: any) {
                                    message.warn('请输入正确的json格式');
                                    const err = JSON.parse(e.message);
                                    setErrorLine([{
                                        row: err.errorLineNo - 1,
                                        column: 0,
                                        text: err.errorMsg,
                                        type: 'error'
                                    }])
                                    // setErrorLine(commonFun.analyseFormatJsonError(e));
                                    return;
                                }
                            } else {
                                const formattedJson = await commonFun.formatConvertToJson5(getDataSource());
                                setRequestJson(formattedJson.data);
                                setBodyJsonViewType(e as EBodyJsonViewTypeEnum)
                            }
                        }}
                    />
                </div>
                <JsonAceEditor
                    className={[css.jsonAceEditor, bodyJsonViewType === EBodyJsonViewTypeEnum.JSON ? '' : css.h0]}
                    json={requestJson}
                    setJsonResp={setRequestJson}
                    formatJsonStr={format}
                    errorLine={errorLine}
                ></JsonAceEditor>
                <EditParamTable
                    style={{
                        display: bodyJsonViewType === EBodyJsonViewTypeEnum.FORMAT ? 'block' : 'none',
                        borderBottom: 'none'
                    }}
                    className={css.editParamTable}
                    type="body"
                    params={body || []}
                    ref={bodyEditParamTableRef}
                    hasRoot
                    filterColumns={props.filterColumns}
                    disabledType={[14]}
                    queryUpdate={async (newData) => {
                        const bodyJosn = convertApiParamArrayToJson(newData[0]);
                        setRequestJson(JSON.stringify(bodyJosn, null, 4));
                    }}
                />
            </div>
            <div
                className={css.row}
                style={{
                    display: (reqRadioType === EReqRadioType.BODY
                        && bodyType === nsHttpApiInterface.EReqBodyFormat.X_WWW_FORM_URLENCODED)
                        ? 'block' : 'none'
                }}
            >
                <EditParamTable
                    type="form"
                    params={urlencoded || []}
                    ref={urlencodedEditParamTableRef}
                    filterColumns={props.filterColumns}
                    isCheckbox={props.isCheckbox}
                    disabledType={[14]}
                />
            </div>
            <div
                className={css.row}
                style={{
                    display: (reqRadioType === EReqRadioType.BODY
                        && bodyType === nsHttpApiInterface.EReqBodyFormat.BINARY)
                        ? 'block' : 'none'
                }}
            >
                <Upload
                    key={binary[0][EApiParam.VALUE]}
                    name="file"
                    beforeUpload={(file) => {
                        if (file.size > 1024 * 1000 * 200) {
                            message.warn('您选择的上传的文件过大，请选择200M以内文件！');
                            return false;
                        }
                        return true;
                    }}
                    action="/api/mock/manage/apiManage/main/api/upload/api/file"
                    onChange={(info) => {
                        if (info.file.status === 'done') {
                            setBinary([{
                                ...binary[0],
                                [EApiParam.VALUE]: info.file.response.data.downUrl
                            }]);
                        }
                    }}
                    onRemove={() => {
                        setBinary([{
                            ...binary[0],
                            [EApiParam.VALUE]: ''
                        }]);
                    }}
                    defaultFileList={binary[0][EApiParam.VALUE] ? [{
                        uid: '1',
                        name: (binary[0][EApiParam.VALUE].split('/').pop() || ''),
                        status: 'done',
                        url: binary[0][EApiParam.VALUE],
                    }] : []}
                >
                    <Button size="small" icon={<KdevIconFont id={common_system_upload} />} >&nbsp;请选择文件</Button>
                </Upload >
            </div>
            <div
                className={css.row}
                style={{ display: reqRadioType === EReqRadioType.QUERY ? 'block' : 'none' }}
            >
                <PathParamsCom model={pathParamsComM} />
            </div>
            <div className={css.row} style={{ display: reqRadioType === EReqRadioType.QUERY ? 'block' : 'none' }}>
                <div style={{ marginBottom: 8, color: 'rgba(137, 138, 140, 1)' }}>
                    query 参数
                </div>
                <EditParamTable
                    type="query"
                    params={props.requestParams?.query || []}
                    ref={queryEditParamTableRef}
                    queryUpdate={queryUpdate}
                    dataChange={() => {
                        const data = queryEditParamTableRef.current?.getParams();
                        if (data) {
                            setRequestParams({
                                ...requestParams,
                                query: data
                            } as any)
                        }
                    }}
                    filterColumns={props.filterColumns}
                    isCheckbox={props.isCheckbox}
                    disabledType={[14]}
                />
            </div>
        </div >
    );
});

export { EditApiRequest, IEditApiRequestRef };
