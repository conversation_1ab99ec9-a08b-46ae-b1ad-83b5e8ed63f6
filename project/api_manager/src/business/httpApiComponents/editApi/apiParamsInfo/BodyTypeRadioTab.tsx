import React from 'react';
import { Radio, Divider } from 'antd';
import { RadioChangeEvent } from 'antd/lib/radio';
import css from './BodyTypeRadioTab.less';
import { nsHttpApiInterface } from '@/remote';

const requestBodyFormatOptions = [
    { label: 'Form-data', value: nsHttpApiInterface.EReqBodyFormat.FORM_DATA },
    { label: 'JSON', value: nsHttpApiInterface.EReqBodyFormat.JSON },
    { label: 'x-www-form-urlencoded', value: nsHttpApiInterface.EReqBodyFormat.X_WWW_FORM_URLENCODED },
    { label: 'Binary', value: nsHttpApiInterface.EReqBodyFormat.BINARY, disabled: true },
    { label: 'XML', value: nsHttpApiInterface.EReqBodyFormat.XML, disabled: true },
    { label: 'Raw', value: nsHttpApiInterface.EReqBodyFormat.RAW, disabled: true },
];

const responseBodyFormatOptions = [
    { label: 'JSON', value: nsHttpApiInterface.EResBodyFormat.JSON },
    { label: 'XML', value: nsHttpApiInterface.EResBodyFormat.XML, disabled: true }
];

const getOptions = (type: EBodyTypeRadioTabType) => {
    const obj = {
        [EBodyTypeRadioTabType.REQUEST]: requestBodyFormatOptions,
        [EBodyTypeRadioTabType.RESPONSE]: responseBodyFormatOptions
    };
    return obj[type] || [];
};

export enum EBodyTypeRadioTabType {
    REQUEST = 1,
    RESPONSE
}

interface IProps {
    type: EBodyTypeRadioTabType;
    value: nsHttpApiInterface.EReqBodyFormat | nsHttpApiInterface.EResBodyFormat;
    onChange(e: RadioChangeEvent): void;
    renderJsonModal?(): React.ReactNode;
    style?: React.CSSProperties;
}

export function BodyTypeRadioTab(props: IProps) {
    return (
        <div className={css.bodyFormatBox} style={props.style}>
            <Radio.Group
                options={getOptions(props.type)}
                value={props.value}
                onChange={props.onChange}
            />
            {
                props.value === 'json' && props.renderJsonModal &&
                <>
                    <Divider type="vertical" className={css.divider} />
                    {props.renderJsonModal()}
                </>
            }
        </div>
    );
}
