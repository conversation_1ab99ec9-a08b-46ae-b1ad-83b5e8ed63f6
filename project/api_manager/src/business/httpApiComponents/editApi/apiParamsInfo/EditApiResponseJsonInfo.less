.optionsLine {
    display: flex;
    flex-wrap: nowrap;
    align-items: center;
    margin: 10px 0 0;
    height: 48px;
    border-radius: 4px 4px 0px 0px;
    border: 1px solid #EBEDF0;
    padding-left: 24px;
    .optionWarp {
        height: 40px;
        margin-right: 24px;
        display: flex;
        flex-wrap: nowrap;
        align-items: center;
        cursor: pointer;
    }
}
.line {
    width: 1px;
    height: 16px;
    background: #D5D6D9;
    margin: 0 24px;
}
.option {
}
.icon {
    margin-right: 4px;
    color: #898A8C !important;
}
.aceEditorWrap {
    overflow: hidden;
    border: 1px solid #EBEDF0;
    border-radius: 0 0 4px 4px;

}
:global {
    .ace-xcode .ace_gutter {
        background-color: #FAFCFF !important;
    }
    // .ant-tooltip {
    //     max-width: none;
    // }
}
.title {
    font-weight: 600;
    margin-bottom: 16px;

    &::before {
        border-radius: 1.5px;
    }
}
.tip {
    // 不换行
    white-space: nowrap;
    margin-left: 16px;
    color: #898A8C;
}