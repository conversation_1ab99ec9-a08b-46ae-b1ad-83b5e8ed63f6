import React, { forwardRef, useImperativeHandle, Ref, useRef, useEffect, useState } from 'react';
import css from './EditApiResponseJsonInfo.less';
import { nsHttpApiInterface, nsMockManageApiManageMainApiParamGET, nsMockManageApiManageMainApiHttpCodeList } from '@/remote';
import { Button, Radio, Input, InputNumber, message, Tooltip, Select } from 'antd';
import {
    QuestionCircleOutlined
} from '@ant-design/icons';
import { KdevIconFont, AceEditor, KdevTitle } from '@/business/commonComponents';
import { CopyToClipboard } from 'react-copy-to-clipboard';
import { convertApiParamArrayToJson } from './ImportJson';
import { common_system_copy02, common_system_remove, common_system_automation_module, common_system_help02 } from '@kid/enterprise-icon/icon/output/icons';
import JSON5 from 'json5';

interface IProps {
    className?: string;
    succJson?: nsMockManageApiManageMainApiParamGET.JsonExample;
    failJson?: nsMockManageApiManageMainApiParamGET.JsonExample;
    apiResponse?: nsHttpApiInterface.IApiResponse;
    statusCodeList?: nsMockManageApiManageMainApiHttpCodeList.StatusCodeType[];
}

interface IEditApiResponseJsonInfo {
    succJson: nsMockManageApiManageMainApiParamGET.JsonExample;
    failJson: nsMockManageApiManageMainApiParamGET.JsonExample;
}

interface EditApiResponseJsonInfoRef {
    getParams(): IEditApiResponseJsonInfo;
    checkParams(): boolean;
}

enum TabEnum {
    SUCC = 'succ',
    FAIL = 'fail',
}

const EditApiResponseJsonInfo = forwardRef((props: IProps, ref: Ref<EditApiResponseJsonInfoRef>) => {
    if (!props.succJson || !props.failJson) {
        return null;
    }
    const [succJson, setSussJson] = useState<nsMockManageApiManageMainApiParamGET.JsonExample>(props.succJson);
    const [failJson, setFailJson] = useState<nsMockManageApiManageMainApiParamGET.JsonExample>(props.failJson);
    const [currentTab, setCurrentTab] = useState<TabEnum.SUCC | TabEnum.FAIL>(TabEnum.SUCC);
    const currentValue = () => currentTab === TabEnum.SUCC ? succJson : failJson;
    const setCurrentValue = () => currentTab === TabEnum.SUCC ? setSussJson : setFailJson;
    const getParams = () => {
        return {
            succJson,
            failJson
        };
    };
    const removeCommentary = (str) => JSON.stringify(JSON5.parse(str));
    const isValidJson = (str) => {
        // 移除注释
        try {
            str = removeCommentary(str);
        } catch (e) {
            return false;
        }

        try {
            JSON.parse(str);
            return true; // 字符串符合 JSON 格式
        } catch (e) {
            return false; // 解析失败，字符串不符合 JSON 格式
        }
    };

    // 返回示例校验（不做任何校验，用户可以写任何东西）
    const checkParams = () => {
        return false;
    };

    useImperativeHandle(ref, () => ({
        getParams,
        checkParams
    }));
    const options = props.statusCodeList?.map((i, index) => (
        <Select.Option value={i.key} label={i.key} key={i.key}>
            <div className="demo-option-label-item">
                <span style={{ marginRight: 12 }}>{i.key}</span>
                <span style={{ color: '#898A8C' }}>{i.value}</span>
            </div>
        </Select.Option>));
    return (
        <div className={props.className}>
            <KdevTitle
                text={<div>
                    <span style={{ marginRight: 4 }}>返回示例</span>
                    <Tooltip placement="topRight" arrowPointAtCenter
                        overlayStyle={{ whiteSpace: 'nowrap', maxWidth: 'none' }}
                        title="返回示例仅为示例展示，与返回参数配置无关，且不可用于后续代码一致性校验。">
                        <QuestionCircleOutlined style={{ color: '#898A8C' }} />
                    </Tooltip>
                </div>}
                size="small"
                className={css.title}
            />
            <Radio.Group value={currentTab} onChange={e => setCurrentTab(e.target.value)}>
                <Radio.Button value={TabEnum.SUCC}>成功示例</Radio.Button>
                <Radio.Button value={TabEnum.FAIL}>失败示例</Radio.Button>
            </Radio.Group>
            <div className={css.optionsLine}>
                <span>状态码：</span>
                <Select
                    style={{ width: 87 }}
                    size="small"
                    value={currentValue().statusCode || 0}
                    showSearch
                    placeholder="状态码"
                    onChange={(e) => {
                        setCurrentValue()({
                            ...currentValue(),
                            statusCode: Number(e)
                        });
                    }}
                    virtual={false}
                    dropdownMatchSelectWidth={200}
                    optionLabelProp="value"
                >
                    {options}
                </Select>
                <div className={css.line}></div>
                <div className={css.optionWarp} onClick={() => {
                    if (props.apiResponse) {
                        setCurrentValue()({
                            ...currentValue(),
                            // tslint:disable-next-line:max-line-length
                            exampleJson: JSON.stringify(convertApiParamArrayToJson(props.apiResponse.body[0]), null, 4)
                        });
                    } else {
                        message.info('请稍后重试');
                    }
                }}>
                    <KdevIconFont className={css.icon} id={common_system_automation_module} />
                    <span className={css.option}>自动生成</span>
                </div>
                <CopyToClipboard text={currentValue().exampleJson || ''}>
                    <div className={css.optionWarp} onClick={() => message.success('复制成功')}>
                        <KdevIconFont className={css.icon} id={common_system_copy02} />
                        <span className={css.option}>复制</span>
                    </div>
                </CopyToClipboard>
                {/* 去掉格式化能力 */}
                {/* <div className={css.optionWarp} onClick={() => {
                    try {
                        const formatJson = JSON.stringify(JSON.parse(currentValue().exampleJson), null, 4);
                        setCurrentValue()({
                            ...currentValue(),
                            exampleJson: formatJson
                        });
                    } catch (e) {
                        message.error('JSON格式错误');
                    }
                }}>
                    <KdevIconFont className={css.icon} id={common_system_remove} />
                    <span className={css.option}>格式化</span>
                </div> */}
            </div>
            <AceEditor
                className={css.aceEditorWrap}
                theme="xcode"
                width="100%"
                value={currentValue().exampleJson || ''}
                onChange={(val) => {
                    setCurrentValue()({
                        ...currentValue(),
                        exampleJson: val
                    });
                }}
                showPrintMargin={false}
                focus
                minLines={5}
                maxLines={50}
            />
        </div>
    );
});

export { EditApiResponseJsonInfo, EditApiResponseJsonInfoRef, TabEnum };
