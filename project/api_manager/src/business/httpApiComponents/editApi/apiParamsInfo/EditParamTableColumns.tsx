import React, { useState, ChangeEvent, useContext, useEffect, useRef, FormEventHandler } from 'react';
import css from './EditApiParamsInfo.less';
import { Input, Select, Checkbox } from 'antd';
import { IRecord } from './EditParamTable';
import { CheckboxChangeEvent } from 'antd/lib/checkbox';
import { nsMockManageApiManageMainApiParamTypeListGET } from '@/remote';
import { EApiParam, EParamType } from '@/business/httpApiComponents/editApi/apiParamsInfo/formatParamsTools';

interface IInputProps extends IRecord {
    index: number;
    onChange(e: ChangeEvent<HTMLInputElement>): void;
    onInput?: FormEventHandler<HTMLInputElement>;
    disabled?: boolean;
    focusKey?: string;
    rowKey?: string;
    handleMouseLeave: () => void;
}

function RenderName(
    {
        onChange, onInput, index, focusKey, rowKey, handleMouseLeave, disabled, ...record
    }: IInputProps
) {
    const [name, setName] = React.useState(record[EApiParam.NAME]);
    const nameInputRef = useRef<Input>(null);

    // T-参数类型 N-参数名称
    useEffect(() => {
        if (record.parent && record.parent[EApiParam.TYPE] === EParamType.array) {
            setName(`${record.parent[EApiParam.NAME]}[${index}]`);
            // 重置name
            onChange({ target: { value: `${record.parent[EApiParam.NAME]}[${index}]` } } as any);
        } else {
            setName(record[EApiParam.NAME]);
        }
    }, [record.parent, record?.parent?.T, record?.parent?.N, index, record[EApiParam.NAME]]);

    useEffect(() => {
        if (focusKey === rowKey && nameInputRef.current) {
            nameInputRef.current?.input.scrollIntoView({ block: 'nearest' });
            nameInputRef.current?.focus();
        }
    }, [focusKey]);

    return (
        <div className={css.tdContainer} style={{ flexGrow: 1 }}>
            <Input
                className={css.nameInput}
                placeholder="请输入"
                value={name}
                onMouseLeave={handleMouseLeave}
                disabled={disabled}
                onChange={(e) => {
                    setName(e.target.value);
                    onChange(e);
                }}
                onInput={onInput}
                ref={nameInputRef}
            />
        </div>
    );
}

interface ISelectProps extends IRecord {
    onChange(val: number): void;
    disabledType?: number[];
}

function ParamTypeSelect({ onChange, disabledType = [], ...record }: ISelectProps) {
    const [type, setType] = useState<number>(Number(record[EApiParam.TYPE]));
    const [paramTypeList, setParamTypeList] = useState<nsMockManageApiManageMainApiParamTypeListGET.IItem[]>([]);

    // 获取参数类型列表
    const getParamTypeList = async () => {
        try {
            const res = await nsMockManageApiManageMainApiParamTypeListGET.remotePromise();
            setParamTypeList(res.paramTypes);
        } catch { }
    };

    useEffect(() => {
        setType(Number(record[EApiParam.TYPE]));
    }, [record[EApiParam.TYPE]]);

    useEffect(() => {
        getParamTypeList();
    }, []);

    return (
        <Select
            style={{ width: 106 }}
            placeholder="请选择"
            virtual={false}
            value={type}
            onChange={(val) => {
                setType(val);
                onChange(val);
            }}
        >
            {
                paramTypeList.map(item => (
                    <Select.Option key={item.code} value={item.code} disabled={disabledType.includes(item.code)}>
                        {`[${item.name}]`}
                    </Select.Option>
                ))
            }
        </Select>
    );
}

interface ICheckboxProps extends IRecord {
    onChange(e: CheckboxChangeEvent): void;
}

function RenderRequired({ onChange, ...record }: ICheckboxProps) {
    const [required, setRequired] = useState<0 | 1>(record[EApiParam.REQUIRED]);

    useEffect(() => {
        setRequired(record[EApiParam.REQUIRED]);
    }, [record]);

    return (
        <Checkbox
            checked={Boolean(required)}
            onChange={(e) => {
                setRequired(e.target.checked ? 1 : 0);
                onChange(e);
            }}
        >是</Checkbox>
    );
}

function RenderDescription({ onChange, ...record }: IInputProps) {
    const [description, setDescription] = useState(record[EApiParam.DESC]);

    return (
        <Input
            placeholder="请填写参数说明"
            value={description}
            onChange={(e) => {
                setDescription(e.target.value);
                onChange(e);
            }}
        />
    );
}

function RenderValue({ onChange, handleMouseLeave, ...record }: IInputProps) {
    const [value, setValue] = useState(record[EApiParam.VALUE]);

    return (
        <Input
            placeholder="请填写参数值"
            value={value}
            onMouseLeave={handleMouseLeave}
            onChange={(e) => {
                setValue(e.target.value);
                onChange(e);
            }}
        />
    );
}

export { RenderName, ParamTypeSelect, RenderRequired, RenderDescription, RenderValue };
