import React, { forwardRef, useImperativeHandle, Ref, useRef, useEffect } from 'react';
import css from './EditApiParamsInfo.less';
import { EditApiRequest, IEditApiRequestRef, EBodyJsonViewTypeEnum } from './EditApiRequest';
import { EditApiResponse, IEditApiResponseRef } from './EditApiResponse';
import { nsHttpApiInterface, nsMockManageApiManageMainApiParamGET } from '@/remote';
import classname from 'classnames';

interface IProps {
    className?: string;
    requestParams?: nsHttpApiInterface.IApiRequest;
    responseParams?: nsHttpApiInterface.IApiResponse;
    path?: string; // path参数有可能未传
    setApiResponse?: (value: any) => void;
    requestJson5Body?: string;
    responseJson5Body?: string;
}

interface IEditApiParamsInfo {
    request: nsHttpApiInterface.IApiRequest;
    response: nsHttpApiInterface.IApiResponse;
}

interface IEditApiParamsInfoRef {
    getParams(): IEditApiParamsInfo;
    checkParams(): boolean;
    getApiParamsList(): nsMockManageApiManageMainApiParamGET.IReturn;
    replaceJsonData(): Promise<boolean>
    setRequestJson: (value: string) => void;
    setResponseJson: (value: string) => void;
}

const EditApiParamsInfo = forwardRef((props: IProps, ref: Ref<IEditApiParamsInfoRef>) => {
    const editApiRequestRef = useRef<IEditApiRequestRef>(null);
    const editApiResponseRef = useRef<IEditApiResponseRef>(null);

    const getParams = (): IEditApiParamsInfo => {
        return {
            request: editApiRequestRef?.current?.getParams() || {
                json5Body: '',
                header: [],
                query: [],
                rest: [],
                body: [],
                bodyType: nsHttpApiInterface.EReqBodyFormat.JSON,
                bodyJsonViewType: editApiRequestRef?.current?.bodyViewType
            },
            response: editApiResponseRef?.current?.getParams() || {
                json5Body: '',
                header: [],
                body: [],
                bodyType: nsHttpApiInterface.EResBodyFormat.JSON,
                bodyJsonViewType: editApiResponseRef?.current?.bodyViewType
            }
        };
    };

    // 校验参数 true-未通过 false-通过
    const checkParams = (): boolean => {
        if (editApiRequestRef?.current?.checkParams()) {
            return true;
        }
        if (editApiResponseRef?.current?.checkParams()) {
            return true;
        }
        return false;
    };

    const getApiParamsList = (): IEditApiParamsInfo => {
        return {
            request: editApiRequestRef?.current?.getSourceParams() || {
                json5Body: '',
                header: [],
                query: [],
                rest: [],
                body: [],
                bodyType: nsHttpApiInterface.EReqBodyFormat.JSON
            },
            response: editApiResponseRef?.current?.getSourceParams() || {
                json5Body: '',
                header: [],
                body: [],
                bodyType: nsHttpApiInterface.EResBodyFormat.JSON
            }
        };
    };

    async function replaceJsonData() {
        let requestCheck = true
        let responseCheck = true
        if (editApiRequestRef?.current?.getParams().bodyType === nsHttpApiInterface.EReqBodyFormat.JSON) {
            requestCheck = await editApiRequestRef?.current?.replaceJsonData();
        }
        if (editApiResponseRef?.current?.getParams().bodyType === nsHttpApiInterface.EResBodyFormat.JSON) {
            responseCheck = await editApiResponseRef?.current?.replaceJsonData();
        }
        return requestCheck && responseCheck
    }

    useImperativeHandle(ref, () => ({
        getParams,
        checkParams,
        getApiParamsList,
        replaceJsonData,
        setRequestJson,
        setResponseJson,
    }));
    const setRequestJson = (value: string) => {
        editApiRequestRef?.current?.setRequestJson(value);
    }
    const setResponseJson = (value: string) => {
        editApiResponseRef?.current?.setResponseJson(value);
    }

    return (
        <div className={classname(css.editApiParamsInfo, props.className)}>
            <EditApiRequest
                className={css.editApiRequest}
                requestParams={props.requestParams}
                ref={editApiRequestRef}
                path={props.path || ''}
                json5Body={props.requestJson5Body}
            />
            <EditApiResponse
                className={css.editApiResponse}
                responseParams={props.responseParams}
                ref={editApiResponseRef}
                setApiResponse={props.setApiResponse}
                json5Body={props.responseJson5Body}
            />
        </div>
    );
});

export { EditApiParamsInfo, IEditApiParamsInfoRef };
