import React from 'react';
import { observer } from 'mobx-react';
import { observable, action} from 'mobx';
import classname from 'classnames';
import { MarkdownEditorCustom } from '@biz/comps';
import css from './RemarkMarkdownEditor.less';

interface IQueryValueMethod {
    getMarkdownValue: () => string;
    getHtmlValue: () => string;
}

interface IProps {
    className?: string;
    markdownValue?: string;
    queryValueMethod?: (IQueryValueMethod) => void;
}

@observer
export class RemarkMarkdownEditor extends React.Component<IProps> {
    @observable private markdownEditor: any;
    private readonly markdownConf = {
        autoHeight: true,
        // height: 250,
        lineNumbers: false,
        htmlDecode: 'style,script,iframe|on*', // 开启 HTML 标签解析，为了安全性，默认不开启
        watch: false, // 关闭实时预览
        saveHTMLToTextarea: false, // html textarea 需要开启配置项 saveHTMLToTextarea == true
        placeholder: '请输入',
        toolbarIcons: () => {
            // 在 MR 区域的使用，由于 sticky 布局，不要打开任何有 弹出框的菜单功能
            return [
                // 'selfPreview',
                // '|',
                'bold',
                'italic',
                'quote',
                // '|',
                // 'code',
                // 'preformatted-text',
                // 'link',
                // '|',
                'list-ul',
                'list-ol',
                // 'table',
                // '||',
                // 'help',
            ];
        },
        // toolbarIconTexts: {
        //     selfPreview: '预 览'
        // },
    };

    @action.bound
    private getMarkdownValue(): string {
        return this.markdownEditor?.getMarkdown();
    }

    @action.bound
    private getHtmlValue(): string {
        return this.markdownEditor?.getHTML();
    }

    // 设置markdown默认值
    @action.bound
    private setMarkdownValue(): void {
        if (this.props.markdownValue) {
            this.markdownEditor.setMarkdown(this.props.markdownValue);
        }
    }

    public render(): React.ReactNode {
        return (
            <div
                className={classname(css.remarkMarkdownEditor, this.props.className)}
            >
                <MarkdownEditorCustom
                    conf={ {
                        ...this.markdownConf,
                        ...{
                            saveHTMLToTextarea : true,
                            onload: (editor) => {
                                if (!editor) {
                                    return;
                                }
                                this.markdownEditor = editor;
                                this.setMarkdownValue();
                                this.props?.queryValueMethod
                                && this.props.queryValueMethod({
                                    getMarkdownValue: this.getMarkdownValue,
                                    getHtmlValue: this.getHtmlValue
                                });
                            }
                        }
                    } }
                    className={css.markdownEditorCustom}
                />
            </div>
        );
    }
}
