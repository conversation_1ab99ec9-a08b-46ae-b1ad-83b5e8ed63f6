import { AView } from 'libs';
import React from 'react';
import { observer } from 'mobx-react';
import { Bind } from 'lodash-decorators';
import { bindObserver } from '@libs/mvvm';
import { Collapse, Radio, Button, Tooltip } from 'antd';
import { RequestParamsM } from './RequestParamsM';
import css from './Requestparams.less';
import { tips, reqDataEnum } from './enum';
import { MultiDimensional } from '@/business/editJsonTable/multiDimensional/MultiDimensional';
import { OneDimensional } from '@/business/editJsonTable/oneDimensional/OneDimensional';
import { JsonImport } from '@/business/jsonImportModal/JsonImport';
import { RequestTypeRadio } from '@/business/httpApiComponents/requestTypeRadio/RequestTypeRadio';

const { Panel } = Collapse;

@observer
export class RequestParams extends AView<RequestParamsM> {

    @Bind
    private onChangeRequestType(e): void {
        this.model.changeRequestType(e.target.value);
    }

    @Bind
    private renderJsonImportBtn(): React.ReactNode {
        const model = this.model;
        if (model.requestType !== 2) {
            return;
        }
        return (
            <>
                <Button
                    type={'primary'}
                    onClick={() => model.onOpenJsonImportModal(model.reqDataType)}
                    className={css.jsonImportBtn}
                >
                    JSON导入
                </Button>
                <span className={css.jsonTootip}>使用json导入会覆盖原有是否必填、备注项的值</span>
                <JsonImport model={model.jsonImportM} />
            </>
        );
    }

    @Bind
    protected renderRequestTable(): React.ReactNode {
        const model = this.model;
        if (model.requestType === 1) {
            return <OneDimensional model={model.reqHeaderOneDimensionalM} />;
        }
        if (model.requestType === 2) {
            if (model.reqDataType === 'body') {
                return (
                    <MultiDimensional
                        model={model.reqBodyMultiDimensionalM}
                        tableBottom={this.renderJsonImportBtn()}
                    />
                );
            }
            if (model.reqDataType === 'form') {
                return <OneDimensional model={model.reqFormOneDimensionalM} />;
            }
        }
        if (model.requestType === 3) {
            return <OneDimensional model={model.reqQueryOneDimensionalM} />;
        }
        if (model.requestType === 4) {
            return <OneDimensional model={model.reqPathOneDimensionalM} />;
        }
    }

    @Bind
    private renderExpandOperate(): React.ReactNode {
        const model = this.model;
        if (model.requestType === 2 && model.reqDataType === reqDataEnum.BODY) {
            return (
                <span className={css.expandBtn}>
                    <a onClick={() => model.expandAllKeys(true, 'reqBodyMultiDimensionalM')}>
                        全部展开
                    </a>/
                    <a onClick={() => model.expandAllKeys(false, 'reqBodyMultiDimensionalM')}>
                        全部收起
                    </a>
                </span>
            );
        }
    }

    public render(): React.ReactNode {
        const model = this.model;
        return (
            <Collapse ghost className={css.requestParamsCollapse} defaultActiveKey={'1'}>
                <Panel header={'请求参数'} key={'1'}>
                    <RequestTypeRadio
                        requestTypeProps={{
                            value: model.requestType,
                            onChange: this.onChangeRequestType
                        }}
                        reqDataTypeProps={{
                            value: model.reqDataType,
                            onChange: model.onChangeReqDataType
                        }}
                        rigthSlot={this.renderExpandOperate()}
                    />
                    {this.renderRequestTable()}
                </Panel>
            </Collapse>
        );
    }
}
