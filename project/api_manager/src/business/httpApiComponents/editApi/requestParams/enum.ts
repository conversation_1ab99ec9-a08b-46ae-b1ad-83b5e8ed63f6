export enum reqDataEnum {
    HEADER = 'header',
    BODY = 'body',
    FORM = 'form',
    QUERY = 'query',
    PATH = 'path'
}

interface ISelectOptions {
    label?: string;
    value: string;
}

export interface IReqList {
    in?: string;
    key: string;
    name: string;
    example?: object;
    description: string;
    required: boolean;
    type: string;
    children?: IReqList[];
}

interface ITips {
    [key: string]: string;
}

export const tips: ITips = {
    queryTips: '111',
    pathTips: ''
};
