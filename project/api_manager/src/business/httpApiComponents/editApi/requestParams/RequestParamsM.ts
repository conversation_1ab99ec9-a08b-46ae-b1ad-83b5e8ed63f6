import { AViewModel } from 'libs';
import { observable, action } from 'mobx';
import { JSONbigStringify, formatKey1, JSONbigParse } from '@/index.config/tools';
import { IReqList, reqDataEnum } from './enum';
import { MultiDimensionalM } from '@/business/editJsonTable/multiDimensional/MultiDimensionalM';
import { OneDimensionalM } from '@/business/editJsonTable/oneDimensional/OneDimensionalM';
import { JsonImportM } from '@/business/jsonImportModal/JsonImportM';

export class RequestParamsM extends AViewModel {
    @observable public requestType: number = 2; // 1：headers 2：body 3：query 4：path
    @observable public reqDataType: string = 'body'; // from｜body
    @observable public reqBodyExample: string = '';

    private docId: number = 0;
    private moduleId: number = 0;

    public reqHeaderOneDimensionalM = new OneDimensionalM();
    public reqBodyMultiDimensionalM = new MultiDimensionalM();
    public reqFormOneDimensionalM = new OneDimensionalM(reqDataEnum.FORM);
    public reqQueryOneDimensionalM = new OneDimensionalM();
    public reqPathOneDimensionalM = new OneDimensionalM();
    public jsonImportM = new JsonImportM();

    @action.bound
    public init(request, moduleId: number, docId: number): void {
        this.moduleId = moduleId;
        this.docId = docId;
        request?.parameters && this.formatParameters(request.parameters);
        this.reqBodyMultiDimensionalM.init('RequestRoot', this.moduleId);
    }

    @action.bound
    public initData(): void {
        this.docId = 0;
        this.requestType = 2;
        this.reqBodyExample = '';
        this.reqHeaderOneDimensionalM.initData();
        this.reqBodyMultiDimensionalM.initData();
        this.reqFormOneDimensionalM.initData();
        this.reqQueryOneDimensionalM.initData();
        this.reqPathOneDimensionalM.initData();
    }

    @action.bound
    public getRequest() {
        const requestHeaderList = this.reqHeaderOneDimensionalM.getList().map(item => {
            item['in'] = 'header';
            return item;
        });
        let requestBodyList: IReqList[];
        if (this.reqDataType === 'form') {
            requestBodyList = this.reqFormOneDimensionalM.getList().map(item => {
                item['in'] = 'form';
                return item;
            });
        } else {
            requestBodyList = this.reqBodyMultiDimensionalM.getList().map(item => {
                item['in'] = 'body';
                item['example'] = JSONbigParse(this.reqBodyExample);
                return item;
            });
        }
        const requestQueryList = this.reqQueryOneDimensionalM.getList().map(item => {
            item['in'] = 'query';
            return item;
        });
        const requestPathList = this.reqPathOneDimensionalM.getList().map(item => {
            item['in'] = 'path';
            return item;
        });
        const parameters = [
            ...requestHeaderList,
            ...requestBodyList,
            ...requestQueryList,
            ...requestPathList
        ];
        return {parameters};
    }

    @action.bound
    public checkParams(): boolean {
        if (this.reqBodyMultiDimensionalM.checkParams()) {
            return true;
        }
        return false;
    }

    @action.bound
    public changeRequestType(requestType): void {
        this.requestType = requestType;
    }

    @action.bound
    public onChangeReqDataType(reqDataType): void {
        this.reqDataType = reqDataType;
    }

    @action.bound
    public expandAllKeys(boo: boolean, type: string): void {
        this[type].onExpandAllKeys(boo);
    }

    @action.bound
    public onOpenJsonImportModal(type: string): void {
        this.jsonImportM.init(type, this.reqBodyExample, this.docId);
        this.jsonImportM.onSaveJsonCallBack = this.onSaveJsonCallBack;
    }

    @action.bound
    public onSaveJsonCallBack(type: string, data, expamle: string) {
        this.formatParameters(data);
        this.reqBodyExample = expamle;
    }

    @action.bound
    private initReqType(bodyList, formList, queryList, pathList, headerList): void {
        if (bodyList.length || formList.length) {
            this.requestType = 2;
            return;
        }
        if (queryList.length) {
            this.requestType = 3;
            return;
        }
        if (pathList.length) {
            this.requestType = 4;
            return;
        }
        if (headerList.length) {
            this.requestType = 1;
            return;
        }
    }

    // 处理parameters参数
    @action.bound
    protected formatParameters(data: IReqList[]) {
        const headerList: IReqList[] = [];
        const bodyList: IReqList[] = [];
        const formList: IReqList[] = [];
        const queryList: IReqList[] = [];
        const pathList: IReqList[] = [];
        data.forEach(item => {
            switch (item.in) {
                case reqDataEnum.HEADER:
                    headerList.push(item);
                    break;
                case reqDataEnum.BODY:
                    this.reqDataType = 'body';
                    bodyList.push(item);
                    break;
                case reqDataEnum.FORM:
                    this.reqDataType = 'form';
                    formList.push(item);
                    break;
                case reqDataEnum.PATH:
                    pathList.push(item);
                    break;
                case reqDataEnum.QUERY:
                    queryList.push(item);
                    break;
            }
        });
        this.initReqType(bodyList, formList, queryList, pathList, headerList);
        if (headerList.length > 0) {
            const list = formatKey1(headerList)[0];
            this.reqHeaderOneDimensionalM.setList(list);
        }
        if (bodyList.length > 0) {
            const [list, expandedKey] = formatKey1(bodyList);
            const newKeys = expandedKey.filter(item => item.split('-').length < 3).splice(0, 10);
            this.reqBodyMultiDimensionalM.setListAndExpanedKeys(list, newKeys);
            this.reqBodyExample =
                list[0]?.example && JSONbigStringify(list[0]?.example) || '';
        }
        if (formList.length > 0) {
            const list = formatKey1(formList)[0];
            this.reqFormOneDimensionalM.setList(list);
        }
        if (queryList.length > 0) {
            const list = formatKey1(queryList)[0];
            this.reqQueryOneDimensionalM.setList(list);
        }
        if (pathList.length > 0) {
            const list = formatKey1(pathList)[0];
            this.reqPathOneDimensionalM.setList(list);
        }
    }

    @action.bound
    public getExpandedKeys(): string[] {
        return this.reqBodyMultiDimensionalM.expandedRowKeys;
    }
}
