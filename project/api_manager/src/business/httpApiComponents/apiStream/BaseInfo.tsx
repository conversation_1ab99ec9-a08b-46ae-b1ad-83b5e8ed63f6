import { AView } from 'libs';
import React from 'react';
import { observer } from 'mobx-react';
import { Bind } from 'lodash-decorators';
import { Descriptions, Tag } from 'antd';
import { ApiCollapse } from '@/business/commonComponents';
import { ApiStreamM } from './ApiStreamM';
import css from './ApiStream.less';
import { INodeBaseInfo, IChildProps } from './configure';

@observer
export class BaseInfo extends AView<ApiStreamM, IChildProps> {

    @Bind
    private renderNodeDetail(baseInfo?: INodeBaseInfo) {
        if (baseInfo) {
            return (
                <Descriptions>
                    <Descriptions.Item label="名称" span={24}>{baseInfo.name}</Descriptions.Item>
                    <Descriptions.Item label="PATH" span={24}>
                        <Tag color="#327DFF" className={css.method}>{baseInfo.method}</Tag>
                        {baseInfo.path}
                    </Descriptions.Item>
                    <Descriptions.Item label="关联分支" span={24}>{baseInfo?.branchName}</Descriptions.Item>
                    <Descriptions.Item label="描述" span={24}>{baseInfo?.desc}</Descriptions.Item>
                </Descriptions>
            );
        }
    }

    @Bind
    private renderExtra() {
        return (
            <Tag color="blue" className={css.nameTag}>{this.props.upOrNow === 'up' ? '上游' : '当前'}</Tag>
        );
    }

    public render(): React.ReactNode {
        const model = this.model;
        const baseInfo = this.props.upOrNow === 'up'
            ? model.upStreamNodeDetail?.baseInfo
            : model.nowNodeDetail?.baseInfo;
        return (
            <ApiCollapse className={css.baseInfo} header="基本信息" extra={this.renderExtra()}>
                {this.renderNodeDetail(baseInfo)}
            </ApiCollapse>
        );
    }
}
