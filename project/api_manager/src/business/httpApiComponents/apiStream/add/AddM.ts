import { AViewModel } from 'libs';
import { Bind } from 'lodash-decorators';
import { observable, action, computed, runInAction } from 'mobx';
import { IApiItem } from './configure';
import {
    nsMockManageKoasApiManageSwaggerSearchUpStreamApiListGet,
    nsMockManageKoasApiManageSwaggerCreateUpStreamRelaPost
} from '@/remote';
import { message } from 'antd';

export class AddM extends AViewModel {
    @observable public visible: boolean = false;

    private apiId: number = 0;
    @observable public searchScope: number = 0;
    @observable public key: string = '';

    private currentPage: number = 1;
    @observable public apiList: IApiItem[] = [];
    @observable public loading: boolean = false;
    @observable public hasMore: boolean = true;

    public queryUpDownStreamRela?(): void;

    constructor(query) {
        super(query);
        this.queryUpDownStreamRela = query;
    }

    @computed
    public get isEmptyData(): boolean {
        return Boolean(this.apiList?.length);
    }

    @action.bound
    public init(apiId: number, searchScope: number = 0): void {
        this.apiId = apiId;
        this.searchScope = searchScope;
        this.visible = true;
        this.searchApi();
    }

    @action.bound
    public onCloseSearch(): void {
        this.initData();
        this.visible = false;
    }

    @action
    private initData() {
        this.apiId = 0;
        this.key = '';
        this.currentPage = 1;
        this.apiList = [];
        this.loading = false;
        this.hasMore = true;
    }

    @action.bound
    public searchResult(): void {
        this.currentPage = 1;
        this.hasMore = true;
        this.apiList = [];
        this.searchApi();
    }

    @action.bound
    public onClearSearch(): void {
        this.key = '';
        this.searchResult();
    }

    @action.bound
    public loadMore() {
        this.currentPage = this.currentPage + 1;
        this.searchApi();
    }

    @Bind
    public async searchApi() {
        runInAction(() => this.loading = true);
        try {
            const params = {
                docId: this.apiId,
                key: this.key,
                currentPage: this.currentPage,
                pageSize: 20,
                searchScope: this.searchScope
            };
            const result = await nsMockManageKoasApiManageSwaggerSearchUpStreamApiListGet.remote(params);
            runInAction(() => {
                this.apiList = [...this.apiList, ...result?.upDownStreamGlobalSearchRowList] || [];
                if (!result?.upDownStreamGlobalSearchRowList?.length) {
                    this.hasMore = false;
                }
                this.loading = false;
            });
        } catch (e) {
            runInAction(() => this.loading = false);
        }
    }

    @Bind
    public async onAdd(rowData: IApiItem) {
        if (rowData.added) {
            return;
        }
        try {
            const params = {
                docId: this.apiId,
                preDocId: rowData.docId
            };
            await nsMockManageKoasApiManageSwaggerCreateUpStreamRelaPost.remote(params);
            message.success('添加成功～');
            runInAction(() => {
                this.apiList.map(item => {
                    if (item.docId === rowData.docId) {
                        item.added = true;
                    }
                });
                this.apiList = [...this.apiList];
            });
            this.queryUpDownStreamRela && this.queryUpDownStreamRela();
        } catch {
        }
    }
}
