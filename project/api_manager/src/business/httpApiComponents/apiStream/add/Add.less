.addModal {
    .titleWrap {
        width: calc(100% - 40px);
        display: flex;
        align-items: center;

        .titleDivider {
            height: 24px;
            margin: 0;
        }
    }

    .listItem {
        padding: 2px 0;
        border-radius: 2px;
        margin-top: 4px;
        border-bottom: 1px solid #f5f6f7;

        .apiTop {
            display: flex;
            justify-content: space-between;
            align-items: center;

            .apiTitle {
                display: flex;
                align-items: center;
            }

            .apiStatus {
                transform: scale(.8);
            }

            .addedBtn {
                margin-right: 8px;
                cursor: pointer;
            }
        }

        .title {
            font-weight: bolder;
            margin-bottom: 2px;
        }

        .main {
            margin-bottom: 2px;
            opacity: .8;
            font-size: 12px;
            display: flex;
        }

        .footer {
            display: flex;
            opacity: .8;
            font-size: 12px;
        }

        .createUser,
        .moduleName,
        .projectName {
            width: 240px;
            min-width: 240px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }

        .url {
            flex: 1;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            margin-left: 20px;
        }
    }

    .listItem:hover {
        background-color: #f5f6f7;
    }

    .api {
        overflow: auto;
        max-height: calc(100vh - 400px);
        margin-top: 4px;

        .loading {
            display: flex;
            justify-content: center;
            padding: 8px;
            opacity: .8;
        }
    }

    .searchEmpty {
        margin: 60px;

        :global {
            .ant-empty-description {
                color: rgba(0, 0, 0, 0.25);
                ;
            }
        }
    }

    :global {
        .ant-modal-header {
            padding: 16px;
        }

        .ant-modal-body {
            padding-top: 0;
        }

        .anticon {
            vertical-align: -0.5em;
        }

        .ant-input-suffix {
            cursor: pointer;
            opacity: .6;
        }

        .ant-tabs-top>.ant-tabs-nav,
        .ant-tabs-bottom>.ant-tabs-nav,
        .ant-tabs-top>div>.ant-tabs-nav,
        .ant-tabs-bottom>div>.ant-tabs-nav {
            margin-bottom: 0;
        }

        .ant-list-header {
            padding: 6px 0;
            font-weight: bolder;
            font-size: 16px;
        }
    }
}