import { AView } from 'libs';
import React from 'react';
import { observer } from 'mobx-react';
import { AddM } from './AddM';
import css from './Add.less';
import { Modal, Input, Divider, Empty, List, Spin, Tag, Button } from 'antd';
import { Bind, Debounce } from 'lodash-decorators';
import { SearchOutlined } from '@ant-design/icons';
import { bindObserver } from '@libs/mvvm';
import { SearchEmptyIcon } from '@/business/commonIcon';
import InfiniteScroll from 'react-infinite-scroller';
import { apiStatusEnum } from '@/business/toolFun/ToolFun';
import { highLight } from '@/index.config/tools';
import { IApiItem } from './configure';

const Input_key = bindObserver(Input, 'key');

@observer
export class Add extends AView<AddM> {

    @Debounce(300)
    @Bind
    private onSearchResult(): void {
        this.model.searchResult();
    }

    @Bind
    private renderClearBtn(): React.ReactNode {
        const model = this.model;
        if (model.key) {
            return (
                <span onClick={model.onClearSearch}>清除</span>
            );
        }
    }

    @Bind
    private renderSearchTitle(): React.ReactNode {
        const model = this.model;
        return (
            <div className={css.titleWrap}>
                <Input_key
                    model={model}
                    placeholder={'请输入关键字'}
                    size={'large'}
                    prefix={<SearchOutlined />}
                    suffix={this.renderClearBtn()}
                    bordered={false}
                    className={css.searchInput}
                    onChange={this.onSearchResult}
                    autoFocus={true}
                />
                <Divider type={'vertical'} className={css.titleDivider} />
            </div>
        );
    }

    @Bind
    private renderListRow(label: string, content: string, cssName: string) {
        return (
            <span className={cssName}>
                {`${label}：`}
                <span dangerouslySetInnerHTML={{ __html: highLight(content, this.model.key) }} />
            </span>
        );
    }

    @Bind
    private renderApiItem(item: IApiItem): React.ReactNode {
        return (
            <div className={css.listItem}>
                <div className={css.apiTop}>
                    <div className={css.apiTitle}>
                        <div
                            className={css.title}
                            dangerouslySetInnerHTML={{ __html: highLight(item.name, this.model.key) }}
                        />
                        {
                            typeof item.status === 'number' && item.apiType === 0 &&
                            <Tag color={apiStatusEnum[item.status]?.color} className={css.apiStatus}>
                                {apiStatusEnum[item.status].statusDesc}
                            </Tag>
                        }
                    </div>
                    <Tag
                        className={css.addedBtn}
                        color={item.added ? '' : '#327DFF'}
                        onClick={() => {
                            this.model.onAdd(item);
                        }}
                    >
                        {item.added ? '已添加' : '添加'}
                    </Tag>
                </div>
                <div className={css.main}>
                    {this.renderListRow('项目', item.projectName, css.projectName)}
                    {this.renderListRow('module name', item.moduleName, css.moduleName)}
                    {this.renderListRow('URL', item.uri, css.url)}
                </div>
                <div className={css.footer}>
                    {this.renderListRow('负责人', item.createUser, css.createUser)}
                    <span>创建时间：{item.updateTime}</span>
                </div>
            </div>
        );
    }

    @Bind
    private renderSearchResult(): React.ReactNode {
        const model = this.model;
        return (
            <div className={css.api}>
                <InfiniteScroll
                    initialLoad={false}
                    pageStart={0}
                    useWindow={false}
                    hasMore={!model.loading && model.hasMore}
                    loadMore={model.loadMore}
                >
                    <List
                        // header={ '最近访问' }
                        dataSource={model.apiList}
                        renderItem={this.renderApiItem}
                    >
                        <div className={css.loading}>
                            {model.loading && <Spin />}
                            {(!model.loading && !model.hasMore) || model.apiList.length < 20 && <span>没有更多数据了～</span>}
                        </div>
                    </List>
                </InfiniteScroll>
            </div>
        );
    }

    public render(): React.ReactNode {
        const model = this.model;
        return (
            <Modal
                title={this.renderSearchTitle()}
                visible={model.visible}
                className={css.addModal}
                onCancel={model.onCloseSearch}
                width={900}
                footer={false}
                destroyOnClose={true}
            >
                {
                    model.isEmptyData
                        ? this.renderSearchResult()
                        : <Empty
                            image={<SearchEmptyIcon />}
                            description={'没有找到搜索结果'}
                            className={css.searchEmpty}
                        />
                }
            </Modal>
        );
    }
}
