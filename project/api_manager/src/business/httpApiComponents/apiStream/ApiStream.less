.apiStream {
    height: 100%;
    overflow: auto;

    .content {
        display: flex;
        justify-content: space-between;
        position: relative;
    }

    .method {
        border-radius: 4px;
    }

    .nameTag {
        border-radius: 4px;
        margin: 0 0 0 4px;
    }

    .apiNode {
        width: 250px;
        height: 70px;
        border: 1px solid #d9d9d9;
        border-radius: 4px;
        padding: 8px;
        overflow: hidden;
        margin-top: 50px;

        .nameWrap {
            margin-bottom: 8px;
            display: flex;
            justify-content: space-between;
            width: 100%;

            .closeTag {
                padding-left: 8px;
                cursor: pointer;
            }
        }

        .pathWrap {
            display: flex;
            align-items: center;
        }
    }

    .pointer {
        cursor: pointer;
    }

    .selectedNode {
        border-color: #327DFF;
    }

    .margTop0 {
        margin-top: 0;
    }

    .nodeList>div:first-of-type {
        .margTop0();
    }

    .addApiNode {
        width: 250px;
        height: 70px;
        border: 1px solid #d9d9d9;
        border-radius: 4px;
        text-align: center;
        line-height: 70px;
        margin-top: 50px;
    }

    .nodeDetail {
        .apiNode();
        .margTop0();
        border-radius: 0;
        width: 100%;
        height: 140px;

        >div {
            display: flex;
        }
    }

    .empty {
        width: 50%;
        border: 1px solid #d9d9d9;
        padding-top: 60px;
        margin: 0 16px 0 0;

        :global {
            .ant-empty-description {
                opacity: .25;
            }
        }
    }

    .left {
        width: 50%;
        margin-right: 16px;
    }

    .right {
        width: 50%;
    }

    .baseInfo,
    .request {
        margin-bottom: 20px;
    }

    .margBot8 {
        margin-bottom: 8px;
    }

    .flexEllipsis {
        flex: 1;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
    }

    .table {
        margin: 16px 0 20px;
    }

    .contentDetail {
        .content();
        margin-top: 20px;
        padding: 20px;
        border: 1px solid #d9d9d9;
        border-radius: 4px;
    }
}