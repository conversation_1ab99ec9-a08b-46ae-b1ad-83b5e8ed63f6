import { AView } from 'libs';
import React from 'react';
import { observer } from 'mobx-react';
import { Bind } from 'lodash-decorators';
import { ApiCollapse, KdevTitle } from '@/business/commonComponents';
import { ApiStreamM } from './ApiStreamM';
import css from './ApiStream.less';
import { IChildProps, resParamsLabelEnum } from './configure';
import { ParamsTable } from './ParamsTable';

@observer
export class Response extends AView<ApiStreamM, IChildProps> {

    @Bind
    private renderParams(title: string) {
        const model = this.model;
        const response = this.props.upOrNow === 'up'
            ? model.upStreamNodeDetail?.response
            : model.nowNodeDetail?.response;
        if (response && response[title] && response[title].length) {
            return (
                <div className={css.tableWrap}>
                    <KdevTitle text={resParamsLabelEnum[title]} size="small" />
                    <ParamsTable dataSource={response[title]} />
                </div>
            );
        }
    }

    public render(): React.ReactNode {
        return (
            <ApiCollapse className={css.response} header="返回参数">
                {this.renderParams('header')}
                {this.renderParams('body')}
            </ApiCollapse>
        );
    }
}
