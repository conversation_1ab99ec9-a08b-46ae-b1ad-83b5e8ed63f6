import { <PERSON><PERSON>iew } from 'libs';
import React from 'react';
import { observer } from 'mobx-react';
import { Bind } from 'lodash-decorators';
import { Button, Tag, Tooltip, Table, Empty, Space } from 'antd';
import { KdevIconFont, SvgLine } from '@/business/commonComponents';
import { ApiStreamM } from './ApiStreamM';
import css from './ApiStream.less';
import { Add } from './add/Add';
import classNames from 'classnames';
import { IApiNode, IObj } from './configure';
import { SearchEmptyIcon } from '@/business/commonIcon';
import { BaseInfo } from './BaseInfo';
import { Request } from './Request';
import { Response } from './Response';

@observer
export class ApiStream extends AView<ApiStreamM> {

    @Bind
    public componentWillMount() {
        window.addEventListener('resize', this.model.resizeBoxWidth);
    }

    @Bind
    public componentWillUnmount() {
        window.removeEventListener('resize', this.model.resizeBoxWidth);
    }

    @Bind
    private renderUpStreamNode(): React.ReactNode {
        const model = this.model;
        return (
            <div className={css.nodeList}>
                {
                    model.upStreamNodeList.map(item => (
                        <div
                            className={item.id === model.preDocId
                                ? classNames(css.apiNode, css.selectedNode)
                                : classNames(css.apiNode, css.pointer)
                            }
                            key={item.id}
                            onClick={() => model.onSelectNode(item.id)}
                        >
                            <div className={css.nameWrap}>
                                <Tooltip title={item.name}>
                                    <span className={css.flexEllipsis}>{item.name}</span>
                                </Tooltip>
                                <Tooltip title="删除关联关系">
                                    <span className={css.closeTag} onClick={() => model.onDeleteConfirm(item)}>
                                        <KdevIconFont id="#iconsubtract-circle" />
                                    </span>
                                </Tooltip>
                            </div>
                            <div className={css.pathWrap}>
                                <Tag color="#327DFF" className={css.method}>{item.method}</Tag>
                                <Tooltip title={item.path}>
                                    <span className={css.flexEllipsis}>{item.path}</span>
                                </Tooltip>
                            </div>
                        </div>
                    ))
                }
                <div className={css.addApiNode}>
                    <Button
                        type="primary"
                        onClick={model.openAdd}
                    >添加上游API</Button>
                </div>
            </div>
        );
    }

    @Bind
    private renderNowNode(): React.ReactNode {
        const model = this.model;
        const apiNode = model.nowNode[0];
        if (!apiNode) {
            return;
        }
        return (
            <div className={classNames(css.apiNode, css.margTop0)}>
                <div className={css.nameWrap}>
                    <Tooltip title={apiNode.name}>
                        <span className={css.flexEllipsis}>{apiNode.name}</span>
                    </Tooltip>
                    <Tag color="blue" className={css.nameTag}>当前</Tag>
                </div>
                <div className={css.pathWrap}>
                    <Tag color="#327DFF" className={css.method}>{apiNode.method}</Tag>
                    <Tooltip title={apiNode.path}>
                        <span className={css.flexEllipsis}>{apiNode.path}</span>
                    </Tooltip>
                </div>
            </div>
        );
    }

    @Bind
    private renderDownStreamNodeList(): React.ReactNode {
        const model = this.model;
        return (
            <div className={css.nodeList}>
                {
                    model.downStreamNodeList.map(item => (
                        <div className={css.apiNode} key={item.id}>
                            <div className={css.nameWrap}>
                                <Tooltip title={item.name}>
                                    <span className={css.flexEllipsis}>{item.name}</span>
                                </Tooltip>
                            </div>
                            <div className={css.pathWrap}>
                                <Tag color="#327DFF" className={css.method}>{item.method}</Tag>
                                <Tooltip title={item.path}>
                                    <span className={css.flexEllipsis}>{item.path}</span>
                                </Tooltip>
                            </div>
                        </div>
                    ))
                }
            </div>
        );
    }

    @Bind
    private renderContentDetail(upOrNow: 'now' | 'up') {
        const model = this.model;
        if (upOrNow === 'up' && !this.model.upStreamNodeDetail) {
            return (
                <Empty
                    image={<SearchEmptyIcon />}
                    description="暂无上游节点"
                    className={css.empty}
                />
            );
        }
        return (
            <div className={upOrNow === 'up' ? css.left : css.right}>
                <BaseInfo model={model} upOrNow={upOrNow} />
                <Request model={model} upOrNow={upOrNow} />
                <Response model={model} upOrNow={upOrNow} />
            </div>
        );
    }

    public render(): React.ReactNode {
        const model = this.model;
        return (
            <div className={css.apiStream}>
                <div className={css.content} ref={model.setSvgWrapNode}>
                    {this.renderUpStreamNode()}
                    {this.renderNowNode()}
                    {this.renderDownStreamNodeList()}
                    <SvgLine points={model.points} />
                </div>
                <div className={css.contentDetail}>
                    {this.renderContentDetail('up')}
                    {this.renderContentDetail('now')}
                </div>
                <Add model={model.addM} />
            </div>
        );
    }
}
