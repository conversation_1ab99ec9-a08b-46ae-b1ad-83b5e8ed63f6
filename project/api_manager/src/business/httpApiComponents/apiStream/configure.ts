import {
    nsMockManageKoasApiManageSwaggerQueryApiNodeDetailGet,
    nsMockManageKoasApiManageSwaggerQueryUpDownStreamRelaGet
} from '@/remote';

export interface IApiNode extends nsMockManageKoasApiManageSwaggerQueryUpDownStreamRelaGet.IItem {}
export interface IObj extends nsMockManageKoasApiManageSwaggerQueryApiNodeDetailGet.IObj {}
export interface INodeDetail extends nsMockManageKoasApiManageSwaggerQueryApiNodeDetailGet.IItem {}
export interface INodeBaseInfo extends nsMockManageKoasApiManageSwaggerQueryApiNodeDetailGet.IBaseInfo {}

export interface ILineSvgConfig {
    start: [number, number];
    end: [number, number];
    color: string;
}

export interface IChildProps {
    upOrNow: 'now' | 'up';
}

export interface IParamsTablePorps {
    dataSource: IObj[];
}

export enum reqParamsLabelEnum {
    header = 'Header参数',
    query = 'Query参数',
    body = 'Body参数',
    form = 'Form参数'
}

export enum resParamsLabelEnum {
    header = '返回Header',
    body = '返回Body',
}
