import { AViewModel } from 'libs';
import { observable, action, runInAction } from 'mobx';
import { Bind, Debounce } from 'lodash-decorators';
import {
    nsMockManageKoasApiManageSwaggerQueryUpDownStreamRelaGet,
    nsMockManageKoasApiManageSwaggerDeleteUpStreamRelaPost,
    nsMockManageKoasApiManageSwaggerQueryApiNodeDetailGet
} from '@/remote';
import { IApiNode, ILineSvgConfig, INodeDetail } from './configure';
import { AddM } from './add/AddM';
import { message, Modal } from 'antd';

export class ApiStreamM extends AViewModel {
    private apiId: number = 0;
    @observable public points: ILineSvgConfig[] = [];
    @observable public upStreamNodeList: IApiNode[] = [];
    @observable public nowNode: IApiNode[] = [];
    @observable public downStreamNodeList: IApiNode[] = [];
    @observable public preDocId: number = 0;
    @observable public nowNodeDetail?: INodeDetail;
    @observable public upStreamNodeDetail?: INodeDetail;

    private svgWrapNode?;
    private boxWidth: number = 0;
    public addM = new AddM(this.queryUpDownStreamRela);

    constructor(apiId: number) {
        super();
        this.setApiId(apiId);
    }

    @action.bound
    public initLoading(apiId: number) {
        if (this.apiId !== apiId) {
            this.setApiId(apiId);
        }
    }

    // 设置api ID
    @action
    private setApiId(apiId: number): void {
        this.apiId = apiId;
        this.initData();
        this.queryUpDownStreamRela();
    }

    @action
    private initData() {
        this.nowNodeDetail = undefined;
        this.upStreamNodeDetail = undefined;
    }

    @Bind
    protected async queryUpDownStreamRela() {
        try {
            const params = {
                docId: this.apiId
            };
            const result = await nsMockManageKoasApiManageSwaggerQueryUpDownStreamRelaGet.remote(params);
            runInAction(() => {
                this.nowNode = [result?.nowNode];
                this.upStreamNodeList = result?.upStreamNodeList || [];
                this.downStreamNodeList = result?.downStreamNodeList || [];
                this.computeLineSvgPoints(this.upStreamNodeList.length, this.downStreamNodeList.length);
                this.onSelectNode(this.upStreamNodeList[0]?.id || 0);
            });
        } catch {}
    }

    @action.bound
    public setSvgWrapNode(node) {
        this.svgWrapNode = node;
    }

    // 计算节点位置
    @action.bound
    private computeLineSvgPoints(upLength: number, downLength: number) {
        upLength += 1;
        if (this.svgWrapNode) {
            // 获取content width
            this.boxWidth = this.svgWrapNode.offsetWidth;
            let spaceWidth = (this.boxWidth - 500) / 2;
            if (downLength) {
                spaceWidth = (this.boxWidth - 750) / 2;
            }
            const points: ILineSvgConfig[] = [];
            for (let i = 0; i < upLength; i++) {
                points.push(
                    {
                        start: [250, 35 + i * 120],
                        end: [spaceWidth + 250, 35],
                        color: '#327DFF'
                    }
                );
            }
            if (downLength) {
                for (let i = 0; i < downLength; i++) {
                    points.push(
                        {
                            start: [spaceWidth + 500, 35],
                            end: [500 + spaceWidth * 2, 35 + i * 120],
                            color: '#327DFF'
                        }
                    );
                }
            }
            this.points = points;
        }
    }

    @Debounce(200)
    @Bind
    public resizeBoxWidth() {
        if (this.svgWrapNode.offsetWidth !== this.boxWidth) {
            this.computeLineSvgPoints(this.upStreamNodeList.length, this.downStreamNodeList.length);
        }
    }

    @action.bound
    public openAdd() {
        this.addM.init(this.apiId);
    }

    @action.bound
    public onDeleteConfirm(rowData: IApiNode) {
        Modal.confirm({
            content: '确认是否删除关联关系？',
            onOk: () => this.deleteUpStreamRela(rowData.id)
        });
    }

    @Bind
    private async deleteUpStreamRela(docId: number) {
        try {
            const params = {
                preDocId: docId,
                docId: this.apiId
            };
            await nsMockManageKoasApiManageSwaggerDeleteUpStreamRelaPost.remote(params);
            message.success('删除成功～');
            this.queryUpDownStreamRela();
        } catch {
        }
    }

    @action.bound
    public onSelectNode(preDocId: number) {
        this.preDocId = preDocId;
        this.queryApiNodeDetail();
    }

    @Bind
    private async queryApiNodeDetail() {
        try {
            const params = {
                docId: this.apiId,
                preDocId: this.preDocId
            };
            const result = await nsMockManageKoasApiManageSwaggerQueryApiNodeDetailGet.remote(params);
            runInAction(() => {
                this.nowNodeDetail = result?.nowNode;
                this.upStreamNodeDetail = result?.upStreamNode;
            });
        } catch {
        }
    }
}
