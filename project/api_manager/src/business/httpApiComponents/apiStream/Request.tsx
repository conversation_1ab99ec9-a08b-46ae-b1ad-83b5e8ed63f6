import { AView } from 'libs';
import React from 'react';
import { observer } from 'mobx-react';
import { Bind } from 'lodash-decorators';
import { ApiCollapse, KdevTitle } from '@/business/commonComponents';
import { ApiStreamM } from './ApiStreamM';
import css from './ApiStream.less';
import { reqParamsLabelEnum, IChildProps } from './configure';
import { ParamsTable } from './ParamsTable';

@observer
export class Request extends AView<ApiStreamM, IChildProps> {

    @Bind
    private renderParams(title: string) {
        const model = this.model;
        const request = this.props.upOrNow === 'up'
            ? model.upStreamNodeDetail?.request
            : model.nowNodeDetail?.request;
        if (request && request[title] && request[title].length) {
            return (
                <div className={css.tableWrap}>
                    <KdevTitle text={reqParamsLabelEnum[title]} size="small" />
                    <ParamsTable dataSource={request[title]} />
                </div>
            );
        }
    }

    public render(): React.ReactNode {
        return (
            <ApiCollapse className={css.request} header="请求参数">
                {this.renderParams('header')}
                {this.renderParams('query')}
                {this.renderParams('body')}
                {this.renderParams('form')}
            </ApiCollapse>
        );
    }
}
