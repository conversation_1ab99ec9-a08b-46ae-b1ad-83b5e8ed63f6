import React from 'react';
import { observer } from 'mobx-react';
import { Bind } from 'lodash-decorators';
import { Table } from 'antd';
import css from './ApiStream.less';
import { IParamsTablePorps } from './configure';

@observer
export class ParamsTable extends React.Component<IParamsTablePorps> {

    public render(): React.ReactNode {
        const columns = [
            {
                title: '名称',
                dataIndex: 'name',
                key: 'name'
            },
            {
                title: '类型',
                dataIndex: 'type',
                key: 'type'
            }
        ];
        return (
            <Table
                className={css.table}
                columns={columns}
                dataSource={this.props.dataSource}
                rowKey="path"
                pagination={false}
                bordered
                defaultExpandAllRows
            />
        );
    }
}
