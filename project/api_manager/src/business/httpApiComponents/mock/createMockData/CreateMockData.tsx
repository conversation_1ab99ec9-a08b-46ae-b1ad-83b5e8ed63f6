import React from 'react';
import { observer } from 'mobx-react';
import { AView, bindObserver } from '@libs/mvvm';
import { Bind } from 'lodash-decorators';
import { Input, Modal, Radio } from 'antd';
import { QuestionTips, questionTipsConfig } from '@/business/commonComponents';
import { AceDrag } from '@/business/aceDrag/AceDrag';
import { GenerateData } from '../GenerateData';
import { dataTypeEnum, responseTypeOptions, editTypeEnum, responseTypeEnum } from './configure';
import { CreateMockDataM } from './CreateMockDataM';
import css from './CreateMockData.less';

const Input_mockName = bindObserver(Input, 'mockName');
const RadioGroup_responseType = bindObserver(Radio.Group, 'responseType');

@observer
export class CreateMockData extends AView<CreateMockDataM> {

    @Bind
    private renderMockName(): React.ReactNode {
        return (
            <>
                <div className={css.rowLabelRequired}>数据名称</div>
                <Input_mockName model={this.model} placeholder="请填写数据名称" />
            </>
        );
    }

    @Bind
    private renderRequest(): React.ReactNode {
        const model = this.model;
        return (
            <>
                <div className={css.rowLabel}>
                    <span>参数过滤 {QuestionTips({ title: questionTipsConfig.MOCK_REQUEST_PARAMS_FILTER })}</span>
                    <GenerateData
                        classNameWrap={css.generateDateWrap}
                        type={dataTypeEnum.REQUEST}
                        generateData={model.generateData}
                        disabled={model.editType === editTypeEnum.NEW}
                    />
                </div>
                <AceDrag
                    value={model.request}
                    onChange={model.onChangeRequest}
                />
            </>
        );
    }

    @Bind
    private renderResponseContent(): React.ReactNode {
        const model = this.model;
        if (model.responseType === responseTypeEnum.STATIC_JSON) {
            return (
                <AceDrag
                    value={model.response}
                    onChange={model.onChangeResponse}
                />
            );
        }
        if (model.responseType === responseTypeEnum.DYNAMIC_JS) {
            return (
                <AceDrag
                    value={model.responseDynamicJs}
                    onChange={model.onChangeResponseDynamicJs}
                    mode="typescript"
                />
            );
        }
    }

    @Bind
    private renderResponse(): React.ReactNode {
        const model = this.model;
        return (
            <>
                <div className={css.rowLabelReponse}>
                    <span>
                        Body
                        <GenerateData
                            classNameWrap={css.generateDateWrap}
                            type={dataTypeEnum.RESPONSE}
                            generateData={model.generateData}
                            disabled={
                                model.editType === editTypeEnum.NEW
                                || model.responseType === responseTypeEnum.DYNAMIC_JS
                            }
                        />
                    </span>
                    <RadioGroup_responseType
                        model={model}
                        options={responseTypeOptions}
                    />
                </div>
                {this.renderResponseContent()}
            </>
        );
    }

    public render() {
        const model = this.model;
        return (
            <Modal
                title={model.editType === editTypeEnum.EDIT ? '编辑Mock数据' : '新建Mock数据'}
                visible={model.visible}
                onCancel={model.closeCreateMockData}
                onOk={model.onSaveMockData}
                width={1000}
                className={css.createMockDataModal}
            >
                {this.renderMockName()}
                {this.renderRequest()}
                {this.renderResponse()}
            </Modal>
        );
    }
}
