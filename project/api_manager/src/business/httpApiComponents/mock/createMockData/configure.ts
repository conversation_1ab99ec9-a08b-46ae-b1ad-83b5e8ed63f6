interface IResponseTypeOptions {
    label: string;
    value: number;
}

export const responseTypeOptions: IResponseTypeOptions[] = [
    { label: '静态json', value: 0 },
    { label: '动态js', value: 1 }
];

export enum responseTypeEnum {
    STATIC_JSON = 0,
    DYNAMIC_JS
}

export enum dataTypeEnum {
    REQUEST = 1,
    RESPONSE = 2
}

export enum editTypeEnum {
    NEW = 'new',
    EDIT = 'edit'
}

export const responseTemplate: string =
    `function mock(response) {
    //请在这里修改response数据 如下示例
    //response.hader.foo = "1234"
    //response.body = {
    //  data: {},
    //  result: 1
    //}
    //最后返回结果
    return response;
}`;
