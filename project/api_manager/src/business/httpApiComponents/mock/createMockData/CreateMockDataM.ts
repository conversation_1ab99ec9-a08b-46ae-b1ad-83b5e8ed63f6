import { action, observable, runInAction } from 'mobx';
import { AViewModel } from '@libs/mvvm';
import { Bind } from 'lodash-decorators';
import { beautifyStrJson, isJsonString } from '@/index.config/tools';
import { responseTemplate, dataTypeEnum, responseTypeEnum, editTypeEnum } from './configure';
import {
    nsMockManageKoasApiManageMockTemplateRandomGet, nsMockManageKoasApiManageMockDetailGet,
    nsMockManageKoasApiManageMockTemplateGet, nsMockManageKoasApiManageMockEditPost,
    nsMockManageKoasApiManageMockAddPost
} from '@/remote';
import { message } from 'antd';

export interface IMockDataParams {
    docId: number;
    id: number;
    mockName: string;
    request: string;
    response: string;
    responseType: number;
}

export class CreateMockDataM extends AViewModel {
    @observable public visible: boolean = false;

    private docId: number = 0;
    @observable public editType: 'new' | 'edit' = editTypeEnum.NEW;
    private mockDataId: number = 0;
    @observable public mockName: string = '';
    @observable public request: string = '';
    @observable public response: string = '';
    @observable public responseDynamicJs: string = responseTemplate;
    @observable public responseType: number = responseTypeEnum.STATIC_JSON;

    public onSaveMockDataCallback?(editType: 'new' | 'edit', parmas: IMockDataParams): void;

    @action
    public openCreateMockData(docId: number, mockDataId: number = 0): void {
        this.docId = docId;
        if (mockDataId) {
            this.mockDataId = mockDataId;
            this.editType = editTypeEnum.EDIT;
            this.queryDetail(this.mockDataId);
        } else {
            this.queryTemplateData();
        }
        this.visible = true;
    }

    @action.bound
    public closeCreateMockData(): void {
        this.initData();
        this.visible = false;
    }

    @action
    private initData(): void {
        this.docId = 0;
        this.mockDataId = 0;
        this.mockName = '';
        this.request = '';
        this.response = '';
        this.responseType = responseTypeEnum.STATIC_JSON;
        this.editType = editTypeEnum.NEW;
    }

    @action.bound
    public onChangeRequest(request: string): void {
        this.request = request;
    }

    @action.bound
    public onChangeResponse(response: string): void {
        this.response = response;
    }

    @action.bound
    public onChangeResponseDynamicJs(responseDynamicJs: string): void {
        this.responseDynamicJs = responseDynamicJs;
    }

    // 获取入参
    @Bind
    private getParams(): IMockDataParams {
        return {
            docId: this.docId,
            id: this.mockDataId,
            mockName: this.mockName,
            request: this.request,
            response: this.responseType === responseTypeEnum.STATIC_JSON ? this.response : this.responseDynamicJs,
            responseType: this.responseType
        };
    }

    // 保存mock数据校验
    @Bind
    public onSaveMockData(): void {
        if (!this.mockName) {
            message.warn('请填写数据名称～');
            return;
        }
        if (this.request && !isJsonString(this.request)) {
            message.warn('请求入参数据不是json～');
            return;
        }
        if (this.response && !this.responseType && !isJsonString(this.response)) {
            message.warn('响应body数据不是json～');
            return;
        }
        this.saveMockData();
    }

    // 随机生成数据
    @Bind
    public async generateData(arrayNodeNum: number, type: number): Promise<void> {
        try {
            const params = {
                mockDataId: this.mockDataId,
                arrayNodeNum,
                type
            };
            const result = await nsMockManageKoasApiManageMockTemplateRandomGet.remote(params);
            runInAction(() => {
                if (type === dataTypeEnum.REQUEST) {
                    this.request = beautifyStrJson(result?.json || '');
                }
                if (type === dataTypeEnum.RESPONSE) {
                    this.response = beautifyStrJson(result?.json || '');
                }
            });
        } catch {
        }
    }

    // 获取mock数据详情
    @Bind
    private async queryDetail(id: number) {
        try {
            const result = await nsMockManageKoasApiManageMockDetailGet.remote({ id });
            runInAction(() => {
                this.mockName = result?.mockName || '';
                this.request = beautifyStrJson(result?.request || '');
                this.responseType = result?.responseType || responseTypeEnum.STATIC_JSON;
                if (this.responseType === responseTypeEnum.DYNAMIC_JS) {
                    this.responseDynamicJs = result?.response || '';
                } else if (this.responseType === responseTypeEnum.STATIC_JSON) {
                    this.response = beautifyStrJson(result?.response || '');
                }
            });
        } catch (e) {
        }
    }

    // 获取默认值
    @Bind
    private async queryTemplateData(): Promise<void> {
        try {
            const result = await nsMockManageKoasApiManageMockTemplateGet.remote({ docId: this.docId });
            this.onChangeRequest(beautifyStrJson(result?.request));
            this.onChangeResponse(beautifyStrJson(result?.response));
        } catch {
        }
    }

    // 保存mock数据
    @Bind
    private async saveMockData(): Promise<void> {
        try {
            const params = this.getParams();
            const request = this.editType === editTypeEnum.EDIT
                ? nsMockManageKoasApiManageMockEditPost
                : nsMockManageKoasApiManageMockAddPost;
            const result = await request.remote(params);
            if (result?.id) {
                params['id'] = result?.id;
            }
            this.onSaveMockDataCallback && this.onSaveMockDataCallback(this.editType, params);
            this.closeCreateMockData();
        } catch {
        }
    }
}
