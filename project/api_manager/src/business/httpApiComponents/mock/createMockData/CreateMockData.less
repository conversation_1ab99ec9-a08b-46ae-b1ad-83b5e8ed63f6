.createMockDataModal {
    @labelColor: #898a8c;
    @requiredColor: #ff4d4f;
    @8px: 8px;
    @24px: 24px;

    .rowLabelRequired {
        color: @labelColor;
        margin-bottom: @8px;
    }

    .rowLabelRequired::after {
        content: ' *';
        color: @requiredColor;
    }

    .rowLabel {
        .rowLabelRequired();
        padding-top: @24px;
        display: flex;
        align-items: center;

        .generateDateWrap {
            margin-left: @8px;
        }
    }

    .rowLabelReponse {
        .rowLabel();

        justify-content: space-between;
    }

    :global {
        .ant-modal-body {
            max-height: calc(100vh - 300px);
            overflow: auto;
        }
    }
}