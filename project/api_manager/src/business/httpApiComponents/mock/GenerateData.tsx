import React, {useState} from 'react';
import { Button, InputNumber, Modal, Space } from 'antd';
import { KdevIconFont } from '@/business/commonComponents/KdevIconFont';
import {LOCAL_STORAGE_KEYS} from '@/index.config/LOCAL_STORAGE_KEYS';

interface IProps {
    classNameWrap?: string;
    disabled?: boolean;
    type: number;
    generateData?(arrayNodeNum: number, type: number): void;
}

function getLocalStorageArrayNodeNum() {
    const localStorageArrayNodeNumStr = localStorage.getItem(LOCAL_STORAGE_KEYS.API_MGR_ARRAY_NODE_NUM);
    let localStorageArrayNodeNum =  {1: 3, 2: 3};
    if (localStorageArrayNodeNumStr) {
        localStorageArrayNodeNum = JSON.parse(localStorageArrayNodeNumStr);
    }
    return localStorageArrayNodeNum;
}

function setLocalStorageArrayNodeNum(apiArrayNodeNum, type: number) {
    const localStorageArrayNodeNum = getLocalStorageArrayNodeNum();
    localStorageArrayNodeNum[type] = apiArrayNodeNum;
    localStorage.setItem(LOCAL_STORAGE_KEYS.API_MGR_ARRAY_NODE_NUM, JSON.stringify(localStorageArrayNodeNum));
}

function getApiArrayNodeNum(type: number): number {
    return getLocalStorageArrayNodeNum()[type] || 3;
}

export function GenerateData(props: IProps) {
    const [visible, setVisible] = useState<boolean>(false);
    const [apiArrayNodeNum, setApiArrayNodeNum] = useState<number>(getApiArrayNodeNum(props.type));

    const ArrayNodeNum = () => {
        return (
            <Modal
                title="高级设置"
                visible={visible}
                onCancel={() => setVisible(false)}
            >
                <Space>
                    指定数组长度
                    <InputNumber
                        value={apiArrayNodeNum}
                        onChange={(val) => {
                            setApiArrayNodeNum(val as any);
                            setLocalStorageArrayNodeNum(val, props.type);
                        }}
                        min={0}
                    />
                </Space>
            </Modal>
        );
    };

    return (
        <span className={props.classNameWrap}>
            <Button
                type="primary"
                size="small"
                disabled={props.disabled}
                onClick={() => props.generateData && props.generateData(getApiArrayNodeNum(props.type), props.type)}
            >
                生成数据
            </Button>
            <Button
                size="small"
                icon={<KdevIconFont id="#iconsettings"/>}
                type="link"
                onClick={() => setVisible(true)}
            />
            {ArrayNodeNum()}
        </span>
    );
}
