import { AVie<PERSON> } from 'libs';
import React from 'react';
import { MockDataM } from './MockDataM';
import { observer } from 'mobx-react';
import { But<PERSON>, Modal } from 'antd';
import { Bind } from 'lodash-decorators';
import css from './MockData.less';
import { KdevIconFont, QuestionTips, questionTipsConfig, CopyBtn, KdevTitle } from '@/business/commonComponents';
import { ERouter } from 'CONFIG';
import { dataType } from './configure';
import { AceDrag } from '@/business/aceDrag/AceDrag';
import { GenerateData } from '@/business/httpApiComponents/mock/GenerateData';
import { RandomJson } from './randomJson/RandomJson';

@observer
export class MockData extends AView<MockDataM> {

    @Bind
    protected renderOperate(): React.ReactNode {
        const model = this.model;
        return <div className={css.operate}>
            <KdevTitle text={'基本信息'} />
            <div className={css.operateBtn}>
                <Button
                    icon={<KdevIconFont id={'#iconyanse'} />}
                    className={css.margLeft8}
                    onClick={this.onOpenDeleteConfirm}
                    disabled={model.default}
                />
                <Button
                    icon={<KdevIconFont id="#iconxingzhuangjiehe" />}
                    className={css.margLeft8}
                    onClick={model.onOpenCreateMockData}
                />
            </div>
        </div>;
    }

    @Bind
    protected onOpenDeleteConfirm() {
        Modal.confirm({
            title: '删除后将无法恢复，确认删除此数据？',
            onOk: () => this.model.deleteMockData()
        });
    }

    @Bind
    private renderResponse(): React.ReactNode {
        const model = this.model;
        return (
            <>
                <KdevTitle text={'响应'} style={{ marginTop: '48px' }} />
                <div className={css.formLabelBody}>
                    <div>
                        Body
                        <GenerateData
                            classNameWrap={css.margLeft8}
                            type={dataType.RESPONSE}
                            generateData={model.generateData}
                            disabled={Boolean(model.responseType)}
                        />
                    </div>
                    <span className={css.responseType}>{model.responseType ? '动态JS' : '静态JSON'}</span>
                </div>
                <AceDrag
                    value={model.response}
                    readOnly
                    key={`response_${model.mockDataId}`}
                />
            </>
        );
    }

    @Bind
    private renderMockAddress(): React.ReactNode {
        const model = this.model;
        if (model.mockDataId) {
            return (
                <>
                    <div className={css.formLabel}>Mock地址</div>
                    <a href={model.mockAddress} target={'_blank'} className={css.mockAddress}>
                        {model.mockAddress}
                    </a>
                    <CopyBtn copyContent={model.mockAddress} />
                </>
            );
        }
    }

    @Bind
    private renderScene(): React.ReactNode {
        const model = this.model;
        const sceneUrl: string = `${ERouter.API_MOCK_ORGANIZESPACE_ASCENE}?id=${model.sceneId}&sceneName=${model.sceneName}`;
        if (model.sceneName) {
            return (
                <>
                    <div className={css.formLabel}>场景名称</div>
                    <a href={sceneUrl}>{model.sceneName}</a>
                </>
            );
        }
    }

    @Bind
    private renderMockName(): React.ReactNode {
        return (
            <>
                <div className={css.formLabelRequired}>数据名称</div>
                <div>{this.model.mockName}</div>
            </>
        );
    }

    public render(): React.ReactNode {
        const model = this.model;
        return (
            <div className={css.mockDataWrap}>
                {this.renderOperate()}
                {this.renderMockAddress()}
                {this.renderScene()}
                {this.renderMockName()}
                <div className={css.formLabel}>
                    <span className={css.requestLabel}>
                        <span className={css.margRight4}>参数过滤</span>
                        {QuestionTips({ title: questionTipsConfig.MOCK_REQUEST_PARAMS_FILTER })}
                    </span>
                    <GenerateData
                        classNameWrap={css.margLeft8}
                        type={dataType.REQUEST}
                        generateData={model.generateData}
                    />
                </div>
                <AceDrag
                    value={model.request}
                    readOnly
                    key={`request_${model.mockDataId}`}
                />
                {this.renderResponse()}
                <RandomJson model={model.randomJsonM}/>
            </div>
        );
    }
}
