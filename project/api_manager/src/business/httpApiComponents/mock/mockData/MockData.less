.mockDataWrap {
  @requiredColor: #ff4d4f;
  @labelColor: #898a8c;
  .margLeft8{
    margin-left: 8px;
  }
  .margRight4{
    margin-right: 4px;
  }

  .mockAddress {
    word-break: break-all;
  }

  .operate {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .operateBtn {
      position: absolute;
      right: 56px;
      z-index: 1;
    }
  }

  .formLabelRequired {
    color: @labelColor;
    margin: 24px 0 8px;
  }

  .formLabelRequired::after {
    content: ' *';
    color: @requiredColor;
  }

  .formLabel {
    .formLabelRequired();
    display: flex;
    align-items: center;

    .requestLabel{
      display: flex;
      align-items: center;
    }
  }

  .formLabelBody {
    .formLabel();
    justify-content: space-between;
    position: relative;
    .responseType{
      display: inline-block;
      background-color: rgba(50, 125, 255, 0.08);
      border-radius: 4px;
      margin-left: 16px;
      color: #327dff;
      padding: 0 4px;
    }
  }
}