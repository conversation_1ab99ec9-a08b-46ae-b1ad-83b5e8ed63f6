import React from 'react';
import { observer } from 'mobx-react';
import { AView } from '@libs/mvvm';
import { Modal } from 'antd';
import { AceDrag } from '@/business/aceDrag/AceDrag';
import { RandomJsonM } from './RandomJsonM';

@observer
export class <PERSON><PERSON>son extends AView<RandomJsonM> {
    public render() {
        const model = this.model;
        return (
            <Modal
                title={'确认是否保存'}
                visible={model.visible}
                onCancel={model.closeRandomJson}
                onOk={model.onSaveRandomJson}
                width={800}
            >
                <AceDrag
                    value={model.json}
                    onChange={model.onChangeJson}
                />
            </Modal>
        );
    }
}
