import { action, observable } from 'mobx';
import { AViewModel } from '@libs/mvvm';
import { Bind } from 'lodash-decorators';
import { beautifyStrJson, isJsonString } from '@/index.config/tools';
import { message } from 'antd';

export class RandomJsonM extends AViewModel {
    @observable public visible: boolean = false;
    private type: number = 1;
    @observable public json: string = '';

    public onSaveRandomJsonCallback?(type: number, json: string): void;

    @action.bound
    public openRandomJson(type: number, json: string): void {
        this.type = type;
        this.onChangeJson(beautifyStrJson(json));
        this.visible = true;
    }

    @action.bound
    public closeRandomJson(): void {
        this.visible = false;
        this.type = 1;
        this.json = '';
    }

    @action.bound
    public onChangeJson(json: string): void {
        this.json = json;
    }

    @Bind
    public onSaveRandomJson(): void {
        if (!isJsonString(this.json)) {
            message.warn('请填写正确的json格式数据～');
            return;
        }
        this.onSaveRandomJsonCallback && this.onSaveRandomJsonCallback(this.type, this.json);
        this.closeRandomJson();
    }
}
