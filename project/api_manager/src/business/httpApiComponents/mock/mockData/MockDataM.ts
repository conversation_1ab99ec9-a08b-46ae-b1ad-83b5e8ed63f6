import { AViewModel } from 'libs';
import { observable, action, runInAction } from 'mobx';
import {
    nsMockManageKoasApiManageMockDetailGet,
    nsMockManageKoasApiManageMockDeletePost,
    nsMockManageKoasApiManageMockTemplateRandomGet,
    nsMockManageKoasApiManageMockEditPost
} from '@/remote';
import { formatJson, isJsonString } from '@/index.config/tools';
import { message } from 'antd';
import { Bind } from 'lodash-decorators';
import { RandomJsonM } from './randomJson/RandomJsonM';

interface ISceneObj {
    sceneId: number;
    sceneName: string;
}

export class MockDataM extends AViewModel {
    @observable protected apiId: number = 0;
    @observable public mockDataId: number = 0;
    @observable public mockAddress: string = '';
    @observable public mockName: string = '';
    @observable public request: string = '';
    @observable public responseType: number = 0;
    @observable public response: string = '';
    @observable public default: boolean = false;

    @observable public sceneId: number = 0;
    @observable public sceneName: string = '';

    public randomJsonM = new RandomJsonM();

    public deleteMockDataCallback?(type: 'delete'): void;
    public openCreateMockData?(mockDataId: number): void;

    @action.bound
    public initLoading(mockDataId: number, apiId: number) {
        this.apiId = apiId;
        this.mockDataId = mockDataId;
        if (!mockDataId) {
            this.initData();
        } else {
            this.queryDetail();
        }
    }

    @action.bound
    protected initData() {
        this.mockDataId = 0;
        this.mockName = '';
        this.request = '';
        this.responseType = 0;
        this.response = '';
    }

    // 设置场景
    @action.bound
    public setScene(sceneObj: ISceneObj): void {
        this.sceneId = sceneObj['sceneId'] || 0;
        this.sceneName = sceneObj['sceneName'] || '';
    }

    // 获取当前参数
    @action.bound
    public getCurrentSaveParams(): any {
        const params = {
            docId: this.apiId,
            mockName: this.mockName,
            request: this.request,
            responseType: this.responseType,
            response: this.response,
        };
        if (this.mockDataId) {
            params['id'] = this.mockDataId;
        }
        return params;
    }

    // 获取mock数据详情
    @action.bound
    public async queryDetail() {
        try {
            const params = {
                id: this.mockDataId
            };
            const result = await nsMockManageKoasApiManageMockDetailGet.remote(params);
            runInAction(() => {
                this.mockDataId = result?.id || 0;
                this.mockName = result?.mockName || '';
                this.request = formatJson(result?.request || '');
                this.responseType = result?.responseType || 0;
                this.response = formatJson(result?.response || '');
                this.default = result?.default;
                this.mockAddress = result?.mockAddress || '';
            });
        } catch (e) {
        }
    }

    // 删除mock数据
    @action.bound
    public async deleteMockData() {
        try {
            const parmas = {
                id: this.mockDataId
            };
            await nsMockManageKoasApiManageMockDeletePost.remote(parmas);
            runInAction(() => {
                message.success('删除成功～');
                this.deleteMockDataCallback && this.deleteMockDataCallback('delete');
            });
        } catch (e) {
        }
    }

    @Bind
    public async generateData(arrayNodeNum: number, type: number): Promise<void> {
        try {
            const params = {
                mockDataId: this.mockDataId,
                arrayNodeNum,
                type
            };
            const result = await nsMockManageKoasApiManageMockTemplateRandomGet.remote(params);
            this.openRandomJson(type, result?.json || '');
        } catch {
        }
    }

    @Bind
    public onOpenCreateMockData(): void {
        this.openCreateMockData && this.openCreateMockData(this.mockDataId);
    }

    @Bind
    private openRandomJson(type: number, json: string): void {
        this.randomJsonM.openRandomJson(type, json);
        this.randomJsonM.onSaveRandomJsonCallback = this.saveMockData;
    }

    // 获取入参
    @Bind
    private getParams() {
        return {
            docId: this.apiId,
            id: this.mockDataId,
            mockName: this.mockName,
            request: this.request,
            response: this.response,
            responseType: this.responseType
        };
    }

    // 保存mock数据
    @Bind
    private async saveMockData(type: number, json: string): Promise<void> {
        try {
            runInAction(() => {
                if (type === 1) {
                    this.request = json;
                }
                if (type === 2) {
                    this.response = json;
                }
            });
            await nsMockManageKoasApiManageMockEditPost.remote(this.getParams());
            message.success('保存成功～');
        } catch {
        }
    }
}
