import React, { useEffect, useRef, useState } from 'react';
import { Popover, Select, Input, Divider, Spin, Menu } from 'antd';
import { nsMockManageApiManageMainRepoListGET, nsMockManageApiManageMainRepoDetailGET } from '@/remote';
import { LOCAL_STORAGE_KEYS } from '@/index.config';
import { useDebounceFn } from 'ahooks';

interface IProjectSelectProps {
    value?: number;
    onChange?: (value: number) => void;
}

function ProjectSelect(props: IProjectSelectProps) {
    const childRef = useRef<HTMLDivElement>(null);
    const [visible, setVisible] = useState<boolean>(false);
    const [loading, setLoading] = useState<boolean>(false);
    const [keyword, setKeyword] = useState<string>('');
    const [projectList, setProjectList] = useState<nsMockManageApiManageMainRepoListGET.IRepoItem[]>([]);
    const [projectPathName, setProjectPathName] = useState<string>('');
    const searchInputRef = useRef<Input>(null);

    const getProjectList = async () => {
        setLoading(true);
        try {
            const res = await nsMockManageApiManageMainRepoListGET.remote(Object.assign({
                key: keyword,
                type: 0,
                pageNo: 1,
                pageSize: 50,
                projectIds: localStorage.getItem(LOCAL_STORAGE_KEYS.API_MGR_RECENT_VIEWS_PORJECT_IDS) || ''
            }));
            setProjectList(res.list);
            setLoading(false);
        } catch {
            setLoading(false);
        }
    };

    const debounceGetProjectList = useDebounceFn(() => {
        getProjectList();
    }, { wait: 300 });

    const debounceSearchAutoFocus = useDebounceFn(() => {
        searchInputRef.current?.focus();
    }, { wait: 300 });

    // 获取仓库详情
    const getRepoDetail = async () => {
        if (props.value) {
            try {
                const res = await nsMockManageApiManageMainRepoDetailGET.remote({ projectId: props.value });
                setProjectPathName(res.pathRepoName);
            } catch { }
        }
    };

    useEffect(() => {
        getProjectList();
    }, []);

    useEffect(() => {
        getRepoDetail();
    }, [props.value]);

    const renderProjectList = () => {
        return (
            <Spin spinning={loading}>
                {
                    !projectList.length
                        ? <div style={{ textAlign: 'center', color: '#898a8c' }}>暂无相关内容</div>
                        : <Menu style={{ maxHeight: 235, overflow: 'auto', borderRight: 'none' }}>
                            {
                                projectList.map((item) => {
                                    return (
                                        <Menu.Item key={item.projectId} onClick={() => {
                                            setProjectPathName(item.pathRepoName);
                                            props.onChange && props.onChange(item.projectId);
                                            setVisible(false);
                                        }}>
                                            {item.pathRepoName}
                                        </Menu.Item>
                                    );
                                })
                            }
                        </Menu>
                }
            </Spin>
        );
    };

    const renderPopoverContent = () => {
        let width = 374;
        childRef.current?.clientWidth && (width = childRef.current?.clientWidth - 32);
        return (
            <div style={{ width }}>
                <Input
                    value={keyword}
                    onChange={(e) => {
                        setKeyword(e.target.value);
                        debounceGetProjectList.run();
                    }}
                    ref={searchInputRef}
                    autoFocus
                />
                <Divider style={{ margin: '16px 0' }} />
                {renderProjectList()}
            </div>
        );
    };

    return (
        <Popover
            content={renderPopoverContent()}
            trigger="click"
            placement="bottom"
            visible={visible}
            onVisibleChange={(bool) => {
                setVisible(bool);
                bool && debounceSearchAutoFocus.run();
            }}
        >
            <div ref={childRef}>
                <Select
                    open={false}
                    style={{ width: '100%' }}
                    placeholder="请选择仓库"
                    loading={loading}
                    value={projectPathName || props.value}
                />
            </div>
        </Popover>
    );
}

export { ProjectSelect };
