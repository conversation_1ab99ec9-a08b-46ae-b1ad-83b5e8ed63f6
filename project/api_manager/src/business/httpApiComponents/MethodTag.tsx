/*
 * @Author: ha<PERSON><PERSON><PERSON>
 * @Date: 2024-04-19 15:18:39
 * @LastEditors: hanweiqi
 * @LastEditTime: 2024-07-20 10:54:10
 * @Description: 请填写简介
 */
import React from 'react';

// type TMethod = 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH';
export enum EMethod {
    GET = 'GET',
    POST = 'POST',
    PUT = 'PUT',
    DELETE = 'DEL',
    PATCH = 'PAT',
    HEAD = 'HEAD',
    OPTIONS = 'OPTIONS',
}

enum EMethodColor {
    GET = '#1133AD',
    POST = '#117830',
    PUT = '#AD8B00',
    DELETE = '#AD1C1A',
    PATCH = '#AD8B00',
    HEAD = '#452287',
    OPTIONS = '#7E3B8C',
}

interface IProps {
    method: EMethod;
    style?: React.CSSProperties;
}

export function MethodTag(props: IProps) {
    const { method } = props;
    return (
        <span
            style={{
                color: EMethodColor[method],
                fontSize: 12,
                marginRight: 8,
                // width: 33,
                display: 'inline-block',
                fontWeight: 'bold',
                ...props.style
            }}
        >
            {EMethod[method]}
        </span>
    );
}
