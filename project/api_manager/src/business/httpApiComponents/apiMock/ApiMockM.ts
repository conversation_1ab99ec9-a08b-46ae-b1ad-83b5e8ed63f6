import { AViewModel } from 'libs';
import { observable, action, runInAction, } from 'mobx';
import {
    nsMockManageKoasApiManageMockDeletePost, nsMockManageKoasApiManageMockGetAllMockDataListGet,
    nsMockManageKoasApiManageMockSetDefaultPost
} from '@/remote';
import { MockDataM } from '@/business/httpApiComponents/mock/mockData/MockDataM';
import { message } from 'antd';
import { CreateMockDataM, IMockDataParams } from '@/business/httpApiComponents/mock/createMockData/CreateMockDataM';
import { Bind } from 'lodash-decorators';

interface IMockList {
    id: number;
    name: string;
    key: number;
    default: boolean;
}

export class ApiMockM extends AViewModel {
    @observable public departmentId: number = 0;
    @observable protected apiId: number = 0;
    @observable public mockDataDetail: boolean = false;
    @observable public mockDataList: IMockList[] = [];
    @observable public selectedKeys: number[] = [];
    @observable public reqExample: string = '';
    @observable public resExample: string = '';

    public mockDataM = new MockDataM();
    public createMockDataM = new CreateMockDataM();

    @action.bound
    public init(apiId: number, departmentId: number) {
        this.apiId = apiId;
        this.departmentId = departmentId;
        this.getAllMockDataList();
    }

    // 打开mock数据
    @action.bound
    public onOpenMockDataDetail(mockDataId: number) {
        this.mockDataDetail = true;
        this.mockDataM.initLoading(mockDataId, this.apiId);
        this.mockDataM.openCreateMockData = this.onCreateMockData;
        this.mockDataM.deleteMockDataCallback = this.onSaveMockDataCallback;
    }

    // 新建打开mock数据
    @action.bound
    public onCreateMockData(mockDataId: number = 0) {
        this.createMockDataM.openCreateMockData(this.apiId, mockDataId);
        this.createMockDataM.onSaveMockDataCallback = this.onSaveMockDataCallback;
    }

    @Bind
    private onSaveMockDataCallback(type: 'new' | 'edit' | 'delete', params?: IMockDataParams): void {
        if (type === 'delete') {
            this.getAllMockDataList();
        } else if (params) {
            this.getAllMockDataList().then(() => {
                this.onSelectKeys([params.id]);
            });
        }
    }

    // 选中mock数据
    @action.bound
    public onSelectKeys(selectedKeys) {
        if (selectedKeys.length) {
            this.selectedKeys = selectedKeys;
            this.onOpenMockDataDetail(selectedKeys[0]);
        }
    }

    // 获取mock列表
    @Bind
    public async getAllMockDataList(mockDataId?: number) {
        try {
            const params = {
                docId: this.apiId,
                departmentId: this.departmentId
            };
            const result = await nsMockManageKoasApiManageMockGetAllMockDataListGet.remote(params);
            runInAction(() => {
                this.mockDataList = [];
                result?.list.forEach((item, index) => {
                    // item['key'] = item.id;
                    const obj = {
                        id: item.id,
                        key: item.id,
                        name: item.name,
                        default: item.default
                    };
                    this.mockDataList.push(obj);
                });
                if (mockDataId) {
                    this.selectedKeys = [mockDataId];
                } else {
                    const selectedKey = this.mockDataList[0]?.key;
                    if (selectedKey) {
                        this.onSelectKeys([this.mockDataList[0].key]);
                    } else {
                        this.mockDataDetail = false;
                    }
                }
            });
        } catch (e) {
        }
    }

    // 删除mock数据
    @action.bound
    public async deleteMockData(id) {
        try {
            const parmas = {
                id
            };
            await nsMockManageKoasApiManageMockDeletePost.remote(parmas);
            runInAction(() => {
                message.success('删除成功～');
                this.getAllMockDataList();
            });
        } catch (e) {
        }
    }

    // 设置默认mock数据
    @action.bound
    public async setDefault(nodeData) {
        try {
            const params = {
                docId: this.apiId,
                id: nodeData.id,
                sceneId: nodeData.sceneId
            };
            await nsMockManageKoasApiManageMockSetDefaultPost.remote(params);
            runInAction(() => {
                // message.success('设置成功~');
                this.mockDataList.map(item => {
                    item.default = nodeData.id === item.id;
                });
                this.mockDataList = [...this.mockDataList];
            });
        } catch (e) {
        }
    }
}
