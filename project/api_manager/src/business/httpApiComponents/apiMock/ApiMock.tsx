import { AView } from 'libs';
import React from 'react';
import { ApiMockM } from './ApiMockM';
import { observer } from 'mobx-react';
import { Tree, Button, Empty, Tooltip, Modal, Switch } from 'antd';
import Bind from 'lodash-decorators/bind';
import css from './ApiMock.less';
import { SearchEmptyIcon } from '@/business/commonIcon';
import { MockData } from '@/business/httpApiComponents/mock/mockData/MockData';
import { CreateMockData } from '@/business/httpApiComponents/mock/createMockData/CreateMockData';

@observer
export class ApiMock extends AView<ApiMockM> {

    @Bind
    protected titleRender(nodeData): React.ReactNode {
        const model = this.model;
        return <div className={css.nodeDataTitle}>
            <div className={css.nodeDataTitleName}>
                <Tooltip title={nodeData.name} placement={'topLeft'}>
                    <span>{nodeData.name}</span>
                </Tooltip>
            </div>
            <div className={css.nodeDataOperation}>
                <Tooltip title={nodeData.default ? '默认数据表示默认被使用的数据' : ''}>
                    <Switch
                        checked={nodeData.default}
                        checkedChildren={'默认'}
                        onChange={e => {
                            model.setDefault(nodeData);
                        }}
                    />
                </Tooltip>
            </div>
        </div>;
    }

    @Bind
    protected onOpenDeleteConfirm(nodeData) {
        Modal.confirm({
            title: '删除后将无法恢复，确认删除此数据？',
            onOk: () => this.model.deleteMockData(nodeData.id)
        });
    }

    @Bind
    protected renderEmpty(): React.ReactNode {
        return <Empty
            className={css.empty}
            image={<SearchEmptyIcon />}
            description={'还未MOCK数据，快去添加吧～'}
        >
            {this.renderCreateBtn()}
        </Empty>;
    }

    @Bind
    protected renderCreateBtn(): React.ReactNode {
        const model = this.model;
        return (
            <Button
                type={'primary'}
                onClick={() => model.onCreateMockData()}
            >
                新建Mock数据
            </Button>
        );
    }

    @Bind
    protected renderMock(): React.ReactNode {
        const model = this.model;
        return <div className={css.apiMock}>
            {
                Boolean(model?.mockDataList?.length) &&
                <div>
                    {this.renderCreateBtn()}
                    <Tree
                        blockNode
                        className={css.apiMockList}
                        treeData={model.mockDataList}
                        titleRender={this.titleRender}
                        selectedKeys={model.selectedKeys}
                        onSelect={model.onSelectKeys}
                    />
                </div>
            }
            <div className={css.apiMockData}>
                <MockData model={model.mockDataM} />
            </div>
        </div>;
    }

    public render(): React.ReactNode {
        const model = this.model;
        return (
            <div className={css.apiMockWrap}>
                {
                    (model?.mockDataList?.length || model.mockDataDetail) ?
                        this.renderMock() :
                        this.renderEmpty()
                }
                <CreateMockData model={model.createMockDataM}/>
            </div>
        );
    }
}
