import { checkObjectValueUnique } from 'libs';

export enum API_MGR_TABLE_COLUMN_KEYS {
    // 所有表格 的 key
    // DATASTRUCTURE_DETAIL_KEYS_TABLE = 'DATASTRUCTURE_DETAIL_KEYS_TABLE', // 数据结构详情表格key

    // REQ_CASE_HEADER_KEYS_TABLE = 'REQ_CASE_HEADER_KEYS_TABLE', // case请求header参数表格key
    // REQ_CASE_BODY_KEYS_TABLE = 'REQ_CASE_BODY_KEYS_TABLE', // case请求body参数表格key
    // REQ_CASE_FORM_KEYS_TABLE = 'REQ_CASE_FORM_KEYS_TABLE', // case请求form参数表格key
    // REQ_CASE_QUERY_KEYS_TABLE = 'REQ_CASE_QUERY_KEYS_TABLE', // case请求query参数表格key
    // REQ_CASE_PATH_KEYS_TABLE = 'REQ_CASE_PATH_KEYS_TABLE', // case请求path参数表格key
    // RES_CASE_HEADER_KEYS_TABLE = 'RES_CASE_HEADER_KEYS_TABLE', // case返回header参数表格key
    // RES_CASE_BODY_KEYS_TABLE = 'RES_CASE_BODY_KEYS_TABLE', // case返回body参数表格key

    REQ_HEADER_COLUMN = 'req_header_column', // api请求header参数表格key
    REQ_BODY_COLUMN = 'req_body_column', // api请求body参数表格key
    REQ_FORM_COLUMN = 'req_form_column', // api请求form参数表格key
    REQ_QUERY_COLUMN = 'req_query_column', // api请求query参数表格key
    REQ_PATH_COLUMN = 'req_path_column', // api请求path参数表格key
    RES_HEADER_COLUMN = 'res_header_column', // api返回header参数表格key
    RES_BODY_COLUMN = 'res_body_column', // api返回body参数表格key

    AUTO_GRPC_API_MGR = 'auto_grpc_api_mgr', // 自动解析grpc api列表
}
checkObjectValueUnique(API_MGR_TABLE_COLUMN_KEYS, 'API_MGR_TABLE_COLUMN_KEYS');
