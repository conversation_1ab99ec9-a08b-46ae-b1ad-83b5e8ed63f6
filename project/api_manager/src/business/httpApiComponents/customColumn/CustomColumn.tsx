import React from 'react';
import { <PERSON><PERSON>, Popover, Checkbox } from 'antd';
import { KdevIconFont } from '@/business/commonComponents/KdevIconFont';
import { LOCAL_STORAGE_KEYS } from '@/index.config';

interface IOptions {
    label: string;
    value: string;
    disabled?: boolean;
}

interface IProps {
    value: string[];
    onChange(val: any[]);
    options: IOptions[];
    className?: string;
    trigger?: 'click';
}

// 获取localStorage里的API_MGR_TABLE_COLUMN
function getLoaclStorageApiMgrTableColumn(): object {
    const apiMgrTableColumn = localStorage.getItem(LOCAL_STORAGE_KEYS.API_MGR_TABLE_COLUMN) || '';
    if (apiMgrTableColumn) {
        try {
            return JSON.parse(apiMgrTableColumn);
        } catch {
            return {};
        }
    } else {
        return {};
    }
}

export function removeApiMgrTableColumn(key: string) {
    try {
        const apiMgrTableColumn: object = getLoaclStorageApiMgrTableColumn();
        delete apiMgrTableColumn[key];
        const apiMgrTableColumnStr: string = JSON.stringify(apiMgrTableColumn);
        localStorage.setItem(LOCAL_STORAGE_KEYS.API_MGR_TABLE_COLUMN, apiMgrTableColumnStr);
    } catch {
    }
}

function ColumnPopoverContent(props: IProps) {
    const { value, onChange, options } = props;
    return (
        <Checkbox.Group
            style={{ width: '100%' }}
            value={value}
            onChange={(val) => onChange(val)}
        >
            {options.map(item => {
                return (
                    <span key={item.value}>
                        <Checkbox value={item.value} disabled={item.disabled}>{item.label}</Checkbox><br />
                    </span>
                );
            })}
        </Checkbox.Group>
    );
}

export function CustomColumn(props: IProps) {
    const { value, options, trigger, className } = props;
    return (
        <Popover
            placement="bottomRight"
            title={`已选择${value.length}/${options.length}`}
            content={ColumnPopoverContent(props)}
            trigger={trigger}
        >
            <Button
                type={'link'}
                size={'small'}
                icon={<KdevIconFont id={'#iconsettings'} />}
                className={className}
            />
        </Popover>
    );
}
