import React from 'react';
import { observer } from 'mobx-react';
import { observable, runInAction } from 'mobx';
import { Button, Menu, Dropdown } from 'antd';
import { LeftOutlined, DownOutlined } from '@ant-design/icons';
import { Bind } from 'lodash-decorators';
import css from './index.less';
import { nsMockManageKoasApiManageSwaggerLangGet, nsMockManageKoasApiManageSwaggerGenerateGet } from '@/remote';

const { SubMenu } = Menu;

interface IProps {
    docId: number;
    className?: string;
}

@observer
export class AutoGenerateCode extends React.Component<IProps> {
    @observable private client: string[] = [];
    @observable private server: string[] = [];
    @observable private loading: boolean = false;

    @Bind
    public componentDidMount(): void {
        this.queryLang();
    }

    @Bind
    private async queryLang(): Promise<void> {
        try {
            const result = await nsMockManageKoasApiManageSwaggerLangGet.remote();
            runInAction(() => {
                this.client = result?.client || [];
                this.server = result?.server || [];
            });
        } catch {
        }
    }

    @Bind
    private async generateCode(e): Promise<void> {
        try {
            runInAction(() => this.loading = true);
            const params = {
                docId: this.props.docId,
                lang: e.key,
                apiUrl: location.href
            };
            const result = await nsMockManageKoasApiManageSwaggerGenerateGet.remote(params);
            runInAction(() => this.loading = false);
            if (result?.link) {
                // window.open(result.link);
                window.location.href = result.link;
            }
        } catch {
            runInAction(() => this.loading = false);
        }
    }

    @Bind
    private renderMenuOverlay() {
        return (
            <Menu onClick={this.generateCode}>
                <SubMenu title={'客户端代码'} key={'client'} icon={<LeftOutlined />}>
                    {
                        this.client.map(item => {
                            return (
                                <Menu.Item key={item}>{item}</Menu.Item>
                            );
                        })
                    }
                </SubMenu>
                <SubMenu title={'服务端代码'} key={'server'} icon={<LeftOutlined />}>
                    {
                        this.server.map(item => {
                            return (
                                <Menu.Item key={item}>{item}</Menu.Item>
                            );
                        })
                    }
                </SubMenu>
            </Menu>
        );
    }

    public render(): React.ReactNode {
        const { className } = this.props;
        return (
            <Dropdown
                overlay={this.renderMenuOverlay()}
                placement={'bottomRight'}
                overlayClassName={css.autoGenerateCodeMenuOverlay}
                className={className}
            >
                <Button type={'primary'} loading={this.loading}>生成代码<DownOutlined /></Button>
            </Dropdown>
        );
    }
}
