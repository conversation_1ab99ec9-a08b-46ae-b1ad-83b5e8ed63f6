import React from 'react';
import css from './RequestTypeRadio.less';
import { Radio, Tooltip, Select } from 'antd';
import { KdevIconFont } from '@/business/commonComponents/KdevIconFont';

interface ITips {
    [key: string]: string;
}

const tips: ITips = {
    queryTips: 'Query参数指的是地址栏中跟在?后面的参数，如/login?username=jackliu',
    // pathTips: ''
};

interface IRequestType {
    value: number;
    onChange: (e) => void | undefined;
}

interface IReqDataTypeProps {
    className?: string;
    value: string;
    onChange: (e) => void | undefined;
}

interface IProps {
    requestTypeProps: IRequestType;
    reqDataTypeProps: IReqDataTypeProps;
    rigthSlot?: React.ReactNode;
}

interface ISelectOptions {
    label?: string;
    value: string;
}

const reqDataTypeOptions: ISelectOptions[] = [
    {
        label: 'FORM',
        value: 'form',
    },
    {
        label: 'JSON',
        value: 'body',
    }
];

function RequestType(props: IRequestType) {
    return (
        <Radio.Group
            {...props}
        >
            <Radio.Button value={1}>Headers</Radio.Button>
            <Radio.Button value={2}>Body</Radio.Button>
            <Tooltip title={tips.queryTips}>
                <Radio.Button value={3}>
                    Query
                    <KdevIconFont id={'#iconquestion'} className={css.tipsIcon} />
                </Radio.Button>
            </Tooltip>
            {/* <Tooltip title={tips.pathTips}>
                <Radio.Button value={4}>
                    Path
                    <KdevIconFont id={'#iconquestion'} className={css.tipsIcon} />
                </Radio.Button>
            </Tooltip> */}
        </Radio.Group>
    );
}

function ReqDataType(props: IReqDataTypeProps) {
    return (
        <div className={props.className}>
            数据类型
            <Select
                options={reqDataTypeOptions}
                {...props}
                className={css.reqDataTypeSelect}
            />
        </div>
    );
}

export function RequestTypeRadio(props: IProps) {
    return (
        <div className={css.requestTypeRadioWrap}>
            <div className={css.requestTypeRadio}>
                <RequestType {...props.requestTypeProps} />
                {
                    props.requestTypeProps?.value === 2 &&
                    <ReqDataType {...props.reqDataTypeProps} className={css.reqDataType} />
                }
            </div>
            {props?.rigthSlot}
        </div>
    );
}
