import React, { useEffect, useState, useImperativeHandle, forwardRef, Ref } from 'react';
import { Divider, Select } from 'antd';
import { SelectProps } from 'antd/lib/select';
import { nsMockManageApiManageMainVersionSimpleListGET } from '@/remote';
import { ApiStatusTag } from '@/pages/httpApi/viewApi/ApiStatusTag';
import PopoverEllipsis from '@/business/common/PopoverEllipsis';
import css from './VersionSelect.less';

interface IProps extends SelectProps<number> {
    apiId: number;
    onInitValue?(version: number): void;
    optionValueName?: 'version' | 'versionId';
}

interface IVersionSelectRef {
    getVersionList(): Promise<void>;
}

const VersionSelect = forwardRef((
    { apiId, onInitValue, optionValueName = 'version', ...props }: IProps,
    ref: Ref<IVersionSelectRef>
) => {
    const [loading, setLoading] = useState<boolean>(false);
    const [versionList, setVersionList] = useState<nsMockManageApiManageMainVersionSimpleListGET.IItem[]>([]);

    const getVersionList = async () => {
        setLoading(true);
        try {
            const res = await nsMockManageApiManageMainVersionSimpleListGET.remote({ apiId });
            setVersionList(res.list);
            onInitValue && onInitValue(res.version);
            setLoading(false);
        } catch {
            setLoading(false);
        }
    };

    useImperativeHandle(ref, () => ({
        getVersionList
    }));

    useEffect(() => {
        (apiId && apiId > 0) ? getVersionList() : setVersionList([]);
    }, [apiId]);

    return (
        <Select
            loading={loading}
            placeholder="请选择版本"
            dropdownMatchSelectWidth={420}
            optionLabelProp="label"
            dropdownClassName={css.versionSelectDropdown}
            {...props}
        >
            {
                versionList.map(item => {
                    let teamName = '';
                    if (item?.teams) {
                        item.teams.forEach(it => {
                            it?.title && (teamName += (teamName ? '/' : '') + it?.title);
                        });
                    }
                    return (
                        <Select.Option
                            key={item[optionValueName]}
                            value={item[optionValueName]}
                            label={`Version ${item.version}`}
                        >
                            <div className={css.version}>
                                {
                                    item.version === 0 ?
                                        <b>master分支版本</b>
                                        :
                                        <b>Version {item.version}</b>
                                }
                                <ApiStatusTag>{item.state}</ApiStatusTag>
                            </div>
                            <div className={css.createUser}>
                                创建者：{item?.createUser?.name}
                                {
                                    teamName &&
                                    <>
                                        <Divider type="vertical" className={css.divider} />
                                        Team任务：
                                        <PopoverEllipsis title={teamName} placement="right">
                                            <span className={css.textEllipsis}>{teamName}</span>
                                        </PopoverEllipsis>
                                    </>
                                }
                            </div>
                            {
                                item?.mark &&
                                <div className={css.mark}>
                                    备注：
                                    <PopoverEllipsis title={item.mark} placement="right">
                                        <span className={css.textEllipsis}>{item.mark}</span>
                                    </PopoverEllipsis>
                                </div>
                            }
                        </Select.Option>
                    );
                })
            }
        </Select>
    );
});

export { VersionSelect, IVersionSelectRef };
