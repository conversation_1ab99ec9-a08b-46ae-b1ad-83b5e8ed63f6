import { <PERSON><PERSON>ie<PERSON> } from 'libs';
import React from 'react';
import { CustomTagM } from './CustomTagM';
import { observer } from 'mobx-react';
import { Tag, Popconfirm, Input } from 'antd';
import css from './CustomTag.less';
import { KdevIconFont } from '@/business/commonComponents';
import { Bind } from 'lodash-decorators';

interface IProps {
    label?: string | React.ReactElement;
    className?: string;
}

@observer
export class CustomTag extends AView<CustomTagM, IProps> {

    @Bind
    private renderAddTagPopover() {
        const model = this.model;
        return (
            <div className={css.popverContent}>
                <Input
                    placeholder="输入完标签请按回车添加"
                    value={model.tag}
                    onInput={model.onChangeTag}
                    onPressEnter={model.onPressEnterTag}
                    className={css.tagInput}
                />
                <div className={css.temporaryTagsBox}>
                    {
                        model.temporaryTags.map((item, index) => (
                            <Tag
                                className={css.temporaryTag}
                                key={`${item}-${index}`}
                                color="#327DFF"
                                closable
                                onClose={(e: any) => {
                                    e.preventDefault();
                                    model.onDeleteTag(index);
                                }}
                            >
                                {item}
                            </Tag>
                        ))
                    }
                </div>
            </div>
        );
    }

    public render(): React.ReactNode {
        const model = this.model;
        return (
            <div
                className={this.props.className}
            >
                {this.props.label}
                <span className={css.customTagsBox}>
                    {
                        model.customTags.map((item, index) => (
                            <Tag className={css.customTag} key={`${item}-${index}`} color="#327DFF">{item}</Tag>
                        ))
                    }
                    <Popconfirm
                        title={this.renderAddTagPopover()}
                        placement="bottomLeft"
                        trigger="click"
                        // title="添加自定义标签"
                        icon={false}
                        overlayClassName={css.popconfirmOverlay}
                        okText="保存"
                        onCancel={model.onCloseTagPopover}
                        onConfirm={model.addCustomizeTag}
                        okButtonProps={{
                            loading: model.saveLoading,
                            // disabled: !model.temporaryTags.length && !model.tag
                        }}
                        visible={model.visible}
                    >
                        <a className={css.circleIconBtn} onClick={model.onOpenPopconfirm}>
                            <KdevIconFont className={css.circleIcon} id="#iconadd-circle" />
                        </a>
                    </Popconfirm>
                </span>
            </div>
        );
    }
}
