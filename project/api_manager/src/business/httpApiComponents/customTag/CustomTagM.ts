import { AViewModel } from 'libs';
import { observable, action, runInAction } from 'mobx';
import { Bind } from 'lodash-decorators';
import { nsMockManageKoasApiManageGroupQueryCustomizeTagGet, nsMockManageKoasApiManageGroupAddCustomizeTagPost } from '@/remote';
import { message } from 'antd';

interface IApiAttributes extends nsMockManageKoasApiManageGroupQueryCustomizeTagGet.IApiAttributes {}

export class CustomTagM extends AViewModel {
    private apiId: number = 0;
    @observable public tag: string = '';
    @observable public temporaryTags: string[] = [];
    @observable public customTags: string[] = [];
    @observable public saveLoading: boolean = false;
    @observable public visible: boolean = false;

    @action.bound
    public initLoading(apiId: number) {
        this.apiId = apiId;
        this.tag = '';
        this.customTags = [];
        this.temporaryTags = [];
        this.queryCustomizeTag();
    }

    @Bind
    private async queryCustomizeTag() {
        try {
            const result = await nsMockManageKoasApiManageGroupQueryCustomizeTagGet.remote({apiId: this.apiId});
            this.getApiCustomTags(result.apiAttributes);
        } catch {
        }
    }

    @action.bound
    private getApiCustomTags(apiAttributes: IApiAttributes[]) {
        apiAttributes.forEach(item => {
            if (item.key === 'customize') {
                this.customTags = item.values;
                this.temporaryTags = this.customTags;
            }
        });
    }

    @action.bound
    public onChangeTag(e: any) {
        this.tag = e.target.value;
    }

    @action.bound
    public onPressEnterTag() {
        if (!this.tag) {
            message.warning('请输入标签～');
            return;
        }
        if (this.temporaryTags.includes(this.tag)) {
            message.warning('标签已存在，请勿重复添加～');
            return;
        }
        this.temporaryTags.push(this.tag);
        this.tag = '';
    }

    @action.bound
    public onDeleteTag(index: number) {
        this.temporaryTags.splice(index, 1);
    }

    @action.bound
    public onCloseTagPopover() {
        this.visible = false;
        this.tag = '';
        this.temporaryTags = [...this.customTags];
    }

    @action.bound
    public async addCustomizeTag() {
        if (!this.temporaryTags.includes(this.tag) && this.tag) {
            this.onPressEnterTag();
        }
        runInAction(() => this.saveLoading = true);
        try {
            const params = {
                apiId: this.apiId,
                key: 'customize',
                values: this.temporaryTags
            };
            await nsMockManageKoasApiManageGroupAddCustomizeTagPost.remote(params);
            message.success('添加成功～');
            runInAction(() => {
                this.saveLoading = false;
                this.tag = '';
                this.customTags = [...this.temporaryTags];
            });
            this.onCloseTagPopover();
        } catch {
            runInAction(() => this.saveLoading = false);
        }
    }

    @action.bound
    public onOpenPopconfirm() {
        this.visible = true;
    }
}
