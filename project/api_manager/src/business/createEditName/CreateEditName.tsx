import { AView, AViewModel } from 'libs';
import React from 'react';
import { observable, action } from 'mobx';
import { observer } from 'mobx-react';
import { bindObserver } from '@libs/mvvm';
import { Bind } from 'lodash-decorators';
import { Input, Modal } from 'antd';
import css from './CreateEditName.less';

const Input_editName = bindObserver(Input, 'editName');

interface IProps {
    nameLabel: string;
    title?: string;
    placeholder?: string;
}

export class CreateEditNameM extends AViewModel {
    @observable public visible: boolean = false;
    @observable public editName: string = '';
    @observable public editId: number = 0;

    public onSaveNameCallback?(name: string, id?: number): void;

    @action.bound
    public init(name: string = '', id: number = 0): void {
        this.editName = name;
        this.editId = id;
        this.visible = true;
    }

    @action.bound
    public closeCreateEditModal(): void {
        this.visible = false;
        this.editName = '';
    }

    @Bind
    public onSaveName(): void {
        this.onSaveNameCallback && this.onSaveNameCallback(this.editName, this.editId);
        this.closeCreateEditModal();
    }
}

@observer
export class CreateEditName extends AView<CreateEditNameM, IProps> {

    @Bind
    public componentWillReceiveProps(nextProps): void {
        this.model = nextProps.model;
    }

    public render(): React.ReactNode {
        const model = this.model;
        const props = this.props;
        return (
            <Modal
                title={ props.title || '新建' }
                visible={ model.visible }
                className={ css.createEditNameModal }
                onCancel={ model.closeCreateEditModal }
                onOk={ model.onSaveName }
                destroyOnClose={ true }
                maskClosable={ false }
            >
                <div className={ css.nameLabel }>
                    {props.nameLabel}
                </div>
                <Input_editName
                    model={ model }
                    placeholder={ props.placeholder || '请输入' }
                    autoFocus
                    onPressEnter={ model.onSaveName }
                />
            </Modal>
        );
    }
}
