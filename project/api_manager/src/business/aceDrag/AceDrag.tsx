import React from 'react';
import { observer } from 'mobx-react';
import css from './AceDrag.less';
import { MenuOutlined } from '@ant-design/icons';
import { Bind } from 'lodash-decorators';
import { Rnd } from 'react-rnd';
import { FullScreen, AsyncAceEditor } from '@/business/commonComponents';

interface IProps {
    className?: string;
    value?: string;
    readOnly?: boolean;
    onChange?: (val) => void;
    aceDragClassName?: string;
    theme?: 'chaos' | 'tomorrow' | 'xcode';
    mode?: 'json5' | 'python' | 'typescript';
    isUseFullScreen?: boolean;
}

@observer
export class AceDrag extends React.Component<IProps> {
    public state = {
        height: 300,
        isFullScreen: false
    };

    @Bind
    private onChangeIsFullScreen(isFullScreen: boolean): void {
        this.setState({
            isFullScreen
        });
    }

    @Bind
    private onResize(e, description, ref): void {
        this.setState({
            height: ref.offsetHeight
        });
    }

    @Bind
    private renderBottomResizeHandle(): React.ReactElement {
        return (
            <div className={css.bottomResizeHandle}>
                <MenuOutlined />
            </div>
        );
    }

    public render(): React.ReactNode {
        return (
            <div
                className={`${css.aceDragWrap} ${this.props.aceDragClassName}`}
                style={{ height: `${this.state.height}px` }}
            >
                <FullScreen
                    isUseFullScreen={this.props.isUseFullScreen}
                    onChange={this.onChangeIsFullScreen}
                >
                    <Rnd
                        disableDragging={true}
                        size={{
                            width: '100%',
                            height: '100%'
                        }}
                        onResize={this.onResize}
                        resizeHandleComponent={{
                            bottom: this.renderBottomResizeHandle()
                        }}
                        enableResizing={{
                            top: false,
                            right: false,
                            bottom: true,
                            left: false,
                            topRight: false,
                            bottomRight: false,
                            bottomLeft: false,
                            topLeft: false
                        }}
                        minHeight={100}
                    >
                        <AsyncAceEditor
                            width={'100%'}
                            height={this.state.isFullScreen ? '100%' : `${this.state.height}px`}
                            showPrintMargin={false}
                            onChange={this.props.onChange}
                            {...this.props}
                        // commands={[{
                        //     name: 'find',
                        //     bindKey: { win: 'Ctrl-F', mac: 'Command-F' },
                        //     exec: () => {
                        //         return false;
                        //     }
                        // }]}
                        />
                    </Rnd>
                </FullScreen>
            </div>
        );
    }
}
