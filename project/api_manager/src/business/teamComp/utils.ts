// team 操作种类
export type ActionType = 'CopyChild' | 'Info' | 'Unrelate' | 'ChildTask';

// team 展示信息种类
export type InfoType = 'type' | 'name' | 'status' | 'assignee' | 'opts';

// team - 展示行 - 场景
export enum EInfoScene {
    normal = 'normal', // 有解除操作
    readOnly = 'readOnly', // 只读 - 无操作,有头像
    simpleReadOnly = 'simpleReadOnly' // 只读 - 无操作,无头像
}

// team - 不同场景下 展示行展示的内容和操作
export const InfoSceneMap = {
    [EInfoScene.normal]: {
        itemArr: ['type', 'name', 'status', 'opts'] as InfoType[],
        optsArr: 'Unrelate' as ActionType,
    },
    [EInfoScene.readOnly]: {
        itemArr: ['type', 'name', 'status', 'assignee'] as InfoType[],
        optsArr: '' as ActionType,
    },
    [EInfoScene.simpleReadOnly]: {
        itemArr: ['type', 'name', 'status'] as InfoType[],
        optsArr: '' as ActionType,
    },
};

// team - 搜索列表 - 场景
export enum ESearchScene {
    normal = 'normal',
    infoOnly = 'infoOnly', // 只支持查看
}

// team - 不同场景下 搜索列表展示的操作
export const SearchSceneMap = {
    [ESearchScene.normal]: {
        optsArr: ['CopyChild', 'ChildTask', 'Info'] as ActionType[],
    },
    [ESearchScene.infoOnly]: {
        optsArr: ['Info'] as ActionType[],
    },
};
