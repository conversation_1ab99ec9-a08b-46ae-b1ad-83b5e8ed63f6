import React from 'react';
import { kdev_commonlyused_teamequirements, common_system_brokenlink } from '@kid/enterprise-icon/icon/output/icons';
import { KdevIconFont } from '@/business/commonComponents';
import { teamStatusMap, teamTypeIconMap } from './TeamMap';
import { Button, Space, Tooltip } from 'antd';
import { TeamDetailModal } from '../team';
import css from './index.less';
import { KdevTag } from '../common/KdevTag';
import classNames from 'classnames';
import { KimUserInfo } from '../KimUserInfo';
import { searchHighLight } from '../common/searchHighLight';
import { nsKdevTeamSearchListGet } from '@/remote';
import { ActionType } from './utils';
import PopoverEllipsis from '../../business/common/PopoverEllipsis';
export function TeamType(props: { item: nsKdevTeamSearchListGet.IReturn_TeamItem }) {
    const { disabled = false } = props.item;
    const taskType = props.item.taskTypeGroup || props.item.taskType || 'demand';
    const taskClass = props.item.taskClassEntity?.name || props.item.taskClass;
    const teamTypeIcon = teamTypeIconMap.find(it => it.key === taskType);
    const title = taskClass || teamTypeIcon?.text || '产品需求';
    return (
        <>
            <span className={classNames(css.teamIcon, { [css.disabledIcon]: disabled })}>
                <KdevIconFont
                    id={teamTypeIcon?.icon || kdev_commonlyused_teamequirements}
                    style={{ fontSize: '16px', marginRight: '4px', verticalAlign: '-0.2em' }}
                    color={taskType === 'demand' ? '#326BFB' : ''}
                />
            </span>
            <PopoverEllipsis title={title}>
                <span className={classNames(css.teamType, { [css.disabled]: disabled })}>{title}</span>
            </PopoverEllipsis>
        </>
    );
}

interface IProps_Name {
    item: nsKdevTeamSearchListGet.IReturn_TeamItem;
    clickable?: boolean;
    keyword?: string;
    getPopupContainer?: (triggerNode: HTMLElement) => HTMLElement;
    isSDK?: boolean;
}
export const TeamName = (props: IProps_Name) => {
    const { item, clickable = false, keyword = '' } = props;
    const info = item.name || item.title;

    const content = () => {
        return (
            <div
                className={classNames(css.teamName, { [css.disabled]: item.disabled })}
                dangerouslySetInnerHTML={
                    { __html: searchHighLight(info, keyword, item.disabled ? '#ABCDFF' : '#326BFB') }
                }
            />
        );
    };

    const teamInfo = () => {
        if (item.tips) {
            return (
                <Tooltip
                    title={item.tips && <span dangerouslySetInnerHTML={{ __html: item.tips }} />}
                    placement={'topLeft'}
                    getPopupContainer={props.getPopupContainer}
                >
                    {content()}
                </Tooltip>
            );
        }
        return (
            <PopoverEllipsis title={!item.tips ? info : undefined} placement={'topLeft'}>
                {content()}
            </PopoverEllipsis>
        );
    };
    if (clickable) {
        return (
            <span className={classNames(css.teamName, css.clickable)}>
                <TeamDetailModal taskId={item.taskId} disabled={item.disabled} forWorkbench={props.isSDK}>
                    {teamInfo()}
                </TeamDetailModal>
            </span>
        );
    }
    return (
        <span className={css.teamName}>
            {teamInfo()}
        </span>
    );
};

export function TeamStatus(props: { item: nsKdevTeamSearchListGet.IReturn_TeamItem }) {
    const { status, statusName, exits } = props.item;
    const statusObj = teamStatusMap.find(it => it.name === statusName);
    if (exits === false || props.item.isDeleted === true) {
        return (
            <KdevTag content={'已删除'} color={'#666f80'} style={{ margin: '0 2px 0 24px' }} size={'x-small'} />
        );
    }
    return (
        <KdevTag
            content={statusName || status?.name || '待解决'}
            color={statusObj?.color || '#666f80'}
            size={'x-small'} style={{ margin: '0 2px 0 24px' }}
        />
    );
}

export function TeamAssignee(props: { item: nsKdevTeamSearchListGet.IReturn_TeamItem }) {
    return (
        <span className={classNames({ [css.disabledUser]: props.item?.disabled }, css.teamAssignee)}>
            <KimUserInfo avatarUrl={props.item?.assignee?.avatarUrl} name={props.item?.assignee?.name} />
        </span>
    );
}
interface IProps_Opts {
    item: nsKdevTeamSearchListGet.IReturn_TeamItem;
    optsArr: ActionType | ActionType[];
    onUnRelate?: (e, data) => void;
    onInfo?: (e, data) => void;
    getPopupContainer?: (triggerNode: HTMLElement) => HTMLElement;
    disabled?: boolean;
}
export const TeamOpts = (props: IProps_Opts) => {
    const { item, optsArr } = props;
    if (!optsArr || !optsArr.length) {
        return null;
    }
    return (
        <div className={css.teamOpts}>
            {
                optsArr.indexOf('Unrelate') > -1 &&
                <Tooltip title={'解除绑定'} getPopupContainer={props.getPopupContainer}>
                    <Button type={'text'} onClick={(e) => props.onUnRelate?.(e, item)} size={'small'}
                        className={css.btn} disabled={props.disabled}>
                        <KdevIconFont id={common_system_brokenlink} style={{ width: '14px', height: '14px' }} />
                    </Button>
                </Tooltip>
            }
        </div>
    );
};
