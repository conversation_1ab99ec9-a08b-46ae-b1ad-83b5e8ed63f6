import React from 'react';
import css from './index.less';
import { Input, Popover, Checkbox, Tooltip, Table, Button, message } from 'antd';
import { nsKdevTeamRestrictCheckGet, nsKdevTeamSearchListGet, nsKdevTeamSubTaskListGet } from '@/remote';
import Bind from 'lodash-decorators/bind';
import { Debounce } from 'lodash-decorators';
import { KdevIconFont } from '@/business/commonComponents';
import { common_system_help02, common_system_search } from '@kid/enterprise-icon/icon/output/icons';
import { TeamTable } from './TeamTable';
import { ESearchScene } from './utils';
import { KdevSearch } from '../kdevSearch/KdevSearch';
import { computed, runInAction } from 'mobx';
import qs from 'qs';
const KEYS_LS = Object.freeze({
    KDEV_TEAM_POPOVER_IS_RELATED: 'kdev_team_popover_is_related', // team搜索组件 - 与我相关
});
export namespace nsKdevTeam {
    /**
     * 获取 team 弹窗搜索 与我相关 勾选
     */
    export function getIsRelateToMe(): boolean {
        return localStorage.getItem(KEYS_LS.KDEV_TEAM_POPOVER_IS_RELATED) === '1';
    }
    /**
     * 设置 team 弹窗搜索 与我相关 勾选
     */
    export function setIsRelateToMe(checked: boolean) {
        localStorage.setItem(KEYS_LS.KDEV_TEAM_POPOVER_IS_RELATED, checked ? '1' : '0');
    }
}
const LOCAL_STORE = {
    // team搜索弹窗
    KDEVTEAM: nsKdevTeam,
};

interface IProps {
    onSearch: (params?: { search: string, related: boolean }) => Promise<nsKdevTeamSearchListGet.IReturn_TeamItem[]>;
    className?: string;
    style?: React.CSSProperties;
    children?: any;
    onSelect?: (record, taskId: string) => void;
    getPopupContainer?: (triggerNode: HTMLElement) => HTMLElement;
    searchScene?: ESearchScene;
    // 多选相关
    multiple?: boolean; // 是否多选
    selectedTaskIds: string[]; // 多选的keys
    onMultiSelect?: (increRecords: any[], taskIds: string[]) => void; // increRecords指增量的选中项, ids指所有选中的项
    isSDK?: boolean;
    showCreateTeamBtn?: boolean; // 是否展示创建Team按钮, 默认展示
    searchParams?: nsKdevTeamSearchListGet.IParam_SceneParam | nsKdevTeamSubTaskListGet.IParams; // 默认搜索的参数

    // 是否在弹窗下方展示二次确认/取消按钮
    showConfirmBtn?: boolean; // 是否展示确认按钮, 默认不展示，开启后变为多选
    onConfirm?: (e, increRecords, deletedTaskIds: string[]) => void; // 确认按钮的回调
    onCancel?: (e) => void; // 取消按钮的回调

    // TeamTable组件checkable属性
    checkDisabled?: (record) => boolean;
}

interface IState {
    visible: boolean;
    drawerVisible: boolean; // 查看team详情的抽屉点击时会同时trigger Popover组件的click事件，所以用两个visible判断popover的展示
    loading: boolean;
    keyword: string;
    dataList: nsKdevTeamSearchListGet.IReturn_TeamItem[];
    flattenDataList: nsKdevTeamSearchListGet.IReturn_TeamItem[]; // 打平的team列表
    checked: boolean;
    // 新建team用
    visibleCreateTeam: boolean;
    keyWithCreateTeam: number;
    queryStringifyInfo: string;
    // 二次确认/取消按钮相关
    increRecords: nsKdevTeamSearchListGet.IReturn_TeamItem[]; // increRecords指增量的选中项
    taskIds: string[]; // ids指所有选中的项
    onConfirmLoading: boolean;
}

export class TeamPopover extends React.Component<IProps, IState> {
    private readonly inputRef;
    private readonly tableRef;

    public state: IState = {
        visible: false,
        drawerVisible: false,
        loading: true,
        keyword: '',
        dataList: [],
        flattenDataList: [],
        checked: LOCAL_STORE.KDEVTEAM.getIsRelateToMe() || false,
        visibleCreateTeam: false,
        keyWithCreateTeam: 0,
        queryStringifyInfo: '',
        increRecords: [],
        taskIds: [],
        onConfirmLoading: false,
    };

    constructor(props) {
        super(props);
        this.inputRef = React.createRef();
        this.tableRef = React.createRef();
    }

    @computed
    public get selectedTaskIds() {
        if (this.props.showConfirmBtn) {
            return this.state.taskIds;
        } else {
            return this.props.selectedTaskIds || [];
        }
    }

    @Bind
    public getFlattenList(list: any[]) {
        return list.reduce((pre, cur) => {
            pre.push(cur);
            if (cur.subTasks?.length > 0) {
                pre = pre.concat(cur.subTasks);
            }
            return pre;
        }, []);
    }

    @Bind
    public getMergedList(list1: any[], list2: any[]) {
        return list1.concat(list2).reduce((pre: any[], current) => {
            const existingItem = pre.find(item => item.taskId === current.taskId);
            if (!existingItem) {
                pre.push(current);
            }
            return pre;
        }, []);
    }

    @Bind
    private ChangeChecked(e) {
        this.setState({
            checked: e.target.checked,
            loading: true,
        }, () => {
            this.getTeamList(this.state.keyword);
        });
        LOCAL_STORE.KDEVTEAM.setIsRelateToMe(e.target.checked);
    }

    @Debounce(300)
    private getTeamList(keyword) {
        this.getData(keyword);
    }

    @Bind
    private onRefresh(useKeyword = true) {
        this.setState({
            loading: true,
        }, () => {
            setTimeout(() => {
                this.getData(useKeyword ? this.state.keyword : '');
            }, 800);
        });
    }

    @Bind
    private async getData(keyword) {
        const byteLength = keyword.trim().replace(/[^\\x0000-\\x00ff]/g, 'aa').length;
        if (byteLength === 1) { // 单字节时不请求接口
            this.setState({
                loading: false,
            });
            return;
        }
        const respData = await this.props.onSearch({ search: keyword.trim(), related: this.state.checked });
        this.setState({
            dataList: respData,
            loading: false,
            flattenDataList: this.getFlattenList(respData)
        });
    }

    @Bind
    private onChangeDrawerVisible(visible) {
        this.setState({
            drawerVisible: visible,
            visible: true // 保证抽屉关闭时popover组件保持可见
        });
    }

    /**
     * 打开选择面板
     * @param visible
     */
    @Bind
    private handleVisibleChange(visible) {
        this.setState({
            visible
        });
        if (visible) {
            setTimeout(() => {
                this.inputRef.current?.focus();
            }, 100);
            this.setState({
                keyword: '',
                dataList: [],
                flattenDataList: [],
                loading: true,
                increRecords: [],
                taskIds: this.props.showConfirmBtn ? this.props.selectedTaskIds : [],
            });
            this.getData('');
        }
    }

    /**
     * 选中
     * @param item
     */
    @Bind
    private onSelect(id) {
        const item = this.state.flattenDataList.find(ele => ele.taskId === id);
        this.props.onSelect?.(item, id);
        this.setState({
            visible: false,
        });
    }

    /**
     * 多选选中
     * @param item
     */
    @Bind
    private onMultiSelect(ids) {
        const selectedList = this.state.flattenDataList.filter(
            item => ids.indexOf(item.taskId) > -1
        );
        this.setState(() => ({
            increRecords: this.getMergedList(selectedList, this.state.increRecords),
            taskIds: ids
        }));
        this.props.onMultiSelect?.(selectedList, ids);
    }

    @Bind
    private async onClickConfirm(e) {
        try {
            this.setState({
                onConfirmLoading: true
            });
            const deletedTaskIds = this.props.selectedTaskIds.reduce((pre: string[], cur: string) => {
                if (this.state.taskIds.indexOf(cur) < 0) {
                    pre.push(cur);
                }
                return pre;
            }, []);
            const increRecords = this.state.increRecords.filter(item => this.props.selectedTaskIds.indexOf(item.taskId) < 0); // -
            await this.props.onConfirm?.(e, increRecords, deletedTaskIds);
            this.setState({
                onConfirmLoading: false,
            });
            this.handleVisibleChange(false);
        } catch (error) {
            this.setState({
                onConfirmLoading: false
            });
        }
    }

    @Bind
    private onCancelConfirm(e) {
        this.handleVisibleChange(false);
        this.props.onCancel?.(e);
    }

    /**
     * 修改关键字，触发搜索
     * @param keyword
     */
    @Bind
    private handleChangeInput(e) {
        const keyword = e.target.value;
        this.setState({
            keyword,
            loading: true
        }, () => {
            this.getTeamList(keyword);
        });
    }

    @Bind
    private async onSuccessCreateTeam(data) {
        if (data.taskType === 'child_task') { // 如果是子任务类型，说明不是【创建任务】按钮成功后的回调
            return;
        }
        this.onRefresh(false);
        try {
            const params = Object.assign({
                teamId: data.taskId
            }, this.props.searchParams);
            const respData = await nsKdevTeamRestrictCheckGet.remote(params);
            runInAction(() => {
                if (!respData) {
                    return;
                }
                if (respData?.pass) {
                    if (this.props.multiple || this.props.showConfirmBtn) {
                        this.setState(() => ({
                            increRecords: this.state.increRecords.concat(data),
                            taskIds: [...this.props.selectedTaskIds, data.taskId]
                        }));
                        this.props.onMultiSelect?.([data], [...this.props.selectedTaskIds, data.taskId]);
                    } else {
                        this.props.onSelect?.(data, data.taskId);
                        this.handleVisibleChange(false);
                    }
                    this.setState({
                        visibleCreateTeam: false,
                    });
                } else {
                    message.error({
                        content: <span dangerouslySetInnerHTML={{ __html: respData.msg }} />,
                        duration: 3
                    });
                }
            });
        } catch (e) {
        }
    }
    private renderFooterBtns(): React.ReactElement {
        return (
            <div className={css.footer}>
                <Button
                    onClick={this.onCancelConfirm}
                    style={{ marginRight: 10 }}
                    size={'small'}
                >
                    取消
                </Button>
                <Button
                    type="primary"
                    onClick={this.onClickConfirm}
                    size={'small'}
                    disabled={this.state.loading}
                    loading={this.state.onConfirmLoading}
                >
                    确认
                </Button>
            </div>
        );
    }

    private renderTable(): React.ReactElement {
        return (
            <div className={css.tableBox}>
                <TeamTable
                    keyword={this.state.keyword}
                    dataSource={this.state.dataList}
                    loading={this.state.loading}
                    onSelect={this.onSelect}
                    searchScene={this.props.searchScene}
                    drawerVisible={this.state.drawerVisible}
                    onChangeDrawerVisible={this.onChangeDrawerVisible}
                    multiple={this.props.multiple || this.props.showConfirmBtn}
                    onMultiSelect={this.onMultiSelect}
                    selectedTaskIds={this.selectedTaskIds}
                    isSDK={this.props.isSDK}
                    searchParams={this.props.searchParams}
                    onRefreshList={this.onRefresh}
                    checkDisabled={this.props.checkDisabled}
                />
            </div>
        );
    }

    public render(): React.ReactElement {
        return <Popover
            overlayClassName={css.teamPopoverContent}
            destroyTooltipOnHide={true}
            placement={'bottomLeft'}
            title={null}
            trigger={'click'}
            visible={this.state.visible || this.state.drawerVisible}
            onVisibleChange={this.handleVisibleChange}
            getPopupContainer={this.props.getPopupContainer}
            content={
                <div>
                    <KdevSearch
                        autoFocus
                        allowClear={true}
                        value={this.state.keyword}
                        onChange={this.handleChangeInput}
                        inputRef={this.inputRef}
                        width={'100%'}
                    />
                    <div className={css.header}>
                        <div>
                            <Checkbox onChange={this.ChangeChecked} checked={this.state.checked}>与我相关</Checkbox>
                            <Tooltip title={'“与我相关”是筛选执行人、参与人、开发工程师包含“我”的任务'}>
                                <span className={css.icon}><KdevIconFont id={common_system_help02} /></span>
                            </Tooltip>
                        </div>
                    </div>
                    {
                        this.renderTable()
                    }
                    {
                        this.props.showConfirmBtn &&
                        this.renderFooterBtns()
                    }
                </div>
            }
        >
            {this.props.children}
        </Popover>;
    }
}
