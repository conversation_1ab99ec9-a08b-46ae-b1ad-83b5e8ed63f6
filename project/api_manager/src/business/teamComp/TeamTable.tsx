import React from 'react';
import css from './index.less';
import { Spin, Table, Tooltip, message } from 'antd';
import { observer } from 'mobx-react';
import { observable, action, runInAction } from 'mobx';
import Bind from 'lodash-decorators/bind';
import { KdevIconFont } from '@/business/commonComponents';
import {common_system_arrowdownmian, common_system_arrowrightmian} from '@kid/enterprise-icon/icon/output/icons';
import { TeamAssignee, TeamName, TeamOpts, TeamType } from './TeamItem';
import { ESearchScene, SearchSceneMap } from './utils';
import { AsyncTaskDetailModal } from '../team';
import { nsKdevTeamSearchListGet, nsKdevTeamSubTaskListGet } from '@/remote';
interface IProp_Table {
    columns?: any; // @todo
    // 多选相关
    multiple?: boolean; // 是否多选
    selectedTaskIds: string[]; // 多选的keys
    onMultiSelect?: (taskIds) => void; // 多选时选中onchange

    dataSource: any;
    loading?: boolean;
    keyword?: string;
    onSelect?: (taskId) => void;
    getPopupContainer?: (triggerNode: HTMLElement) => HTMLElement;
    searchScene?: ESearchScene;

    onChangeDrawerVisible: (visible: boolean) => void;
    drawerVisible: boolean;

    isSDK?: boolean;
    searchParams?: nsKdevTeamSearchListGet.IParam_SceneParam | nsKdevTeamSubTaskListGet.IParams; // 默认搜索的参数

    onRefreshList?: () => void;

    // TeamTable组件checkable属性
    checkDisabled?: (record) => boolean;

}

@observer
export class TeamTable extends React.Component<IProp_Table, any> {
    private readonly tableRef;
    @observable public taskId: string = '';

    @observable public flattenList: any[] = [];

    public columns = [
        {
            title: <span>{!this.props.multiple && <span className={ css.expandMargin }/>}标题</span>,
            dataIndex: 'taskId',
            width: this.props.multiple ? 290 : 330,
            render: (text, record, index) => {
                return <span className={css.title} style={{width: this.props.multiple ? 235 : 260}}>
                    <TeamType item={record}/>
                    <TeamName
                        item={record}
                        keyword={this.props.keyword}
                        getPopupContainer={ () => this.tableRef.current}
                    />
                </span>;
            }
        },
        {
            title: '执行人',
            align: 'left',
            dataIndex: 'assignee',
            width: 100,
            render: (text, record, index) => {
                return <TeamAssignee item={record}/>;
            }
        },
        // {
        //     title: <span className={ css.optsHeader }>操作</span>,
        //     dataIndex: 'operation',
        //     width: 90,
        //     render: (text, record, index) => {
        //         return <TeamOpts
        //                     item={record}
        //                     onInfo={this.onOpenDrawer}
        //                     optsArr={SearchSceneMap[this.props.searchScene || ESearchScene.normal].optsArr}
        //                 />;
        //     }
        // }
    ];

    constructor(props) {
        super(props);
        this.tableRef = React.createRef();
    }

    /**
     * @description 展开/收起 icon
     */
    @Bind
    public renderExpandIcon(expanded, onExpand, record): React.ReactNode {
        const onExpandRow = (e) => {
            e?.stopPropagation();
            onExpand(record, e);
        };

        if (record.subTasks?.length > 0) {
            return (
                <span onClick={onExpandRow} className={ css.expand }>
                {
                    expanded ?
                    <KdevIconFont id={ common_system_arrowdownmian } className={css.icon}/>
                    : <KdevIconFont id={ common_system_arrowrightmian } className={css.icon}/>
                }
                </span>
            );
        }
        if (record.taskType !== 'child_task') {
            return <span className={ css.expandMargin } id={'expandMargin'}/>;
        }
    }

    @action.bound
    public onOpenDrawer(e, item) {
        e?.stopPropagation();
        this.props.onChangeDrawerVisible(true);
        this.taskId = item.taskId;
    }

    @action.bound
    public onCloseDrawer(e) {
        e?.stopPropagation();
        this.props.onChangeDrawerVisible(false);
        this.taskId = '';
    }

    @action.bound
    public onSelectAll(selected, selectedRows, changeRows) {
        if (selected) {
            const keys = selectedRows.filter(ele => Boolean(ele)).map(item => item.taskId);
            this.props.onMultiSelect?.(Array.from(new Set([...this.props.selectedTaskIds, ...keys])));
        } else {
            const allKeys = changeRows.map(ele => ele.taskId);
            const restKeys = this.props.selectedTaskIds.reduce((pre: string[], cur: string) => {
                if (allKeys.indexOf(cur) < 0) {
                    pre.push(cur);
                }
                return pre;
            }, []);
            this.props.onMultiSelect?.(restKeys);
        }
    }

    @Bind
    public handleClickRow(record) {
        if (record.disabled || this.props.checkDisabled?.(record)) {
            return;
        }
        if (this.props.multiple) { // 处理多选情况
            if (this.props.selectedTaskIds && this.props.selectedTaskIds.indexOf(record.taskId) > -1) { // 取消选中
                const selectedKeys = this.props.selectedTaskIds.filter(key => key !== record.taskId);
                this.props.onMultiSelect?.(selectedKeys);
            } else { // 选中
                this.props.onMultiSelect?.([...this.props.selectedTaskIds, record.taskId]);
            }
        } else {
            this.props.onSelect?.(record.taskId);
        }
    }

    @action.bound
    public handleAfterCreate(record) {
        if (this.props.multiple) {
            this.props.onMultiSelect?.([...this.props.selectedTaskIds, record.taskId]);
            this.props.onChangeDrawerVisible(false);
            this.taskId = '';
        } else {
            this.props.onSelect?.(record.taskId);
        }
    }

    public render(): React.ReactElement {
        return (
            <div ref={this.tableRef} className={ css.teamTable }>
                {
                    this.props.loading ?
                    <Spin spinning={this.props.loading} className={css.spin}/>
                    : <Table
                        columns={ this.props.columns || this.columns  }
                        className={ css.table }
                        size={'small'}
                        bordered={false}
                        rowKey={ (record) => record.taskId }
                        pagination={ false }
                        dataSource={ this.props.dataSource }
                        loading={ this.props.loading }
                        expandable={{
                            childrenColumnName: 'subTasks',
                            indentSize: 22,
                            defaultExpandAllRows: true,
                            expandIcon: ({expanded, onExpand, record}) => this.renderExpandIcon(expanded, onExpand, record) // -
                        }}
                        onRow={record => {
                            return {
                                onClick: e => this.handleClickRow(record),
                            };
                        }}
                        rowSelection={this.props.multiple ? {
                            columnWidth: 16,
                            renderCell: (checked, record, index, originNode) => {
                                return (
                                    <Tooltip
                                        title={record.tips && <span dangerouslySetInnerHTML={ {__html: record.tips} }/>}
                                    >
                                        <span>{originNode}</span>
                                    </Tooltip>
                                );
                            },
                            getCheckboxProps: (record) => {
                                return {disabled: record.disabled || this.props.checkDisabled?.(record)};
                            },
                            onSelect: this.handleClickRow,
                            onSelectAll: this.onSelectAll,
                            selectedRowKeys: this.props.selectedTaskIds,
                          } : undefined}
                        rowClassName={(record, index) => {
                            if (record.disabled) {
                                return css.disabledTableRow;
                            }
                            return css.tableRow;
                        }}
                    />
                }
                <AsyncTaskDetailModal
                    taskId={this.taskId}
                    onClose={this.onCloseDrawer}
                    visible={this.props.drawerVisible}
                    forWorkbench={this.props.isSDK}
                />
            </div>
        );
    }
}
