.teamAssignee {
  div {
    display: flex;
  }

  span {
    color: #fff;
  }
}

.teamIcon {
  margin-right: 4px;
  width: 16px;
}

.teamType {
  color: #898A8C;
  max-width: 100px;
  min-width: 32px;
  padding-right: 4px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.teamName {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  flex: 1;
}

.teamLabel {
  font-size: 14px;
  color: #575859;
  margin-right: 16px;

  .required:before {
    margin-left: 4px;
    display: inline-block;
    //margin-right: 4px;
    color: #ff4d4f;
    font-size: 14px;
    font-family: SimSun, sans-serif;
    line-height: 1;
    content: '*';
  }
}

.clickable {
  cursor: pointer;
}

.clickable:hover {
  color: #326BFB;
}

.teamOpts {
  display: flex;

  .btn {
    margin: 0 6px;
    padding: 0;
    color: #898A8C;
  }

  .btn:hover {
    color: #326BFB;
  }
}

.disabled {
  color: #BBBDBF !important;
  cursor: not-allowed;
}

.disabledUser {
  span {
    color: #BBBDBF !important;
  }
}

.disabledIcon {
  opacity: 0.3;
}

.relateBtn {
  height: 22px;
  line-height: 20px;
  padding: 0 !important;

  .relateIcon {
    margin-right: 4px;
  }
}

.teamPopoverContent {
  .header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin: 12px 0;
    width: 516px;

    // .inputBox {
    //   width: 384px;
    // }
  }

  .icon {
    color: #898989;
    width: 16px;
    margin-right: 8px;
  }

  .expandMargin {
    margin-right: 22px;
  }

  .expand {
    cursor: pointer;
  }

  .tableBox {
    max-height: 320px;
    overflow: auto;

  }

  .footer {
    padding-top: 12px;
    text-align: right;
  }
}

.teamLine {
  width: 100%;
  padding: 2px 6px 2px 12px;
  border-radius: 4px;
  border: 1px solid #F0F2F5;
  background-color: #FFFFFF;
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin: 8px 0;

  .left {
    display: flex;
    min-width: 0;
  }

  .right {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    flex: 1;

    .assigneeBox {
      margin: 0 6px;
      width: max-content;
    }
  }
}

// 多个teamLine行
.teamLineRow {
  display: flex;
  align-items: center;

  .itemLine {
    margin-right: 8px;
    width: 320px;
  }

  &:last-child {
    margin-right: 0;
  }

  // 【更多】tag
  .more {
    border-radius: 4px;
    border: 1px solid #F0F2F5;
    background-color: #FFFFFF;
    margin: 8px 0;
    padding: 5px 12px;
  }
}

// 【更多】tag的popover
.morePop {
  width: 352px;
}

.selectTeamContainer {
  .selectTeamBtn {
    padding: 0;
  }
}

// 加号按钮的popover
.selectTeamPopover {
  width: 532px;

  .popoverTitle {
    color: #BBBDBF;
    font-size: 12px;
    font-weight: 400;
  }
}

.teamTable {
  .spin {
    width: 100%;
    margin: 2px 0;
  }

  .table {
    .title {
      width: 260px;
      display: inline-flex;
    }

    .optsHeader {
      margin-left: 6px;
    }

    .tableRow {
      cursor: pointer;
    }

    .tableRow:hover {
      background-color: #F0F7FF;
    }

    .disabledTableRow {
      cursor: not-allowed;
    }

    :global {
      .ant-table .ant-table-container {
        .ant-table-thead .ant-table-cell {
          padding: 5px 12px;
        }

        .ant-table-tbody .ant-table-cell {
          padding: 2px 12px;
          border-bottom: 0;
        }

        .ant-table-selection-column {
          padding: 2px 0px 2px 12px !important;
          min-width: 16px;
          width: 16px;

          span[id='expandMargin'] {
            display: none;
          }
        }
      }

      .kdev-antd-table .kdev-antd-table-container {
        .kdev-antd-table-thead .kdev-antd-table-cell {
          padding: 5px 12px;
        }

        .kdev-antd-table-tbody .kdev-antd-table-cell {
          padding: 2px 12px;
          border-bottom: 0;
        }

        .kdev-antd-table-selection-column {
          padding: 2px 0px 2px 12px !important;
          min-width: 16px;
          width: 16px;

          span[id='expandMargin'] {
            display: none;
          }
        }
      }
    }
  }
}

.relateTeamPopconfirm {
  z-index: 1030;

  .popTitle {
    font-size: 14px;
    font-weight: 500;
    line-height: 22px;
    margin-bottom: 16px;
  }

  .content {
    max-height: 200px;
    overflow: auto;
  }
}