import React from 'react';
import css from './index.less';
import { Button } from 'antd';
import { nsKdevTeamSearchListGet, nsKdevTeamSubTaskListGet } from '@/remote';
import Bind from 'lodash-decorators/bind';
import { KdevIconFont } from '@/business/commonComponents';
import { common_system_guanlianrenwu_line } from '@kid/enterprise-icon/icon/output/icons';
import { TeamPopover } from './TeamPopover';
import { ESearchScene } from './utils';
import { observer } from 'mobx-react';

interface IProps {
    className?: string;
    disabled?: boolean;
    style?: React.CSSProperties;
    children?: any;
    onSelect?: (record, taskId: string) => void;
    getPopupContainer?: (triggerNode: HTMLElement) => HTMLElement;
    searchParams: nsKdevTeamSearchListGet.IParam_SceneParam | nsKdevTeamSubTaskListGet.IParams; // 默认搜索的参数
    searchScene?: ESearchScene;
    // 多选相关
    multiple?: boolean; // 是否多选
    selectedTaskIds?: string[]; // 多选的keys
    onMultiSelect?: (increRecords, taskIds: string[]) => void; // increRecords 指增量的选中项, ids指所有选中的项
    isSDK?: boolean;
    showCreateTeamBtn?: boolean; // 是否展示创建Team按钮, 默认展示
    btnText?: string; // 按钮文案
    btnChildren?: any; // 按钮子元素

    // 是否在弹窗下方展示二次确认/取消按钮
    showConfirmBtn?: boolean; // 是否展示确认按钮, 默认不展示
    onConfirm?: (e, increRecords, deletedTaskIds: string[]) => void; // 确认按钮的回调
    onCancel?: (e) => void; // 取消按钮的回调

    rowSelection?: {  // TeamTable组件checkable属性
        disabled?: (record) => boolean;
    };
}

@observer
export class TeamRelateButton extends React.Component<IProps, any> {

    @Bind
    public async getTeamList(props): Promise<nsKdevTeamSearchListGet.IReturn_TeamItem[]> {
        try {
            let params = this.props.searchParams;
            if (props) {
                params = Object.assign(params, props);
            }
            let respData;
            if (params['teamId']) { // 有父任务id，则调用仅搜索子任务的接口
                respData = await nsKdevTeamSubTaskListGet.remote(params as nsKdevTeamSubTaskListGet.IParams);
            } else {
                respData = await nsKdevTeamSearchListGet.remote(params as nsKdevTeamSearchListGet.IParam_SceneParam);
            }
            return Promise.resolve(respData.list);
        } catch (e) {
            return Promise.resolve([]);
        }
    }

    // 关联team button样式
    @Bind
    private renderTeamRelateButton() {
        return (
            <Button type={'link'} className={css.relateBtn} disabled={this.props.disabled}>
                {
                    this.props.btnChildren ||
                    <>
                        <KdevIconFont id={common_system_guanlianrenwu_line} className={css.relateIcon} />
                        {this.props.btnText || '关联/新建Team任务'}
                    </>
                }
            </Button>
        );
    }

    public render(): React.ReactElement {
        // 禁用状态下不加载 TeamPopover
        if (this.props.disabled) {
            return this.renderTeamRelateButton();
        }
        return (
            <TeamPopover
                onSearch={this.getTeamList}
                onSelect={this.props.onSelect}
                className={this.props.className}
                style={this.props.style}
                getPopupContainer={this.props.getPopupContainer}
                searchScene={this.props.searchScene}
                multiple={this.props.multiple}
                onMultiSelect={this.props.onMultiSelect}
                selectedTaskIds={this.props.selectedTaskIds || []}
                isSDK={this.props.isSDK}
                showCreateTeamBtn={this.props.showCreateTeamBtn}
                searchParams={this.props.searchParams}
                showConfirmBtn={this.props.showConfirmBtn}
                onConfirm={this.props.onConfirm}
                onCancel={this.props.onCancel}
                checkDisabled={this.props.rowSelection?.disabled}
            >
                {this.renderTeamRelateButton()}
            </TeamPopover>
        );
    }
}
