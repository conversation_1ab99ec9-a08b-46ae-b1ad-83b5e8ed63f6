import { useState, useEffect, RefObject } from 'react';

interface IHoverOptions {
    onEnter?(): void;
    onLeave?(): void;
    onChange?(isHover: boolean): void;
}

// custom hooks: useHover
function useHover(ref: RefObject<HTMLDivElement>, options?: IHoverOptions) {
    const [isHover, setIsHover] = useState(false);
    useEffect(() => {
        function handleMouseenter() {
            setIsHover(true);
            options?.onEnter && options.onEnter();
            options?.onChange && options.onChange(true);
        }
        function handleMouseleave() {
            setIsHover(false);
            options?.onLeave && options.onLeave();
            options?.onChange && options.onChange(false);
        }
        ref?.current?.addEventListener('mouseenter', handleMouseenter);
        ref?.current?.addEventListener('mouseleave', handleMouseleave);
        return () => {
            ref?.current?.removeEventListener('mouseenter', handleMouseenter);
            ref?.current?.removeEventListener('mouseleave', handleMouseleave);
        };
    }, []);
    return isHover;
}

export { useHover };
