import { useState, useEffect, RefObject } from 'react';

interface IObserverDomHeightOptions {
    onChange?(height: number): void;
}

// custom hooks: useObserverDomHeight
function useObserverDomHeight(ref: RefObject<HTMLDivElement>, options?: IObserverDomHeightOptions): number | undefined {
    const [domHeight, setDomHeight] = useState<number>();

    const onChangeDomHeight = (entries: ResizeObserverEntry[]) => {
        if (entries[0] && entries[0].contentRect) {
            requestAnimationFrame(() => {
                setDomHeight(entries[0].contentRect.height);
            })
            options?.onChange && options.onChange(entries[0].contentRect.height);
        }
    };

    useEffect(() => {
        if (ref?.current) {
            const resizeObserver = new ResizeObserver(onChangeDomHeight);
            resizeObserver.observe(ref?.current);
            return () => {
                resizeObserver.disconnect();
            };
        }
    }, []);

    return domHeight;
}

export { useObserverDomHeight };
