import React, { useState, useEffect } from 'react'
import { Input, Checkbox, Tooltip, Button, Popover, Table, Popconfirm } from 'antd'
import { SearchOutlined, PlusOutlined, CopyOutlined } from '@ant-design/icons'
import { RepoAvatar } from '@/business/repo/RepoAvatar'
import css from './SelectRepoList.less'
import { common_system_add, common_system_brokenlink, kdev_repositories } from '@kid/enterprise-icon/icon/output/icons'
import { AvatarList, KdevIconFont } from '../commonComponents'
import { nsKdevRepoRecentVisitList, nsKdevRepoListAllWithLimit } from '@/remote'
import { debounce } from 'lodash'
import { nsMockManageApiManageMainApiProgram } from '@/remote/mock/manage/apiManage/main/api/program'

// 仓库数据接口
// interface RepoItem {
//     projectId: number;
//     projectName: string;
//     sshUrlToRepo: string;
// }


export default function SelectRepoList(Props: {
    repoList: nsMockManageApiManageMainApiProgram.BindProjectType[],
    onChange: (repoList: nsMockManageApiManageMainApiProgram.BindProjectType[]) => void,
    showSelectedRepoType?: 'list' | 'table'
}) {
    const [repoList, setRepoList] = useState<nsKdevRepoListAllWithLimit.IList_item[]>([]);
    const [searchKey, setSearchKey] = useState<string>('');
    const [showSelectedRepoType, setShowSelectedRepoType] = useState<'list' | 'table'>('list');

    // 创建防抖的搜索函数
    const debouncedSearch = debounce((key: string) => {
        nsKdevRepoListAllWithLimit.remote({ groupId: undefined, search: key }).then((res) => {
            setRepoList(res.list)
        })
    }, 300);

    useEffect(() => {
        setShowSelectedRepoType(Props.showSelectedRepoType || 'list');
    }, [Props.showSelectedRepoType]);


    useEffect(() => {
        debouncedSearch(searchKey);
        // 清理防抖函数
        return () => {
            debouncedSearch.cancel();
        };
    }, [searchKey]);

    // 处理复选框变化
    const handleCheckboxChange = (projectId: string | number, checked: boolean) => {
        if (checked) {
            const target = repoList.find(item => item.projectId === projectId);
            if (target) {
                const userInfo = JSON.parse(localStorage.getItem('KDEV_USER_INFO') || '{}');
                Props.onChange([...Props.repoList, {
                    projectId: target.projectId,
                    projectName: target.projectName,
                    projectSsh: target.sshUrlToRepo,
                    bindingOperator: {
                        id: userInfo.userId,
                        name: userInfo.chineseName,
                        username: userInfo.userName,
                        email: userInfo.email,
                        photo: userInfo.photo
                    }
                }]);
            }
        } else {
            Props.onChange(Props.repoList.filter(item => item.projectId !== projectId))
        }
    }

    // 高亮匹配的文本
    const highlightText = (text: string, keyword: string) => {
        if (!keyword.trim() || !text) {
            return <span>{text || ''}</span>;
        }

        const parts = text.split(new RegExp(`(${keyword})`, 'gi'));
        return (
            <span>
                {parts.map((part, index) =>
                    part.toLowerCase() === keyword.toLowerCase()
                        ? <span key={index} style={{ color: '#326BFBff' }}>{part}</span>
                        : <span key={index}>{part}</span>
                )}
            </span>
        );
    };

    const addRepoPopoverContent = () => {
        return (
            <div className={css.addRepoPopoverContent}>
                {/* 搜索框 */}
                <div className={css.searchBox}>
                    <Input
                        className={css.searchInput}
                        placeholder="请输入关键词进行搜索"
                        prefix={<SearchOutlined style={{ color: '#D5D6D9' }} />}
                        value={searchKey}
                        onChange={(e) => setSearchKey(e.target.value)}
                        allowClear
                    />
                </div>

                {/* 仓库列表 */}
                <div className={css.repoList}>
                    {repoList.map(repo => (
                        <div key={repo.projectId} className={css.repoItem}
                            onClick={() => {
                                handleCheckboxChange(
                                    repo.projectId,
                                    !Props.repoList.some(item => item.projectId === repo.projectId)
                                )
                            }}>
                            <Checkbox
                                className={css.checkbox}
                                checked={Props.repoList.some(item => item.projectId === repo.projectId)}
                            />
                            <RepoAvatar
                                name={repo.projectName}
                                className={css.avatar}
                                size={24}
                            />
                            <div className={css.repoItemContent}>
                                <div className={css.projectName}>
                                    {highlightText(repo.projectName, searchKey)}
                                </div>
                                <Tooltip title={repo.sshUrlToRepo}>
                                    <div className={css.projectPath}>
                                        {highlightText(repo.sshUrlToRepo || '', searchKey)}
                                    </div>
                                </Tooltip>
                            </div>
                        </div>
                    ))}
                </div>
            </div >
        )
    }

    return (
        <div className={css.selectRepoListContainer}>
            {/* 已选仓库 */}
            {Props.repoList.length > 0 && showSelectedRepoType === 'list' && <div className={css.repoShowList}>
                {Props.repoList.map((item) => (
                    <div key={item.projectId} className={css.repoShowItem}>
                        <KdevIconFont id={kdev_repositories} style={{ color: '#898A8C', marginRight: 8 }} />
                        <span className={css.repoShowItemName}>{item.projectName}</span>
                        <Popconfirm
                            title="确定要解除仓库关联吗？"
                            okText="确定"
                            cancelText="取消"
                            onConfirm={() => handleCheckboxChange(item.projectId, false)}
                        >
                            <span style={{ cursor: 'pointer' }}>
                                <KdevIconFont id={common_system_brokenlink} style={{ color: '#898A8C' }} />
                            </span>
                        </Popconfirm>
                    </div>
                ))}
            </div>}
            {/* 已选仓库表格 */}
            {Props.repoList.length > 0 && showSelectedRepoType === 'table' && <div className={css.repoShowList}>
                <Table
                    dataSource={Props.repoList}
                    pagination={false}
                    rowKey="projectId"
                    columns={[
                        {
                            title: 'git 仓库',
                            dataIndex: 'projectName',
                            key: 'projectName',
                            render: (text, record) => (
                                <div style={{ display: 'flex', alignItems: 'center' }}>
                                    <span>{text}</span>
                                </div>
                            )
                        },
                        {
                            title: '关联人',
                            dataIndex: 'bindingOperator',
                            key: 'bindingOperator',
                            width: 120,
                            render: (text, record) => (
                                <div style={{ display: 'flex', alignItems: 'center' }}>
                                    <AvatarList reviewers={[{
                                        name: text.name,
                                        username: text.username,
                                        email: text.email,
                                        avatarUrl: text.photo
                                    }]} size={24} />
                                </div>
                            )
                        },
                        {
                            title: '操作',
                            key: 'action',
                            width: 120,
                            render: (_, record) => (
                                <Popconfirm
                                    title="确定要解除仓库关联吗？"
                                    okText="确定"
                                    cancelText="取消"
                                    onConfirm={() => handleCheckboxChange(record.projectId, false)}
                                >
                                    <span className={css.unlinkText}>
                                        解除关联
                                    </span>
                                </Popconfirm>
                            )
                        }
                    ]}
                />
            </div>}
            {/* 添加仓库按钮 */}
            <Popover
                content={addRepoPopoverContent}
                title={null}
                trigger="click"
                placement="bottomLeft"
                overlayClassName={css.menuOverlay}
                destroyTooltipOnHide
                onOpenChange={(open) => {
                    if (!open) {
                        setSearchKey('')
                    }
                }}
            >
                <Button type="link" size="small" className={css.addRepoButton}>
                    <KdevIconFont id={common_system_add} style={{ marginRight: 4 }} />
                    添加关联仓库
                </Button>
            </Popover>
        </div>
    )
}
