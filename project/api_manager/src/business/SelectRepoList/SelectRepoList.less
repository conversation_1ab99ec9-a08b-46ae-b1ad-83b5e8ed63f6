.selectRepoListContainer {
  width: 100%;
  // border-radius: 8px;
  // box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  // background-color: #fff;
  // padding: 16px;

  .addRepoButtonWarp {}

  .addRepoButton {
    padding: 0;
    display: flex;
    align-items: center;
    // justify-content: center;
    color: #326BFBff;
    cursor: pointer;
    // margin-bottom: 16px;
    font-size: 14px;
  }
}

.addRepoPopoverContent {
  .searchBox {
    margin-bottom: 8px;

    .searchInput {
      width: 100%;
      border-radius: 4px;
    }
  }

  .repoList {
    max-height: 400px;
    overflow-y: auto;
    gap: 8px;

    .repoItem {
      display: flex;
      align-items: center;
      min-height: 48px;
      padding: 8px;
      border-radius: 4px;
      cursor: pointer;
      width: 100%;

      &:hover {
        background-color: #F5F7FA;
      }

      .checkbox {
        margin-right: 8px;
        margin-top: 2px;
        flex-shrink: 0;
      }

      .avatar {
        color: var(---text_brand, #326BFB);
        text-align: center;
        font-family: "PingFang SC";
        font-size: 12px !important;
        font-style: normal;
        font-weight: 500;
        line-height: 22px
          /* 183.333% */
        ;
      }

      .repoItemContent {
        display: flex;
        flex-direction: column;
        width: calc(100% - 24px);
        overflow: hidden;

        .projectName {
          -webkit-box-orient: vertical;
          -webkit-line-clamp: 1;
          color: var(---text_primary, #252626);
          text-overflow: ellipsis;
          font-family: "PingFang SC";
          font-size: 14px;
          font-style: normal;
          font-weight: 400;
          line-height: 22px
            /* 157.143% */
          ;
        }

        .projectPath {
          color: var(---text_secondary, #575859);
          /* font_caption */
          font-family: "PingFang SC";
          font-size: 12px;
          font-style: normal;
          font-weight: 400;
          line-height: 18px
            /* 150% */
          ;
          width: 100%;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
      }

      .copyIcon {
        margin-left: 8px;
        color: #898A8C;
        cursor: pointer;
        visibility: hidden;
      }

      &:hover .copyIcon {
        visibility: visible;
      }
    }
  }

}

.repoShowList {
  display: flex;
  gap: 8px;
  flex-direction: column;
  margin-bottom: 8px;
  border-radius: var(--radius-radius_normal, 4px);
  border: 1px solid var(---stroke_table, #EBEDF0);
  border-bottom: none;
  background: var(--bg-bg-container, #FFF);

  .repoShowItem {
    display: flex;
    align-items: center;
    padding: 5px 12px;
    flex: 1 0 0;
    border-radius: 4px;
    border: 1px solid var(---stroke_table, #EBEDF0);
    background: #FFF;

    .repoShowItemName {
      flex: 1 0 0;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }
  }
}

.menuOverlay {
  // left: 12px !important;
  // padding: 8px 21px;

  :global {
    .ant-popover-arrow {
      display: none;
    }

    .ant-popover-inner-content {
      padding: 8px 12px;
      width: 432px;
    }
  }
}

.unlinkText {
  color: #326BFB;
  cursor: pointer;
}