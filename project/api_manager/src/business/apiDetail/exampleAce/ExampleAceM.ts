import { AViewModel } from '@libs/mvvm';
import { observable, action } from 'mobx';

export class ExampleAceM extends AViewModel {
    @observable public example: string = '';
    @observable public paneSize: string = '267px';
    // @observable public paneBottomSize: string = '24px';

    // @action.bound
    // public setData(): void {
    //     this.initData();
    //     // this.setFields();
    // }

    @action.bound
    public initData(): void {
        this.example = '';
    }

    @action.bound
    public onChangeExample(example: string): void {
        this.example = example;
    }

    @action.bound
    public onChangeExampleSize(size): void {
        this.paneSize = size[0];
    }
}
