import { AViewModel } from 'libs';
import { observable, action, runInAction, } from 'mobx';
import {
    nsMockManageKoasApiManageAutoDocQueryStandardInfoGet
} from '@/remote';
import { Bind } from 'lodash-decorators';

export class StandardInfoM extends AViewModel {
    @observable public visible: boolean = false;
    @observable public docId: number = 0;
    private isAuto: number = 2;
    @observable public standardInfo: string[] = [];

    @action.bound
    public init(docId: number, isAuto: number = 2, standardInfo?: string[]) {
        this.standardInfo = standardInfo ? standardInfo : this.standardInfo;
        if (this.docId !== docId || this.isAuto !== isAuto) {
            this.docId = docId;
            this.isAuto = isAuto;
            if (!standardInfo) {
                this.queryStandardInfo();
            }
        }
        this.visible = true;
    }

    @action.bound
    public onCloseStandardInfo() {
        this.visible = false;
    }

    @Bind
    private async queryStandardInfo(): Promise<void> {
        try {
            const params = {
                docId: this.docId,
                isAuto: this.isAuto
            };
            const result = await nsMockManageKoasApiManageAutoDocQueryStandardInfoGet.remote(params);
            runInAction(() => {
                // this.isStandard = result?.isStandard;
                this.standardInfo = result?.standardInfo;
            });
        } catch {
        }
    }
}
