import { AView } from 'libs';
import React from 'react';
import { StandardInfoM } from './StandardInfoM';
import { observer } from 'mobx-react';
import { Button, Modal } from 'antd';
import { Bind } from 'lodash-decorators';

@observer
export class StandardInfo extends AView<StandardInfoM> {

    @Bind
    private renderFooter(): React.ReactNode {
        return (
            <Button
                type={'primary'}
                onClick={this.model.onCloseStandardInfo}
            >
                关闭
            </Button>
        );
    }

    public render(): React.ReactNode {
        const model = this.model;
        return (
            <Modal
                title={'详情'}
                visible={model.visible}
                onCancel={model.onCloseStandardInfo}
                footer={this.renderFooter()}
            >
                <p>不规范内容：</p>
                <ul>{model.standardInfo.map(item => <li key={item}>{item}</li>)}</ul>
            </Modal>
        );
    }
}
