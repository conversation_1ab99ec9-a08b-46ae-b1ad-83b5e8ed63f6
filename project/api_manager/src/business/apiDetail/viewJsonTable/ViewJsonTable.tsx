import { AView } from 'libs';
import React from 'react';
import { inject, observer } from 'mobx-react';
import { ViewJsonTableM } from './ViewJsonTableM';
import { Table } from 'antd';
import Bind from 'lodash-decorators/bind';
import { KdevIconFont } from '@/business/commonComponents/KdevIconFont';
import css from './ViewJsonTable.less';
import { CustomColumn } from '@/business/httpApiComponents/customColumn/CustomColumn';
import { ResizableTable, KEYS_TABLE } from '@/business/commonComponents/ResizableTable';
import { LOCAL_STORAGE_KEYS } from '@/index.config';
import { JSONObject } from '@/index.config/tools';

interface IProps {
    unifiedId?: KEYS_TABLE;
    // operate?: React.ReactElement | React.ReactNode | boolean;
    className?: string;
    valueTitle?: string;
    // isResponse?: boolean;
    checkColumnKey?: string;
    tableTitleKey?: string[];
    editJsonStore?: any;
    enableResize?: boolean;
}

interface IOptions {
    label: string;
    value: string;
}

// interface IState {
//     caseOperateTypeList: IOptions[];
// }

@inject('editJsonStore')
@observer
export class ViewJsonTable extends AView<ViewJsonTableM, IProps> {
    public caseOperateTypeList = this.props?.editJsonStore && this.props?.editJsonStore.caseOperateTypeList || [];

    @Bind
    public componentDidMount(): void {
        const { checkColumnKey, tableTitleKey } = this.props;
        if (tableTitleKey) {
            this.model.onChangeCheckColumn(tableTitleKey);
        }
        if (checkColumnKey) {
            const localStorageKeys: object = JSONObject(localStorage.getItem(LOCAL_STORAGE_KEYS.API_MGR_TABLE_COLUMN) || '');
            // const checkColumnStr = localStorage.getItem(checkColumnKey);
            const checkColumnStr: string = localStorageKeys[checkColumnKey];
            localStorage.removeItem(checkColumnKey);
            if (checkColumnStr) {
                this.model.onChangeCheckColumn(checkColumnStr.split(','), checkColumnKey);
            }
        }
        this.model.onChangeTableLoading(true);
    }

    @Bind
    private renderName(record): React.ReactNode {
        return (
            <div className={css.name}>
                {record.name}
            </div>
        );
    }

    @Bind
    private renderValue(record): React.ReactNode {
        if (this.model.type === 'form' && record.type === 'file' && record.value) {
            return (
                <a href={`${record.value.split('|')[1]}`}>{record.value.split('|')[0]}</a>
            );
        }
        if (record.type === 'boolean') {
            return `${record.value}`;
        }
        // 如果value是object要转为string进行展示
        if (record.type === 'object' && typeof record.value === 'object') {
            let newValue: string = '';
            try {
                newValue = JSON.stringify(record.value);
            } catch { }
            return newValue;
        }
        return record.value;
    }

    @Bind
    private renderRealValue(record): React.ReactNode {
        const sameType: string = typeof record.same;
        return (
            <span className={css.realValue}>
                {record.realValue}
                {
                    sameType === 'boolean' &&
                    <KdevIconFont
                        className={css.paramsResultSame}
                        id={record.same ? '#iconchenggong' : '#iconshibai'}
                        style={{ color: record.same ? '#31bf30' : '#ff4d4f' }}
                    />
                }
            </span>
        );
    }

    @Bind
    private renderOperationsType(record): React.ReactNode {
        let operationTypeDesc: string = '';
        this.caseOperateTypeList.forEach(item => {
            if (item.value === record.operationType) {
                operationTypeDesc = item.label;
            }
        });
        return (
            <span>{operationTypeDesc || record.operationType}</span>
        );
    }

    @Bind
    public columns(): any[] {
        const props = this.props;
        let column: any[] = [
            {
                title: '名称',
                // dataIndex: 'name',
                key: 'name',
                width: 400,
                disabled: true,
                render: this.renderName
            },
            {
                title: '类型',
                dataIndex: 'type',
                key: 'type',
                width: 100,
                disabled: true,
            },
            {
                title: '是否必填',
                dataIndex: 'required',
                key: 'required',
                width: 100,
                disabled: true,
                render: text => text ? '是' : '否'
            },
            {
                title: props.valueTitle || '参数值',
                // dataIndex: 'value',
                key: 'value',
                width: 100,
                disabled: true,
                render: this.renderValue
            },
            {
                title: '实际结果',
                key: 'realValue',
                width: 100,
                disabled: true,
                render: this.renderRealValue
            },
            {
                title: '备注',
                dataIndex: 'description',
                width: 100,
                key: 'description',
            },
            {
                title: '原因',
                dataIndex: 'desc',
                width: 100,
                key: 'desc',
            },
            {
                title: '参数取值范围',
                dataIndex: 'valueScope',
                width: 100,
                key: 'valueScope',
            },
            {
                title: '参数取值最大值',
                dataIndex: 'valueMaxLength',
                width: 100,
                key: 'valueMaxLength',
            },
            {
                title: '参数取值最小值',
                dataIndex: 'valueMinLength',
                width: 100,
                key: 'valueMinLength',
            },
            {
                title: '用例生成操作类型',
                // dataIndex: 'operationType',
                key: 'operationsType',
                width: 100,
                render: this.renderOperationsType
            },
        ];
        if (props.tableTitleKey) {
            column = column.filter(item => props.tableTitleKey?.includes(item.key));
        }
        if (props.checkColumnKey) {
            column = this.formatColumn(column);
        }
        return column;
    }

    @Bind
    private renderOpreateTitle(title: string, options: IOptions[]): React.ReactNode {
        return (
            <div className={css.operateTitle}>
                <span>{title}</span>
                <CustomColumn
                    value={this.model.checkColumn}
                    onChange={(checkColumn) => this.model.onChangeCheckColumn(checkColumn, this.props.checkColumnKey)}
                    options={options}
                />
            </div>
        );
    }

    @Bind
    private formatColumn(column: any[]): any[] {
        let newColumn: any[] = column;
        const options: IOptions[] = newColumn.map(item => {
            return {
                label: item.title,
                value: item.key || item.dataIndex,
                disabled: item.disabled || false
            };
        });
        newColumn = newColumn.filter(item => this.model.checkColumn?.includes(item.key));
        newColumn[newColumn.length - 1].title = this.renderOpreateTitle(newColumn[newColumn.length - 1].title, options);
        return newColumn;
    }

    // 添加行className
    @Bind
    private rowClassName(record): string {
        if (record.children) {
            return '';
        }
        return css.indentBorder;
    }

    public render(): React.ReactNode {
        const model = this.model;
        return (
            <div>
                {
                    model.tableLoading &&
                    <ResizableTable
                        key={this.model.checkColumn.join(',')}
                        columns={this.columns()}
                        dataSource={model.list}
                        bordered
                        pagination={false}
                        rowKey={'key'}
                        defaultExpandAllRows
                        rowClassName={this.rowClassName}
                        expandable={{
                            expandedRowKeys: model.expandedRowKeys,
                            onExpand: model.onExpandRowKeys
                        }}
                        className={`${this.props.className} ${css.viewJsonTable}`}
                        enableResize={this.props.enableResize}
                    />
                }
            </div>
        );
    }
}
