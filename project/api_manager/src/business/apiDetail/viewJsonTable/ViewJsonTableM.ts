import { AViewModel } from 'libs';
import { observable, action } from 'mobx';
import { formatKey1, JSONbigStringify, JSONObject } from '@/index.config/tools';
import { LOCAL_STORAGE_KEYS } from '@/index.config';

interface IList {
    key: string;
    name: string;
    type: string;
    value: string;
    required: boolean;
    description: string;
    children?: IList[];
}

export class ViewJsonTableM extends AViewModel {
    @observable public list: IList[] = [];
    @observable public expandedRowKeys: string[] = [];
    @observable public checkColumn: string[] = ['name', 'type', 'required', 'value', 'realValue'];
    @observable public tableLoading: boolean = false;
    @observable public type: string = '';

    constructor(query: string = '') {
        super();
        this.init(query);
    }

    @action.bound
    private init(type: string) {
        this.type = type;
    }

    // column加载完毕再渲染table
    @action
    public onChangeTableLoading(tableLoading: boolean): void {
        this.tableLoading = tableLoading;
    }

    @action.bound
    public onChangeCheckColumn(checkColumn, checkColumnKey: string = ''): void {
        this.checkColumn = checkColumn;
        if (checkColumnKey) {
            const localStorageKeys: object = JSONObject(localStorage.getItem(LOCAL_STORAGE_KEYS.API_MGR_TABLE_COLUMN) || '');
            localStorageKeys[checkColumnKey] = this.checkColumn.join(',');
            // localStorage.setItem(checkColumnKey, this.checkColumn.join(','));
            localStorage.setItem(LOCAL_STORAGE_KEYS.API_MGR_TABLE_COLUMN, JSONbigStringify(localStorageKeys));
        }
    }

    @action.bound
    public setListAndExpanedKeys(list?: IList[], expandedRowKeys?: string[]): void {
        this.list = list || this.list;
        this.expandedRowKeys = expandedRowKeys || this.expandedRowKeys;
    }

    // 初始化数据
    @action.bound
    public initData() {
        this.list = [];
        this.expandedRowKeys = [];
    }

    // 控制展开行
    @action.bound
    public onExpandRowKeys(expanded: boolean, record): void {
        if (expanded) {
            if (!this.expandedRowKeys.includes(record.key)) {
                this.expandedRowKeys.push(record.key);
                this.expandedRowKeys = [...this.expandedRowKeys];
            }
        } else {
            this.expandedRowKeys = this.expandedRowKeys.filter(item => item !== record.key);
        }
    }

    @action.bound
    public onExpandAllKeys(boo?: boolean): void {
        if (boo) {
            this.expandedRowKeys = formatKey1(this.list)[1];
        } else {
            this.expandedRowKeys = [];
        }
    }

}
