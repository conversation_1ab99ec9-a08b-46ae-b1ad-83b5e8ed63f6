import { AView } from 'libs';
import React from 'react';
import { CommentM } from './CommentM';
import { observer } from 'mobx-react';
import { Collapse, Avatar, Input, Button, Comment, Modal } from 'antd';
import css from './Comment.less';
import Bind from 'lodash-decorators/bind';
// import { KDevRichText } from '@/business/richText/RichText';
// import ParticipantList from '@/business/participant/participantList/ParticipantList';
import { bindObserver } from '@libs/mvvm';

const { Panel } = Collapse;
const { TextArea } = Input;

const TextArea_commentValue = bindObserver(TextArea, 'commentValue');

interface IList {
    id: number;
    comment: string;
    commentTime: string;
    replyer: string;
    children: IList[];
}

@observer
export class CommentT extends AView<CommentM> {

    @Bind
    protected onDeleteComment(item): void {
        Modal.confirm({
            title: '确认删除该评论？',
            onOk: () => this.model.deleteComment(item.id)
        });
    }

    public componentDidMount(): void {
        // this.model.participant.initParticipant();
    }

    @Bind
    protected renderAddComment(): React.ReactNode {
        const model = this.model;
        const { userInfo } = model;
        return <div className={css.addComment}>
            {/*<Avatar*/}
            {/*    src={ userInfo.photo }*/}
            {/*    className={ css.avatar }*/}
            {/*>*/}
            {/*    { userInfo.chineseName }*/}
            {/*</Avatar>*/}
            {this.renderAvatar(userInfo.chineseName, userInfo.photo, css.avatar)}
            {
                model.ifInserting ?
                    this.renderRichText() :
                    <Input
                        placeholder={'添加评论'}
                        onClick={model.showCreateTex}
                    />
            }
        </div>;
    }

    @Bind
    protected renderRichText(): React.ReactNode {
        const model = this.model;
        // const { participant } = model;
        return <div className={css.KDevRichText}>
            {/*<KDevRichText*/}
            {/*    menus={ RichTextMenus }*/}
            {/*    value={ model.commentValue || '' }*/}
            {/*    onChange={ model.onRichTextChange }*/}
            {/*/>*/}
            {/*<div*/}
            {/*    style={ {*/}
            {/*        position: 'fixed',*/}
            {/*        top: participant.popPosition.top,*/}
            {/*        left: participant.popPosition.left,*/}
            {/*        zIndex: 9999999*/}
            {/*    } }*/}
            {/*>*/}
            {/*    <ParticipantList*/}
            {/*        model={ participant.participantListModel }*/}
            {/*    />*/}
            {/*</div>*/}
            <TextArea
                value={model.commentValue}
                placeholder={'添加评论'}
                onChange={model.onChangeCommentValue}
                autoFocus={true}
            />
            <div className={css.KDevRichTextOperate}>
                <Button
                    className={css.cancelBtn}
                    onClick={model.onCancelComment}
                >取消</Button>
                <Button
                    type={'primary'}
                    disabled={!model.commentValue}
                    onClick={model.onSaveComment}
                >
                    评论
                </Button>
            </div>
        </div>;
    }

    @Bind
    protected renderAvatar(chineseName: string, photo?: string, classnames?: string): React.ReactNode {
        return (
            <Avatar src={photo || ''} className={css.avatar}>
                {chineseName}
            </Avatar>
        );
    }

    @Bind
    protected commentActions(item, parentId: number): React.ReactNode[] {
        const model = this.model;
        if (item.isEdit) {
            return [
                <span key={1} onClick={() => model.onCancelEditComment(item)}>取消</span>,
                <span key={2} onClick={() => model.updateComment(item)}>确认</span>
            ];
        }
        if (item.isReply) {
            return [
                <span key={1} onClick={() => model.onCancelReplyComment(item)}>取消</span>,
                <span key={2} onClick={() => model.replyComment(item, parentId || item.id)}>确认</span>
            ];
        }
        return [
            <span key={1} onClick={() => model.onReply(item)}>回复</span>,
            <span key={2} onClick={() => model.onEditComment(item)}>编辑</span>,
            <span key={3} onClick={() => this.onDeleteComment(item)}>删除</span>
        ];
    }

    @Bind
    protected renderCommentContent(item): React.ReactNode {
        const model = this.model;
        if (item.isEdit) {
            return (
                <TextArea
                    value={item.comment_copy}
                    placeholder={'添加评论'}
                    onChange={(e) => model.onChangeCommentCopy(item, e)}
                // autoFocus={ true }
                />
            );
        }
        return (
            <>
                <p>{item.comment}</p>
                {
                    item.isReply &&
                    <TextArea
                        key={'7'}
                        className={css.margTop8}
                        value={item.replyComment}
                        onChange={(e) => model.onChangeReplyComment(item, e)}
                        autoFocus={true}
                        placeholder={'回复评论'}
                    />
                }
            </>
        );
    }

    @Bind
    protected showComments(list: IList[], parentId?: number): React.ReactNode {
        const model = this.model;
        return list.map(item => {
            return <Comment
                key={item.id}
                actions={this.commentActions(item, parentId || 0)}
                author={<a>{item.replyer}</a>}
                avatar={this.renderAvatar(item.replyer?.substr(0, 1))}
                content={this.renderCommentContent(item)}
                datetime={<span>{item.commentTime}</span>}
            >
                {item.children && item.children.length && this.showComments(item.children, item.id)}
            </Comment>;
        });
    }

    public render(): React.ReactNode {
        const model = this.model;
        return (
            <Collapse ghost className={css.collapse} defaultActiveKey={'1'}>
                <Panel header={'评论'} key={'1'}>
                    {this.showComments(model.comments || [])}
                    {this.renderAddComment()}
                </Panel>
            </Collapse>
        );
    }
}
