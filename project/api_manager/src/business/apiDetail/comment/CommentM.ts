import { AViewModel } from 'libs';
import { observable, action, runInAction } from 'mobx';
import {
    nsMockManageKoasApiManageCommentCommentDocPost, nsMockManageKoasApiManageCommentGetCommentsGet,
    nsMockManageKoasApiManageCommentUpdateCommentPost, nsMockManageKoasApiManageCommentDeleteCommentGet,
    nsMockManageKoasApiManageCommentReplyCommentPost
} from '@/remote';
import { team } from '@/business/global';
import { Participant } from '@/business/participant/participant';
import { message } from 'antd';

interface IQuery {
    apiId: number;
}

interface IUserInfo {
    userName: string;
    userId?: number;
    photo?: string;
    chineseName: string;
    email: string;
}

export class CommentM extends AViewModel {
    @observable public apiId: number = 0;
    @observable public userInfo: IUserInfo = team.getUserInfo();
    @observable public ifInserting: boolean = false;
    @observable public commentValue: string = '';
    @observable public comments: nsMockManageKoasApiManageCommentGetCommentsGet.IList[] = [];

    public participant = new Participant();

    constructor(query: IQuery) {
        super();
        query && this.init(query);
    }

    @action.bound
    public init(query: IQuery): void {
        this.apiId = query.apiId;
        this.getComments();
    }

    @action.bound
    public showCreateTex(): void {
        this.ifInserting = true;
    }

    // 回复评论
    @action.bound
    public onReply(item): void {
        item.isReply = true;
        this.comments = [...this.comments];
    }

    // 取消回复评论
    @action.bound
    public onCancelReplyComment(item): void {
        item.isReply = false;
        item.replyComment = '';
        this.comments = [...this.comments];
    }

    // 输入回复评论
    @action.bound
    public onChangeReplyComment(item, e): void {
        item.replyComment = e.target.value;
        this.comments = [...this.comments];
    }

    // 输入评论
    @action.bound
    public onChangeCommentValue(e): void {
        this.commentValue = e.target.value;
    }

    // 编辑评论
    @action.bound
    public onEditComment(item): void {
        item.isEdit = true;
        item.comment_copy = item.comment;
        this.comments = [...this.comments];
    }

    // 输入评论
    @action.bound
    public onChangeCommentCopy(item, e): void {
        item.comment_copy = e.target.value;
        this.comments = [...this.comments];
    }

    // 取消编辑评论
    @action.bound
    public onCancelEditComment(item): void {
        item.isEdit = false;
        item.comment_copy = item.comment;
        this.comments = [...this.comments];
    }

    // @action.bound
    // public onSaveEditComment(item): void {
    //     item.isEdit = false;
    //     item.comment = item.comment_copy;
    //     this.comments = [...this.comments];
    // }

    // 取消评论
    @action.bound
    public onCancelComment() {
        this.ifInserting = false;
        this.commentValue = '';
        // this.participant.handleCommentChange('');
    }

    // 保存评论
    @action.bound
    public async onSaveComment() {
        try {
            const params = {
                docId: this.apiId,
                comment: this.commentValue
            };
            await nsMockManageKoasApiManageCommentCommentDocPost.remote(params);
            runInAction(() => {
                this.ifInserting = false;
                this.commentValue = '';
                this.getComments();
            });
        } catch (e) {
        }
    }

    // 获取评论信息
    @action.bound
    public async getComments() {
        try {
            const params = {
                docId: this.apiId
            };
            const result = await nsMockManageKoasApiManageCommentGetCommentsGet.remote(params);
            runInAction(() => {
                this.comments = result?.list || [];
            });
        } catch (e) {
        }
    }

    // 更新评论
    @action.bound
    public async updateComment(item) {
        try {
            const params = {
                commentId: item.id,
                comment: item.comment_copy
            };
            await nsMockManageKoasApiManageCommentUpdateCommentPost.remote(params);
            runInAction(() => {
                this.getComments();
            });
        } catch (e) {
        }
    }

    // 删除评论
    @action.bound
    public async deleteComment(commentId: number) {
        try {
            const params = {
                commentId
            };
            await nsMockManageKoasApiManageCommentDeleteCommentGet.remote(params);
            runInAction(() => {
                message.success('删除成功');
                this.getComments();
            });
        } catch (e) {
        }
    }

    // 回复评论
    @action.bound
    public async replyComment(item, commentId: number) {
        try {
            const params = {
                comment: item.replyComment,
                commentId
            };
            await nsMockManageKoasApiManageCommentReplyCommentPost.remote(params);
            runInAction(() => {
                this.getComments();
            });
        } catch (e) {
        }
    }
}
