.apiBaseInfoWrap {
  .collapse {
    border: 1px solid #d9d9d9;
    border-radius: 4px;

    .collapsePanelRow {
      margin-bottom: 8px;
      display: flex;
      align-items: center;

      >div {
        display: flex;
        align-items: center;
      }

      .description{
        display: block;
        flex: 1;
      }

      .apiPath {
        width: 50%;

        .path{
          overflow: hidden;
          text-overflow: ellipsis;
        }
      }

      .width25 {
        min-width: 25%;
      }

      .method {
        background-color: rgba(50, 125, 255, .08);
        color: #327dff;
        margin-right: 8px;
        border-radius: 4px;
        padding: 0 4px;
      }

      .isDiff, .isStandard {
        margin-right: 4px;
        color: #000;
        opacity: .85;
      }

      .ignore {
        font-size: 12px;
        margin-top: 4px;
      }

      .noIgnore {
        font-size: 12px;
        margin-top: 4px;
        color: #898a8c;
      }

      .label {
        color: #898a8c;
        display: inline-block;
        line-height: 32px;
        white-space: nowrap;

        .questionIcon{
          margin-left: 4px;
        }
      }

      .priority {
        font-weight: 700;
      }

      .teamsWrap {
        display: flex;
        flex-direction: column;
        align-items: baseline;
      }

      .teams {
        display: flex;
        align-items: center;
        white-space: nowrap;
        overflow: hidden;
        background: #f7f9fa;
        padding: 4px 8px;
        margin: 4px 12px 4px 0;
        border-radius: 4px;
        cursor: pointer;
        // max-width: 400px;
        overflow: hidden;

        .taskId {
          cursor: pointer;
          background-color: #666f8014;
          // color: #666f80;
          border-radius: 4px;
          padding: 2px 8px;
          margin-right: 8px;
        }

        .title {
          flex: 1;
          // display: flex;
          // overflow: hidden;
          // text-overflow: ellipsis;
        }
      }
    }

    .flexStart{
      align-items: flex-start;
    }

    .flexBaseline {
      align-items: baseline;
    }
  }

  :global {
    .ant-collapse-ghost>.ant-collapse-item>.ant-collapse-content>.ant-collapse-content-box {
      padding: 0 24px;
    }

    .ant-collapse>.ant-collapse-item>.ant-collapse-header {
      padding: 24px 24px 24px 40px;
    }

    .ant-typography {
      margin-bottom: 0;
    }
  }
}