import { AViewModel } from '@libs/mvvm';
import { observable, action, runInAction } from 'mobx';
import {
    nsMockPermissionBatchGetUserGet, nsMockManageKoasApiManageHttpApiQueryDiffBranchListGet,
    nsMockManageKoasApiManageAutoDocQueryStandardInfoGet, nsMockManageKoasApiManageHttpApiIgnoredConsistencyPost,
    nsMockManageKoasApiManageModuleQueryArDocBranchListGet, nsMockManageKoasApiManageHttpApiQueryTeamsByApiGet
} from '@/remote';
import {Bind} from 'lodash-decorators';
import { StandardInfoM } from '@/business/apiDetail/standardInfo/StandardInfoM';
import { message } from 'antd';
import { ITeams, IParticipants } from './configure';
import { CustomTagM } from '@/business/httpApiComponents/customTag/CustomTagM';

export class ApiBaseInfoM extends AViewModel {
    public id: number = 0;
    private isAutoApiDoc: boolean = false;
    @observable public name: string = '';
    @observable public diffBranchList: nsMockManageKoasApiManageHttpApiQueryDiffBranchListGet.IDiffBranch[] = [];
    @observable private owner: string = '';
    @observable public ownerInfoList: IParticipants[] = [];
    @observable public cooperList: string[] = [];
    @observable public participants: IParticipants[] = [];
    @observable public qaList: string[] = [];
    @observable public qaUserInfos: IParticipants[] = [];
    @observable public method: string = '';
    @observable public path: string = '';
    @observable public isDiff: number = 0;
    @observable public isStandard: number = 1;
    private standardInfo: string[] = [];
    @observable public branchName: string = '';
    @observable public description: string = '';
    @observable public priority: string = '';
    @observable public readOnly: number = 0;
    @observable public branchVoList: nsMockManageKoasApiManageModuleQueryArDocBranchListGet.IBranchVoItem[] = [];
    @observable public teams: ITeams[] = [
        // {taskId: 'T2374631', title: '艾特入口支持多选艾特'},
        // {taskId: 'B944165', title: 'rpc接口更新编辑时，并没有接口负责人的保存，是否需要手动引入接口负责人'},
        // {taskId: 'T2542710', title: 'kwaibi数据看板，按业务线查看接口标记数据'},
    ];

    public changeBranchCallback?(branchName: string, docId: number): void;

    public standardInfoM = new StandardInfoM();
    public customTagM = new CustomTagM();

    @action.bound
    public setBaseInfo(baseInfo: object): void {
        this.initData();
        this.setFields(baseInfo);
        this.queryArDocBranchList();
        this.queryTeamsByApi();
        this.queryStandardInfo();
        this.batchGetUserInfo();
        this.customTagM.initLoading(this.id);
    }

    @action.bound
    public initData(): void {
        this.id = 0;
        this.name = '';
        this.diffBranchList = [];
        this.owner = '';
        this.ownerInfoList = [];
        this.cooperList = [];
        this.participants = [];
        this.qaList = [];
        this.qaUserInfos = [];
        this.method = '';
        this.path = '';
        this.isDiff = 0;
        this.isStandard = 1;
        this.branchName = '';
        this.description = '';
        this.priority = '';
        this.readOnly = 0;
        this.teams = [];
    }

    // 切换分支
    @action.bound
    public onChangeBranch(branchName, e): void {
        if (this.branchName !== branchName) {
            this.branchName = branchName;
            this.changeBranchCallback && this.changeBranchCallback(branchName, e.docid);
        }
    }

    @action
    public onChangeIsAutoApiDoc(isAutoApiDoc: boolean): void {
        this.isAutoApiDoc = isAutoApiDoc;
    }

    @action.bound
    public onOpenStandardInfo(): void {
        this.standardInfoM.init(this.id, this.isAutoApiDoc ? 1 : 2, this.standardInfo);
    }

    // 获取规范性校验信息
    @Bind
    private async queryStandardInfo(): Promise<void> {
        try {
            const params = {
                docId: this.id,
                isAuto: this.isAutoApiDoc ? 1 : 2
            };
            const result = await nsMockManageKoasApiManageAutoDocQueryStandardInfoGet.remote(params);
            runInAction(() => {
                this.isStandard = result?.isStandard;
                this.standardInfo = result?.standardInfo;
            });
        } catch {
        }
    }

    @Bind
    private async batchGetUserInfo(): Promise<void> {
        let usernames: string[] = [];
        if (this.owner) {
            usernames.push(this.owner);
        }
        if (this.cooperList && this.cooperList.length) {
            usernames = [...usernames, ...this.cooperList];
        }
        if (this.qaList && this.qaList.length) {
            usernames = [...usernames, ...this.qaList];
        }
        usernames = [...new Set(usernames)];
        const users: IParticipants[] = await nsMockPermissionBatchGetUserGet.batchGetUser(usernames.join(','));
        runInAction(() => {
            users.forEach(item => {
                if (this.owner === item.username) {
                    this.ownerInfoList = [item];
                }
                if (this.cooperList.includes(item.username)) {
                    this.participants.push(item);
                }
                if (this.qaList.includes(item.username)) {
                    this.qaUserInfos.push(item);
                }
            });
        });
    }

    @Bind
    public async ignoredConsistency(): Promise<void> {
        try {
            const params = {
                docId: this.id
            };
            await nsMockManageKoasApiManageHttpApiIgnoredConsistencyPost.remote(params);
            runInAction(() => {
                this.isDiff = 3;
                message.success('已忽略');
            });
        } catch {
        }
    }

    @Bind
    private async queryArDocBranchList(): Promise<void> {
        try {
            const params = {docId: this.id};
            const result = await nsMockManageKoasApiManageModuleQueryArDocBranchListGet.remote(params);
            runInAction(() => {
                this.branchVoList = result?.branchVoList;
            });
        } catch {}
    }

    // 获取关联team
    @Bind
    private async queryTeamsByApi() {
        try {
            const result = await nsMockManageKoasApiManageHttpApiQueryTeamsByApiGet.remote({apiId: this.id});
            runInAction(() => this.teams = result?.result);
        } catch {
        }
    }
}
