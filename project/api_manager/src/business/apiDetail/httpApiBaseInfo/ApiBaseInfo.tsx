import React from 'react';
import { observer } from 'mobx-react';
import { AView } from 'libs';
import { ApiBaseInfoM } from './ApiBaseInfoM';
import css from './ApiBaseInfo.less';
import { Collapse, Row, Col, Modal, Tooltip, Select, Tag } from 'antd';
import { Bind } from 'lodash-decorators';
import { KdevIconFont, AvatarList, CopyBtn } from '@/business/commonComponents';
import { priorityColor, isStandardData, isDiffData, readOnlyEnum } from './configure';
import { ERouter } from 'CONFIG';
import { StandardInfo } from '@/business/apiDetail/standardInfo/StandardInfo';
import classNames from 'classnames';
import { CustomTag } from '@/business/httpApiComponents/customTag/CustomTag';

const { Panel } = Collapse;
const { Option } = Select;

interface IProps {
    isAutoApiDoc?: boolean;
    isShowBranch?: boolean;
}

@observer
export class ApiBaseInfo extends AView<ApiBaseInfoM, IProps> {

    @Bind
    public componentDidMount(): void {
        this.model.onChangeIsAutoApiDoc(this.props?.isAutoApiDoc || false);
    }

    @Bind
    private renderUserOrOwner(): React.ReactNode {
        const model = this.model;
        return (
            <Col span={8}>
                <span className={css.label}>负责人：</span>
                <AvatarList reviewers={model.ownerInfoList} />
            </Col>
        );
    }

    @Bind
    private renderApiPath(): React.ReactNode {
        const model = this.model;
        return (
            <div className={css.apiPath}>
                <span className={css.label}>PATH：</span>
                <span className={css.method}>{model.method}</span>
                <span className={css.path}>{model.path}</span>
                <CopyBtn copyContent={model.path} />
            </div>
        );
    }

    @Bind
    private ignoreConfirm(): void {
        Modal.confirm({
            content: '是否要忽略该文档与最新代码的一致性？',
            onOk: () => this.model.ignoredConsistency()
        });
    }

    @Bind
    private renderIsDiff(): React.ReactNode {
        const model = this.model;
        if (!this.props.isAutoApiDoc) {
            return (
                <div className={css.width25}>
                    <span className={css.label}>
                        一致性校验
                        {
                            <Tooltip title="API文档与最新编译的一个分支进行对比，校验文档与代码是否一致">
                                <KdevIconFont id="#iconquestion" className={css.questionIcon} />
                            </Tooltip>
                        }：
                    </span>
                    {
                        model.isDiff
                            ? <Tooltip title={'点击查看详情'}>
                                <a
                                    style={{ color: isDiffData[model.isDiff]?.color }} className={css.isDiff}
                                    href={`${ERouter.API_MOCK_VERSIONCOMPARISON}?artificailId=${model.id}`}
                                    target="_blank"
                                >
                                    {isDiffData[model.isDiff]?.text}
                                </a>
                            </Tooltip>
                            : '-'
                    }
                    {
                        model.isDiff === 1 &&
                        <a target="_blank" className={css.ignore} onClick={this.ignoreConfirm}>
                            忽略
                        </a>
                    }
                    {
                        model.isDiff === 3 &&
                        <span className={css.noIgnore}>（已忽略）</span>
                    }
                </div>
            );
        }
    }

    @Bind
    private renderIsStandard(): React.ReactNode {
        const model = this.model;
        return (
            <div className={css.width25}>
                <span className={css.label}>
                    规范性校验：
                </span>
                <span style={{ color: isStandardData[model.isStandard]?.color }} className={css.isStandard}>
                    {isStandardData[model.isStandard]?.text}
                </span>
                {
                    model.isStandard === 2 &&
                    <a onClick={model.onOpenStandardInfo}>详情</a>
                }
                <StandardInfo model={model.standardInfoM} />
            </div>
        );
    }

    @Bind
    private renderParticipants(): React.ReactNode {
        return (
            <Col span={8}>
                <span className={css.label}>研发参与人：</span>
                <AvatarList reviewers={this.model.participants} />
            </Col>
        );
    }

    @Bind
    private renderQaUser(): React.ReactNode {
        return (
            <Col span={8}>
                <span className={css.label}>测试参与人：</span>
                <AvatarList reviewers={this.model.qaUserInfos} />
            </Col>
        );
    }

    @Bind
    private renderPriority(): React.ReactNode {
        const { priority } = this.model;
        if (!this.props.isAutoApiDoc) {
            return (
                <Col span={8}>
                    <span className={css.label}>优先级：</span>
                    <span className={css.priority} style={{ color: priorityColor[priority] }}>{priority}</span>
                </Col>
            );
        }
    }

    @Bind
    private renderReadOnly(): React.ReactNode {
        return (
            <Col span={8}>
                <span className={css.label}>读写属性：</span>
                {readOnlyEnum[this.model.readOnly]}
            </Col>
        );
    }

    @Bind
    private renderBranch(): React.ReactNode {
        const model = this.model;
        return (
            <Col span={8}>
                <span className={css.label}>关联分支：</span>
                {
                    this.props.isShowBranch
                        ? <Select
                            onChange={model.onChangeBranch}
                            value={model.branchName}
                            dropdownMatchSelectWidth={false}
                        >
                            {
                                model.branchVoList.map(item => {
                                    return (
                                        <Option key={item.branchName} docid={item.docId} value={item.branchName}>
                                            {item.branchName || '未关联分支'}
                                        </Option>
                                    );
                                })
                            }
                        </Select>
                        : model.branchName
                }
            </Col>
        );
    }

    @Bind
    private renderTeam() {
        const teams = this.model?.teams || [];
        return (
            <Row className={classNames(css.collapsePanelRow, css.flexStart)}>
                <span className={css.label} style={{ lineHeight: '42px' }}>关联Team：</span>
                <div className={css.teamsWrap}>
                    {
                        teams.map(item => (
                            <div key={item.taskId} className={css.teams}>
                                <span className={css.taskId}>{item.taskId}</span>
                                <Tooltip title={item.title}>
                                    <a
                                        className={css.title}
                                        target="_blank"
                                        href={item.teamUrl}
                                    >
                                        {item.title}
                                    </a>
                                </Tooltip>
                            </div>
                        ))
                    }
                </div>
            </Row>
        );
    }

    public render(): React.ReactNode {
        const model = this.model;
        return (
            <div className={css.apiBaseInfoWrap}>
                <Collapse ghost className={css.collapse} defaultActiveKey={'1'}>
                    <Panel header={'基本信息'} key={'1'}>
                        <div className={css.collapsePanelRow}>
                            {this.renderApiPath()}
                            {this.renderIsDiff()}
                            {this.renderIsStandard()}
                        </div>
                        <Row className={css.collapsePanelRow}>
                            {this.renderUserOrOwner()}
                            {this.renderParticipants()}
                            {this.renderQaUser()}
                        </Row>
                        <Row className={css.collapsePanelRow}>
                            {this.renderPriority()}
                            {this.renderReadOnly()}
                            {this.renderBranch()}
                        </Row>
                        {this.renderTeam()}
                        {
                            !this.props.isAutoApiDoc &&
                            <CustomTag
                                model={model.customTagM}
                                className={classNames(css.collapsePanelRow, css.flexStart)}
                                label={<span className={css.label}>自定义标签：</span>}
                            />
                        }
                        <div className={classNames(css.collapsePanelRow, css.flexBaseline)}>
                            <span className={css.label}>备注：</span>
                            <div className={css.description} dangerouslySetInnerHTML={{ __html: model.description }} />
                        </div>
                    </Panel>
                </Collapse>
            </div>
        );
    }
}
