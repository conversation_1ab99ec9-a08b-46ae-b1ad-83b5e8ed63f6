interface IPriorityColorEnum {
    [key: string]: string;
}

export const priorityColor: IPriorityColorEnum = {
    P0: '#ff4d4f',
    P1: '#ffaf60',
    P2: '#31bf30'
};

export const isDiffData = {
    1: {
        color: '#ff4d4f',
        text: '未通过'
    },
    2: {
        color: '#31bf30',
        text: '通过'
    },
    3: {
        color: '#ff4d4f',
        text: '未通过'
    },
};

export const isStandardData = {
    1: {
        color: '#31bf30',
        text: '通过'
    },
    2: {
        color: '#ff4d4f',
        text: '未通过'
    }
};

export const readOnlyEnum = {
    1: '读',
    2: '写'
};

export interface ITeams {
    taskId: string;
    title: string;
    teamUrl: string;
}

export interface IParticipants {
    name: string;
    username: string;
    avatarUrl: string;
}
