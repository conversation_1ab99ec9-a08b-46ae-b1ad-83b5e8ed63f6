import { Avatar } from 'antd';
import css from './index.less';
import React from 'react';
interface IProps {
    avatarUrl: string;
    name: string;
}
export function KimUserInfo(props) {
    return (
        <div className={css.userInfoBox}>
            <Avatar style={{ color: '#fff' }} src={props.avatarUrl || ''} className={css.kimImg} size={24}>
                {props.name.slice(0, 1)}
            </Avatar>
            <span className={css.name}>{props.name}</span>
        </div>
    );
}
