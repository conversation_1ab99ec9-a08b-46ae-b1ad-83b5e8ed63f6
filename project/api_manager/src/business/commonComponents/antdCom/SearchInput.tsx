import React from 'react';
import { Input } from 'antd';
import { InputProps } from 'antd/lib/input';
import classNames from 'classnames';
import css from './index.less';
import { KdevIconFont } from '@/business/commonComponents';
import { common_system_search } from '@kid/enterprise-icon/icon/output/icons';

interface ISearchInputProps extends InputProps {
    inputRef?: React.Ref<Input>;
}

function SearchInput({inputRef, ...props}: ISearchInputProps) {
    return (
        <Input
            prefix={
                <KdevIconFont
                    id={common_system_search}
                    color="#D5D6D9"
                />
            }
            {...props}
            ref={inputRef}
            className={classNames(props.className, css.searchInput)}
        />
    );
}

export { SearchInput };
