import React from 'react';
import { Tag } from 'antd';
import { TagProps } from 'antd/lib/tag';

const kTagColorBgMap = {
    '#252626': '#F5F7FA', // 灰色 默认
    '#FFAA00': '#FFFBE6', // 橙色
    '#326BFB': '#F0F7FF', // 深蓝
    '#19B2FF': '#E8F5FF', // 浅蓝
    '#30C453': '#F0FFF1' // 绿色
};

type TColor = keyof typeof kTagColorBgMap;

interface IKTagProps extends TagProps {
    color?: string | TColor;
    size?: number;
}

function KTag({color, size, ...props}: IKTagProps) {
    return (
        <Tag
            {...props}
            style={{
                backgroundColor: kTagColorBgMap[color || '#F5F7FA'],
                color: color || '#252626',
                border: 0,
                borderRadius: 4,
                margin: 0,
                padding: '1px 7px',
                fontSize: size || 12,
                minHeight: 20,
                ...props.style
            }}
        />
    );
}

export { KTag, IKTagProps };
