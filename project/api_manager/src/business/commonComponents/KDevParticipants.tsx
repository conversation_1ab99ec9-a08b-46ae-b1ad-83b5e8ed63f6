import React from 'react';
import { Participants } from '@kdev-fe-common/common/kdevSearch';
import { nsKdevUser, nsMockPermissionGetUserListGet } from '@/remote';
import Bind from 'lodash-decorators/bind';
import { runInAction } from 'mobx';

interface IProps {
    value: nsKdevUser.IUser[];
    disabled?: boolean;
    onChange: (value: nsKdevUser.IUser[]) => void;
    onChecked?: (checked: boolean, userInfo: nsKdevUser.IUser) => void;
    maxLength?: number;
    size?: 'large' | 'small' | 'default';
}

export class KDevParticipants extends React.Component<IProps, any> {

    @Bind
    private async getUserList(value): Promise<nsKdevUser.IUser[]> {
        if (!value.trim() || value.length <= 3) {
            return Promise.resolve([]);
        }
        const params = {
            username: value.trim()
        };
        const respData = await nsMockPermissionGetUserListGet.remote(params);
        runInAction(() => {
            respData.list.map(item => {
                item['avatarUrl'] = item.photo || '';
            });
        });
        return Promise.resolve(respData.list);
    }

    public render(): React.ReactElement {
        return <Participants
            onSearch={this.getUserList}
            {...this.props}
        />;
    }
}
