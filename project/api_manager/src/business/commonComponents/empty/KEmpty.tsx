/*
 * @Author: han<PERSON><PERSON>
 * @Date: 2024-04-19 15:18:39
 * @LastEditors: hanwei<PERSON>
 * @LastEditTime: 2024-04-22 15:42:24
 * @Description: 请填写简介
 */
import React from 'react';
import css from './KEmpty.less';
import classNames from 'classnames';

type TEmptyImg = 'EMPTY_SIMPLE_DATA' | 'NOMAL_SIMPLE_SEARCH' | 'NOMAL_EMPTY_LIST' | 'NOMAL_SIMPLE_EMPTY' | 'NOMAL_EMPTY_DATA' | 'NOMAL_SIMPLE_MESSAGE' | 'NOMAL_EMPTY_SEARCH' | 'NOMAL_SIMPLE_SEARCH_2' | 'PROXY_EMPTY';


type TKEmptyEnum = {
    [key in TEmptyImg]: {
        image: string;
        imgHeight: number;
        desc: string;
    };
};

const KEmptyEnum: TKEmptyEnum = {
    EMPTY_SIMPLE_DATA: {
        image: '/assets/img/empty-simple-data.png',
        imgHeight: 60,
        desc: '暂无数据'
    },
    NOMAL_SIMPLE_SEARCH: {
        image: '/assets/img/nomal-simple-search.png',
        imgHeight: 60,
        desc: '搜索无结果'
    },
    NOMAL_SIMPLE_SEARCH_2: {
        image: '/assets/img/nomal-simple-search.png',
        imgHeight: 60,
        desc: '暂无数据'
    },
    NOMAL_EMPTY_LIST: {
        image: '/assets/img/nomal-empty-list.png',
        imgHeight: 92,
        desc: '您还没有创建过数据，快去新建吧～'
    },
    NOMAL_SIMPLE_EMPTY: {
        image: '/assets/img/nomal-simple-empty.png',
        imgHeight: 60,
        desc: '暂无数据'
    },
    NOMAL_EMPTY_DATA: {
        image: '/assets/img/nomal-empty-data.png',
        imgHeight: 112,
        desc: '暂无配置代理场景'
    },
    NOMAL_SIMPLE_MESSAGE: {
        image: '/assets/img/nomal-simple-message.png',
        imgHeight: 60,
        desc: '暂无内容'
    },
    // 搜索无结果
    NOMAL_EMPTY_SEARCH: {
        image: '/assets/img/normal-empty-search.png',
        imgHeight: 112,
        desc: '暂无数据'
    },
    // 搜索无结果
    PROXY_EMPTY: {
        image: '/assets/img/proxy-empty.png',
        imgHeight: 112,
        desc: '暂无数据，此处将显示所有截获的网络日志'
    }
};

interface IProps {
    image?: TEmptyImg;
    description?: React.ReactNode | string;
    extra?: React.ReactNode | string;
    className?: string;
    style?: React.CSSProperties;
}

function KEmpty(props: IProps) {
    const { image = 'EMPTY_SIMPLE_DATA' } = props;

    return (
        <div
            style={props.style}
            className={classNames(css.kEmpty, props.className)}
        >
            <img
                src={KEmptyEnum[image]?.image}
                height={KEmptyEnum[image]?.imgHeight}
            />
            <div className={css.kEmptyDesc}>
                {props.description ?? KEmptyEnum[image]?.desc}
            </div>
            {props.extra}
        </div>
    );
}

export { KEmpty };
