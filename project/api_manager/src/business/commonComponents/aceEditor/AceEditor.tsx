import React, { useRef } from 'react';
import ReactACE, { IAceEditorProps } from 'react-ace';
import 'ace-builds/src-noconflict/mode-json5';
import 'ace-builds/src-noconflict/ext-language_tools';
import classNames from 'classnames';
import css from './AceEditor.less';
import JSON5 from 'json5';
// import 'brace/mode/json';
// import 'brace/mode/python';
// import 'brace/mode/typescript';

// import 'brace/theme/chaos';
// import 'brace/theme/tomorrow';
// import 'brace/theme/xcode';
// import 'brace/theme/kuroir';

// import 'brace/ext/searchbox';
// import 'brace/ext/language_tools';
// import 'brace/ext/beautify';

import 'ace-builds/src-noconflict/mode-python';
import 'ace-builds/src-noconflict/mode-typescript';
import 'ace-builds/src-noconflict/mode-java';

import 'ace-builds/src-noconflict/theme-chaos';
import 'ace-builds/src-noconflict/theme-tomorrow';
import 'ace-builds/src-noconflict/theme-xcode';
import 'ace-builds/src-noconflict/theme-kuroir';

import 'ace-builds/src-noconflict/ext-searchbox';
import 'ace-builds/src-noconflict/ext-beautify';
import { Bind } from 'lodash-decorators';

interface IFold {
    start: { row: number; column: number };
    end: { row: number; column: number };
    placeholder: string;
    range: any;
    ranges: any[];
}

export interface IProps extends IAceEditorProps {
    mode?: 'json5' | 'python' | 'typescript' | 'java' | 'yaml';
    theme?: 'chaos' | 'tomorrow' | 'xcode' | 'kuroir';
}

export class AceEditor extends React.Component<IProps> {
    private editorRef = React.createRef<any>();

    componentDidMount() {
        const editor = this.editorRef.current?.editor;
        if (editor) {
            const session = editor.getSession();

            // 设置折叠样式
            session.setFoldStyle('markbeginend');

            // 监听折叠变化事件
            session.on('changeFold', () => {
                const allFolds = session.getAllFolds() as IFold[];

                allFolds.forEach((fold) => {
                    try {
                        // 获取折叠区域的完整文本
                        const range = {
                            start: { row: fold.start.row, column: fold.start.column - 1 },
                            end: { row: fold.end.row, column: fold.end.column + 1 }
                        };
                        const foldedText = session.getTextRange(range);

                        // 清理并解析文本
                        const cleanText = foldedText.trim();
                        const firstChar = cleanText[0];

                        if (firstChar === '{' || firstChar === '[') {
                            const parsedData = JSON5.parse(cleanText);
                            const count = firstChar === '[' ? parsedData.length : Object.keys(parsedData).length;

                            // 设置折叠文本
                            fold.placeholder = firstChar === '['
                                ? ` ...${count} items `
                                : ` ...${count} properties `;
                        }
                    } catch (error) {
                        // 如果解析失败，使用默认的折叠文本
                        fold.placeholder = ' ... ';
                    }
                });
            });

            // 监听点击折叠图标事件
            editor.on('gutterclick', (e: any) => {
                const target = e.domEvent.target;
                if (target.className.indexOf('ace_fold-widget') === -1) {
                    return;
                }

                const row = e.getDocumentPosition().row;
                const line = session.getLine(row);

                if (line.trim().endsWith('[') || line.trim().endsWith('{')) {
                    setTimeout(() => {
                        session.emit('changeFold');
                    }, 0);
                }
            });
        }
    }

    /**
     * 展开全部
     */
    @Bind
    public expandAll() {
        const editor = this.editorRef.current?.editor;
        editor.session.getUndoManager().reset();
        editor.session.unfold(null, true);
    }

    /**
     * 收起全部
     */
    @Bind
    public unExpandAll() {
        const editor = this.editorRef.current?.editor;
        editor.session.getUndoManager().reset();
        editor.session.foldAll(1);
    }

    public render(): React.ReactNode {
        return (
            <ReactACE
                ref={this.editorRef}
                mode="json5"
                theme="chaos"
                editorProps={{ $blockScrolling: true }}
                setOptions={{
                    showFoldWidgets: true, // 显示折叠部件
                }}
                {...this.props}
                className={classNames(this.props.className, css.aceEditor)}
            />
        );
    }
}

export default AceEditor;
