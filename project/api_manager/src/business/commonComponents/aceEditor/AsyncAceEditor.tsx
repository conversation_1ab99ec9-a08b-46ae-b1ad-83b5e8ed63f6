import React from 'react';
import { IProps } from './AceEditor';
import { SuspenseHolder } from '@libs/a-component/SuspenseHolder';

const AsyncComp = React.lazy(
    () => import('@/business/commonComponents/aceEditor/AceEditor'),
);

export class AsyncAceEditor extends React.Component<IProps> {
    public render(): React.ReactNode {
        const holder = (
            <SuspenseHolder style={{ height: this.props.style?.height || this.props.height || 180 }} />
        );
        return (
            <React.Suspense fallback={holder}>
                <AsyncComp {...this.props} />
            </React.Suspense>
        );
    }
}
