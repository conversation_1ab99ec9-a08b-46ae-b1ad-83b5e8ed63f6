import React from 'react';
import { Upload, message, Spin } from 'antd';
import { KdevIconFont } from '@/business/commonComponents';
import {
    common_system_upload,
    common_system_clip,
    common_system_correct02,
    common_system_delete02
} from '@kid/enterprise-icon/icon/output/icons';
import css from './FileUploader.less';
import classnames from 'classnames';

const { Dragger } = Upload;

interface FileUploaderProps {
    fileUrl: string;
    fileName: string;
    onFileChange?: (fileUrl: string, fileName: string) => void;
    readonly?: boolean;
    maxSize?: number;
}

export const FileUploader: React.FC<FileUploaderProps> = ({
    fileUrl, onFileChange,
    fileName,
    readonly = false,
    maxSize = 1024 * 1024 * 10
}) => {
    const [loadingUpload, setLoadingUpload] = React.useState<boolean>(false);

    return fileUrl ? (
        <div className={classnames(css.fileList, {
            [css.readonly]: readonly
        })}>
            <div className={css.fileBox}>
                <span className={css.fileNameBox}>
                    <span className={css.clipIcon}>
                        <KdevIconFont id={common_system_clip} />
                    </span>
                    <span className={css.fileName}>
                        <a
                            href={fileUrl}
                            target="_blank"
                            rel="noopener noreferrer"
                            className={css.fileLink}
                        >
                            {fileName ? fileName : fileUrl.split('/').pop()}
                        </a>
                    </span>
                </span>
                <span className={css.statusIcon}>
                    <KdevIconFont id={common_system_correct02} />
                </span>
            </div>
            {!readonly && (
                <div
                    className={css.deleteIcon}
                    onClick={() => onFileChange?.('', '')}
                >
                    <KdevIconFont id={common_system_delete02} />
                </div>
            )}
        </div>
    ) : (
        <Dragger
            className={classnames(css.dragger, {
                [css.readonly]: readonly
            })}
            name="file"
            action="/api/mock/manage/apiManage/main/api/upload/api/file"
            multiple={false}
            showUploadList={false}
            disabled={readonly}
            onChange={(info) => {
                if (info.file.status === 'done') {
                    setLoadingUpload(false);
                    if (info.file.response.status === 200) {
                        onFileChange?.(info.file.response.data.downUrl, info.file.response.data.originalFileName);
                    } else {
                        message.error(info.file.response.message);
                    }
                } else if (info.file.status === 'error') {
                    message.error('上传失败');
                    setLoadingUpload(false);
                }
            }}
            beforeUpload={(file) => {
                if (file.size > maxSize) {
                    message.warn(`您选择的上传的文件过大，请选择${maxSize / 1024 / 1024}M以内文件！`);
                    return false;
                }
                setLoadingUpload(true);
                return true;
            }}
        >
            <Spin spinning={loadingUpload}>
                <div className={css.uploadIcon}>
                    <KdevIconFont id={common_system_upload} />
                </div>
                <div className={css.uploadTitle}>
                    {readonly ? '暂无文件' : '点击或者拖拽上传'}
                </div>
            </Spin>
        </Dragger>
    );
}; 