.fileList {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 8px 12px;
    background-color: #F7F8FA;
    border-radius: 4px;

    &.readonly {
        background-color: #F7F8FA;
        opacity: 0.9;
        cursor: not-allowed;
    }

    .fileBox {
        display: flex;
        align-items: center;
        justify-content: space-between;
        flex: 1;

        .fileNameBox {
            display: flex;
            align-items: center;

            .clipIcon {
                color: #898A8C;
                margin-right: 8px;
                font-size: 16px;
            }

            .fileName {
                color: #1F2329;
                font-size: 14px;
            }
        }

        .statusIcon {
            color: #30C453;
            font-size: 16px;
        }
    }

    .deleteIcon {
        color: #898A8C;
        font-size: 16px;
        margin-left: 16px;
        cursor: pointer;

        &:hover {
            color: #1F2329;
        }
    }
}

.dragger {
    background: #F7F8FA;
    border: 1px dashed #D1D2D3;
    border-radius: 4px;
    padding: 16px;
    text-align: center;
    height: fit-content !important;

    &.readonly {
        background-color: #F7F8FA;
        opacity: 0.9;
        cursor: not-allowed;
        border-color: #D1D2D3;

        &:hover {
            border-color: #D1D2D3;
        }

        .uploadIcon {
            color: #898A8C;
        }

        .uploadTitle {
            color: #898A8C;
        }
    }

    .uploadIcon {
        color: #898A8C;
        font-size: 24px;
        margin-bottom: 8px;
    }

    .uploadTitle {
        color: #1F2329;
        font-size: 14px;
        margin-bottom: 4px;
    }

    .uploadSubTitle {
        color: #898A8C;
        font-size: 12px;
    }

    &:hover {
        border-color: #326BFB;
    }
}

.fileLink {
    color: inherit;
    text-decoration: none;
    cursor: pointer;

    &:hover {
        color: #1890ff;
        text-decoration: underline;
    }
}