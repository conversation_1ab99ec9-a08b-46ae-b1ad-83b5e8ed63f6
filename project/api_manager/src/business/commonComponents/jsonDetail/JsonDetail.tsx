import React from 'react';
import { AView, AViewModel } from 'libs/mvvm';
import { action, observable } from 'mobx';
import { observer } from 'mobx-react';
import { formatJson } from '@/index.config/tools';
import { Modal, Button } from 'antd';
import { CopyBtn } from '@/business/commonComponents';
import css from './JsonDetail.less';

export class JsonDetailM extends AViewModel {
    @observable public visible: boolean = false;
    @observable public detail: string = '';

    @action.bound
    public onOpenJsonDetail(detail) {
        this.visible = true;
        this.detail = formatJson(detail);
    }

    @action.bound
    public onCloseJsonDetail() {
        this.visible = false;
        this.detail = '';
    }
}

interface IProps {
    title?: string | HTMLElement | React.ReactHTMLElement<any>;
}

@observer
export class JsonDetail extends AView<JsonDetailM, IProps> {
    public render(): React.ReactNode {
        const model = this.model;
        return (
            <Modal
                title={ this.props.title || '详情' }
                visible={ model.visible }
                onCancel={ model.onCloseJsonDetail }
                footer={ <Button type={ 'primary' } onClick={ model.onCloseJsonDetail }>知道了</Button> }
                className={ css.jsonDetailModal }
                wrapClassName={ css.jsonDetailModalWrap }
                width={ 700 }
            >
                <pre className={ css.detail }>
                    <CopyBtn copyContent={model.detail} className={css.copyJsonDetialBtn} />
                    { model.detail }
                </pre>
            </Modal>
        );
    }
}
