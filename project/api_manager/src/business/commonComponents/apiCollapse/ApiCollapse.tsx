import React from 'react';
import classNames from 'classnames';
import { Collapse } from 'antd';
import css from './ApiCollapse.less';

const { Panel } = Collapse;

interface IProps {
    header?: React.ReactNode;
    children?: React.ReactNode;
    key?: number | string;
    defaultActiveKey?: number | string | number[] | string[];
    className?: string;
    extra?: React.ReactNode;
}

export function ApiCollapse(props: IProps) {
    return (
        <Collapse
            ghost
            className={classNames(css.apiCollapse, props.className)}
            defaultActiveKey={props.defaultActiveKey || 1}
        >
            <Panel
                header={props.header || ''}
                key={props.key || 1}
                extra={props.extra || ''}
            >
                {props.children || ''}
            </Panel>
        </Collapse>
    );
}
