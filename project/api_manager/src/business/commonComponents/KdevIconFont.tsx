import React from 'react';
import classnames from 'classnames';
import css from './KdevIconFont.less';
import Icon from '@kid/enterprise-icon/icon/output/Icon.js';
const icons = {};
interface IProps {
    id: string | any;
    size?: number;
    color?: string;
    style?: React.CSSProperties;
    className?: string;
}

const ICON_STYLE: React.CSSProperties = {
    width: '1em',
    height: '1em',
    verticalAlign: '-0.15em',
    fill: 'currentColor',
    overflow: 'hidden'
};

export class KdevIconFont extends React.Component<IProps, any> {

    public render() {
        const { id, size = 16, color = '', style, className } = this.props;
        const config = typeof id === 'string' ? icons[id] : id; // @todo 全部修改完成后，修改config=id
        if (!config) {
            return (
                <svg
                    className={ classnames('icon', className) }
                    aria-hidden="true"
                    style={{...ICON_STYLE, ...style}}
                >
                    <use xlinkHref={id}/>
                </svg>
            );
        }
        return (
            <Icon
                config={config}
                size={size}
                color={color}
                style={{...ICON_STYLE, ...style}}
                className={ classnames(css.icon, className) }
            />
        );

    }
}
