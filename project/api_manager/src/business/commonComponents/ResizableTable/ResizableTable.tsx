import React, { Component } from 'react';
import { Table } from 'antd';
import { TableProps } from 'antd/lib/table/index';
import { DraggableCore } from 'react-draggable';
import { observer } from 'mobx-react';
import css from './index.less';

const ResizableTitle = props => {
    const {
        onResize,
        enableResize,
        onResizeStart,
        onResizeEnd,
        draggableOpts,
        children,
        ...restProps
    } = props;
    if (!enableResize) {
        return <th {...restProps} >
            {children}
        </th>;
    }
    return (
        <th {...restProps} >
            {children}
            <DraggableCore
                {...draggableOpts}
                key={'resizableHandle-se'}
                onDrag={onResize}
                axis="x"
                onStart={onResizeStart}
                onStop={onResizeEnd}
            // defaultPosition={{x: 0, y: 0}}
            >
                <span
                    className={css.reactResizableHandle}
                    onClick={e => {
                        e.stopPropagation();
                    }}
                />
            </DraggableCore>
        </th>
    );
};

interface IProps extends TableProps<any> {
    enableResize?: boolean;
    unifiedId?: string;
}

/**
 * table的宽度基准；
 */
const RADIX = 800;
/**
 * 列的最小宽度 单位px
 */
const COLUMN_MIN_WIDTH = 50; // 内容区域 50px，左右padding共 24px
const LOCALSTORAGE_KEY = '_resizable-table';
@observer
export class ResizableTable extends Component<IProps, any, any> {
    /**
     * table的wrap div 防止页面有多个ResizableTable，导致取不到改table
     * @private
     */
    private tableWrapRefs: React.RefObject<HTMLDivElement> = React.createRef();
    /**
     * 开始拖拽前记录鼠标位置
     */
    private startScreen;
    /**
     * 记录在拖拽前的每列的实际宽度
     * @private
     */
    private curColGroupsWidths: number[] = [];
    private components = {
        header: {
            cell: ResizableTitle,
        },
    };
    constructor(props) {
        super(props);
    }
    public componentDidMount() {
        this.correctColgroupWidth();
    }

    /**
     * 从localstorage获取当前表格的数据
     * return [{key:string, width: number}]
     */
    private getCurTableLocalstorage() {
        if (!this.props.unifiedId) {
            return [];
        }
        const storage = localStorage.getItem(LOCALSTORAGE_KEY);
        const settings = storage ? (JSON.parse(storage)?.[this.props.unifiedId] || []) : [];
        return settings;
    }
    /**
     * 将数据存入localstorage
     * 当前项目所有table公用一个key
     * 存入数据结构
     * {
     *     [unifiedId]: [{key:string, width: number}]
     * }
     * return [{key:string, width: number}]
     */
    private setCurTableLocalstorage(data) {
        if (!this.props.unifiedId) {
            return;
        }
        const storage = localStorage.getItem(LOCALSTORAGE_KEY);
        const historyStorage = storage ? JSON.parse(storage) : {};
        historyStorage[this.props.unifiedId] = data;
        localStorage.setItem(LOCALSTORAGE_KEY, JSON.stringify(historyStorage));
    }

    private dataIndexToString(colDataIndex: string | string[]): string {
        if (Array.isArray(colDataIndex)) {
            return colDataIndex.join(',');
        }
        return colDataIndex;
    }

    get minColWidth(): any {
        return this.props.columns?.map((item, index) => {
            // @ts-ignore
            if (item.fixedMinWidth) {
                // @ts-ignore
                if (typeof item.fixedMinWidth === 'string') {
                    throw new Error('fixedMinWidth必须为数字；假如你想设置最小值为70px, 你设置为70就可以了！');
                }
                // @ts-ignore
                return item.fixedMinWidth;
            }
            return COLUMN_MIN_WIDTH;
        });
    }

    get columns() {
        const { enableResize = false } = this.props;
        if (!enableResize) {
            // 不触发 resize 时，直接返回 columns
            return this.props.columns;
        }
        const historyWidths = this.getCurTableLocalstorage();
        let shouldSync = false;
        /**
         * 当columns的长度变化后，不使用缓存
         * 当columns的key、dataIndex变化时，也不适用缓存
         */
        if (
            historyWidths.length === this.props.columns?.length
            && this.props.columns?.every(column => !!historyWidths.find(item =>
                // @ts-ignore
                item.key === (this.dataIndexToString(column.dataIndex) || column.key)
            ))
        ) {
            shouldSync = true;
        }
        return this.props.columns?.map((col, index) => {
            return {
                ...col,
                // @ts-ignore
                width: shouldSync
                    ? historyWidths.find(
                        // @ts-ignore
                        item => item.key === (this.dataIndexToString(col.dataIndex) || col.key)
                    ).width
                    : col.width,
                onHeaderCell: column => ({
                    // 最后一列不允许拖拽，列的enableResize属性优先级高于table
                    enableResize: (index === (this.props.columns?.length || 0) - 1)
                        ? false
                        // @ts-ignore
                        : (col.enableResize ?? enableResize),
                    onResize: this.handleResize(index),
                    onResizeStart: this.handleResizeStart(index),
                    onResizeEnd: this.handleResizeEnd(index),
                }),
            };
        }) || [];
    }
    /**
     * 初始化时根据table每列的实际宽度，计算每列的占比；并以基准值写回
     */
    private correctColgroupWidth() {
        const tableWrapDom = this.tableWrapRefs.current as HTMLElement;
        const colGroups = tableWrapDom.querySelectorAll('table colgroup col');
        const { rate } = this.getCurColumnsWidth();
        colGroups.forEach((item, index) => {
            (item as HTMLElement).style.minWidth = COLUMN_MIN_WIDTH + 'px';
            (item as HTMLElement).style.width = (rate[index]) * RADIX + 'px';
        });
    }

    /**
     * 获取实际table的各列的实际宽度、及其占比、总宽度
     */
    private getCurColumnsWidth() {
        const tableWrapDom = this.tableWrapRefs.current as HTMLElement;
        const curTable = tableWrapDom.querySelector('table') as HTMLTableElement;
        const columnWidths: number[] = [];
        for (let i = 0; i < curTable.rows[0].cells.length; i++) {
            columnWidths[i] = curTable.rows[0].cells[i].offsetWidth;
        }
        const sum = columnWidths.reduce((accumulator, cur) => accumulator + cur, 0);
        // 计算最小宽度换算成的百分比
        const rateByMinWidth = COLUMN_MIN_WIDTH / sum;
        return {
            columnWidths,
            sum,
            // 每一列的占比
            rate: columnWidths.map(item => item / sum),
            rateByMinWidth
        };
    }

    private handleResize = resizeColumnIndex => (e, ui) => {
        const colGroups = (this.tableWrapRefs.current as HTMLElement).querySelectorAll('table colgroup col');
        let offset = e.screenX - this.startScreen;
        const total = this.curColGroupsWidths.reduce((accumulator, cur) => accumulator + cur, 0);
        const { columnWidths } = this.getCurColumnsWidth();
        const widthSumBeforeCurColumn = this.curColGroupsWidths.reduce(
            (accumulator, cur, i) => {
                if (i <= resizeColumnIndex) {
                    return accumulator + cur;
                }
                return accumulator;
            },
            0);
        if (offset < 0
            && this.curColGroupsWidths[resizeColumnIndex] - Math.abs(offset) <= this.minColWidth[resizeColumnIndex]
        ) { //
            offset = this.minColWidth[resizeColumnIndex] - this.curColGroupsWidths[resizeColumnIndex];
        }
        // 更改后的每一列宽度
        const colGroupsWidthModif: number[] = [...this.curColGroupsWidths];

        /**
         * 拖拽时，需要等比列分配的列
         * 满足条件：向右拖拽时，列宽大于最小宽度之后的列 || 向左拖拽时之后的列
         */
        const needDistributionColumn = columnWidths.map((item, index) => {
            if (
                index > resizeColumnIndex
                && (
                    offset < 0
                    || (offset > 0 && columnWidths[index] > this.minColWidth[index])
                )
            ) {
                return index;
            }
        }).filter(Boolean);
        /**
         * table是按照比例分配宽度的
         * 在拖拽时，需要根据拖拽的offset占total（整个table 宽度）的比例，来重新分配table的宽度
         * 将拖拽产生的offset 平均分配给后面的columns， 之前的不变
         */
        if (needDistributionColumn.length === 0) {
            return;
        }
        for (let i = 0; i < colGroups.length; i++) {
            if (i === resizeColumnIndex) {
                colGroupsWidthModif[i] = (this.curColGroupsWidths[i] + offset);
            } else if (i > resizeColumnIndex) {
                if (i === colGroups.length - 1) {
                    colGroupsWidthModif[i] = Math.max(
                        this.minColWidth[i],
                        total - colGroupsWidthModif.reduce(
                            (accumulator, cur, index) => {
                                if (index < colGroups.length - 1) {
                                    return accumulator + cur;
                                }
                                return accumulator;
                            }, 0)
                    );
                } else {
                    const extraTotal = needDistributionColumn.reduce(
                        (pre, cur) => {
                            if (cur) {
                                return this.curColGroupsWidths[cur] + (pre || 0);
                            }
                            return pre;
                        },
                        0
                    );
                    if (needDistributionColumn.indexOf(i) !== -1) {
                        const proportion = this.curColGroupsWidths[i] / (extraTotal as number);
                        colGroupsWidthModif[i] = this.curColGroupsWidths[i] - offset * proportion;
                    }
                }
            }
            (colGroups[i] as HTMLElement).style.width = colGroupsWidthModif[i] + 'px';
        }
    }

    private handleResizeStart = index => (e, { node }) => {
        this.startScreen = e.screenX;
        this.curColGroupsWidths = this.getCurColumnsWidth().columnWidths;
    }

    /**
     * 当resize结束，同步数据到缓存
     */
    private handleResizeEnd = index => () => {
        const { columnWidths, sum } = this.getCurColumnsWidth();
        const storage = this.props.columns?.map((item, i) => (
            {
                // @ts-ignore
                key: this.dataIndexToString(item.dataIndex) || item.key,
                // width: columnWidths[i],
                width: columnWidths[i] / sum * RADIX,
            }
        ));
        this.setCurTableLocalstorage(storage);
        this.correctColgroupWidth();
    }

    public render() {
        const { columns, enableResize = false, ...resProps } = this.props;
        return <div ref={this.tableWrapRefs} className={this.props.unifiedId}>
            <Table
                bordered
                {...resProps}
                components={this.components}
                columns={this.columns as any}
            />
        </div>;
    }
}
