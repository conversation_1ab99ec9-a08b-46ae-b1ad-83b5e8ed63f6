
import { checkObjectValueUnique } from 'libs';
/**
 * 所有 kdev 表格 的 key
 * 问题：给每一列设置width，比例不生效；解决方法：修改 key，后面跟上年月日（20211214） 作为版本，重新调试即可
 */
export enum KEYS_TABLE {
    DATASTRUCTURE_DETAIL_KEYS_TABLE = 'DATASTRUCTURE_DETAIL_KEYS_TABLE', // 数据结构详情表格key

    REQ_CASE_HEADER_KEYS_TABLE = 'REQ_CASE_HEADER_KEYS_TABLE', // case请求header参数表格key
    REQ_CASE_BODY_KEYS_TABLE = 'REQ_CASE_BODY_KEYS_TABLE', // case请求body参数表格key
    REQ_CASE_FORM_KEYS_TABLE = 'REQ_CASE_FORM_KEYS_TABLE', // case请求form参数表格key
    REQ_CASE_QUERY_KEYS_TABLE = 'REQ_CASE_QUERY_KEYS_TABLE', // case请求query参数表格key
    REQ_CASE_PATH_KEYS_TABLE = 'REQ_CASE_PATH_KEYS_TABLE', // case请求path参数表格key
    RES_CASE_HEADER_KEYS_TABLE = 'RES_CASE_HEADER_KEYS_TABLE', // case返回header参数表格key
    RES_CASE_BODY_KEYS_TABLE = 'RES_CASE_BODY_KEYS_TABLE', // case返回body参数表格key

    REQ_API_HEADER_KEYS_TABLE = 'REQ_API_HEADER_KEYS_TABLE', // api请求header参数表格key
    REQ_API_BODY_KEYS_TABLE = 'REQ_API_BODY_KEYS_TABLE', // api请求body参数表格key
    REQ_API_FORM_KEYS_TABLE = 'REQ_API_FORM_KEYS_TABLE', // api请求form参数表格key
    REQ_API_QUERY_KEYS_TABLE = 'REQ_API_QUERY_KEYS_TABLE', // api请求query参数表格key
    REQ_API_PATH_KEYS_TABLE = 'REQ_API_PATH_KEYS_TABLE', // api请求path参数表格key
    RES_API_HEADER_KEYS_TABLE = 'RES_API_HEADER_KEYS_TABLE', // api返回header参数表格key
    RES_API_BODY_KEYS_TABLE = 'RES_API_BODY_KEYS_TABLE', // api返回body参数表格key
}
checkObjectValueUnique(KEYS_TABLE, 'KEYS_TABLE');
