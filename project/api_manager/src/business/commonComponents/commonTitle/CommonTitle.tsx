import React from 'react';
import css from './CommonTitle.less';

interface IProps {
    text: string | React.ReactNode;
    className?: string;
    extra?: React.ReactNode;
    size?: string;
    style?: React.CSSProperties;
    bold?: string | number;
}

export class KdevTitle extends React.Component<IProps, any> {
    public render() {
        return (
            <div
                className={ [
                    css.kdevTitle,
                    this.props.size === 'small' && css.kdevTitlesmall,
                    this.props.className
                ].join(' ') }
                style={ {
                    ...this.props.style
                } }
            >
                { this.props.text }
                { this.props.extra || null }
            </div>
        );
    }
}
