import React, { useEffect, useRef, useState } from 'react';
import { Table } from 'antd';
import type { TableProps } from 'antd/lib/table';
import { useHover } from '@/business/commonHooks';

function VTableRow({ ...props }) {
    const trRef = useRef<HTMLTableRowElement | null>(null);
    const [isIntersecting, setIsIntersecting] = useState<boolean>(false);
    const isHover = useHover(trRef);

    const obCallback = (entires: IntersectionObserverEntry[]) => {
        const entry = entires[0];
        entry.isIntersecting && setIsIntersecting(entry.isIntersecting);
    };

    const ob: IntersectionObserver = new IntersectionObserver(obCallback, {
        root: null,
        rootMargin: '0px',
        threshold: 0
    });

    useEffect(() => {
        if (trRef.current) {
            ob.observe(trRef.current);
            return () => {
                ob.disconnect();
            };
        }
    }, [trRef.current]);

    useEffect(() => {
        isIntersecting && ob.disconnect();
    }, [isIntersecting]);

    return (
        <tr {...props} ref={trRef}>
            {
                isIntersecting
                    ? props?.children
                    : (props?.children).map((item: any) => <td key={item.key} />)
            }
            {/* {props.children.map((item: any) => <td key={item.key} className={css.tdSkeleton} />)} */}
        </tr>
    );
}

function VTable(props: TableProps<any>) {
    return (
        <Table
            bordered
            pagination={false}
            components={
                (props?.dataSource && props.dataSource.length)
                    ? { body: { row: VTableRow } }
                    : undefined
            }
            {...props}
        />
    );
}

export { VTable };
