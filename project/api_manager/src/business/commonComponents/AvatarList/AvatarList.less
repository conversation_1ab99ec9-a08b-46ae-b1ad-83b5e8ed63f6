.avatarGroup {
    display: inline-block;

    .ellipIcon {
        background-color: #f5f7fa;
        color: #898a8c;
        font-size: 24px;
        transition:all 0.3s;
        border: 2px solid #ffffff;
      }

    .ellipIcon:hover {
      background-color: #CCD8FF;
      color: #5990FF;

    }
    .ellipIconActive {
        background-color: #CCD8FF;
        color: #5990FF;

      }
}
.popReviewer {
    padding: 8px 0;
    max-height: 286px;
    overflow: auto;
    min-width: 168px;

    :global {
        .ant-dropdown-menu-item {
            padding: 8px 16px;
        }
        .ant-dropdown-menu-item:hover {
            background: #f5f7fa;
        }
    }

    .reviewerListAvatar {
        // display: inline-block;
        width: 36px;
        height: 36px;
        margin-right: 8px;
        line-height: 36px;

    }

  }
.hoverKim {
  color: #252626;
}
.hoverKim:hover {
    color: #327DFF;
}

.avatarBox {
  :global {
    .ant-popover-inner-content {
      white-space: nowrap;
    }
  }
}
.singleUser{
  display: flex;
  align-items: center;
  .username{
    display: inline-block;
    margin-left: 4px;
    color: #252626;
  }
}
