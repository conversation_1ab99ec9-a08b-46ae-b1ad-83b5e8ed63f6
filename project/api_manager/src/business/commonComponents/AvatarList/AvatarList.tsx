import React from 'react';
import { Ava<PERSON>, Col, Dropdown, <PERSON>u, Popover, Row } from 'antd';
import css from './AvatarList.less';
import { EllipsisOutlined } from '@ant-design/icons';
import { RealDOM } from '@libs/utils';

function onClickItem(e, item, kim) {
    if (kim) {
        window.location.href = `kim://username?username=${item.username}`;
    }
    e.stopPropagation();
}

function showReviewers(reviewers, kim, size = 'default') {
    return <Menu className={css.popReviewer}>
        {reviewers.map(item => {
            return (
                <Menu.Item key={item?.username} onClick={(e) => onClickItem(e, item, kim)}>
                    <Row align="middle" justify={'space-between'}>
                        <Col>
                            <Avatar src={item?.avatarUrl} className={css.reviewerListAvatar} size={size}>
                                {item?.name}
                            </Avatar>
                            {item?.name}
                        </Col>
                        {
                            kim &&
                            <Col>
                                <img src={'https://cdnfile.corp.kuaishou.com/kc/files/a/QA-SERVEREE-FE-IN/kim.png'}
                                    style={{ height: '18px', marginLeft: '6px' }} />
                            </Col>
                        }
                    </Row>
                </Menu.Item>
            );
        })}
    </Menu>;
}

function renderPopoverName(ele, kim) {
    return (
        kim ?
            <a className={css.hoverKim} href={`kim://username?username=${ele.username}`}>
                <img src={'https://cdnfile.corp.kuaishou.com/kc/files/a/QA-SERVEREE-FE-IN/kim.png'}
                    style={{ height: '18px', marginRight: '6px' }} />
                {ele.name + ' ' + ele.username}
            </a>
            : <span>{ele.name + ' ' + ele.username}</span>
    );
}

interface IProps_AvatarList {
    reviewers: any[];
    max?: number;
    kim?: boolean;
    popupContainerInBody?: boolean; // Dropdown是否挂载在body上，默认为false
    size?: number | 'large' | 'small' | 'default';
    style?: React.CSSProperties;
}

/**
 * 带头像的用户列表
 * @param props
 * @constructor
 */
export function AvatarList(props: IProps_AvatarList) {
    const domOut = new RealDOM();
    const max = props.max || 3;
    if (!props.reviewers) {
        return null;
    }
    if (props.reviewers.length === 1) {
        const item = props.reviewers[0];
        return (
            <div className={css.singleUser}>
                <Avatar src={item.avatarUrl} size={props.size}>{item.name}</Avatar>
                <span className={css.username}>{item.name}</span>
            </div>
        );
    }
    return (
        <div ref={domOut.setDom} className={css.avatarBox} style={{ ...props.style }}>
            <Avatar.Group
                maxStyle={{ color: '#f56a00', backgroundColor: '#fde3cf' }}
                maxPopoverPlacement={'bottom'}
                className={css.avatarGroup}
            >
                {
                    props.reviewers.length <= max ?
                        props.reviewers.map(item => {
                            return (
                                <Popover
                                    content={() => renderPopoverName(item, props.kim)}
                                    key={item.username}
                                    placement="topRight"
                                    getPopupContainer={() => domOut.dom as HTMLDivElement}
                                >
                                    <Avatar src={item.avatarUrl} size={props.size}>{item.name}</Avatar>
                                </Popover>
                            );
                        })
                        :
                        <>
                            {
                                props.reviewers.slice(0, max - 1).map(item => {
                                    return (
                                        <Popover
                                            content={() => renderPopoverName(item, props.kim)}
                                            key={item.username}
                                            placement="topRight"
                                            getPopupContainer={() => domOut.dom as HTMLDivElement}
                                        >
                                            <Avatar src={item.avatarUrl} size={props.size}>{item.name}</Avatar>
                                        </Popover>
                                    );
                                })
                            }
                            <Dropdown
                                overlay={showReviewers(props.reviewers, props.kim, props.size)}
                                placement="bottomCenter"
                                arrow
                                getPopupContainer={() => props.popupContainerInBody ? document.body :
                                    domOut.dom as HTMLDivElement}
                            >
                                <Avatar className={css.ellipIcon} icon={<EllipsisOutlined />} size={props.size} />
                            </Dropdown>
                        </>
                }
            </Avatar.Group>
        </div>
    );

}
