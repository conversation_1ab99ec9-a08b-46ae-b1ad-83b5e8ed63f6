import React, { MouseEvent, useEffect, useState } from 'react';
import { Button, Tooltip } from 'antd';
import type { ButtonProps } from 'antd/lib/button';
import { KdevIconFont } from '@/business/commonComponents';
import { common_system_collectmian, common_system_collection } from '@kid/enterprise-icon/icon/output/icons';

type TFollowResult = 'SUCCESS' | 'FAIL';

interface IFollowBtnProps extends ButtonProps {
    follow: boolean;
    onFollow?(follow: boolean, e: MouseEvent<HTMLElement>): Promise<TFollowResult>;
    iconStyle?: React.CSSProperties;
    tooltipTitle?: {
        follow: string;
        unFollow: string;
    };
}

function FollowBtn({ follow, onFollow, iconStyle, tooltipTitle, ...props }: IFollowBtnProps) {
    const [isFollow, setIsFollow] = useState<boolean>(follow);
    const [loading, setLoading] = useState<boolean>(false);

    const onClickFollowBtn = async (e: MouseEvent<HTMLElement>) => {
        if (onFollow) {
            setLoading(true);
            const result = await onFollow(!isFollow, e);
            result === 'SUCCESS' && setIsFollow(!isFollow);
            setLoading(false);
        } else {
            setIsFollow(!isFollow);
        }
    };

    useEffect(() => {
        setIsFollow(follow);
    }, [follow]);

    return (
        <Tooltip title={
            tooltipTitle
                ? isFollow ? tooltipTitle.follow : tooltipTitle.unFollow
                : ''
        }>
            <Button
                icon={
                    <KdevIconFont
                        id={isFollow ? common_system_collectmian : common_system_collection}
                        color={isFollow ? '#FFAA00' : ''}
                        style={iconStyle}
                    />
                }
                loading={loading}
                onClick={onClickFollowBtn}
                {...props}
            />
        </Tooltip>
    );
}

export { FollowBtn };
