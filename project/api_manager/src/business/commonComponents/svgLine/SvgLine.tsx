import React from 'react';
import css from './SvgLine.less';

interface ISvgContentPoint {
    start: [number, number];
    end: [number, number];
    color?: string;
    style?: 'solid' | 'dotted';
    translateX?: number;
    leftLength?: number;
    rightLength?: number;
}

interface ISvgLineProps {
    points: Array<ISvgContentPoint>;
    type?: 'straight' | 'bezier';
}

export class SvgLine extends React.Component<ISvgLineProps, any> {
    public render(): React.ReactElement {
        const type = this.props.type || 'straight';
        return <div className={css.svgLine}>
            <svg className={css.svgContent}>
                {
                    this.props.points.map((item, index) => {
                        const stroke = item.style === 'dotted' ? '#898a8c' : (item.color || '#898a8c');
                        const startX = item.start[0];
                        const startY = item.start[1];
                        const endX = item.end[0];
                        const endY = item.end[1];

                        // 贝塞尔曲线
                        const temp = Math.abs(endX - startX) / 2;
                        const d = `M${[...item.start]} C${temp},${startY} ${temp},${endY} ${[...item.end]}`;

                        // 圆角折线
                        const radius = 8;
                        const lWidth = (item.leftLength || (endX - startX) / 2) - radius;
                        const rWidth = (item.rightLength || (endX - startX) / 2) - radius;
                        const height = Math.abs(endY - startY) - 2 * radius;
                        const d1 = `M${[...item.start]} l ${lWidth},0 a ${radius},${radius} 0 0,${endY >
                            startY ? 1 : 0} ${radius},${endY > startY ? radius : -radius} l 0,${endY > startY ?
                                height : -height} a ${radius},${radius} 0 0,${endY > startY ? 0 :
                                    1} ${radius},${endY > startY ? radius : -radius} l ${rWidth},0`;

                        return (startX === endX || startY === endY) ?
                            <line
                                key={index}
                                x1={startX} y1={startY} x2={endX} y2={endY}
                                stroke={stroke}
                                strokeDasharray={
                                    item.style === 'dotted' ? '2,2' : '0'
                                }
                                strokeWidth="2px"
                                fill="none"
                                style={{ transform: `translateX(${item.translateX}px)` }}
                            />
                            :
                            <path
                                key={index}
                                d={type === 'straight' ? d1 : d}
                                stroke={stroke}
                                strokeDasharray={
                                    item.style === 'dotted' ? '2,2' : '0'
                                }
                                strokeWidth="2px"
                                fill="none"
                                style={{ transform: `translateX(${item.translateX}px)` }}
                            />
                            ;
                    })
                }
            </svg>
        </div>;
    }
}
