import React, { MouseEvent } from 'react';
import css from './BackHeader.less';
import { KdevIconFont } from '@/business/commonComponents';
import { common_system_arrowback } from '@kid/enterprise-icon/icon/output/icons';
import classNames from 'classnames';

interface IApiBackHeaderProps {
    title: string | React.ReactElement | React.ReactNode;
    extra?: string | React.ReactElement | React.ReactNode;
    height?: number;
    onBackClick?(e: MouseEvent<HTMLSpanElement>): void;
    className?: string;
}

function ApiBackHeader(props: IApiBackHeaderProps) {
    return (
        <div
            className={classNames(css.apiBackHeader, props.className)}
            style={{height: props.height ?? 56}}
        >
            <div className={css.titleBox}>
                {
                    props.onBackClick &&
                    <span className={css.backIconBox} onClick={props.onBackClick}>
                        <KdevIconFont id={common_system_arrowback} />
                        <span style={{color: '#000000d9', marginLeft: '10px'}}>{props.title}</span>
                    </span>
                }
            </div>
            {props.extra || ''}
        </div>
    );
}

export { ApiBackHeader };
