.aceEditorWrap {
    height: 100%;
    overflow: hidden;
    border: 1px solid #EBEDF0;
    border-radius: 4px;
    display: flex;
    flex-direction: column;

    :global {
        .ace-xcode .ace_gutter {
            background-color: #FAFCFF;
        }
    }

    .optionsLine {
        display: flex;
        flex-wrap: nowrap;
        align-items: center;
        height: 40px;
        padding-left: 12px;
        border-bottom: 1px solid #EBEDF0;
        position: relative;

        .jsonTip {
            position: absolute;
            right: 16px;
            top: 9px;
            color: #575859;
        }

        .jsonCommonIcon {
            color: #326BFB !important;
            margin-right: 4px;
        }
    }

    .optionWarp {
        height: 40px;
        margin-right: 16px;
        display: flex;
        flex-wrap: nowrap;
        align-items: center;
        cursor: pointer;
    }

    .icon {
        margin-right: 4px;
        color: #898A8C !important;
    }
}