/**
 * @description 回退事件
 * <AUTHOR>
 */
import { router } from '@libs/mvvm';
import * as QS from 'qs';

export const goBack = (url: string, hrefType: number = 1, params?: object) => {
    // if (window.history.length === 1) {
    //     router.push(url, params);
    // } else {
    //     window.history.go(-1);
    // }
    switch (hrefType) {
        case 1:
            router.push(url, params);
            break;
        case 2:
            if (params && url.indexOf('?')) {
                Object.assign(params, QS.parse(url.split('?')[1]));
                location.href = url + '?' + QS.stringify(params);
            } else {
                location.href = url;
            }
            break;
    }
};
