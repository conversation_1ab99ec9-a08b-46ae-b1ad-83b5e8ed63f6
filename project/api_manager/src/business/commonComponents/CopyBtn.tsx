import React, { <PERSON><PERSON><PERSON>Hand<PERSON> } from 'react';
import { Button, Tooltip, message } from 'antd';
import { KdevIconFont } from '@/business/commonComponents/KdevIconFont';
import Clipboard from 'clipboard';
import classname from 'classnames';

interface IProps {
    copyContent: string;
    className?: string;
    text?: string;
    type?: 'text' | 'link' | 'ghost' | 'default' | 'primary' | 'dashed' | undefined;
    icon?: boolean | React.ReactElement;
    size?: 'large' | 'middle' | 'small';
    onClick?: MouseEventHandler<HTMLElement>;
    title?: string;
}

export const CopyBtn = (props: IProps) => {
    new Clipboard('.copybtn');
    let icon: boolean | React.ReactElement = <KdevIconFont id={'#iconcopy'} />;
    if (typeof props.icon === 'boolean' || props.icon) {
        icon = props.icon;
    }

    const onCopySussce = () => {
        message.success('复制成功～');
    };

    return (
        <Tooltip title={props.title || ''}>
            <Button
                type={props.type || 'link'}
                icon={icon}
                className={classname('copybtn', props.className)}
                data-clipboard-text={props.copyContent}
                onClick={props.onClick ?? onCopySussce}
                size={props.size || 'middle'}
            >
                {props?.text || ''}
            </Button>
        </Tooltip>
    );
};
