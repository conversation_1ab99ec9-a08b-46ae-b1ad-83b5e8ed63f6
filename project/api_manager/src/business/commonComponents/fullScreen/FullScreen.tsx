import React from 'react';
import classNames from 'classnames';
import { FullscreenOutlined, FullscreenExitOutlined } from '@ant-design/icons';
import { Button } from 'antd';
import css from './FullScreen.less';
import { Bind } from 'lodash-decorators';

interface IProps {
    isUseFullScreen?: boolean;
    onChange?(isFullScreen: boolean): void;
}

export class FullScreen extends React.Component<IProps> {
    public state = {
        isFullScreen: false
    };

    @Bind
    private onChangeIsFullScreen(): void {
        const { onChange } = this.props;
        this.setState({
            isFullScreen: !this.state.isFullScreen
        });
        onChange && onChange(!this.state.isFullScreen);
    }

    public render(): React.ReactNode {
        const { isUseFullScreen } = this.props;
        return (
            <div
                className={
                    this.state.isFullScreen
                        ? classNames(css.fullScreen, css.isUseFullScreen)
                        : css.fullScreen
                }
            >
                {
                    (isUseFullScreen ?? true) &&
                    <Button
                        icon={
                            this.state.isFullScreen
                                ? <FullscreenExitOutlined />
                                : <FullscreenOutlined />
                        }
                        className={css.fullScreenBtn}
                        onClick={this.onChangeIsFullScreen}
                    />
                }
                {this.props.children}
            </div>
        );
    }
}
