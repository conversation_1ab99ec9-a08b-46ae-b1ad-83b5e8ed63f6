import { Tooltip } from 'antd';
import { TooltipPlacement } from 'antd/lib/tooltip';
import React from 'react';
import { KdevIconFont } from '@/business/commonComponents/KdevIconFont';
import css from './QuestionTips.less';

interface IProps {
    title: string | React.ReactNode;
    placement?: TooltipPlacement;
    overlayClassName?: string;
    visible?: boolean;
    popDom?: React.RefObject<HTMLDivElement>;
}

export enum questionTipsConfig {
    MOCK_REQUEST_PARAMS_FILTER = '如果只有一条mock数据存在，可忽略此字段；当有多条MOCK数据存在时， 根据入参匹配使用哪条数据，如果都没匹配上，则返回默认数据',
    TEST_CASE_VALUE_CHECK_RULE = '时间戳：在执行测试用例时，当前字段会被替换为13位当前时间戳\n随机数：在执行测试用例时，当前字段会被替换为0-8位随机数值\nMD5：在执行测试用例时，当前字段的值会被MD5算法加密，如果值为空则加密一个随机数',
    MOCK_PROXY_CONFIG_DESC = 'web mock代理工具是专门为前端开发的代理接口，可以等同于webpack的proxyTable工具。相比proxyTable，其优点是可以动态的配置请求的代理地址，并支持一键将接口path代理到对应的mock接口中，减少了频繁复制粘贴地址的麻烦。',
    EXEC_PIPELINE_GET_APIS = '1、API详情页增加【获取最新接口】按钮和commiti信息\n2、点击【获取最新API】，弹出选择流水线弹框，运行流水线解析API\n3、手动解析API不支持并发操作，如果正在解析中，按钮状态置灰不可点击，hover显示提示信息；解析完成后，按钮恢复正常状态\n4、帮助信息内容：获取当前分支最新代码的API信息\n5、commitid为最近一次的API解析的信息，可点击，跳转到KDev commit详情页',
}

export const QuestionTips = (props: IProps) => {
    return (
        <Tooltip
            {...props}
            getPopupContainer={() => props.popDom?.current || document.body}
        >
            <span style={{ display: 'flex', alignItems: 'center' }}>
                <KdevIconFont id="#iconquestion" className={css.iconquestion} />
            </span>
        </Tooltip>
    );
};
