import React, { useEffect, useState } from 'react';
import { Ava<PERSON>, Button } from 'antd';
import { KdevIconFont } from '@/business/commonComponents';
import { common_system_add_o } from '@kid/enterprise-icon/icon/output/icons';
import css from './Owner.less';
import { team } from '@/business/global';
import classNames from 'classnames';
import { CreateMaintainer, CreateMaintainerModel } from '@/business/createMaintainer/CreateMaintainer';

interface IUserInfo {
    id: number;
    name: string;
    username: string;
    email: string;
    photo: string;
}

interface IOwnerProps {
    value?: IUserInfo | null;
    onChange?: (value: IUserInfo) => void;
    isTemplate?: boolean;
    avatarSize?: 24 | 32;
    isAdd?: boolean;
    className?: string;
}

async function formatLoginUser(): Promise<IUserInfo> {
    const { userId, chineseName, userName, email, photo } = await team.awaitGetUserInfo();
    return {
        id: userId,
        name: chinese<PERSON><PERSON>,
        username: userName,
        email,
        photo
    };
}

function Owner(props: IOwnerProps) {
    const [ownerInfo, setOwnerInfo] = useState<IUserInfo>({
        photo: '',
        name: '',
        username: '',
        email: '',
        id: -1
    });
    const createMaintainerModel = new CreateMaintainerModel();

    useEffect(() => {
        props.value && setOwnerInfo({ ...ownerInfo, ...props.value });
    }, [props.value]);

    return (
        <div className={classNames(css.ownerBox, props.className)}>
            <Avatar size={props.avatarSize} src={ownerInfo?.photo}>
                {Boolean(ownerInfo?.name?.length) && ownerInfo?.name[0]}
            </Avatar>
            <span className={css.name}>{ownerInfo?.name}</span>
            {
                (props.isAdd ?? true) &&
                <CreateMaintainer
                    model={createMaintainerModel}
                    btnNode={
                        <Button
                            type="link"
                            icon={<KdevIconFont id={common_system_add_o} />}
                            disabled={props.isTemplate}
                            style={{ padding: 0 }}
                        />
                    }
                    handelSearchSelect={(owner: IUserInfo) => {
                        setOwnerInfo(owner);
                        props.onChange?.(owner);
                    }}
                />
            }
        </div>
    );
}

export { Owner, IUserInfo, formatLoginUser };
