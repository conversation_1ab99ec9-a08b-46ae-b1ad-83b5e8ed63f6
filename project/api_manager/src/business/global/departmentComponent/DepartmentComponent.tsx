import React from 'react';
import { observer } from 'mobx-react';
import { AView } from 'libs';
import { DepartmentComponentModel } from './DepartmentComponentModel';
import { Select } from 'antd';
import Bind from 'lodash-decorators/bind';

const { Option } = Select;

@observer
export class DepartmentComponent extends AView<DepartmentComponentModel> {

    @Bind
    public onSelectDepart(departmentId) {
        this.model.selectDepart(departmentId);
    }

    @Bind
    public componentDidMount(): void {
        this.model.ajax_queryDepartmentList();
    }

    public render(): React.ReactNode {
        return !this.model.hidden && (
            <Select
                value={this.model.departmentId || undefined}
                style={{ width: '200px' }}
                placeholder={'请选择部门'}
                disabled={this.model.disabled}
                onSelect={this.onSelectDepart}
                virtual={false}
            >
                {
                    this.model.departmentList.map(item => {
                        return <Option
                            key={item.departmentId}
                            value={item.departmentId}>
                            {item.departmentName}
                        </Option>;
                    })
                }
            </Select>
        );
    }
}
