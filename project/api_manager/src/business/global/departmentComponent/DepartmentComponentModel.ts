import { AViewModel, router } from 'libs';
import { action, observable, runInAction } from 'mobx';
import * as QS from 'query-string';
import {
    nsMockManageApiManageDepartmentQueryDepartmentListGet
} from '@/remote';

interface IDepartmentList {
    departmentId: number;
    departmentName: string;
}

export class DepartmentComponentModel extends AViewModel {
    @observable public departmentId: number = 0;
    @observable public departmentList: IDepartmentList[] = [];
    @observable public hidden: boolean = true;
    @observable public loaded: boolean = false;
    @observable public disabled: boolean = false;

    @observable public open:boolean = false;

    constructor() {
        super();
        this.init();
    }

    @action
    public init() {
        this.departmentId = Number(localStorage.getItem('departmentId'));
    }

    /**
     * 获取部门列表
     */
    @action.bound
    public async ajax_queryDepartmentList() {
        try {
            const result = await nsMockManageApiManageDepartmentQueryDepartmentListGet.remote({});
            runInAction(() => {
                this.departmentList = result.departmentList || [];
                if (this.departmentList.length) {
                    const boo = this.departmentList.some(item => item.departmentId === this.departmentId);
                    if (!boo) {
                        this.selectDepart(this.departmentList[0].departmentId);
                    }
                }
                this.loaded = true;
            });
        } catch (e) {
            runInAction(() => {
                this.loaded = true;
            });
        }
    }

    @action
    public selectDepart(departmentId) {
        this.departmentId = departmentId;
        localStorage.setItem('departmentId', this.departmentId.toString());
        const paramObj = QS.parse(location.search);
        router.reload(location.pathname + '?', { ...paramObj, ...{ departmentId: this.departmentId } });
    }

    @action
    setOpen(open: boolean) {
        this.open = open;
    }
}
