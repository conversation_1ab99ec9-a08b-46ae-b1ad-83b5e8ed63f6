import React from 'react';
import { observer } from 'mobx-react';
import { AView } from 'libs';
import { DepartmentComponentModel } from './DepartmentComponentModel';
import { Select } from 'antd';
import Bind from 'lodash-decorators/bind';
import { HeaderCard } from '../HeaderCard';
import { setSiderCollapsed } from 'kdev-fe-common/src/component/KDevPageLayout/KDevPageLayout';

const { Option } = Select;

interface IProps {
    collapse: boolean
}

@observer
export class DepartmentComponent extends AView<DepartmentComponentModel, IProps> {

    @Bind
    public onSelectDepart(departmentId) {
        this.model.selectDepart(departmentId);
    }

    @Bind
    public componentDidMount(): void {
        this.model.ajax_queryDepartmentList();
    }


    private renderCollapsedBox() {
        return <HeaderCard onClick={() => {
            setSiderCollapsed(false);
            this.model.setOpen(true);
        }}/>;
    }

    public render(): React.ReactNode {
        const model = this.model;
        if (model.hidden) {
            return null;
        }

        if (this.props.collapse) {
            return this.renderCollapsedBox()
        }

        return  <Select
            value={this.model.departmentId || undefined}
            style={{ width: '100%' }}
            placeholder={'请选择部门'}
            disabled={this.model.disabled}
            onSelect={this.onSelectDepart}
            virtual={false}
            onDropdownVisibleChange={(visible) => {
                this.model.setOpen(visible);
            }}
            open={this.model.open}
        >
            {
                this.model.departmentList.map(item => {
                    return <Option
                        key={item.departmentId}
                        value={item.departmentId}>
                        {item.departmentName}
                    </Option>;
                })
            }
        </Select>;
    }
}
