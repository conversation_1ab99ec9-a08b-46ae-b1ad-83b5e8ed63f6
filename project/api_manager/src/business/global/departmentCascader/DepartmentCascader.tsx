import React from 'react';
import { observer } from 'mobx-react';
import { AView } from 'libs';
import { DepartmentCascaderModel } from './DepartmentCascaderModel';
import { Cascader, Select } from 'antd';
import Bind from 'lodash-decorators/bind';
import css from './DepartmentCascader.less';

const { Option } = Select;

@observer
export class DepartmentCascader extends AView<DepartmentCascaderModel> {

    @Bind
    public onChangeParentDepartmentInfo(department, info) {
        this.model.setFields({
            topDepartmentId: department[0]
        });
        this.model.changeParentDepartmentInfo(department, info[info.length - 1].departmentName);
    }

    @Bind
    public componentDidMount(): void {
        this.model.ajax_queryAllDepartmentList();
    }

    public render(): React.ReactNode {
        const model = this.model;

        return !model.hidden && <Cascader
            placeholder={ '请选择父级部门' }
            options={ model.cascaderDepartmentList }
            showSearch={ false }
            fieldNames={ {
                label: 'departmentName',
                value: 'departmentId',
                children: 'children',
            } }
            disabled={ model.disabled }
            value={ model.cascaderDepartmentId }
            onChange={ this.onChangeParentDepartmentInfo }
            // loadData={ this.loadDataCascaderDepartList }
            changeOnSelect
            className={ css.deptCascader }
            popupClassName={ css.departmentCascader }
            expandTrigger={ 'hover' }
            allowClear={ false }
        />;
    }
}
