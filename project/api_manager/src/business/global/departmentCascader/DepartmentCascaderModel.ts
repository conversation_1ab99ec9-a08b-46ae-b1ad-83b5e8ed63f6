import { AViewModel, router } from 'libs';
import { action, observable, runInAction } from 'mobx';
import * as QS from 'query-string';
import {
    nsMockManageApiManageDepartmentQueryAllDepartmentListPost
} from '@/remote';
// import { eachNode } from '@libs/utils';

interface ICascaderDepartmentList {
    departmentId: number;
    departmentName: string;
}

export class DepartmentCascaderModel extends AViewModel {
    @observable public cascaderDepartmentId: number[] = [];
    @observable public cascaderDepartmentName: string = '';
    @observable public cascaderDepartmentList: ICascaderDepartmentList[] = [];
    @observable public hidden: boolean = true;
    @observable public loaded: boolean = false;
    @observable public disabled: boolean = false;
    @observable public departmentListLoading: boolean = false;
    @observable public departmentSubList: Array<any> = [];
    @observable public topDepartmentId: number = 0;

    @observable public open:boolean = false;

    constructor() {
        super();
        this.init();
    }

    @action
    public init() {
        try {
            const jsonString = localStorage.getItem('cascaderDepartmentId') || '';
            this.cascaderDepartmentId = Array.isArray(JSON.parse(jsonString)) ? JSON.parse(jsonString) : [];
        } catch (e) {
            this.cascaderDepartmentId = [];
        }
    }

    /**
     * 获取全部部门列表
     */
    @action.bound
    public async ajax_queryAllDepartmentList() {
        try {
            const result = await nsMockManageApiManageDepartmentQueryAllDepartmentListPost.remote({});
            runInAction(() => {
                this.cascaderDepartmentList = result.treeInfos || [];
                if (!this.cascaderDepartmentId.length && this.cascaderDepartmentList.length) {
                    // this.changeParentDepartmentInfo([this.cascaderDepartmentList[0].departmentId],
                    //     this.cascaderDepartmentList[0].departmentName);
                    this.loaded = false;
                } else {
                    this.loaded = true;
                }
            });
        } catch (e) {
            runInAction(() => {
                this.loaded = true;
            });
        }
    }

    @action.bound
    public changeParentDepartmentInfo(cascaderDepartmentId, cascaderDepartmentName, noReload?: boolean) {
        this.cascaderDepartmentId = cascaderDepartmentId;
        this.cascaderDepartmentName = cascaderDepartmentName;
        localStorage.setItem('cascaderDepartmentId', JSON.stringify(this.cascaderDepartmentId));
        if (!noReload) {
            const paramObj = QS.parse(location.search);
            router.reload(location.pathname + '?',
                { ...paramObj, ...{ departmentId: this.cascaderDepartmentId[this.cascaderDepartmentId.length - 1] } });
        }
    }

    @action
    setOpen(open: boolean) {
        this.open = open;
    }
}
