import React from 'react';
import { observer } from 'mobx-react';
import { AView } from 'libs';
import { DepartmentCascaderModel } from './DepartmentCascaderModel';
import { Cascader, Select } from 'antd';
import Bind from 'lodash-decorators/bind';
import css from './DepartmentCascader.less';
import { HeaderCard } from '../HeaderCard';
import { setSiderCollapsed } from 'kdev-fe-common/src/component/KDevPageLayout/KDevPageLayout';

const { Option } = Select;

interface IProps {
    collapse: boolean
}

@observer
export class DepartmentCascader extends AView<DepartmentCascaderModel, IProps> {

    @Bind
    public onChangeParentDepartmentInfo(department, info) {
        this.model.setFields({
            topDepartmentId: department[0]
        });
        this.model.changeParentDepartmentInfo(department, info[info.length - 1].departmentName);
    }

    @Bind
    public componentDidMount(): void {
        this.model.ajax_queryAllDepartmentList();
    }

    private renderCollapsedBox() {
        return <HeaderCard onClick={() => {
            setSiderCollapsed(false);
            this.model.setOpen(true);
        }}/>;
    }

    public render(): React.ReactNode {
        const model = this.model;
        if (model.hidden) {
            return null;
        }

        if (this.props.collapse) {
            return this.renderCollapsedBox()
        }

        return <Cascader
            placeholder={ '请选择父级部门' }
            options={ model.cascaderDepartmentList }
            showSearch={ false }
            fieldNames={ {
                label: 'departmentName',
                value: 'departmentId',
                children: 'children',
            } }
            disabled={ model.disabled }
            value={ model.cascaderDepartmentId }
            onChange={ this.onChangeParentDepartmentInfo }
            // loadData={ this.loadDataCascaderDepartList }
            changeOnSelect
            className={ css.deptCascader }
            popupClassName={ css.departmentCascader }
            expandTrigger={ 'hover' }
            allowClear={ false }
            onDropdownVisibleChange={(visible) => {
                this.model.setOpen(visible);
            }}
            open={model.open}
        />;
    }
}
