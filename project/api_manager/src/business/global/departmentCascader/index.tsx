import React from 'react';
import { DepartmentCascader } from './DepartmentCascader';
import { DepartmentCascaderModel } from './DepartmentCascaderModel';
import { isKDevPageV2 } from 'kdev-fe-common/src/component/KDevPageLayout/KDevPageLayout';
import { DepartmentCascader as  DepartmentCascaderV2 } from './DepartmentCascaderV2';

const departmentCascaderModel = new DepartmentCascaderModel();
export const departmentCascader = {
    renderSelect(collapse?: boolean): React.ReactNode {
        if (isKDevPageV2) {
            return <DepartmentCascaderV2 model={ departmentCascaderModel } collapse={collapse || false} />
        }

        return <DepartmentCascader model={ departmentCascaderModel } />;
    },
    getDepartmentFullId(): number[] {
        return departmentCascaderModel.cascaderDepartmentId;
    },
    getDepartmentId(): number {
        return Number(
            departmentCascaderModel.cascaderDepartmentId[departmentCascaderModel.cascaderDepartmentId.length - 1]) || 0;
    },
    setDepartment(cascaderDepartmentId: number[], cascaderDepartmentName: string, noReload?: boolean): void {
        departmentCascaderModel.changeParentDepartmentInfo(cascaderDepartmentId, cascaderDepartmentName,
            noReload || true);
    },
    departmentCascaderModel
};
