import { departmentComponent } from './departmentComponent';
import { departmentCascader } from './departmentCascader';
export * from './team';
export * from './departmentComponent';
export * from './departmentCascader';
import { ENV } from '@/index.config';

export const GLOBAL = {
    loaded(): boolean {
        if (!departmentComponent.departmentComponentModel.hidden) {
            return departmentComponent.departmentComponentModel.loaded;
        }
        if (!departmentCascader.departmentCascaderModel.hidden) {
            return departmentCascader.departmentCascaderModel.loaded;
        }
        return true;
    },
    IS_ONLINE: ENV.IS_ONLINE,
};
