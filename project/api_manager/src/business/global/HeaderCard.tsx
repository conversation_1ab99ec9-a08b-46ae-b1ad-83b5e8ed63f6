import React from 'react';

import classNames from 'classnames';
import { KdevIconFont } from '@kdev-fe-common/component/KdevIconFont';
import css from './HeaderCard.less';
import { common_system_arrowdownmian } from '@kid/enterprise-icon/icon/output/icons';

export function HeaderCard(props:{onClick: any}) {
    return (
        <div className={css.menuTopInfoBox}>
            <div
                className={classNames(
                    css.menuTopInfo,
                    css.collapsedMenuTopInfo,
                )}
                onClick={props.onClick}
            >
                <div className={css.menuIcon}>
                    <KdevIconFont id={common_system_arrowdownmian} />
                </div>
            </div>
        </div>
    );
}
