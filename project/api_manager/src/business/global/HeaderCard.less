.menuTopInfoBox {
    border-radius: 4px;
    overflow: hidden;
    border-radius: 4px;
    border: 1px solid #E2F0FF;
    background: linear-gradient(180deg, #D7E7FF 0%, #EEF5FF 100%);
    box-shadow: 0px 2px 2px 0px rgba(12, 18, 31, 0.02);
    width: var(--kdev-sider-content-collapsed-width);
}

.menuTopInfo {
    display: flex;
    align-items: center;
    cursor: pointer;
    background-image: url(/static/assets/img/menu-top-repo-change.svg);
}

.collapsedMenuTopInfo {
    width: var(--kdev-sider-content-collapsed-width);
    padding: 7px 8px;
    overflow: hidden;
}

.menuIcon {
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 6px !important;
    background: #fff;
    margin-right: 0px !important;
    width: 32px;
    height: 32px;

    font-size: 16px;
    color: #575859;
}