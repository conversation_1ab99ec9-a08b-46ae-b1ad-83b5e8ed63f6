import React from 'react';
import Icon from '@ant-design/icons';

const DefaultEmptySvg = () => (
    <svg width="200px" height="140px" viewBox="0 0 200 140" version="1.1" xmlns="http://www.w3.org/2000/svg">
        <title>默认占位图</title>
        <desc>Created with Sketch.</desc>
        <g id="页面-1" stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
            <g id="直播pk-列表（初始）" transform="translate(-720.000000, -380.000000)">
                <g id="编组-10" transform="translate(720.000000, 380.000000)">
                    <g id="编组-7">
                        <rect id="矩形" x="0" y="0" width="200" height="140" />
                        <g id="插图" transform="translate(40.000000, 16.000000)">
                            <g id="泳池" transform="translate(0.000000, 33.818182)" stroke="#AFB7C6">
                                <rect id="矩形复制-26" fill="#FFFFFF" x="0.5" y="0.536078521" width="119"
                                      height="72.6363636" rx="2.4" />
                                <rect id="矩形复制-27" fill="#FAFAFA"
                                      transform="translate(60.000000, 30.000000) scale(1, -1) translate(-60.000000, -30.000000) "
                                      x="0.5" y="0.5" width="119" height="59" rx="2.4" />
                                <line x1="21.8181818" y1="64.9090909" x2="37.1194167" y2="64.9090909" id="直线-11"
                                      strokeWidth="1.2" strokeLinecap="square" />
                                <line x1="12" y1="69.2727273" x2="18.9919215" y2="69.2727273" id="直线-11复制"
                                      strokeWidth="1.2" strokeLinecap="square" />
                                <line x1="20.7272727" y1="69.2727273" x2="22.9375985" y2="69.2727273" id="直线-11复制-2"
                                      strokeWidth="1.2" strokeLinecap="square" />
                                <line x1="24.7048686" y1="69.2727273" x2="25.1144607" y2="69.2727273" id="直线-11复制-3"
                                      strokeWidth="1.2" strokeLinecap="square" />
                            </g>
                            <g id="say-hi" transform="translate(68.727273, 0.000000)">
                                <circle id="椭圆形" stroke="#AFB7C6" fill="#FFFFFF" cx="14.1818182" cy="14.1818182"
                                        r="13.6818182" />
                                <text id="Hi" fontFamily="STYuanti-TC-Bold, Yuanti TC" fontSize="16.8"
                                      fontWeight="bold" fill="#AFB7C6">
                                    <tspan x="6.45454545" y="20.0909091">Hi</tspan>
                                </text>
                                <path
                                    d="M8.04565602,23.4252548 L8.50833453,23.5138568 C9.11011422,23.6290965 9.60332204,24.059252 9.79929607,24.6397804 L11.7667045,30.467779 C11.9756721,31.0867977 11.6432599,31.7580133 11.0242412,31.9669809 C10.8308472,32.0322666 10.623851,32.0464014 10.4233774,32.0080111 L9.96069891,31.919409 C9.35891922,31.8041694 8.8657114,31.3740138 8.66973737,30.7934855 L6.70232892,24.9654869 C6.49336134,24.3464681 6.8257735,23.6752526 7.44479226,23.466285 C7.63818626,23.4009993 7.84518247,23.3868645 8.04565602,23.4252548 Z"
                                    id="矩形" stroke="#AFB7C6" fill="#FFFFFF"
                                    transform="translate(9.234517, 27.716633) rotate(35.000000) translate(-9.234517, -27.716633) " />
                            </g>
                            <g id="按钮底座" transform="translate(8.321004, 41.627181)" stroke="#AFB7C6">
                                <rect id="矩形复制-25" fill="#E3E5E9" x="0.906268479" y="11.4090909" width="103.727273"
                                      height="35" rx="2.4" />
                                <rect id="矩形" fill="#FFFFFF" x="0.906268479" y="0.5" width="103.727273"
                                      height="38.2727273" rx="2.4" />
                                <rect id="矩形" fill="#FFFFFF" x="0.906268479" y="0.5" width="103.727273"
                                      height="31.7272727" rx="2.4" />
                            </g>
                            <g id="小人笑脸" transform="translate(43.806366, 30.272727)">
                                <path
                                    d="M4.57415038,17.8466941 L29.6064834,14.2543972 C30.9185255,14.066111 32.134782,14.9770951 32.3230683,16.2891372 C32.3305443,16.3412325 32.3363047,16.3935597 32.3403401,16.4460337 L33.0575165,25.7716548 C33.1544553,27.0321747 32.2569263,28.1517563 31.0055047,28.3313431 L9.76984337,31.3787938 L9.76984337,31.3787938 L4.01788536,35.5254434 C3.48028004,35.913009 2.73028084,35.7913777 2.34271526,35.2537724 C2.18873481,35.0401809 2.10928036,34.7818733 2.1165995,34.5186667 L2.51600022,20.1556442 C2.54848111,18.9875849 3.41748911,18.0126823 4.57415038,17.8466941 Z"
                                    id="矩形" fill="#AFB7C6" opacity="0.300000012"
                                    transform="translate(17.639477, 25.417507) rotate(10.000000) translate(-17.639477, -25.417507) " />
                                <g id="路径-8" transform="translate(0.000000, 0.181818)" fill="#FFFFFF" stroke="#AFB7C6">
                                    <path
                                        d="M2.93061569,1.23891081 C1.88127466,1.23891081 1.03061569,2.08956979 1.03061569,3.13891081 L1.03061569,30.0373145 C1.03061569,30.1826687 1.07586422,30.3244179 1.1600812,30.4428887 C1.38407362,30.7579862 1.82109235,30.8318415 2.13618985,30.607849 L8.0263491,26.420729 L29.0727644,26.420729 C30.1221055,26.420729 30.9727644,25.57007 30.9727644,24.520729 L30.9727644,3.13891081 C30.9727644,2.08956979 30.1221055,1.23891081 29.0727644,1.23891081 L2.93061569,1.23891081 Z"
                                        id="蒙版" />
                                </g>
                                <path
                                    d="M16.0016901,16.4545455 C18.6307481,16.4545455 20.7620206,14.5073814 20.7620206,12.1054328 C20.7620206,12.0727545 18.6307481,12.1054328 16.0016901,12.1054328 C13.3726321,12.1054328 11.2413595,12.0803591 11.2413595,12.1054328 C11.2413595,14.5073814 13.3726321,16.4545455 16.0016901,16.4545455 Z"
                                    id="椭圆形" fill="#E6D0BF" />
                                <ellipse id="椭圆形-copy-19" fill="#7E8795" cx="9.45623552" cy="9.36363636" rx="1.78512397"
                                         ry="1.63636364" />
                                <ellipse id="椭圆形-copy-20" fill="#7E8795" cx="22.5471446" cy="9.36363636" rx="1.78512397"
                                         ry="1.63636364" />
                            </g>
                        </g>
                    </g>
                </g>
            </g>
        </g>
    </svg>
);

export const DefaultEmptyIcon = props => <Icon component={ DefaultEmptySvg } { ...props } />;
