import React from 'react';
import Icon from '@ant-design/icons';

const QuestionSvg = () => (
    // <svg width="1em" height="1em" fill="currentColor" viewBox="0 0 1024 1024">
    <svg viewBox="0 0 20 20" version="1.1" fill="currentColor" xmlns="http://www.w3.org/2000/svg" width="1em"
        height="1em">
        <path
            d="M9.16666667,15 L10.8333333,15 L10.8333333,13.3333333 L9.16666667,13.3333333 L9.16666667,15 Z M10,1.66666667 C5.4,1.66666667 1.66666667,5.4 1.66666667,10 C1.66666667,14.6 5.4,18.3333333 10,18.3333333 C14.6,18.3333333 18.3333333,14.6 18.3333333,10 C18.3333333,5.4 14.6,1.66666667 10,1.66666667 Z M10,16.6666667 C6.325,16.6666667 3.33333333,13.675 3.33333333,10 C3.33333333,6.325 6.325,3.33333333 10,3.33333333 C13.675,3.33333333 16.6666667,6.325 16.6666667,10 C16.6666667,13.675 13.675,16.6666667 10,16.6666667 Z M10,5 <PERSON>8.15833333,5 6.66666667,6.49166667 6.66666667,8.33333333 L8.33333333,8.33333333 C8.33333333,7.41666667 9.08333333,6.66666667 10,6.66666667 C10.9166667,6.66666667 11.6666667,7.41666667 11.6666667,8.33333333 C11.6666667,10 9.16666667,9.79166667 9.16666667,12.5 L10.8333333,12.5 C10.8333333,10.625 13.3333333,10.4166667 13.3333333,8.33333333 C13.3333333,6.49166667 11.8416667,5 10,5 Z"
            id="形状" fill="#898A8C"></path>
    </svg>
);

export const QuestionIcon = props => <Icon component={QuestionSvg} {...props} />;
