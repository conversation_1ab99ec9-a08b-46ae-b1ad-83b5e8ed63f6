import React from 'react';
import Icon from '@ant-design/icons';

const <PERSON>houcangmianSvg = () => (
    <svg className="icon" viewBox="0 0 1024 1024" fill="currentColor" version="1.1" xmlns="http://www.w3.org/2000/svg"
         p-id="2292" width="20" height="20">
        <path
            d="M750.592 938.666667a38.954667 38.954667 0 0 1-18.858667-4.906667L512 812.672l-219.733333 121.088a39.04 39.04 0 0 1-42.752-3.242667 43.562667 43.562667 0 0 1-16.128-41.6l41.984-256.597333-177.792-181.717333a44.117333 44.117333 0 0 1-10.24-43.605334 41.258667 41.258667 0 0 1 32.768-29.013333l245.632-37.418667 109.909333-233.386666c13.568-29.098667 59.136-29.098667 72.704 0l109.909333 233.386666 245.632 37.418667a41.258667 41.258667 0 0 1 32.810667 29.013333c4.736 15.36 0.768 32.298667-10.282667 43.605334l-177.792 181.717333 41.984 256.597333c2.602667 15.957333-3.669333 32.085333-16.128 41.6a39.509333 39.509333 0 0 1-23.893333 8.149334"
            p-id="2293" />
    </svg>
);

export const ShoucangmianIcon = props => <Icon component={ ShoucangmianSvg } { ...props } />;
