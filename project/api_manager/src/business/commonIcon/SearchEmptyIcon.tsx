import React from 'react';
import Icon from '@ant-design/icons';

const SearchEmptySvg = () => (
    <svg width="200px" height="110px" viewBox="0 0 200 110" version="1.1" xmlns="http://www.w3.org/2000/svg">
        <title>搜索空</title>
        <desc>Created with Sketch.</desc>
        <g id="页面-1" stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
            <g id="直播pk-列表-搜索空" transform="translate(-715.000000, -440.000000)">
                <g id="编组-6" transform="translate(715.000000, 440.000000)">
                    <g id="编组-10">
                        <g id="编组-7">
                            <rect id="矩形" x="0" y="0" width="200" height="120" />
                            <g id="编组" transform="translate(35.000000, 16.000000)">
                                <g id="编组-9" transform="translate(0.000000, 7.000000)">
                                    <path
                                        d="M90.8746876,75.487013 L106.461039,58.9057881 L106.461039,4.4025974 C106.461039,1.69496762 104.266071,-0.5 101.558442,-0.5 L37.4025974,-0.5 C34.6949676,-0.5 32.5,1.69496762 32.5,4.4025974 L32.5,70.5844156 C32.5,73.2920454 34.6949676,75.487013 37.4025974,75.487013 L90.8746876,75.487013 Z"
                                        id="矩形备份-18" stroke="#AFB7C6" fill="#FFFFFF"
                                        transform="translate(69.480519, 37.493506) rotate(-90.000000) translate(-69.480519, -37.493506) " />
                                    <rect id="矩形" fill="#AFB7C6" x="42" y="22" width="55" height="1" />
                                    <rect id="矩形备份-17" fill="#AFB7C6" opacity="0.5" x="42" y="40" width="55"
                                          height="1" />
                                    <path
                                        d="M126.623377,16.8831169 C124.372294,21.3852814 120.995671,24.7619048 116.493506,27.012987"
                                        id="路径-10" stroke="#AFB7C6" />
                                    <path
                                        d="M21.4831284,60.7792208 C17.6180784,68.4302516 11.5825767,73.4946388 3.37662338,75.9723825"
                                        id="路径-10备份" stroke="#AFB7C6"
                                        transform="translate(12.429876, 68.375802) rotate(40.000000) translate(-12.429876, -68.375802) " />
                                    <path
                                        d="M129.291884,47.2810714 C115.538385,55.4507414 101.866587,57.6414306 88.2764891,53.853139"
                                        id="路径-11" stroke="#AFB7C6"
                                        transform="translate(108.784187, 51.467206) rotate(-20.000000) translate(-108.784187, -51.467206) " />
                                </g>
                                <path
                                    d="M0.673257995,48.8936629 L4.71644504,58.1772405 C7.47850904,55.8175485 9.84382255,54.2687294 11.8256493,53.5313831 C13.7742606,52.8063947 16.829633,52.3176808 21.0073097,52.0561076 L16.5657321,46.4788995 C13.4229816,46.2022958 10.9242042,46.2085588 9.07636613,46.4941338 C7.25890131,46.7750147 4.45345452,47.5751436 0.673257995,48.8936629 Z"
                                    id="矩形" stroke="#AFB7C6" fill="#FAFAFA" />
                                <path
                                    d="M106.79478,2.38455293 L109.410189,8.48219129 C112.461127,8.31261938 114.606406,7.99197933 115.8233,7.53226395 C117.002049,7.08695859 118.506155,6.0564924 120.318518,4.44129886 L117.366503,0.67748081 C115.871844,1.6722095 114.442871,2.27969905 113.077513,2.4939552 C111.748996,2.70243024 109.660676,2.66438732 106.79478,2.38455293 Z"
                                    id="矩形备份-3" stroke="#AFB7C6" fill="#FAFAFA" />
                            </g>
                        </g>
                    </g>
                </g>
            </g>
        </g>
    </svg>
);

export const SearchEmptyIcon = props => <Icon component={ SearchEmptySvg } { ...props } />;
