/*
 * @Author: han<PERSON><PERSON>
 * @Date: 2024-04-19 13:48:39
 * @LastEditors: hanweiqi
 * @LastEditTime: 2024-05-19 16:24:50
 * @Description: 请填写简介
 */
import React, { useEffect, useRef, useState } from 'react';
import { Tooltip } from 'antd';
import { TooltipProps } from 'antd/lib/tooltip';

export default function PopoverEllipsis(props: TooltipProps) {
    const [isLongText, setIsLongText] = useState<boolean>(false);
    const childRef = useRef<any>();

    useEffect(() => {
        if (childRef.current) {
            const { scrollWidth, clientWidth } = childRef.current;
            if (scrollWidth > clientWidth) {
                setIsLongText(true);
            } else {
                setIsLongText(false);
            }
        }
    }, [childRef, props.title]);

    const renderChild = () => {
        return React.cloneElement(props.children as React.ReactElement, {
            ref: childRef
        });
    };

    return (
        <Tooltip
            {...props}
            title={isLongText ? props.title : ''}
        >
            {renderChild()}
        </Tooltip>
    );
}
