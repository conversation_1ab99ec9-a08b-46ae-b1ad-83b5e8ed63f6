import React from 'react';
import classnames from 'classnames';
import { Tag } from 'antd';
import css from './KdevTag.less';
import { TagProps } from 'antd/es/tag';

interface IProps {
    style?: React.CSSProperties;
    className?: string;
    color?: string; // 可填写green/blue/yellow 或HEX6位色号， 自动生成背景色
    backgroundColor?: string;
    content: React.ReactNode;
    tagProps?: TagProps;
    bordered?: boolean;
    type?: 'success' | 'error' | 'warning' | 'processing' | 'primary' | 'default'; // 绿-成功 / 红-失败 / 黄-警示/ 浅蓝-信息 / 深蓝-强调 /灰-默认
    size?: 'middle' | 'small' | 'large' | 'x-small'; // tag尺寸，kdevTag组件默认small
}

const TAG_STYLE: React.CSSProperties = {
    height: '24px',
    borderRadius: '4px',
    fontSize: '12px',
    fontWeight: 'bold',
    textAlign: 'left',
    lineHeight: '14px',
};

const sizeClassMap = {
    'x-small': 'xsm',
    'small': 'sm',
    'large': 'lg',
    'middle': 'm'
};

const COLOR = {
    green: '#36CFA2',
    blue: '#19B2FF',
    yellow: '#FFA114',
    gray: '#252626',
    closed: '#898a8c',
};

const BACKGROUND_COLOR = {
    green: '#36CFA214',
    blue: '#19B2FF14',
    yellow: '#FFA11414',
    gray: '#E5EDFA',
    closed: '#F5F7FA',
};

export class KdevTag extends React.Component<IProps, any> {

    private getColor(): string {
        const { color } = this.props;
        if (color && COLOR[color]) {
            return COLOR[color];
        }
        if (color && color.startsWith('#')) {
            return color;
        }
        return '#575859';
    }

    private getBackgroundColor(): string {
        if (this.props.backgroundColor) {
            return this.props.backgroundColor;
        }
        const { color } = this.props;
        if (color && BACKGROUND_COLOR[color]) {
            return BACKGROUND_COLOR[color];
        }
        if (color && color.startsWith('#')) {
            return color + '14';
        }
        return '#F5F7FA';
    }

    public render(): React.ReactElement {
        const {
            style, className, color, content, tagProps, backgroundColor, bordered, type, size,
            ...restProps
        } = this.props;
        const sizeClass = sizeClassMap[this.props.size || 'small'];
        if (this.props.bordered) {
            return (
                // @ts-ignore
                <Tag
                    className={classnames(css.tag, this.props.className, !!type && `ant-tag-${type}`, !!sizeClass && `ant-tag-${sizeClass}`)}
                    style={{
                        ...(this.props.style as any),
                        color: this.getColor(),
                        backgroundColor: '#ffffff',
                        border: `1px solid ${this.getColor()}`,
                        lineHeight: '22px'
                    }}
                    {...(this.props.tagProps as any)}
                    {...(restProps as any)}
                >
                    {this.props.content}
                </Tag>
            );
        }
        return (
            // @ts-ignore
            <Tag
                className={classnames(css.tag, this.props.className, !!type && `ant-tag-${type}`, !!sizeClass && `ant-tag-${sizeClass}`)}
                style={{
                    ...(this.props.style as any),
                    color: this.getColor(),
                    backgroundColor: this.getBackgroundColor()
                }}
                {...(this.props.tagProps as any)}
                {...(restProps as any)}
            >
                {this.props.content}
            </Tag>
        );
    }
}
