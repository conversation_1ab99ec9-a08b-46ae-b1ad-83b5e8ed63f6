/**
 * 搜索关键词高亮
 * @param content
 */
export function  searchHighLight(content, keyword, hlColor?: string) {
    try {
        if (keyword.trim()) {
            // 首尾含有/要做转义
            if (keyword[0] === '\\') {
                keyword = '\\' + keyword;
            }
            if (keyword[keyword.length - 1] === '\\') {
                keyword = keyword + '\\';
            }
            const reg = new RegExp(keyword.trim(), 'gi'); // 不区分大小写
            const str = content.replace(/[<>&"]/g, c => { // 对代码里的标签进行转义
                return {
                    '<': '&lt;',
                    '>': '&gt;',
                    '&': '&amp;',
                    '"': '&quot;'
                }[c];
            });
            content = str.replace(reg, txt => {
                return `<span style="color:${hlColor || '#327DFF'};">${ txt }</span>`;
            });
        }
        return content;
    } catch (e) {
        return content;
    }

}
