import React from 'react';
import classnames from 'classnames';

interface IProps {
    id: string;
    style?: React.CSSProperties;
    className?: string;
}

const ICON_STYLE: React.CSSProperties = {
    width: '1em',
    height: '1em',
    verticalAlign: '-0.15em',
    fill: 'currentColor',
    overflow: 'hidden'
};

export class KdevIconFont extends React.Component<IProps, any> {

    public render(): React.ReactElement {
        return (
            <svg
                className={ classnames('icon', this.props.className) }
                aria-hidden="true"
                { ...this.props }
                style={ { ...ICON_STYLE, ...this.props.style } }
            >
                <use xlinkHref={ this.props.id } />
            </svg>
        );
    }
}
