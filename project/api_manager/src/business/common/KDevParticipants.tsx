import React from 'react';
import { Participants } from '@kdev-fe-common/common/kdevSearch';
import { nsKdevUser, nsKdevUserListGET } from '@/remote';
import Bind from 'lodash-decorators/bind';

interface IProps {
    value: nsKdevUser.IUser[];
    disabled?: boolean;
    showNameIfSingle?: boolean; // 是否在用户数为1时显示用户名，默认为false
    onChange: (value: any[]) => void;
    onChecked?: (checked: boolean, userInfo: nsKdevUser.IUser) => void;
    maxLength?: number;
    getPopupContainer?: (triggerNode: HTMLElement) => HTMLElement | null;
    size?: 'large' | 'small' | 'default';
}

export class KDevParticipants extends React.Component<IProps, any> {

    @Bind
    private async getUserList(value): Promise<nsKdevUser.IUser[]> {
        // if (!value.trim()) {
        //     return Promise.resolve([]);
        // }
        const params = {
            search: value.trim()
        };
        const respData = await nsKdevUserListGET.remote(params);
        return Promise.resolve(respData.list);
    }

    public render(): React.ReactElement | null {
        if (!this.props.value) {
            return null;
        }
        return <Participants
            maxLength={this.props.maxLength}
            value={this.props.value.map(item => ({
                ...item,
                photo: item.avatarUrl,
            }))}
            disabled={this.props.disabled || false}
            showNameIfSingle={this.props.showNameIfSingle || false}
            onSearch={this.getUserList}
            onChecked={this.props.onChecked}
            onChange={(users) => {
                this.props.onChange(users?.map(user => ({ ...user, photo: user.avatarUrl })));
            }}
            getPopupContainer={this.props.getPopupContainer}
            size={this.props.size}
        />;
    }
}
