@import '~@lynx/design-token/dist/less/token.less';

body {
  :global {
    .ant-progress-bg {
      border-radius: 6px;
    }
    .ant-progress-text {
      color: @color-text-primary;
    }
    .ant-progress-inner:not(.ant-progress-circle-gradient) .ant-progress-circle-path {
      stroke: @color-bg-brand;
    }
    .ant-progress-status-exception .ant-progress-inner:not(.ant-progress-circle-gradient) .ant-progress-circle-path {
      stroke: @color-bg-error;
    }
    .ant-progress-status-success .ant-progress-inner:not(.ant-progress-circle-gradient) .ant-progress-circle-path {
      stroke: @color-border-success;
    }
    .ant-progress-circle .ant-progress-text {
      font-family: 'DIN Alternate';
      font-style: normal;
      font-weight: 700;
      color: @color-text-primary;
    }
    .ant-progress-success-bg, .ant-progress-bg {
      background-color: @color-bg-brand;
    }
    .ant-progress-status-exception .ant-progress-bg {
      background-color: @color-bg-error;
    }
    .ant-progress-status-success .ant-progress-bg {
      background-color: @color-border-success;
    }
  }
}
