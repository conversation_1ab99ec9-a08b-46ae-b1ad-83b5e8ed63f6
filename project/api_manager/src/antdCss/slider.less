@import '~@lynx/design-token/dist/less/token.less';
body {
  :global {
    .ant-slider-rail {
      background-color: @color-bg-tertiary;
    }
    .ant-slider-track {
      background-color: @color-bg-brand;
    }
    .ant-slider-handle {
      width: 12px;
      height: 12px;
      border: 2px solid @color-border-brand;
    }
    .ant-slider:hover .ant-slider-rail {
      background-color: @color-bg-quaternary;
    }
    .ant-slider:hover .ant-slider-track {
      background-color: @color-bg-brand1-hover
    }
    .ant-slider:hover .ant-slider-handle:not(.ant-tooltip-open) {
      border-color: @color-border-brand1-hover;
    }
    .ant-slider-handle:focus {
      border-color: @color-border-brand1-active;
      box-shadow: none;
      -webkit-box-shadow: none;
    }
    .ant-slider-handle.ant-tooltip-open {
      border-color: @color-border-brand1-active;
    }
  }
}
