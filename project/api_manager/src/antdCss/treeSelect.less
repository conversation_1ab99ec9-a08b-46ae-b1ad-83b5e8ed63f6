@import '~@lynx/design-token/dist/less/token.less';

body {
  :global {

    // 树节点
    .ant-select-tree .ant-select-tree-treenode {
      padding: 7px 12px;
      height: 36px;
      display: flex;
      align-items: center;

      &:hover {
        background-color: @color-bg-basic-hover;
      }

      .ant-select-tree-node-content-wrapper {
        background-color: initial;

        &:hover {
          background-color: initial;
        }
      }
    }

    // 选中
    .ant-select-tree .ant-select-tree-treenode-selected {
      background-color: @color-bg-basic-active;
      color: @color-text-brand;

      .ant-select-tree-node-content-wrapper.ant-select-tree-node-selected {
        background-color: inherit;
        color: inherit;
      }
    }

    // 禁用
    .ant-select-tree .ant-select-tree-treenode-disabled .ant-select-tree-node-content-wrapper {
      color: @color-palette-gray-600;
    }

    // 节点可选择的checkbox
    .ant-select-tree-checkbox-checked .ant-select-tree-checkbox-inner {
      background-color: @color-bg-brand;
      border-color: @color-border-brand;
    }

    .ant-select-tree .ant-select-tree-checkbox {
      margin: 0 8px 0 0;
    }

    .ant-select-tree-checkbox-inner {
      border-radius: 4px;
      border: 1px solid @color-border-form-default;
    }

    // 滚动条样式
    .ant-select-tree-list-scrollbar {
      width: 6px !important;

      .ant-select-tree-list-scrollbar-thumb {
        background-color: @color-fill-tertiary  !important;

        &:hover {
          background-color: @color-fill-secondary  !important;
        }
      }
    }
  }
}