@import '~@lynx/design-token/dist/less/token.less';

body {
  :global {
    .ant-btn {
      border-radius: @radius-border-medium;
      border-color: @color-border-form-default;
      text-shadow: none;
      -webkit-box-shadow: none;
      box-shadow: none;
      padding: 5px 16px;
      .font_body();
      line-height: 21px;

      .svgfont {
        font-size: 16px !important;
      }

      &:hover,
      &:focus {
        color: @color-text-button-secondary-hover;
        border-color: @color-border-brand1-hover;
      }

      &:active {
        color: @color-text-button-secondary-hover;
        border-color: @color-border-brand1-active;
      }

      &[disabled],
      &[disabled]:hover,
      &[disabled]:focus,
      &[disabled]:active {

        color: @color-text-disabled;
        background: @color-bg-primary;
        border-color: @color-border-table;
      }
    }

    .ant-btn-circle,
    .ant-btn-circle-outline {
      border-radius: 50%;
    }

    .ant-btn>.anticon+span,
    .ant-btn>span+.anticon {
      margin-left: 6px;
    }

    // type:default
    .ant-btn-default {
      padding: 5px 16px;
    }

    // type:primary
    .ant-btn-primary {
      background-color: @color-bg-brand;
      border-color: @color-border-brand;
      color: @color-text-white;
      padding: 5px 16px;

      &:hover,
      &:focus {
        background-color: @color-bg-brand1-hover;
        border-color: @color-border-brand1-hover;
        color: @color-text-white;
      }

      &:active {
        background-color: @color-bg-brand1-active;
        border-color: @color-border-brand1-active;
        color: @color-text-white;
      }

      &[disabled],
      &[disabled]:hover,
      &[disabled]:focus,
      &[disabled]:active {
        background-color: @color-bg-brand1-disabled;
        border-color: @color-border-brand1-disabled;
        color: @color-text-white;
      }
    }

    // type:link
    .ant-btn-link {
      color: @color-text-brand;
      background: transparent;
      border-color: transparent;
      -webkit-box-shadow: none;
      box-shadow: none;

      &:hover,
      &:focus {
        color: @color-text-brand1-hover;
        border-color: transparent;
        background-color: transparent;
      }

      &:active {
        color: @color-text-brand1-active;
        border-color: transparent;
        background-color: transparent;
      }

      &[disabled],
      &[disabled]:hover,
      &[disabled]:focus,
      &[disabled]:active {
        color: @color-text-disabled;
        border-color: transparent;
        background-color: transparent;
      }

      &.ant-btn-sm {
        font-size: @font_size_9;
        line-height: @line_height_7;
        padding: 0;
        height: @height_5;
        border: none;
        min-width: unset;
      }
    }

    //type:text
    .ant-btn-text {
      color: @color-text-secondary;
      background: transparent;
      border-color: transparent;
      -webkit-box-shadow: none;
      box-shadow: none;

      &:hover,
      &:focus {
        color: @color-text-tertiary;
        border-color: transparent;
        background-color: transparent;
      }

      &:active {
        color: @color-text-primary;
        border-color: transparent;
        background-color: transparent;
      }

      &[disabled],
      &[disabled]:hover,
      &[disabled]:focus,
      &[disabled]:active {
        color: @color-text-disabled;
        border-color: transparent;
        background-color: transparent;
      }
    }

    // size: L
    .ant-btn-lg {
      height: @height_1;
      font-size: @font_size_9;
      line-height: @line_height_9;
    }

    // size: s
    .ant-btn-sm {
      height: @height_3;
      font-size: @font_size_9;
      line-height: 16px;
    }

    // danger
    .ant-btn-dangerous {
      color: @color-text-error;
      border-color: @color-border-error;
      padding: 5px 16px;

      &:hover,
      &:focus {
        color: @color-text-error1-hover;
        border-color: @color-border-error1-hover;
      }

      &:active {
        color: @color-text-error1-active;
        border-color: @color-border-error1-active;
      }

      &[disabled],
      &[disabled]:hover,
      &[disabled]:focus,
      &[disabled]:active {
        color: @color-text-error1-disabled;
        border-color: @color-border-error1-disabled;
      }
    }

    .ant-btn-dangerous.ant-btn-primary {
      background: @color-bg-error;
      border-color: @color-border-error;
      color: @color-text-white;

      &:hover,
      &:focus {
        border-color: @color-border-error1-hover;
        background: @color-bg-error1-hover;
      }

      &:active {
        border-color: @color-border-error1-active;
        background-color: @color-bg-error1-active;
      }

      &[disabled],
      &[disabled]:hover,
      &[disabled]:focus,
      &[disabled]:active {
        color: @color-text-white;
        border-color: @color-border-error1-disabled;
        background-color: @color-bg-error1-disabled;
      }
    }

    .ant-btn-dangerous.ant-btn-text,
    .ant-btn-dangerous.ant-btn-link {
      color: @color-text-error;

      &:hover,
      &:focus {
        color: @color-text-error1-hover;
        background: transparent;
      }

      &:active {
        color: @color-text-error1-active;
        background: transparent;
      }

      &[disabled],
      &[disabled]:hover,
      &[disabled]:focus,
      &[disabled]:active {
        color: @color-text-error1-disabled;
        background: transparent;
      }
    }

    // 有icon
    .ant-btn-icon-only {
      padding: 7px;
      line-height: 16px;
      border-radius: @radius-border-medium;

      &.ant-btn-circle,
      &.ant-btn-circle-outline {
        border-radius: 50%;
      }

      // small
      &.ant-btn-sm {
        width: @height_3;
        height: @height_3;
        padding: 5px;
        border-radius: @radius-border-medium;

        &>* {
          font-size: 16px;
        }
      }

      // large
      &.ant-btn-lg {
        width: @height_1;
        height: @height_1;
        padding: 8px;
        border-radius: @radius-border-medium;

        &>* {
          font-size: 16px;
        }
      }

      &>* {
        font-size: 16px;
      }
    }

    // ghost
    .ant-btn-background-ghost {
      border-color: transparent;
    }

    .ant-btn-background-ghost {
      color: #fff;
      border-color: #fff;
    }

    .ant-btn-background-ghost.ant-btn-primary {
      color: @color-text-brand;
      background: transparent;
      border-color: @color-border-brand;
      text-shadow: none;

      &:hover,
      &:focus {
        color: @color-text-brand1-hover;
        border-color: @color-border-brand1-hover;
      }

      &:active {
        color: @color-text-brand1-active;
        border-color: @color-border-brand1-active;
      }

      &[disabled],
      &[disabled]:hover,
      &[disabled]:focus,
      &[disabled]:active {
        color: @color-text-brand1-disabled;
        border-color: @color-border-brand1-disabled;
      }
    }
  }
}