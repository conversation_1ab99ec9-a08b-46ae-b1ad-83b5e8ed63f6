@import '~@lynx/design-token/dist/less/token.less';

/**
antd checkbox组件重构
 */
body {
  :global {

    .ant-checkbox-wrapper,
    .ant-checkbox {
      .font_body();
    }

    .ant-checkbox+span {
      padding-right: @spacing_gap_2;
      padding-left: @spacing_gap_2;
    }

    .ant-checkbox-inner {
      width: 16px;
      height: 16px;
      border-radius: @radius-border-medium;
    }

    // checked
    .ant-checkbox-checked {
      .ant-checkbox-inner {
        background-color: @color-bg-brand;
        border-color: @color-border-brand;
      }

      &::after {
        border: 1px solid @color-border-brand;
        border-radius: @radius-border-medium;
      }
    }

    // disabled
    .ant-checkbox-disabled {
      .ant-checkbox-inner {
        background-color: @color-bg-basic-disabled !important;
        border-color: @color-border-table !important;
      }

      &+span {
        color: @color-text-disabled;
      }
    }

    // hover
    .ant-checkbox-wrapper:hover .ant-checkbox-inner,
    .ant-checkbox:hover .ant-checkbox-inner,
    .ant-checkbox-input:focus+.ant-checkbox-inner {
      border-color: @color-border-brand1-hover;
    }

    .ant-checkbox-indeterminate {
      .ant-checkbox-inner {
        background: #326BFBff;
        position: relative;

        &::after {
          content: '';
          position: absolute;
          width: 50%;
          height: 50%;
          background: #fff;
          transform: translate(-50%, -50%);
          border-radius: 20px;
          width: 10px;
          height: 2px;
        }
      }
    }
  }
}