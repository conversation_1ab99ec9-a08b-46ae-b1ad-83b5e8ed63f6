@import '~@lynx/design-token/dist/less/token.less';

/**
antd message样式重构
 */
body {
  :global {
    .ant-message {
      z-index: 99999
    }

    .ant-message-notice-content {
      .font_body();
      border: 1px solid @color-border-divider;
      border-radius: @radius-border-medium;
      padding: 7px 16px;
      min-height: @height_1;
      box-shadow: @shadow-float-down;
      text-shadow: none;

      .ant-message .anticon {
        top: 0px;
        margin-right: @spacing_gap_2;
      }

      .ant-message-success .anticon {
        color: @color-fill-success-default;
      }

      .ant-message-error .anticon {
        color: @color-fill-error-default;
      }

      .ant-message-info .anticon,
      .ant-message-loading .anticon {
        color: @color-fill-brand-default;
      }

      .ant-message-warning .anticon {
        color: @color-fill-warning-default;
      }
    }
  }
}