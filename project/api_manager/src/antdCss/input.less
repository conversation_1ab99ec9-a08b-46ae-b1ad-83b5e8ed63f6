@import '~@lynx/design-token/dist/less/token.less';

/**
  antd input组件样式重构
 */
body {
  :global {
    .ant-input-affix-wrapper {
      border-radius: @radius-border-medium;
    }

    .ant-input-affix-wrapper:hover {
      border-color: @color-brand-primary-active;
    }

    .ant-input-affix-wrapper:focus,
    .ant-input-affix-wrapper-focused {
      box-shadow: 0 0 0 2px #5C8FFF33;
      border-color: @color-brand-primary-active;
    }

    .ant-input {
      .font_body();
      border-radius: @radius-border-medium;
      border: 1px solid @color-border-form-default;

      &:focus {
        border-color: @color-border-input-focused;
      }

      &:hover {
        border-color: @color-border-brand1-hover;
      }
    }

    .ant-picker-input {
      >input {
        border-radius: @radius-border-medium;
      }
    }

    .ant-input-lg {
      color: @color-text-input-default;
      font-size: @font_size_8;
      line-height: @line_height_6;
    }

    .ant-input-sm {
      .font_caption();
    }

    // InputGroup
    .ant-input-group.ant-input-group-compact>*:first-child,
    .ant-input-group.ant-input-group-compact>.ant-select:first-child>.ant-select-selector,
    .ant-input-group.ant-input-group-compact>.ant-calendar-picker:first-child .ant-input,
    .ant-input-group.ant-input-group-compact>.ant-select-auto-complete:first-child .ant-input,
    .ant-input-group.ant-input-group-compact>.ant-cascader-picker:first-child .ant-input,
    .ant-input-group.ant-input-group-compact>.ant-mention-wrapper:first-child .ant-mention-editor,
    .ant-input-group.ant-input-group-compact>.ant-time-picker:first-child .ant-time-picker-input {
      border-top-left-radius: @radius-border-medium;
      border-bottom-left-radius: @radius-border-medium;
    }

    .ant-input-group.ant-input-group-compact>*:last-child,
    .ant-input-group.ant-input-group-compact>.ant-select:last-child>.ant-select-selector,
    .ant-input-group.ant-input-group-compact>.ant-calendar-picker:last-child .ant-input,
    .ant-input-group.ant-input-group-compact>.ant-select-auto-complete:last-child .ant-input,
    .ant-input-group.ant-input-group-compact>.ant-cascader-picker:last-child .ant-input,
    .ant-input-group.ant-input-group-compact>.ant-cascader-picker-focused:last-child .ant-input,
    .ant-input-group.ant-input-group-compact>.ant-mention-wrapper:last-child .ant-mention-editor,
    .ant-input-group.ant-input-group-compact>.ant-time-picker:last-child .ant-time-picker-input {
      border-top-right-radius: @radius-border-medium;
      border-bottom-right-radius: @radius-border-medium;
    }

    // 数字类型输入框
    .ant-input-number {
      border-radius: @radius-border-medium;
    }

    .ant-input-number-handler-wrap {
      border-radius: @radius-border-medium;
    }

    .ant-input-number:hover {
      border-color: @color-border-brand1-hover;
    }

    .ant-input-number:focus,
    .ant-input-number-focused {
      border-color: @color-border-brand1-active;
      box-shadow: 0 0 0 2px #D7E1FC;
    }
  }
}