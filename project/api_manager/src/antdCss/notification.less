@import '~@lynx/design-token/dist/less/token.less';

/**
  antd notification组件样式重构
 */
body {
  :global {
    .ant-notification-notice {
      border-radius: @radius-border-medium;
      border-color: @color-border-divider;
      box-shadow: @shadow-float-down;
      -webkit-box-shadow: @shadow-float-down;
      padding: @spacing_gap_4;
      width: 320px;
    }

    .ant-notification-notice-message {
      .font_body_bold();
    }

    .ant-notification-notice-description {
      .font_body();
    }

    .ant-notification-notice-close {
      color: @color-fill-secondary;
      font-size: 16px;
    }

    .ant-notification-notice-icon {
      font-size: 16px;
      margin-left: 0;
      line-height: 22px;
    }

    .ant-notification-notice-with-icon .ant-notification-notice-message {
      margin-bottom: 0;
      margin-left: 26px;
      font-weight: 500;
      font-size: 14px;
      line-height: 22px;
      color: #252626;
    }

    .ant-notification-notice-with-icon .ant-notification-notice-description {
      margin-left: 0;
      margin-top: 8px;
    }

    .ant-notification-close-icon {
      font-size: 16px;
    }

    .anticon.ant-notification-notice-icon-success {
      color: @color-fill-success-default;
    }

    .anticon.ant-notification-notice-icon-info {
      color: @color-fill-brand-default;
    }

    .anticon.ant-notification-notice-icon-warning {
      color: @color-fill-warning-default;
    }

    .anticon.ant-notification-notice-icon-error {
      color: @color-fill-error-default;
    }
  }
}