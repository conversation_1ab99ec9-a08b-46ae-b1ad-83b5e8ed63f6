@import '~@lynx/design-token/dist/less/token.less';
/**
  antd modal对话框组件样式重构
 */
body {
  :global {
    // 基本对话框
    .ant-modal-content {
      border-radius: @radius-border-medium;
      -webkit-box-shadow: @shadow-table-down;
      box-shadow: @shadow-table-down;
    }
    .ant-modal-header {
      padding: 24px 24px 0px;
      border-bottom: none;
      border-radius: @radius-border-medium @radius-border-medium 0 0;
      .ant-modal-title {
        .font_title_4();
      }
    }
    .ant-modal-body {
      padding: 20px 24px 24px 24px;
      .font_body();
    }
    .ant-modal-footer {
      padding: 0px 24px 24px;
      border-radius: 0 0 @radius-border-medium @radius-border-medium;
      border-top: 0;
      button + button {
        margin-left: @spacing_gap_3;
      }
    }

    .ant-modal-close {
      .ant-modal-close-x {
        line-height: 72px;
        padding-right: 4px;
      }
    }

    // 信息提示对话框
    .ant-modal-confirm .ant-modal-close {
      display: block;
      top: 26px;
      right: 24px;
      .ant-modal-close-x {
        width: 16px;
        height: 16px;
        font-size: 16px;
        line-height: 16px;
      }
    }
    .ant-modal-confirm-body {
      .ant-modal-confirm-title {
        font-family: @font_family_1;
        color: @neutral_10;
        font-weight: @font_weight_500;
        font-size: @font_size_8;
        line-height: 20px;
        margin-right: 24px;
      }
      .ant-modal-confirm-content {
        .font_body();
      }
    }
    .ant-modal-confirm .ant-modal-body {
      padding: 24px;
    }
    .ant-modal-confirm-body .ant-modal-confirm-content {
      padding-top: 20px;
      margin-top: 0;
    }
    .ant-modal-confirm-body > .anticon + .ant-modal-confirm-title + .ant-modal-confirm-content {
      margin-left: 0;
    }
    .ant-modal-confirm-body > .anticon {
      font-size: 20px;
      margin-right: 8px;
    }
    .ant-modal-confirm-info .ant-modal-confirm-body > .anticon {
      color: @color-fill-info-default;
    }
    .ant-modal-confirm-success .ant-modal-confirm-body > .anticon {
      color: @color-fill-success-default;
    }
    .ant-modal-confirm-error .ant-modal-confirm-body > .anticon {
      color: @color-fill-error-default;
    }
    .ant-modal-confirm-warning .ant-modal-confirm-body > .anticon, .ant-modal-confirm-confirm .ant-modal-confirm-body > .anticon {
      color: @color-fill-warning-default;
    }
  }
}
