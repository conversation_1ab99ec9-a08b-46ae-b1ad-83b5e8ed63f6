@import '~@lynx/design-token/dist/less/token.less';
/**
  TimePicker 样式统一
 */
body {
  :global {

    // 时间选择

    .ant-picker-time-panel-column {
      width: 63px;
    }
    .ant-picker-time-panel-column:not(:first-child) {
      border-color: 1px solid @color-border-default;
    }
    .ant-picker-time-panel-column > li.ant-picker-time-panel-cell .ant-picker-time-panel-cell-inner {
      height: 32px;
      line-height: 32px;
      border-radius: @radius-border-medium;
      color: @color-text-primary;
      font-family: 'DIN Alternate';
      font-style: normal;
      font-weight: 700;
      &:hover {
        background: @color-bg-secondary;
      }
    }
    .ant-picker-time-panel-column > li {
      padding: 0 7px 0 8px;
    }
    .ant-picker-time-panel-column > li.ant-picker-time-panel-cell-selected .ant-picker-time-panel-cell-inner {
      background: @color-bg-basic-active;
      //font-weight: 700;
      color: @color-text-brand;
    }
    .ant-picker-ranges {
      //padding: 16px;
    }




  }
}
