@import '~@lynx/design-token/dist/less/token.less';

/**
  antd redio组件样式重构
 */
body {
  :global {

    // radio.button
    .ant-radio-button-wrapper {
      font-family: @font_family_1;
      color: @color-text-button-secondary-default;
      font-size: @font_size_9;

      &:first-child {
        border-radius: @radius-border-medium 0 0 @radius-border-medium;
      }

      &:last-child {
        border-radius: 0 @radius-border-medium @radius-border-medium 0;
      }

      &:not(.ant-radio-button-wrapper-disabled):hover {
        color: @color-text-brand1-hover;
      }

      &:not(.ant-radio-button-wrapper-disabled):active {
        color: @color-text-brand1-active;
      }
    }

    .ant-radio-button-wrapper-checked {
      &:not([class*=' ant-radio-button-wrapper-disabled']) {
        &.ant-radio-button-wrapper:first-child {
          border-right-color: @color-border-brand;
        }
      }

      &:not(.ant-radio-button-wrapper-disabled) {
        color: @color-text-brand;
        border-color: @color-border-brand;

        &:first-child {
          border-color: @color-border-brand;
        }

        &::before {
          background-color: @color-bg-brand;
        }

        &:hover {
          color: @color-text-brand1-hover;
          border-color: @color-border-brand1-hover;

          &::before {
            background-color: @color-bg-brand1-hover;
          }
        }

        &:active {
          color: @color-text-brand1-active;
          border-color: @color-border-brand1-active;

          &::before {
            background-color: @color-bg-brand1-active;
          }
        }
      }
    }

    // radio
    .ant-radio-wrapper {
      .font_body();
    }

    .ant-radio-inner {
      width: 16px;
      left: calc(50% - 16px/2);
      top: calc(50% - 16px/2);
      height: @height_6;
    }

    .ant-radio-checked .ant-radio-inner {
      border-color: @color-border-brand;
      background-color: @color-bg-brand;
    }

    .ant-radio-inner::after {
      top: 5px;
      left: 5px;
      width: 4px;
      height: 4px;
      background-color: @color-bg-white; // @color-bg-brand;
    }

    .ant-radio-wrapper:hover .ant-radio,
    .ant-radio:hover .ant-radio-inner,
    .ant-radio-input:focus+.ant-radio-inner {
      border-color: @color-border-brand;
    }

    .ant-radio-disabled {
      .ant-radio-inner {
        background: @color-bg-basic-disabled;
        border-color: @color-border-form-disabled !important;
      }

      &+span {
        color: @color-text-disabled;
      }
    }

    .ant-radio-input {
      opacity: 1;
    }
  }
}