@import '~@lynx/design-token/dist/less/token.less';

body {
  :global {
    // 锚点
    .ant-anchor-link  {
      padding: 7px 0 7px 9px;
    }
    .ant-anchor-link-title {
      font-size: 12px;
      font-weight: 400;
      color: @color-text-secondary;
      line-height: 18px;
      white-space: unset;
    }
    // 锚点的那个小蓝条
    .ant-anchor-ink-ball {
      position: absolute;
      left: 1px;
      width: 2px;
      height: 18px;
      margin-top: -5px;
      background-color: @color-bg-brand;
      border-radius: 0;
      border: none;
    }
    // 激活的锚点
    .ant-anchor-link-active > .ant-anchor-link-title  {
      color: @color-text-brand;
    }
    .ant-anchor-link-title-active {
      color: @color-text-brand;
    }
    // 那条竖线
    .ant-anchor-ink::before {
      width: 1px;
      background-color: @color-bg-tertiary;
    }

  }
}
