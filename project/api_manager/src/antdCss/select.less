@import '~@lynx/design-token/dist/less/token.less';

/**
  antd select组件样式重构
 */
body {
  :global {
    .ant-select {
      .font_body();
    }

    .ant-select:not(.ant-select-disabled):hover .ant-select-selector {
      border-color: @color-border-brand1-hover;
    }

    .ant-select-focused.ant-select-single:not(.ant-select-customize-input) .ant-select-selector:not(.ant-select-disabled.ant-select-single:not(.ant-select-customize-input) .ant-select-selector) {
      border-color: @color-border-input-active;
    }

    .ant-select-single.ant-select-lg:not(.ant-select-customize-input) .ant-select-selector,
    .ant-select-single:not(.ant-select-customize-input) .ant-select-selector,
    .ant-select-multiple .ant-select-selector {
      // padding: 0 @spacing_gap_3;
    }

    .ant-select-multiple .ant-select-selection-item {
      border-radius: @radius-border-medium;
      background: @color-bg-basic-tag;
      margin-right: @spacing_gap_1;
    }

    .ant-select-multiple .ant-select-selection-placeholder {
      // left: 19px;
    }

    // 单选
    .ant-select-single {
      &:not(.ant-select-customize-input) {
        .ant-select-selector {
          border-radius: @radius-border-medium;
          border-color: @color-border-form-default;
        }
      }
    }

    // 多选
    .ant-select-multiple {
      .ant-select-selector {
        border-radius: @radius-border-medium;
        border-color: @color-border-form-default;
      }
    }

    // 下拉选择器
    .ant-select-dropdown {
      .font_body();
      border-radius: @radius-border-medium;
      padding: 4px 0px;
      box-shadow: @shadow-normal-down;
      background: @color-bg-primary;
    }

    .ant-select-item {
      .font_body();
    }

    .ant-select-item-option-disabled {
      color: @color-text-disabled;
    }

    .ant-select-item-option-selected:not(.ant-select-item-option-disabled) {
      font-weight: 400;
      background-color: @color-bg-brand1-notice !important;
    }

    .ant-select-item-option-active:not(.ant-select-item-option-disabled) {
      background-color: @color-bg-basic-hover !important;
    }

    .ant-select-arrow {
      top: 50%;

      span[aria-label="down"] {
        transform: scaleY(0.7) translateX(-4px);
      }
    }

    .ant-select-item-group {
      font-size: 12px;
      color: #BBBDBF;
    }
  }
}