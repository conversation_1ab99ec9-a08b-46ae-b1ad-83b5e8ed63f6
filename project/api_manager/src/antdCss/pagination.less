@import '~@lynx/design-token/dist/less/token.less';
/**
  antd pagination组件样式重构
 */
body {
  :global {
    .ant-pagination {
      position: relative;
      display: flex;
      align-items: center;
      // justify-content: space-between;
      justify-content: flex-end; // API修改
      color: @color-text-primary;
      & > ul {
        flex: 1;
        position: relative;
        display: flex;
        justify-content: flex-end;
      }
    }
    .ant-table-pagination-right {
      float: inherit;
    }
    .ant-pagination::after  {
      display: none; // 去掉，这个会导致ant-pagination的justify-content: space-between没有到达预期的左右两靠边的效果
    }
    // pagesize
    .ant-pagination-options-size-changer.ant-select {
      position: absolute; // 相对于上面的ul定位
      left: 0;
      top: 0;
    }
    .ant-pagination {
      :global {
        .ant-select-single:not(.ant-select-customize-input) .ant-select-selector {
          padding: 5px 16px;
        }
        .ant-select-arrow {
          right: 13px;
        }
        .ant-select-single .ant-select-selector .ant-select-selection-item, .ant-select-single .ant-select-selector .ant-select-selection-placeholder {
          line-height: 21px;
        }
      }
    }
    .ant-pagination-options {
      margin-left: 0px;
    }
    .ant-pagination-item {
      background: @color-bg-white;
      border: 1px solid @color-border-form-default;
      border-radius: @radius-border-medium;
    }
    .ant-pagination-item a {
      color: @color-text-primary;
    }
    .ant-pagination-prev button, .ant-pagination-next button {
      color: @color-text-tertiary;
    }
    .ant-pagination-prev:focus .ant-pagination-item-link, .ant-pagination-next:focus .ant-pagination-item-link, .ant-pagination-prev:hover .ant-pagination-item-link, .ant-pagination-next:hover .ant-pagination-item-link {
      color: @color-text-brand;
    }
    .ant-pagination-prev, .ant-pagination-next, .ant-pagination-jump-prev, .ant-pagination-jump-next {
      border-radius: @radius-border-medium;
    }
    .ant-pagination-disabled .ant-pagination-item-link, .ant-pagination-disabled:hover .ant-pagination-item-link, .ant-pagination-disabled:focus .ant-pagination-item-link {
      color: @color-text-disabled;
    }
    .ant-pagination-prev .ant-pagination-item-link, .ant-pagination-next .ant-pagination-item-link {
      border-radius: @radius-border-medium;
      color: @color-text-tertiary;
      border: 1px solid @color-border-form-default;
      font-size: @typography-number-5-font-size;
      line-height: 16px;
    }
    .ant-pagination-total-text {
      color: @color-text-secondary;
      font-weight: 400;
      font-size: @typography-number-6-font-size;
      line-height: 32px;
    }
    .ant-pagination-options-quick-jumper input {
      padding: 0px 8px;
      border-radius: @radius-border-medium;
      height: 32px;
      width: 80px;
      border: 1px solid @color-border-form-default;
    }
    .ant-pagination-options-quick-jumper {
      margin-left: 24px;
    }

    // 选中的页码
    .ant-pagination-item-active {
      background: @color-bg-basic-active;
      border: 1px solid @color-border-brand;
      a {
        color: @color-text-brand;
      }
    }
    .ant-pagination-item:focus a, .ant-pagination-item:hover a {
      color: @color-text-brand;
    }
    // mini
    .ant-pagination.mini .ant-pagination-item {
      min-width: 28px;
      height: 28px;
      margin: 0;
      line-height: 22px;
      margin-right: 4px;
      padding: 2px 0;
    }
    .ant-pagination.mini .ant-pagination-options {
      margin-left: 0;
    }
    .ant-pagination.mini .ant-pagination-prev, .ant-pagination.mini .ant-pagination-next {
      min-width: 28px;
      height: 28px;
      margin: 0;
      line-height: 22px;
    }
    .ant-pagination.mini .ant-pagination-prev {
      margin-right: 4px;
    }
    .ant-pagination.mini .ant-pagination-item a {
      height: 22px;
      font-size: 14px;
      line-height: 22px;
    }
    .ant-pagination.mini .ant-pagination-total-text, .ant-pagination.mini .ant-pagination-simple-pager {
      height: 28px;
      line-height: 28px;
    }

    .ant-pagination.mini {
      :global {
        .ant-select-single.ant-select-sm:not(.ant-select-customize-input) .ant-select-selector {
          height: 28px;
          line-height: 28px;
          display: flex;
          align-items: center;
        }
        .ant-select-single.ant-select-sm:not(.ant-select-customize-input) .ant-select-selector {
          padding: 3px 12px;
        }
        .ant-select-arrow {
          right: 11px;
        }
      }
    }

    .ant-pagination.mini .ant-pagination-options-quick-jumper input {
      height: 28px;
      width: 80px;
    }
    .ant-pagination.mini .ant-pagination-options-quick-jumper {
      height: 28px;
      line-height: 22px;
      margin-left: 8px;
    }
  }

}
