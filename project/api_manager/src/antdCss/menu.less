@import '~@lynx/design-token/dist/less/token.less';
/**
  antd menu组件样式重构
 */
body {
  :global {
    .ant-menu {
      color: @color-text-primary;
    }
    .ant-menu-item {
      padding: 13px 16px;
      height: 48px;
      display: flex;
      align-items: center;
    }
    .ant-menu-item .anticon, .ant-menu-submenu-title .anticon {
      margin-right: 12px;
      font-size: 16px;
    }
    // 解决菜单折叠时，unhover二级菜单不消失的问题
    .ant-menu-submenu-hidden{
      display: none !important;
    }

    // 二级菜单选中样式问题
    .ant-menu-item-selected {
      color: @color-text-brand;
      background: @color-bg-basic-active;
    }
    .ant-menu:not(.ant-menu-horizontal) .ant-menu-item-selected {
      background-color: fade(@color-bg-brand, 8%);
    }

    .ant-menu-inline .ant-menu-item, .ant-menu-inline .ant-menu-submenu-title {
      width: 100%;
    }
    .ant-menu-sub.ant-menu-inline > .ant-menu-item, .ant-menu-sub.ant-menu-inline > .ant-menu-submenu > .ant-menu-submenu-title {
      height: 48px;
      line-height: 48px;
    }
    .ant-menu-vertical > .ant-menu-item, .ant-menu-vertical-left > .ant-menu-item, .ant-menu-vertical-right > .ant-menu-item, .ant-menu-inline > .ant-menu-item, .ant-menu-vertical > .ant-menu-submenu > .ant-menu-submenu-title, .ant-menu-vertical-left > .ant-menu-submenu > .ant-menu-submenu-title, .ant-menu-vertical-right > .ant-menu-submenu > .ant-menu-submenu-title, .ant-menu-inline > .ant-menu-submenu > .ant-menu-submenu-title {
      height: 48px;
      line-height: 48px;
    }
    .ant-menu-vertical .ant-menu-item:not(:last-child), .ant-menu-vertical-left .ant-menu-item:not(:last-child), .ant-menu-vertical-right .ant-menu-item:not(:last-child), .ant-menu-inline .ant-menu-item:not(:last-child) {
      margin-bottom: 0;
    }
    .ant-menu-vertical .ant-menu-item, .ant-menu-vertical-left .ant-menu-item, .ant-menu-vertical-right .ant-menu-item, .ant-menu-inline .ant-menu-item, .ant-menu-vertical .ant-menu-submenu-title, .ant-menu-vertical-left .ant-menu-submenu-title, .ant-menu-vertical-right .ant-menu-submenu-title, .ant-menu-inline .ant-menu-submenu-title {
      margin-bottom: 0;
      margin-top: 0;
      padding: 13px 16px;
      display: flex;
      align-items: center;
    }
    .ant-menu-inline-collapsed > .ant-menu-item, .ant-menu-inline-collapsed > .ant-menu-item-group > .ant-menu-item-group-list > .ant-menu-item, .ant-menu-inline-collapsed > .ant-menu-item-group > .ant-menu-item-group-list > .ant-menu-submenu > .ant-menu-submenu-title, .ant-menu-inline-collapsed > .ant-menu-submenu > .ant-menu-submenu-title{
      padding: 16px;
    }
  }
}
