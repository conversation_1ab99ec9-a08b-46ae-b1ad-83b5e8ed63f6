@import '~@lynx/design-token/dist/less/token.less';
body {
  :global {
    .ant-picker-suffix {
      color: @color-fill-secondary;
    }
    .ant-picker-clear {
      font-size: 16px;
    }
    .ant-picker-range .ant-picker-clear {
      right: 9px;
    }
    .ant-picker {
      padding: 7px 8px 7px 12px;
      height: 32px;
      background: @color-bg-white;
      border: 1px solid @color-border-form-default;
      border-radius: @radius-border-medium;
    }
    .ant-picker.ant-picker-disabled {
      border-color: @color-border-form-disabled;
      background: @color-bg-basic-disabled;
      &:hover {
        border-color: @color-border-form-disabled;
      }
    }
    .ant-picker-input > input[disabled] {
      color: @color-text-disabled;
    }
    .ant-picker-panel-container {
      border-radius: @radius-border-medium;
      position: absolute;
      transition: left 0.3s ease-out;
    }
    .ant-picker-header {
      align-items: center;
      height: 40px;
      background: @color-bg-secondary;
      border-radius: 4px 4px 0px 0px;
      border-bottom: 1px solid @color-border-default;
      padding: 12px 24px;
      color: @color-text-primary;
    }
    .ant-picker-header-view {
      line-height: initial;
      font-weight: 400;
    }
    .ant-picker-header > button {
      height: 16px;
      min-width: auto;
    }
    .ant-picker-header button {
      line-height: 16px;
    }
    // 图标
    .ant-picker-prev-icon, .ant-picker-super-prev-icon, .ant-picker-next-icon, .ant-picker-super-next-icon {
      -webkit-transform: inherit;
      transform: inherit;
      width: fit-content;
      height: fit-content;
      font-size: 16px;
    }
    .ant-picker-prev-icon::before, .ant-picker-next-icon::before, .ant-picker-super-prev-icon::before, .ant-picker-super-next-icon::before {
      display: none;
    }
    .ant-picker-super-prev-icon::after, .ant-picker-super-next-icon::after {
      display: none;
    }
    // body
    .ant-picker-date-panel .ant-picker-body {
      padding: 16px;
    }
    .ant-picker-content > thead > tr > th {
      color: @color-text-tertiary;
    }
    .ant-picker-cell {
      padding: 4px;
      width: 32px;
      height: 32px;
      color: @color-text-quaternary;
    }
    .ant-picker-cell-in-view {
      color: @color-text-primary;
    }
    .ant-picker-cell:hover:not(.ant-picker-cell-in-view) .ant-picker-cell-inner, .ant-picker-cell:hover:not(.ant-picker-cell-selected):not(.ant-picker-cell-range-start):not(.ant-picker-cell-range-end):not(.ant-picker-cell-range-hover-start):not(.ant-picker-cell-range-hover-end) .ant-picker-cell-inner {
      background: @color-bg-basic-hover;
    }
    .ant-picker-cell-in-view.ant-picker-cell-selected .ant-picker-cell-inner, .ant-picker-cell-in-view.ant-picker-cell-range-start .ant-picker-cell-inner, .ant-picker-cell-in-view.ant-picker-cell-range-end .ant-picker-cell-inner {
      background: @color-bg-brand;
      border-radius: @radius-border-medium;
    }
    .ant-picker:hover, .ant-picker-focused {
      border-color: @color-border-brand;
    }
    .ant-picker-cell-disabled .ant-picker-cell-inner {
      color: @color-text-disabled;
      background: @color-bg-basic-disabled;
    }
    .ant-picker-cell-disabled::before {
      background: @color-bg-basic-disabled;
    }
    .ant-picker-focused {
      box-shadow: 0 0 0 2px RGBA(207, 218, 247, 1.00);
    }
    .ant-picker-range.ant-picker-focused .ant-picker-active-bar {
      opacity: 0;
      background: @color-bg-brand;
    }
    .ant-picker-ranges .ant-picker-ok {
      :global {
        .ant-btn {
          //height: 24px;
          height: 28px;
          font-size: 12px;
          line-height: 14px;
        }
      }
    }
    .ant-picker-input > input {
      color: @color-text-primary;
    }
    .ant-picker-footer {
      border-top: 1px solid @color-border-default;
      height: 44px;
      border-bottom: none;
      line-height: inherit;
    }
    .ant-picker-today-btn.ant-picker-today-btn-disabled {
      color: @color-text-disabled;
    }
    .ant-picker-today-btn {
      color: @color-text-brand;
      line-height: 44px;
    }
    .ant-picker-cell-in-view.ant-picker-cell-today .ant-picker-cell-inner::before {
      display: none;
    }
    .ant-picker-cell-disabled.ant-picker-cell-today .ant-picker-cell-inner::before {
      border: 1px solid @color-border-form-disabled;
    }
    .ant-picker-cell-in-view.ant-picker-cell-today .ant-picker-cell-inner::after {
      content: '';
      width: 4px;
      height: 4px;
      background: @color-bg-brand;
      border-radius: 50%;
      top: 19px;
      left: 11px;
      position: absolute;
    }

    // 日期选择
    .ant-picker-cell-in-view.ant-picker-cell-in-range::before {
      background: @color-bg-basic-active;
    }
    .ant-picker-cell-in-view.ant-picker-cell-range-start:not(.ant-picker-cell-range-start-single):not(.ant-picker-cell-range-end) .ant-picker-cell-inner {
      border-radius: @radius-border-medium;
    }
    .ant-picker-cell-in-view.ant-picker-cell-range-end:not(.ant-picker-cell-range-end-single):not(.ant-picker-cell-range-start) .ant-picker-cell-inner {
      border-radius: @radius-border-medium;
    }
    .ant-picker-cell-in-view.ant-picker-cell-range-start:not(.ant-picker-cell-range-start-single)::before, .ant-picker-cell-in-view.ant-picker-cell-range-end:not(.ant-picker-cell-range-end-single)::before {
      background: @color-bg-basic-active;
    }

    .ant-picker-date-panel .ant-picker-cell-in-view.ant-picker-cell-in-range.ant-picker-cell-range-hover-start.ant-picker-cell-today .ant-picker-cell-inner::after {
      background: @color-bg-brand;
      top: 19px;
      left: 11px;
    }
    .ant-picker-cell-in-view.ant-picker-cell-in-range.ant-picker-cell-range-hover::before, .ant-picker-cell-in-view.ant-picker-cell-range-start.ant-picker-cell-range-hover::before, .ant-picker-cell-in-view.ant-picker-cell-range-end.ant-picker-cell-range-hover::before, .ant-picker-cell-in-view.ant-picker-cell-range-start:not(.ant-picker-cell-range-start-single).ant-picker-cell-range-hover-start::before, .ant-picker-cell-in-view.ant-picker-cell-range-end:not(.ant-picker-cell-range-end-single).ant-picker-cell-range-hover-end::before, .ant-picker-panel > :not(.ant-picker-date-panel) .ant-picker-cell-in-view.ant-picker-cell-in-range.ant-picker-cell-range-hover-start::before, .ant-picker-panel > :not(.ant-picker-date-panel) .ant-picker-cell-in-view.ant-picker-cell-in-range.ant-picker-cell-range-hover-end::before {
      background: @color-bg-basic-active;
    }
    .ant-picker-cell-in-view.ant-picker-cell-range-hover-start:not(.ant-picker-cell-in-range):not(.ant-picker-cell-range-start):not(.ant-picker-cell-range-end)::after, .ant-picker-cell-in-view.ant-picker-cell-range-hover-end:not(.ant-picker-cell-in-range):not(.ant-picker-cell-range-start):not(.ant-picker-cell-range-end)::after, .ant-picker-cell-in-view.ant-picker-cell-range-hover-start.ant-picker-cell-range-start-single::after, .ant-picker-cell-in-view.ant-picker-cell-range-hover-end.ant-picker-cell-range-end-single::after, .ant-picker-cell-in-view.ant-picker-cell-range-hover:not(.ant-picker-cell-in-range)::after {
      border-top: 1px dashed @color-border-brand;
      border-bottom: 1px dashed @color-border-brand;
    }
    tr > .ant-picker-cell-in-view.ant-picker-cell-range-hover:last-child::after, tr > .ant-picker-cell-in-view.ant-picker-cell-range-hover-start:last-child::after, .ant-picker-cell-in-view.ant-picker-cell-range-hover-edge-end:not(.ant-picker-cell-range-hover-edge-end-near-range)::after, .ant-picker-cell-in-view.ant-picker-cell-range-hover-end::after {
      border-right: 1px dashed #326bfb;
      border-top-right-radius: 4px;
      border-bottom-right-radius: 4px;
    }
    .ant-picker-now {
      float: left;
    }
    .ant-picker-ranges {
      padding: 8px 12px;
      line-height: inherit;
      //display: flex;
      //align-items: center;
      //justify-content: space-between;
    }
  }
}
