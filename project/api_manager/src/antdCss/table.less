@import '~@lynx/design-token/dist/less/token.less';
/**
  表格样式重构
 */
body {
  :global {
    // 表头
    .ant-table .ant-table-container .ant-table-thead .ant-table-cell {
      background: @color-bg-basic-table-head;
      border-color: @color-border-divider;
      font-size: @typography-font-size;
      line-height: 22px;
      height: 22px;
      padding: 9px 12px;
      font-weight: bold; // 500
      color: @color-text-primary;
    }

    .ant-table-thead > tr > th {
      border-bottom: 1px solid @color-border-table;
    }

    .ant-table .ant-table-cell {
      background-color: @color-bg-white;
    }

    .ant-table .ant-table-container {
      border-radius: @shape-border-radius;
    }

    // 表格 body
    .ant-table .ant-table-container .ant-table-tbody .ant-table-cell {
      height: 22px;
      padding: 9px 12px;
    }

    .ant-table-tbody > tr > td {
      border-bottom: 1px solid @color-border-table;
    }

    // 有边框的表格
    .ant-table.ant-table-bordered > .ant-table-container > .ant-table-content > table > thead > tr > th {
      border-right: 1px solid @color-border-table;
    }

    .ant-table.ant-table-bordered > .ant-table-container > .ant-table-content > table > tbody > tr > td {
      border-right: 1px solid @color-border-table;
    }

    .ant-table.ant-table-bordered > .ant-table-container {
      border: 1px solid @color-border-table;
    }

    .ant-table .ant-table-container .ant-table-tbody .ant-table-row {
      &:hover {
        .mainLink {
          color: @color-text-brand !important;
        }
      }
    }

    .ant-table {
      .ant-table-container {
        .ant-table-tbody {
          .ant-table-row:nth-last-child(1) {
            .ant-table-cell:nth-child(1) {
              border-radius: 0 0 0 @shape-border-radius;
            }

            .ant-table-cell:nth-last-child(1) {
              border-radius: 0 0 @shape-border-radius 0;
            }
          }
        }
      }
    }
    // 表格有边框时,最后一列不应该有border
   .ant-table.ant-table-bordered > .ant-table-container {
     .ant-table-content > table > thead > tr > th {
       &:last-of-type {
         border-right: none;
       }
     }
     .ant-table-tbody > tr {
       td {
         &:last-of-type {
           border-right: none;
         }
       }
       &:last-of-type {
         td {
           border-bottom: none;
         }
       }
     }
   }
    // 表头有问号图标
    .ant-table-thead .ant-table-cell .svg-common_system_question_surface {
      color: @color-fill-tertiary;
      margin-left: 4px;
    }
    .ant-table-thead .ant-table-cell .svg-common_system_help02 {
      color: @color-fill-tertiary;
      margin-left: 4px;
    }
    // 筛选图标
    .ant-table-filter-column {
      display: flex;
      justify-content: space-between;
      margin: 0;
    }
    .ant-table-filter-column-title {
      padding: 0;
    }
    .ant-table-filter-trigger {
      font-size: 16px;
      color: @color-fill-secondary;
    }
    .ant-table-filter-trigger-container-open, .ant-table-filter-trigger-container:hover, .ant-table-thead th.ant-table-column-has-sorters:hover .ant-table-filter-trigger-container:hover {
      background: transparent;
    }
    .ant-table-filter-trigger-container-open .ant-table-filter-trigger, .ant-table-filter-trigger:hover {
      color: @color-fill-brand-default;
    }
    .ant-table-column-sorter-up.active, .ant-table-column-sorter-down.active {
      color: @color-fill-brand-default;
    }
    .ant-table-column-sorters {
      padding: 0;
      display: flex;
      justify-content: space-between;
    }
    .ant-table-column-sorter-full {
      color: @color-fill-secondary;
    }
    .ant-table-column-sorter-up, .ant-table-column-sorter-down {
      font-size: 13px;
    }
    // 可展开的表格
    .ant-table-expanded-row .ant-table-cell {
      background-color: @color-bg-basic-contain;
      padding: 12px;
    }
    .ant-table-cell.ant-table-row-expand-icon-cell {
      font-size: 16px;
      color: @color-fill-primary;
    }
  }

}
