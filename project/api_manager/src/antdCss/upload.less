@import '~@lynx/design-token/dist/less/token.less';

body {
  :global {
    .ant-upload {
      color: @color-text-primary;
    }
    .ant-upload.ant-upload-drag {
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      padding: 14px 39px;
      border: 1px solid @color-border-form-default;
      border-radius: @radius-border-medium;
    }
    .ant-upload-list-item {
      height: auto;
      padding: 5px 8px;
      border: 1px solid @color-border-form-default;
      border-radius: @radius-border-small;
    }
    .ant-upload-list-item-info {
      display: flex;
      align-items: center;
      position: relative;
      border-radius: @radius-border-small;
    }
    .ant-upload-list-item-info > span {
      display: flex;
      align-items: center;
    }
    .ant-upload-list-item-name {
      padding-left: 4px;
    }
    .ant-upload-list-item:hover .ant-upload-list-item-info {
      background-color: #fff;
    }
    .ant-upload-list-item-info .anticon-loading .anticon, .ant-upload-list-item-info .ant-upload-text-icon .anticon {
      position: relative;
      top: 0px;
    }
    .ant-upload.ant-upload-drag:not(.ant-upload-disabled):hover {
      border-color: @color-border-brand1-hover;
    }
    .ant-upload-list-item .ant-upload-list-item-card-actions-btn {
      opacity: 1;
    }
  }
}
