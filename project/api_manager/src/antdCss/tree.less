@import '~@lynx/design-token/dist/less/token.less';

body {
    :global {

        // tree checkbox
        .ant-tree {
            .ant-tree-checkbox {
                margin-right: 4px;
            }
        }

        // border-radius 4px
        .ant-tree-checkbox-inner {
            border-radius: @radius-border-medium;
        }

        // checked
        .ant-tree-checkbox-checked {
            .ant-tree-checkbox-inner {
                background-color: @color-bg-brand;
                border: 1px solid @color-border-brand;
            }

            &::after {
                border: 1px solid @color-border-brand;
                border-radius: @radius-border-medium;
            }
        }

        // 行样式
        .ant-tree.ant-tree-directory {
            .ant-tree-treenode {
                &:hover::before {
                    background-color: @color-bg-basic-tag;
                }

                .ant-tree-node-content-wrapper.ant-tree-node-selected {
                    color: @color-text-primary;
                }
            }

            // 选中行背景色 & 文字颜色
            .ant-tree-treenode-selected {
                &:hover::before {
                    background-color: @color-bg-brand1-tag;
                }

                &::before {
                    background-color: @color-bg-brand1-tag;
                }

                .ant-tree-switcher {
                    color: @color-fill-primary;
                }
            }
        }

        // 滚动条样式
        .ant-tree-list-scrollbar {
            width: 6px !important;

            .ant-tree-list-scrollbar-thumb {
                background-color: @color-fill-tertiary  !important;

                &:hover {
                    background-color: @color-fill-secondary  !important;
                }
            }
        }
    }
}