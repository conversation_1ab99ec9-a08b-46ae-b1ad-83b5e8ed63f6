@import '~@lynx/design-token/dist/less/token.less';
/**
  antd tag组件样式重构
 */
body {
  :global {
    .ant-tag {
      border-radius: @radius-border-medium;
      min-height: 24px;
      padding: 3px 8px;
      line-height: 18px;
    }
    .ant-tag-success {
      color: @color-text-success !important;
      background-color: @color-bg-success1-tag !important;
    }
    .ant-tag-processing {
      color: @color-text-info1-default !important;
      background-color: @color-bg-info1-tag !important;
    }
    .ant-tag-error {
      color: @color-text-error !important;
      background-color: @color-bg-error1-tag !important;
    }
    .ant-tag-warning {
      color: @color-text-warning !important;
      background-color: @color-bg-warning1-tag !important;
    }
    .ant-tag-primary {
      color: @color-text-brand !important;
      background-color: @color-bg-brand1-tag !important;
    }
    .ant-tag-default {
      color: @color-text-primary !important;
      background-color: @color-bg-basic-tag !important;
    }
    // 尺寸
    .ant-tag-lg {
      line-height: 26px;
      height: 32px;
      min-height: 32px;
      font-size: 14px;
    }
    .ant-tag-m {
      line-height: 22px;
      height: 28px;
      min-height: 28px;
      font-size: 14px;
    }
    .ant-tag-sm {
      line-height: 18px;
      height: 24px;
      min-height: 24px;
      font-size: 14px;
    }
    .ant-tag-xsm {
      padding: 1px 8px;
      font-size: 12px;
      line-height: 18px;
      height: 20px;
      min-height: 20px;
    }
  }
}
