@import '~@lynx/design-token/dist/less/token.less';
/**
antd switch开关样式重构
 */
body {
  :global {
    //纯按钮尺寸：M
    .ant-switch {
      min-width: 46px;
      color: @color-text-white;
      background-color: @color-palette-gray-500;
    }
    .ant-switch-checked {
      background-color: @color-bg-brand;
      .ant-switch-inner {
        margin: 0px @spacing_gap_6 0 @spacing_gap_2;
      }
    }
    .ant-switch-handle::before {
      box-shadow: none;
      -webkit-box-shadow: none;
    }
    .ant-switch-inner {
      margin: 0 @spacing_gap_2 0 @spacing_gap_6;
      font-size: @typography-number-6-font-size;
    }

    // disabled
    .ant-switch-checked.ant-switch-disabled {
      background-color: @color-bg-brand1-disabled;
      .ant-switch-handle::before {
        background-color: @color-bg-primary;
        opacity: 0.8;
      }
      .ant-switch-inner {
        color: @color-text-white;
      }
    }
    .ant-switch-loading, .ant-switch-disabled {
      background-color: @color-bg-quaternary;
      opacity: 1;
      .ant-switch-handle::before {
        background-color: @color-palette-gray-500;
      }
      .ant-switch-inner {
        color: @color-text-tertiary;
      }
    }

    // 纯按钮尺寸：S
    .ant-switch-small {
      min-width: 24px;
      height: 12px;
      line-height: 12px;
    }
    .ant-switch-small .ant-switch-handle {
      width: 10px;
      height: 10px;
      left: 1px;
      top: 1px;
    }
    .ant-switch-small.ant-switch-checked .ant-switch-handle {
      left: calc(100% - 8px - 2px);
    }
  }
}
