@import '~@lynx/design-token/dist/less/token.less';
/**
  antd alert组件样式重构
 */
body {
  :global {
    .ant-alert {
      display: flex;
      align-items: center;
      border: none;
      padding: 7px 12px;
      .font_body();
      border-radius: @radius-border-medium;
      .ant-alert-icon {
        position: static;
        margin-right: 8px;
        font-size: 16px;
      }
    }
    .ant-alert-message {
      flex: 1;
    }
    .ant-alert.ant-alert-closable {
      padding: 12px;
    }
    .ant-alert-with-description .ant-alert-message {
      display: inline-block;
    }
    .ant-alert-with-description .ant-alert-description {
      margin-left: 24px;
    }
    .ant-alert-with-description {
      padding: 12px;
    }
    .ant-alert-success {
      background-color: @color-bg-success1-notice;
      border: none;
      .ant-alert-icon {
        color: @color-fill-success-default;
      }
    }
    .ant-alert-info {
      background-color: @color-bg-brand1-notice;
      border: none;
      .ant-alert-icon {
        color: @color-fill-brand-default;
      }
    }
    .ant-alert-warning {
      background-color: @color-bg-warning1-notice;
      border: none;
      .ant-alert-icon {
        color: @color-fill-warning-default;
      }
    }
    .ant-alert-error {
      background-color: @color-bg-error1-notice;
      border: none;
      .ant-alert-icon {
        color: @color-fill-error-default;
      }
    }
  }
}
