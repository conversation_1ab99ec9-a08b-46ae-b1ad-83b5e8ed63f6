@import '~@lynx/design-token/dist/less/token.less';
// 滚动条
*::-webkit-scrollbar {
  /*滚动条整体样式*/
  margin-right: 4px;
  width: 6px;  /*高宽分别对应横竖滚动条的尺寸*/
  height: 6px;
}
*::-webkit-scrollbar-thumb {
  /*滚动条里面小方块*/
  border-radius: 100px;
  // background-color: rgba(0, 0, 0, 0.618); // rgba(24,144,255, 0.8);
  background-color: @color-palette-gray-500;
}
*::-webkit-scrollbar-track {
  /*滚动条里面轨道*/
  box-shadow   : inset 0 0 5px rgba(0, 0, 0, 0);
  background   : transparent;
  border-radius: 10px;
}
/* 设置鼠标悬浮时的样式(将原本为#ccc改为#999) */
::-webkit-scrollbar-thumb:hover {
  background-color: @color-bg-filter-active;
}
