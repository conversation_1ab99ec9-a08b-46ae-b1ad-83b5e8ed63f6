.menuTopInfoBox {
    // margin-left: 8px;
    margin-right: 8px;
    overflow: hidden;
    width: fit-content;
    border-radius: 6px;

    &.hover {
        background-color: #F5F7FA;
    }

    :hover {
        background-color: #F5F7FA;
    }
}

.menuTopInfo {
    padding: 4px 8px;
    display: flex;
    align-items: center;
    cursor: pointer;
}

.collapsedMenuTopInfo {
    width: var(--kdev-sider-content-collapsed-width);
    padding: 7px 8px;
    overflow: hidden;
}

.avatar {
    border-radius: 6px !important;
    // background: #fff;
    margin-right: 0px !important;
}

.title {
    margin-left: 8px;
    // width: 100px;
    flex: 1 0 0;
    color: var(---text_primary, #252626);
    font-family: 'PingFang SC';
    font-size: 14px;
    font-style: normal;
    font-weight: 500;
    line-height: 22px;
    overflow: hidden;
    display: -webkit-box;
    text-overflow: ellipsis;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 1;
    overflow-wrap: break-word;
}

.toggleIcon {
    margin-left: 4px;
    font-size: 16px;
    color: #898A8C;
}