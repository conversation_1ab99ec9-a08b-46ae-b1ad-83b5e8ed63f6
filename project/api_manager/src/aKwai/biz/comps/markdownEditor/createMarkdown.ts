import { loadScript, createCSSLink } from '@libs/utils';

declare var editormd: any;
// const RES_ROOT = '/assets/editor.md';
// 版本：v1.5.0
const RES_ROOT = 'https://cdnfile.corp.kuaishou.com/kc/files/a/QA-SERVEREE-FE-IN/fe/common/editor.md';

let idIndex = 0;
function getContainer(prefix = 'preloadMarkdownRes_') {
    const ID = prefix + idIndex++;
    const mddom = document.createElement('div');
    mddom.id = ID;
    return mddom;
}

const COMMON_MD_CONFIG = {
    path: RES_ROOT + '/lib/',
    saveHTMLToTextarea: true,
    emoji: true,
    taskList: true,
    codeFold: true,
    tocm: true,
    tocContainer: '',
    tocDropdown: false,
    tocTitle: '目录',
    searchReplace: true,
    // 下面的选择暂时无法打开，用户确实有需求时，联系 @wanglifei03
    tex: false,
    flowChart: false,
    sequenceDiagram: false,
    autoLoadModules: false,
};
const htmlDecodeSetting = 'style,script,iframe|on*';
let loadedCreateEditorProm: Promise<void> | null = null;
const loadedDisplayEditor = false;

/**
 * 可用于提前手动加载 markdown 所需的资源
 * @param preload
 */
async function loadMarkdownRes(preload = false): Promise<void> {
    if (!loadedCreateEditorProm) {
        loadedCreateEditorProm = new Promise(async (resolve) => {
            createCSSLink(RES_ROOT + '/lib/codemirror/codemirror.min.css');
            createCSSLink(RES_ROOT + '/css/editormd.min.css');
            await Promise.all([
                loadScript(RES_ROOT + '/jquery.min.js'),
                loadScript(RES_ROOT + '/lib/marked20230131.js'),
                loadScript(RES_ROOT + '/lib/codemirror/codemirror.min.js'),
            ]);
            // 此文件依赖 codemirror 且必须靠前加载；其次 await 是为了解决初次加载时 当前编辑的行背景不显示颜色问题
            await Promise.all([
                loadScript(RES_ROOT + '/lib/codemirror/addons.min.js'),
                loadScript(RES_ROOT + '/lib/codemirror/modes.min.js'),
            ]);
            // 依赖 jq
            await loadScript(RES_ROOT + '/editormd20220803.js');
            resolve();
            // if (preload) {
            //     const mddom = getContainer('preloadMarkdownRes_');
            //     document.body.appendChild(mddom);
            //     const editor = editormd(
            //         mddom.id, {
            //             width: '1px',
            //             height: '1px',
            //             markdown: '',
            //         },
            //     );
            //     setTimeout(() => {
            //         editor?.editor?.remove();
            //         if (mddom) {
            //             mddom.innerHTML = '';
            //             mddom.parentNode?.removeChild(mddom);
            //         }
            //     }, 1000);
            // }
        });
    }
    return loadedCreateEditorProm;
}

function getConf(destConf, resolve) {
    const oldOnload = destConf.onload;
    destConf.onload = function onloadMD() {
        resolve(this);
        const editorinst = this;
        // 这里为了兼容项目中已有的代码和用法
        requestAnimationFrame(() => {
            oldOnload?.call(editorinst, editorinst);
        });
    };
    return destConf;
}
/**
 * 创建一个 markdown 编辑器
 * @param dom
 * @param config
 *
 * e.g. const editor = await createMarkdown(dom_div);
 *
 * API: https://pandao.github.io/editor.md/examples/index.html
 * github: https://github.com/pandao/editor.md
 */
export async function createMarkdown(
    dom: HTMLDivElement | undefined,
    conf: any = {}
): Promise<IPlainObject | null> {
    const config = Object.assign({
        htmlDecode: htmlDecodeSetting
    }, conf);
    try {
        await loadMarkdownRes();
        const mddom = getContainer('createMarkdown_');
        dom && dom.appendChild(mddom);
        const destConf = Object.assign({
            width: '100%',
            // height: 700,
            markdown: '',
        }, COMMON_MD_CONFIG, config);
        return new Promise(resolve => {
            editormd(mddom.id, getConf(destConf, resolve));
        });
    } catch {
        return Promise.resolve(null);
    }
}
