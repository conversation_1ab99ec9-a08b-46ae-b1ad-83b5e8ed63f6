import React from 'react';
import { createMarkdown } from './createMarkdown';
import { PluginBase, IPluginBase } from './plugins';
import './MarkdownEditor.less';
import { ifWindows, ifMac } from './whichSystem';

/* 使用示例如下：

import { MarkdownEditorCustom, IMarkdownEditorCustomPropsPlugin, MentionPlugin } from '@biz/comps';

const PLUGINS: IMarkdownEditorCustomPropsPlugin[] = [{
    plugin: MentionPlugin,
    pluginConf: {
        getMentions: async (val) => {
            // 调用接口 返回用户列表，数据格式如下
            return [{
                username: 'wanglifei03',
                name: '王利飞',
                avatarUrl: ''
            }, {
                username: 'litongtong03',
                name: '李同同',
                avatarUrl: ''
            }];
        }
    }
}];

// conf 参考：view-source:http://editor.md.ipandao.com/examples/full.html
// https://github.com/pandao/editor.md/blob/master/editormd.js
// 原 api 完全不变；仅有 onload 增加了第一个参数是 editor，可以直接用
<MarkdownEditorCustom
    conf={{
        readOnly: false,
        onload: (editor) => {
            if (!editor) {
                return;
            }
        }}
    }
    plugins={ PLUGINS }
    className={ css.markdown }
/>
 */

export interface IMarkdownEditorCustomPropsPlugin {
    plugin: new (arg: IPluginBase) => PluginBase; // 插件的类名
    pluginConf: IPlainObject; // 插件的配置
}

/**
 * 基础 Markdown 编辑器组件 props
 */
export interface IMarkdownEditorCustomProps extends React.HTMLAttributes<HTMLDivElement> {
    conf?: IPlainObject; // editormd 的初始化 配置
    plugins?: IMarkdownEditorCustomPropsPlugin[];
    delay?: number;
}

declare var editormd: any;

/**
 * 基础 Markdown 编辑器，支持自定义插件
 * 默认不带 任何自定义插件
 */
export class MarkdownEditorCustom extends React.Component<IMarkdownEditorCustomProps, any> {
    private domRef = React.createRef<HTMLDivElement>();
    private editor;
    private plugins: PluginBase[] = [];

    public async componentDidMount() {
        if (this.domRef.current) {
            const _this = this;
            const conf = Object.assign({}, this.props.conf, {
                indentWithTabs: false,
                disabledKeyMaps: ['Ctrl-B'], // 此处为恢复Ctrl-B在MAC系统的默认功能，
                /**
                 * 注意此处的 onload 事件 不能使用 匿名函数，以便接收 this 上下文（ editor 实例 ）
                 */
                onload: (editorinst) => {
                    this.onEditorLoaded(editorinst);
                }
            });
            setTimeout(() => {
                this.domRef.current && createMarkdown(this.domRef.current, conf);
            }, this.props.delay ?? 1000);
        }
    }

    public componentWillUnmount(): void {
        this.uninstallPlugins(this.editor);
    }

    public render() {
        const { conf, plugins, ...props } = this.props;
        return <div ref={this.domRef} {...(props as any)} />;
    }

    /**
     * 编辑器加载完成后执行
     */
    private onEditorLoaded(editor) {
        if (!editor) {
            return;
        }
        // https://codemirror.net/doc/manual.html#config
        const cmConfig = {
            spellcheck: false,
            autocorrect: false,
            autocapitalize: false,
        };
        Object.keys(cmConfig).forEach(key => editor.setCodeMirrorOption(key, cmConfig[key]));

        this.fixKeyboard(editor);

        // this.domRef.current?.classList.add(this.props.className || '');
        /**
         * 「@」后的字符串识别为 用户邮箱前缀，可以 kim 打开
         */
        editormd.urls.atLinkBase = 'kim://username?username=';
        this.editor = editor;
        this.installPlugins(editor);
        /**
         * editor 加载后实例要回传至外部，以便外部进行处理
         * （后续可以考虑回传一个 wrapped 对象，代理访问某些api）
         */
        this.props.conf?.onload?.call(editor, editor);
    }

    /**
     * 修改快捷键，更符合用户习惯
     */
    private fixKeyboard(editor) {
        editor.addKeyMap({
            // tslint:disable-next-line:only-arrow-functions
            Tab: function (cm) {
                const spaces = Array(cm.getOption('indentUnit') + 1).join(' ');
                cm.replaceSelection(spaces);
            }
        });

        // 因前面为考虑MAC兼容性，配置禁用了自带Ctrl-B，所以在Windows系统时需恢复此功能
        if (ifWindows()) {
            editor.addKeyMap({
                // tslint:disable-next-line:only-arrow-functions
                'Ctrl-B': function (cm) {
                    cm.execCommand('goCharLeft');
                }
            });
        }
        if (ifMac()) {
            editor.addKeyMap({
                // tslint:disable-next-line:only-arrow-functions
                'Cmd-B': function (cm) {
                    const content = cm.doc.getSelection();
                    const position = cm.doc.getCursor('to');
                    cm.doc.replaceSelection(`**${content}**`);
                    cm.doc.setCursor(position.line, position.ch + 2);
                },
                // tslint:disable-next-line:only-arrow-functions
                'Ctrl-E': function (cm) {
                    // 移动至本行末
                    cm.execCommand('goLineEnd');
                },
                // tslint:disable-next-line:only-arrow-functions
                'Ctrl-D': function (cm) {
                    // 移动至本行末
                    cm.execCommand('delCharAfter');
                }
            });
        }
    }

    /**
     * 按顺序初始化所有插件，启动插件，并存储在本实例内
     */
    private installPlugins(editor) {
        this.props.plugins?.forEach(({ plugin, pluginConf }) => {
            const inst = new plugin({ editor, pluginConf });
            inst.run();
            this.plugins.push(inst);
        });
    }

    /**
     * 按顺序销毁所有插件
     */
    private uninstallPlugins(editor) {
        this.plugins.forEach(it => it.destroy());
        editor?.editor?.remove();
    }
}
