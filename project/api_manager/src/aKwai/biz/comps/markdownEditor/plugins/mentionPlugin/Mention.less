.MentionPlugin {
  z-index: 999;
}

.Mention {
  max-width: 300px;
  background: #fff;
  position: relative;
  // max-height: 242px;
  height: 200px;
  overflow: auto;
  border-radius: 2px;
  border: 1px solid #eee;
  // box-shadow: 0 3px 6px -4px #0000001f, 0 6px 16px #00000014, 0 9px 28px 8px #0000000d !important;
  * {
    font-family: PingFangSC, PingFangSC-Regular, Helvetica;
  }
  :global {
    .ant-menu-item {
      display: flex;
      padding: 0;
      margin-bottom: 0 !important;
      margin-top: 0;
      &:hover {
        color: unset;
      }
    }
    .ant-avatar-string {
      line-height: 32px;
    }
  }
  .option {
    display: flex;
    align-items: center;
    flex: 1;
    padding: 5px 12px;
    &:hover {
      background-color:#f5f5f5;
    }
    &.active {
      background-color: rgba(50, 125, 255, 0.08);
    }
    .info{
      margin-left: 10px;
      p{
        margin: 0;
        line-height: 14px;
      }
    }
  }
}
:global {
  .kdev-mention-wrap {
    left: -10px!important;
  }
}
