export interface IPluginBase<T = any> {
    editor: any;
    pluginConf: T;
}

/**
 * 插件基类，是 editormd 支持插件架构的基础
 * 已包含 核心context： editor、pluginConf
 */
export class PluginBase<T = any> {
    protected editor: any; // 编辑器实例
    protected readonly pluginConf: T; // 本插件的配置

    constructor({ editor, pluginConf }: IPluginBase<T>) {
        this.editor = editor;
        this.pluginConf = pluginConf;
    }

    /**
     * 插件开始运行
     */
    public run() {
        console.info('PluginBase.run');
    }

    /**
     * 插件停止运行
     */
    public stop() {
        console.info('PluginBase.stop');
    }

    /**
     * 销毁插件
     */
    public destroy() {
        console.info('PluginBase.destroy');
    }
}
