import ReactDOM from 'react-dom';
import React from 'react';
import { Mention } from './Mention';
import { MentionVM } from './MentionVM';
import { PluginBase } from '../PluginBase';
import { Bind, Debounce } from 'lodash-decorators';
import css from './Mention.less';

let idIndex = 0;
const DEBOUNCE_TIME = 300;
const MENTION_LIST_WIDTH = 302;

interface IMentionPluginConf extends IPlainObject {
    getMentions(val: string): Promise<IEntityUser[]>;
}
export const History_Participants = 'History_Participants';
interface IWordAndPos {
    word: string;
    ifMention?: boolean;
    pos?: {
        from: {
            ch: number;
            line: number;
        },
        to: {
            ch: number;
            line: number;
        }
    };
}

interface IEventKeyMap {
    [key: string]: {
        event: (event?: KeyboardEvent) => void;
    };
}

/**
 * 实现 @人（也称提及、Mention）功能的插件
 */
export class MentionPlugin extends PluginBase<IMentionPluginConf> {
    private state = new MentionVM({
        onSelect: this.onMentionSelect
    });
    private ifInput = false;
    private prevKey = '';
    private readonly cursorMoveKeyMap: { [name: string]: string } = {
        left: 'ArrowLeft',
        right: 'ArrowRight',
        down: 'ArrowDown',
        up: 'ArrowUp'
    };
    private readonly eventCustomKeyMap: IEventKeyMap = {
        ArrowDown: {
            event: (event?: KeyboardEvent) => {
                event && event.stopPropagation();
                this.state.onIndexAdd(this.focusOption);
            },
        },
        ArrowUp: {
            event: (event?: KeyboardEvent) => {
                event && event.stopPropagation();
                this.state.onIndexMinus(this.focusOption);
            },
        },
        Enter: {
            event: (event?: KeyboardEvent) => {
                // 阻止换行
                event && event.stopPropagation();
            }
        }
    };

    private handleKeyUp(editor, state: MentionVM) {
        editor.cm.on('keyup', (instance, event: KeyboardEvent) => {
            if (event.key === 'Enter') {
                /* 
                    为了解决在输入中文后，按Enter键做选择，输入法会在keydown后把拼音附着在@内容后的bug
                    Enter事件必须放在keyup事件里
                 */
                this.onInputEnterKey(event);
                return;
            }
            if (state.showDropDownList) {
                if (event.key.length === 1 && /[0-9a-zA-Z_ ]/.test(event.key)) {
                    // 防止用户输入@符号时，shift+2不同步，导致误识别，造成候选列表闪退
                    if (this.prevKey === 'Shift' && event.key === '2') {
                        return;
                    }
                    this.onInputKeyword(instance, event.key);
                    return;
                }
                if (event.key === 'Backspace') {
                    this.onDeleteKeyword(instance);
                    return;
                }
                this.prevKey = event.key;
            } else if (event.key === 'Backspace') {
                this.prevKey = event.key;
                this.handleAnalyzeInput(instance);
            }
            this.prevKey = event.key;
        });
    }

    private handleKeyDown(editor, state: MentionVM) {
        editor.cm.on('keydown', (instance, event: KeyboardEvent) => {
            // https://codemirror.net/doc/manual.html#events
            if (
                event.key !== this.cursorMoveKeyMap.left
                && event.key !== this.cursorMoveKeyMap.right
                && (
                    event.key !== this.cursorMoveKeyMap.up
                    && !state.showDropDownList
                )
                && (
                    event.key !== this.cursorMoveKeyMap.down
                    && !state.showDropDownList
                )
            ) {
                this.ifInput = true;
            }
            if (state.showDropDownList) {
                if (this.ifInputCustomKey(event.key)) {
                    // 禁止 按钮生效
                    event.preventDefault();
                    this.eventCustomKeyMap[event.key].event(event);
                }
            } else if (event.key === '@') {
                this.onInputMentionMark(instance);
            }
        });
    }

    private handleCursorActivity(editor) {
        /**
         * 当光标移动时
         */
        editor.cm.on('cursorActivity', (instance) => {
            const { line, ch } = instance.getCursor();
            const contentBeforeCursor: string = instance.doc.getLine(line);
            // 输入时，改变光标，不做处理
            if (this.ifInput) {
                this.ifInput = false;
                return;
            }
            this.handleAnalyzeInput(instance);
        });
    }

    private handleBlur(editor, state: MentionVM) {
        editor.cm.on('blur', (instance, e) => {
            state.showDropDownList && setTimeout(() => {
                // !instance.hasFocus()是为解决列表出来后，点击框内，blur事件执行后立马又触发focus事件问题
                !instance.hasFocus() && this.hideMentionList();
            }, DEBOUNCE_TIME * 2);
        });
    }
    private handleFocus(editor) {
        editor.cm.on('focus', (instance) => {
            this.handleAnalyzeInput(instance);
        });
    }

    private onInputEnterKey(eventKey) {
        eventKey && eventKey.stopPropagation();
        this.state.onIndexChange();
    }

    private ifInputCustomKey(eventKey): boolean {
        return Object.keys(this.eventCustomKeyMap).some((key) => key === eventKey);
    }

    private async onInputMentionMark(instance) {
        if (await this.searchMentionedUsers('')) {
            this.setListPosition(instance);
            !this.state.dom && this.createMentionWidget(instance);
            instance.addLineWidget(instance.getCursor().line, this.state.dom, true);
        }
    }

    private createMentionWidget(instance) {
        const id = 'codemirrormd' + idIndex++;
        const div = this.state.dom =
            (
                document.getElementById(id) || document.createElement('div')
            ) as HTMLDivElement;
        div.id = id;
        div.className = css.MentionPlugin;
        ReactDOM.render(<Mention model={this.state} />, div as HTMLDivElement);
    }

    @Debounce(DEBOUNCE_TIME)
    private onInputKeyword(instance, char) {
        const keyword = this.getWordAndPosition(instance).word;
        if (!keyword) {
            this.hideMentionList();
            return;
        }
        this.searchMentionedUsers(keyword);
    }

    @Debounce(DEBOUNCE_TIME)
    private async onDeleteKeyword(instance) {
        const { word, ifMention } = this.getWordAndPosition(instance);
        if (!word && !ifMention) {
            this.hideMentionList();
            return;
        }
        if (await this.searchMentionedUsers(word)) {
            instance.addLineWidget(instance.getCursor().line, this.state.dom, true);
        }
    }

    /**
     * 处理光标移动时，关键词的搜索
     */
    @Debounce(DEBOUNCE_TIME)
    private async handleAnalyzeInput(instance) {
        const { word, ifMention } = this.getWordAndPosition(instance);
        if (!word && !ifMention) {
            this.state.showDropDownList && this.hideMentionList();
            return;
        }
        if (await this.searchMentionedUsers(word)) {
            this.setListPosition(instance);
            !this.state.dom && this.createMentionWidget(instance);
            instance.addLineWidget(instance.getCursor().line, this.state.dom, true);
        }
    }
    private getWordAndPosition(instance): IWordAndPos {
        const doc = instance.doc;
        const { line, ch } = instance.getCursor();
        const contentBeforeCursor: string = doc.getLine(line).slice(0, ch);
        const anchorCh = contentBeforeCursor.lastIndexOf('@');
        if (anchorCh < 0) { // 应该不需要，因为弹窗展示表示@符号出现，除非有异常？？
            return { word: '' };
        }
        const word = doc.getRange({ line, ch: anchorCh }, { line, ch });
        if (!/^@[\u4e00-\u9fa5_a-zA-Z0-9\']+$/.test(word)) {
            return { word: '', ifMention: word === '@' };
        }
        const wordFrom = { line, ch: anchorCh + 1 };
        const from = { line, ch: anchorCh }; // 含@符号
        const to = { line, ch };
        return {
            word: doc.getRange(wordFrom, to),
            pos: { from: wordFrom, to },
        };
    }

    private async searchMentionedUsers(word: string): Promise<boolean> {
        try {
            const users = await this.pluginConf.getMentions(word);
            if (!users || users.length === 0) {
                this.hideMentionList();
                return false;
            }
            this.state.showDropDown();
            this.state.setUsers(users);
            return true;
        } catch (e) {
            console.error(e);
            return false;
        }
    }

    @Bind
    private onMentionSelect() {
        const instance = this.editor.cm;
        const user = this.state.selectedUser;
        if (user) {
            const { word, pos } = this.getWordAndPosition(instance);
            const cursorPos = instance.getCursor();
            const replacePos = pos || { from: cursorPos, to: cursorPos };
            const mentionContent = user.username + ' ';
            const arr = JSON.parse(localStorage.getItem(History_Participants) || '[]');
            arr.push(user.username);
            localStorage.setItem(History_Participants, JSON.stringify(arr));
            // const mentionContent = `[@${user.name}](kim://username?username=${user.username})`;
            instance.doc.replaceRange(
                mentionContent,
                { ...replacePos.from },
                { ...replacePos.to },
            );

            const currentPos = {
                line: cursorPos.line,
                ch: replacePos.to.ch + mentionContent.length,
            };
            instance.focus();
            instance.doc.setCursor(currentPos);
        }
    }

    private hideMentionList() {
        this.state.hideDropDown();
        this.state.setUsers([]);
    }

    private focusOption(distance = 0) {
        const wrap = document.querySelector('.kdev-mention-wrap');
        if (!wrap) {
            return;
        }
        wrap.scrollTo(0, distance);
    }

    /* 设置候选框偏移位置 */
    private setListPosition(instance) {
        const editorWidth = this.editor.editor.width();
        const charWidth = this.editor.cm.defaultCharWidth();
        const length = instance.getCursor().ch;
        const left = length * charWidth;
        if (this.state.dom && this.state.dom.querySelector('ul')) {
            (this.state.dom as any).querySelector('ul').style.left =
                left > editorWidth - MENTION_LIST_WIDTH
                    ? `${editorWidth - MENTION_LIST_WIDTH}px`
                    : `${left}px`;
        }
    }

    public run() {
        this.handleKeyDown(this.editor, this.state);
        this.handleKeyUp(this.editor, this.state);
        this.handleCursorActivity(this.editor);
        this.handleBlur(this.editor, this.state);
        this.handleFocus(this.editor);
    }
}
