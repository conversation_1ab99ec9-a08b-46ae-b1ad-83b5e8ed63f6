import React from 'react';
import { observer } from 'mobx-react';
import { AView } from '@libs/mvvm';
import { MentionVM } from './MentionVM';
import { Menu, Avatar } from 'antd';
import css from './Mention.less';
import { Bind } from 'lodash-decorators';
import classNames from 'classnames';
import { KDevAvatar } from '@kdev-fe-common/common/KDevAvatar';

/**
 * Mention 功能的 UI
 */
@observer
export class Mention extends AView<MentionVM> {
    @Bind
    private onSelectUser(e, index) {
        this.model.onIndexChange(index);
    }
    public render(): React.ReactNode {
        if (!this.model.showDropDownList) {
            return '';
        }
        return (
            <Menu className={classNames(
                'kdev-mention-wrap',
                css.Mention
            )}>
                {
                    this.model.users.map((user, index) => (
                        <Menu.Item
                            key={index.toString()}
                            selectedKeys={ [this.model.index.toString()] }
                            onClick={ (e) => this.onSelectUser(e, index) }
                        >
                            <div
                                className={classNames(
                                    this.model.index === index ? css.active : css.normal,
                                    css.option
                                )}
                            >
                                <KDevAvatar src={user.avatarUrl} username={user.name} />
                                <div className={css.info}>
                                    <p style={{fontWeight: 'bold'}}>
                                        {user.name}
                                    </p>
                                    <p>{user.email}</p>
                                </div>
                            </div>
                        </Menu.Item>
                    ))
                }
            </Menu>
        );
    }
}
