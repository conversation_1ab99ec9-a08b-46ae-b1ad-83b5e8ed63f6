import { Bind } from 'lodash-decorators';
import React from 'react';
import { observable, action, runInAction, computed } from 'mobx';
/**
 * Mention 功能的 数据模型
 */
const OPTION_HEIGHT = 40; // 选项高度
export class MentionVM {
    public dom: HTMLDivElement | null = null;
    public onSelect: () => void;
    @observable private _showDropDownList = false;
    @observable private _users: IEntityUser[] = [];
    @observable private _index: number = -1;

    @computed public get showDropDownList() {
        return this._showDropDownList;
    }
    @computed public get users() {
        return this._users;
    }
    @computed public get index() {
        if (
            this._index > this._users.length - 1
            || this._users.length < 1
            || this._index < 0
        ) {
            return -1;
        }
        return this._index;
    }
    @computed public get selectedUser(): IEntityUser | null {
        return this.users[this.index];
    }

    constructor(params: {
        onSelect: () => void
    }) {
        this.onSelect = params.onSelect;
    }

    @action
    public showDropDown() {
        this._showDropDownList = true;
        this._index = 0;
    }

    @action
    public hideDropDown() {
        this._showDropDownList = false;
    }

    @action
    public setUsers(users: IEntityUser[]) {
        this._users = users;
        if (users.length > 0) {
            this.handleChangeIndex(0);
        }
    }

    /**
     * @description 选中事件
     */
    @action.bound
    public onIndexChange(index = this.index) {
        this.handleChangeIndex(index);
        this.onSelect();
        this.hideDropDown();
    }

    private handleChangeIndex(index) {
        this._index = index;
    }

    @action.bound
    public onIndexAdd(focusFn?: (distance: number) => void) {
        const index = this.index + 1 === this.users.length ? 0 : this.index + 1;
        this.handleChangeIndex(index);
        focusFn?.(index * OPTION_HEIGHT);
    }
    @action.bound
    public onIndexMinus(focusFn?: (distance: number) => void) {
        const index = this.index - 1 < 0 ? this.users.length - 1 : this.index - 1;
        this.handleChangeIndex(index);
        focusFn?.(index * OPTION_HEIGHT);
    }
}
