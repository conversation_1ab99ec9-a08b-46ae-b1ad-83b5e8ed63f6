import React from 'react';
import { displayHTML } from '@libs/utils';
interface IMarkdownViewerProps {
    content: string;
}
/**
 * 基础 Markdown 浏览器
 */
export class MarkdownViewer extends React.Component<IMarkdownViewerProps> {
    private domRef = React.createRef<HTMLDivElement>();
    private editor;

    public async componentDidMount() {
        if (this.domRef.current) {
            const textView = await displayHTML(this.domRef.current,
                {
                    atLink: false, // 关闭编辑器对@的解析功能
                }, this.props.content);
            this.editor = textView;
        }
    }

    public componentWillUnmount(): void {
        /**
         * 按顺序销毁所有插件
         */
        this.editor?.editor?.remove();
    }

    public render() {
        return <div ref={this.domRef} {...this.props} />;
    }
}
