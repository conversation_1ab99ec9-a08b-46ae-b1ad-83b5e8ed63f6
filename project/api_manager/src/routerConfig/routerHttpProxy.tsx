import React from 'react';
import { RouteSetting } from '@libs/a-component';
import { ERouter } from 'CONFIG';
import { PartitionOutlined } from '@ant-design/icons';
import { KdevIconFont } from 'kdev-fe-common/src/component/KdevIconFont';
import {
    new_kdev_branches, common_system_currentnumberofclients,
    common_system_data_distribution,
    common_system_setting
} from '@kid/enterprise-icon/icon/output/icons';

const routerProxy: RouteSetting = new RouteSetting({
    title: '代理抓包',
    iconType: <KdevIconFont id={common_system_currentnumberofclients
    } className="anticon" />,
    path: ERouter.API_MOCK_HTTP_PROXY + '-',
    // importPromise: () => import('@/pages/apiHttpProxy'),
    routeSettings: [
        {
            iconType: null,
            title: '代理规则',
            path: ERouter.API_MOCK_HTTP_PROXY_RULE,
            importPromise: () => import('@/pages/apiHttpProxy'),
        },
        {
            iconType: null,
            title: '日志抓包',
            path: ERouter.API_MOCK_HTTP_PROXY,
            importPromise: () => import('@/pages/apiHttpProxy'),
        }
    ]
});

const routers = [
    routerProxy,
];

export default routers;

export const projectInfoRouter = new RouteSetting({
    iconType: <KdevIconFont id={common_system_data_distribution} className="anticon" />,
    title: '接口目录',
    qsFields: ['projectId', 'selectedKey', 'apiDocTab'],
    path: ERouter.API_MOCK_REPO_MGR_PROJECT_INFO,
    importPromise: () => import('@/pages/ApiManager/ProjectView/ProjectInfo/ProjectInfo'),
});
export const projectSettingRouter = new RouteSetting({
    iconType: <KdevIconFont id={common_system_setting} className="anticon" />,
    title: '项目设置',
    qsFields: ['projectId', 'selectedKey', 'apiDocTab'],
    path: ERouter.API_MOCK_REPO_MGR_PROJECT_SETTING,
    importPromise: () => import('@/pages/ApiManager/ProjectView/ProjectSetting/ProjectSetting'),
});
