import { RouteSetting } from 'libs/a-component';
import { ERouter } from 'CONFIG';
import React from 'react';
import {
    ClusterOutlined,
    FileTextOutlined
} from '@ant-design/icons';

const routerApiTemplate: RouteSetting = new RouteSetting({
    iconType: <FileTextOutlined />,
    title: '项目信息',
    path: ERouter.API_MOCK_REPO_MGR_PROJECT_INFO,
    menuKey: ERouter.API_MOCK_REPO_MGR,
    forbiddenMenu: true,
    importPromise: () => import('@/pages/ApiManager/ProjectView/ProjectInfo/ProjectInfo'),
});

const routers = [
    routerApiTemplate
];

export default routers;
