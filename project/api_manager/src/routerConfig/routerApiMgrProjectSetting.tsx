import { RouteSetting } from 'libs/a-component';
import { ERouter } from 'CONFIG';
import React from 'react';
import {
    ClusterOutlined,
    FileTextOutlined
} from '@ant-design/icons';

const routerApiTemplate: RouteSetting = new RouteSetting({
    iconType: <FileTextOutlined />,
    title: '设置',
    path: ERouter.API_MOCK_REPO_MGR_PROJECT_SETTING,
    menuKey: ERouter.API_MOCK_REPO_MGR,
    forbiddenMenu: true,
    importPromise: () => import('@/pages/ApiManager/ProjectView/ProjectSetting/ProjectSetting'),
});

const routers = [
    routerApiTemplate
];

export default routers;
