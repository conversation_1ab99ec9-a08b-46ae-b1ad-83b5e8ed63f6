import { RouteSetting } from 'libs/a-component';
import { ERouter } from 'CONFIG';
import React from 'react';
import { CodeSandboxOutlined, GoogleOutlined } from '@ant-design/icons';
import { KdevIconFont } from 'kdev-fe-common/src/component/KdevIconFont';
import { new_kdev_grpc, new_kdev_http } from '@kid/enterprise-icon/icon/output/icons';

const routerNewAutomaticParsingApi: RouteSetting = new RouteSetting({
    iconType: <KdevIconFont id={new_kdev_http} className="anticon" />,
    title: 'HTTP 自动解析',
    path: ERouter.API_MOCK_HTTP_API_MGR_SWAGGER,
    importPromise: () => import('@/pages/automaticParsingApi/httpSwaggerApi')
});

const routerNewRpcAutomaticParsingApi: RouteSetting = new RouteSetting({
    iconType: <KdevIconFont id={new_kdev_grpc} className="anticon" />,
    title: 'GRPC 自动解析',
    path: ERouter.API_MOCK_RPC_API_MGR,
    importPromise: () => import('@/pages/automaticParsingApi/grpcApiMgr')
});

const routerNewRpcAutomaticParsingApiDetail: RouteSetting = new RouteSetting({
    iconType: <GoogleOutlined />,
    title: 'GRPC API详情',
    path: ERouter.API_MOCK_RPC_API_DETAIL,
    menuKey: ERouter.API_MOCK_RPC_API_MGR,
    forbiddenMenu: true,
    importPromise: () => import('@/pages/automaticParsingApi/grpcApiDetail')
});

const routerOther: RouteSetting = new RouteSetting({
    iconType: <KdevIconFont id={new_kdev_http} className="anticon" />,
    title: '其他功能',
    path: ERouter.API_MOCK_HTTP_API_MGR_SWAGGER,
    routeSettings: [
        {
            iconType: null,
            title: 'HTTP 自动解析',
            path: ERouter.API_MOCK_HTTP_API_MGR_SWAGGER,
            importPromise: () => import('@/pages/automaticParsingApi/httpSwaggerApi')
        },
        {
            iconType: null,
            title: 'GRPC 自动解析',
            path: ERouter.API_MOCK_RPC_API_MGR,
            importPromise: () => import('@/pages/automaticParsingApi/grpcApiMgr')
        },
        {
            iconType: <GoogleOutlined />,
            title: 'GRPC API详情',
            path: ERouter.API_MOCK_RPC_API_DETAIL,
            menuKey: ERouter.API_MOCK_RPC_API_MGR,
            forbiddenMenu: true,
            importPromise: () => import('@/pages/automaticParsingApi/grpcApiDetail')
        }
    ]
});

const routers = [
    // routerNewAutomaticParsingApi,
    // routerNewRpcAutomaticParsingApi,
    // routerNewRpcAutomaticParsingApiDetail
    routerOther
];

export default routers;
