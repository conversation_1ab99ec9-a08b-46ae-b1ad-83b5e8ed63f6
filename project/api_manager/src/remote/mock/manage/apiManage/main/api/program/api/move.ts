import { request } from '@libs/utils';

export namespace nsMockManageApiManageMainApiProgramApiMove {
    export interface IParams {
        apiId: number;
        originCatalogId: number;
        targetCatalogId: number;
    }

    export interface IReturn {
        status: number;
        message: string;
        traceId: string;
    }

    export const remote = async (params: IParams): Promise<IReturn> => {
        return request.put('/api/mock/manage/apiManage/main/api/program/api/move', params)
            .then((result: any) => result || {});
    };
} 