import { request } from '@libs/utils';

export namespace nsMockManageApiManageMainApiProgramDomainSearch {

    export interface IParams {
        key: string; // 搜索词条，不可为空
    }

    export interface IChildren {
        id: number;
        name: string;
        parentId: number;
        createBy: string;
        createTime: number;
        updateBy: string;
        updateTime: number;
        isDelete: number;
        hasChild: boolean;
    }

    export interface IDataItem {
        id: number;
        name: string;
        parentId: number;
        createBy: string;
        createTime: number;
        updateBy: string;
        updateTime: number;
        isDelete: number;
        hasChild: boolean;
        children: Array<IChildren>;
    }


    export const remote = async (params: IParams): Promise<Array<IDataItem>> => {
        return request.get('/api/mock/manage/apiManage/main/api/program/domain/search', params)
            .then((result: any) => result || {});
    };
} 