import { request } from '@libs/utils';

export namespace nsMockManageApiManageMainApiProgramDomainList {

    export interface IParams {
        parentId?: number;
    }

    export interface IReturn_Item {
        id: number;
        name: string;
        parentId: number;
        createBy: string;
        createTime: number;
        updateBy: string;
        updateTime: number;
        isDelete: number;
        hasChild: boolean;
    }

    export const remote = async (params?: IParams): Promise<IReturn_Item[]> => {
        return request.get('/api/mock/manage/apiManage/main/api/program/domain/list', params)
            .then((result: any) => result || {});
    };
} 