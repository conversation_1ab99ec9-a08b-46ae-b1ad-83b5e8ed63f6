import { request } from '@libs/utils';

export namespace nsMockManageApiManageMainApiProgramImportKapiGroupList {
    // 空请求参数
    export interface IParams { }

    // 返回数据项定义
    export interface IReturn_Item {
        id: number;
        name: string;
    }

    export const remote = async (params?: IParams): Promise<Array<IReturn_Item>> => {
        return request.get('/api/mock/manage/apiManage/main/api/program/import/kapi/group/list', params)
            .then((result: any) => result || {});
    };
} 