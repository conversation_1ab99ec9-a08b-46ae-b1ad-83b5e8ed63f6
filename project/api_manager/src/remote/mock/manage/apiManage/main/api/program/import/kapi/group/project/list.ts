import { request } from '@libs/utils';

export namespace nsMockManageApiManageMainApiProgramImportKapiGroupProjectList {
    // 请求参数定义
    export interface IParams {
        groupId: string;
    }

    // 返回数据项定义
    export interface IReturn_Item {
        id: number;
        name: string;
    }

    export const remote = async (params: IParams): Promise<Array<IReturn_Item>> => {
        return request.get('/api/mock/manage/apiManage/main/api/program/import/kapi/group/project/list', params)
            .then((result: any) => result || {});
    };
} 