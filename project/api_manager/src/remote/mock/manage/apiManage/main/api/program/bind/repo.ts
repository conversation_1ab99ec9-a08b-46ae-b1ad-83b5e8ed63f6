import { request } from '@libs/utils';

export namespace nsMockManageApiManageMainApiProgramBindRepo {
    interface IBindProject {
        projectId: number;
        projectSsh: string;
    }

    interface IParams {
        programId: number;
        bindProjects: IBindProject[];
    }

    export interface IReturn {
        status: number;
        message: string;
        data: null;
    }

    export const remote = async (params: IParams): Promise<IReturn> => {
        return request.delete('/api/mock/manage/apiManage/main/api/program/bind/repo', params);
    };
} 