import { request } from '@libs/utils';

export namespace nsMockManageApiManageMainApiProgramItemsSearch {
    export interface IParams {
        programId: number;
        key: string;
    }

    export interface IReturn_Item {
        id: number;
        parentId: number;
        name: string;
        type: string;
        hasChild?: boolean;
        children?: IReturn_Item[];
    }


    export const remote = async (params: IParams): Promise<IReturn_Item[]> => {
        return request.get('/api/mock/manage/apiManage/main/api/program/items/search', params)
            .then((result: any) => result || {});
    };
}
