import { request } from '@libs/utils';

export namespace nsMockManageApiManageMainApiProgramSearch {
    export interface RequestItem {
        type: 'term' | 'fuzzy';
        field: string;
        value: string | number;
    }

    export interface Request {
        pageNumber: number;
        pageSize: number;
        filters: RequestItem[];
    }

    export interface ResponseItem {
        id: number;
        name: string;
        desc: string;
        groupId: number;
        isDelete: number;
        pinned: boolean;
        createBy: string;
        createTime: number;
        updateBy: string;
        updateTime: number;
        admins: {
            id: number,
            name: string,
            username: string,
            email: string,
            photo: string
        }[]
    }

    export interface Response {
        pageNumber: number;
        pageSize: number;
        total: number;
        items: ResponseItem[];
    }

    export function remote(params: Request): Promise<Response> {
        return request.post('/api/mock/manage/apiManage/main/api/program/search', params);
    }
}

export default nsMockManageApiManageMainApiProgramSearch; 