import { request } from '@libs/utils';

export namespace nsMockManageApiManageMainApiProgramImportKapiProjectItems {
    // 请求参数定义
    export interface IParams {
        projectId: number;
    }

    // 数据项定义
    export interface IDataItem {
        id: number;
        name: string;
        type: 'CATALOG' | 'API';
        hasChild: boolean;
        children?: IDataItem[];
        apiPath?: string;
    }

    export const remote = async (params: IParams): Promise<IDataItem[]> => {
        return request.get('/api/mock/manage/apiManage/main/api/program/import/kapi/project/items', params)
            .then((result: any) => result || {});
    };
} 