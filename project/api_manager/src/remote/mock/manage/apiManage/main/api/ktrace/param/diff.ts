import { request } from '@libs/utils';

export namespace nsMockManageApiManageMainApiKtraceParamDiffGet {

    export interface IParams {
        apiId: number;
        versionId?: number;
    }

    export interface IReturn {
        baseInfo: {
            apiId: number,
            groupId: number,
            projectId: number,
            repoName: string,
            pathRepoName: string,
            name: string,
            path: string,
            schema: string,
            state: string,
            admin: [
                {
                    id: number,
                    name: string,
                    username: string,
                    email: string,
                    photo: string
                }
            ],
            desc: string,
            permission: number,
            subscription: number,
            follow: number,
            createUser: string,
            updateUser: {
            },
            updateTime: number,
            teams: [
                {
                    title: string,
                    taskId: string,
                    status: string,
                    assignee: string,
                    creator: string,
                    creatorAt: number,
                    section: string,
                    taskType: string,
                    url: string
                }
            ],
            version: number,
            groupName: string,
            groupPath: string,
            versionId: number,
            confirmStatus: boolean,
            tagList: [
                {
                    id: number,
                    name: string,
                    desc: string,
                    color: string,
                    groupId: number,
                    isDelete: number,
                    createTime: number,
                    createBy: string,
                    updateTime: number,
                    updateBy: string
                }
            ],
            tagIdList: [
                number
            ],
            customTag: string,
            priority: string,
            apiGatewayPrefix: string,
            diffWithKtrace: boolean
        },
        reqHeaderDiffLineList: [
            {
                left: {
                    type: string,
                    lineContent: string,
                    leftLineNumber: number,
                    rightLineNumber: number
                },
                right: {
                    type: string,
                    lineContent: string,
                    leftLineNumber: number,
                    rightLineNumber: number
                }
            }
        ],
        reqQueryDiffLineList: [
            {
                left: {
                    type: string,
                    lineContent: string,
                    leftLineNumber: number,
                    rightLineNumber: number
                },
                right: {
                    type: string,
                    lineContent: string,
                    leftLineNumber: number,
                    rightLineNumber: number
                }
            }
        ],
        reqBodyDiffLineList: [ // 请求体diff列表
            {}
        ],
        respBodyDiffLineList: [ // 响应体diff列表
            {}
        ],
        ktraceCollectSource: {
            env: string,
            host: string,
            landId: string
        },
        leftParamWithDiff: {
            request: {
                query: [],
                header: [],
                body: []
            },
            response: {
                header: [],
                body: []
            }
        },
        rightParamWithDiff: {
            request: {
                query: [],
                header: [],
                body: []
            },
            response: {
                header: [],
                body: []
            }
        }
    }
    export const remote = async (params: IParams): Promise<IReturn> => {
        return request.get('/api/mock/manage/apiManage/main/api/ktrace/param/diff', params)
            .then((result: any) => result || {});
    };
}