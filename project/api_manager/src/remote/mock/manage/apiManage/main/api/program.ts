import { request } from '@libs/utils';

export namespace nsMockManageApiManageMainApiProgram {
    interface IParams {
        id: number;
    }

    interface IBindingOperator {
        id: number;
        name: string;
        username: string;
        email: string;
        photo: string;
    }

    export interface BindProjectType {
        projectId: number;
        projectSsh: string;
        projectName: string;
        bindingOperator: IBindingOperator;
    }

    export interface IReturn {
        pinned: boolean;
        id: number;
        name: string;
        desc: string;
        groupId: number;
        isDelete: number;
        bindProjects: BindProjectType[];
        createBy: string;
        createTime: number;
        updateBy: string;
        updateTime: number;
    }

    export const remote = async (params: IParams): Promise<IReturn> => {
        return request.get('/api/mock/manage/apiManage/main/api/program', params)
            .then((result: any) => {
                return result || {};
            });
    };


    export interface BindProjectTypeEasy {
        projectId: number;
    }
    // 创建项目接口
    export interface ICreateParams {
        name: string;
        desc: string;
        groupId: number;
        bindProjects: BindProjectTypeEasy[];
    }

    export interface ICreateReturn {
        id: number;
        name: string;
        desc: string;
        groupId: number;
        isDelete: number;
        createBy: string;
        createTime: number;
        updateBy: string;
        updateTime: number;
    }

    export const create = async (params: ICreateParams): Promise<ICreateReturn> => {
        return request.post('/api/mock/manage/apiManage/main/api/program', params)
            .then((result: any) => {
                return result?.data || {};
            });
    };

    // 更新项目接口
    export interface IUpdateParams {
        id: number;
        name: string;
        desc: string;
        bindProjects: BindProjectTypeEasy[];
    }

    export const update = async (params: IUpdateParams): Promise<void> => {
        return request.put('/api/mock/manage/apiManage/main/api/program', params)
            .then((result: any) => {
                return result?.data || {};
            });
    };

    // 删除项目接口
    export interface IDeleteParams {
        id: number;
    }

    export interface IDeleteReturn {
        id: number;
        name: string;
        desc: string;
        groupId: number;
        isDelete: number;
        createBy: string;
        createTime: number;
        updateBy: string;
        updateTime: number;
    }

    export const remove = async (params: IDeleteParams): Promise<IDeleteReturn> => {
        return request.delete('/api/mock/manage/apiManage/main/api/program?id=' + params.id, { ...params })
            .then((result: any) => {
                return result?.data || {};
            });
    };
}
