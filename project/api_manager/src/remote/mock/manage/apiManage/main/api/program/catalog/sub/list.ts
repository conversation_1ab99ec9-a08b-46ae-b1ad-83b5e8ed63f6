import { request } from '@libs/utils';

export namespace nsMockManageApiManageMainApiProgramCatalogSubList {
    export interface IParams {
        id: number;
    }

    export interface IReturn_Item {
        id: number;
        type: 'CATALOG' | 'API';
        name: string;
        hasChild: boolean;
        apiPath?: string;
        updateTime: string;
        parentId: number;
        apiSchema?: string;
        admin: {
            id: number;
            name: string;
            username: string;
            email: string;
            photo: string;
        };
    }

    export const remote = async (params: IParams): Promise<Array<IReturn_Item>> => {
        return request.get('/api/mock/manage/apiManage/main/api/program/catalog/sub/list', { ...params })
            .then((result: any) => result || []);
    };
} 