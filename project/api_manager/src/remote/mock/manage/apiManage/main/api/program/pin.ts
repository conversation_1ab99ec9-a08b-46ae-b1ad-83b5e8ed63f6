import { request } from '@libs/utils';

export namespace nsMockManageApiManageMainApiProgramPin {
    // GET 接口返回数据类型
    export interface IGetReturn_Item {
        id: number;
        name: string;
        desc: string;
        groupId: number;
        isDelete: number;
        createBy: string;
        createTime: number;
        updateBy: string;
        updateTime: number;
    }


    // PUT 接口参数类型
    export interface IPutParams {
        id: number;
    }


    // GET 接口：获取全部已PIN项目
    export const getPinList = async (): Promise<Array<IGetReturn_Item>> => {
        return request.get('/api/mock/manage/apiManage/main/api/program/pin')
            .then((result: any) => result || {});
    };

    // PUT 接口：PIN项目至导航栏
    export const pinProject = async (params: IPutParams): Promise<null> => {
        const { id } = params;
        return request.put(`/api/mock/manage/apiManage/main/api/program/pin?id=${id}`)
            .then((result: any) => result || {});
    };
} 