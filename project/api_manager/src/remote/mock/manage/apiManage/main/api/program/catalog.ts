import { request } from '@libs/utils';

export namespace nsMockManageApiManageMainApiProgramCatalog {
    // POST 接口参数类型
    export interface IPostParams {
        name: string;
        desc: string;
        programId: number;
        parentId: number;
    }

    // PUT 接口参数类型
    export interface IPutParams {
        id: number;
        name: string;
        desc: string;
    }

    // DELETE 接口参数类型
    export interface IDeleteParams {
        id: number;
    }

    // 返回数据类型
    export interface ICatalogData {
        id: number;
        name: string;
        desc: string;
        programId: number;
        parentId: number;
        order: number;
        createBy: string;
        createTime: number;
        updateBy: string;
        updateTime: number;
        key: string;
        children: ICatalogData[];
        hasChild?: boolean;
        apiPath?: string;
        apiSchema?: string;
        admin: {
            id: number;
            name: string;
            username: string;
            email: string;
            photo: string;
        };
        priority: string;
        tagList: any[];
    }

    // POST 接口：新建目录
    export const createCatalog = async (params: IPostParams): Promise<ICatalogData> => {
        return request.post('/api/mock/manage/apiManage/main/api/program/catalog', params)
            .then((result: any) => result || {});
    };

    // PUT 接口：更新目录
    export const updateCatalog = async (params: IPutParams): Promise<null> => {
        return request.put('/api/mock/manage/apiManage/main/api/program/catalog', params)
            .then((result: any) => result || {});
    };

    // DELETE 接口：删除目录
    export const deleteCatalog = async (params: IDeleteParams): Promise<ICatalogData> => {
        return request.delete('/api/mock/manage/apiManage/main/api/program/catalog', params)
            .then((result: any) => result || {});
    };
} 