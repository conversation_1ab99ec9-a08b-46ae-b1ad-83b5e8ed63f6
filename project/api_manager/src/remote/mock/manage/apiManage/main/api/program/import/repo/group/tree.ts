import { request } from '@libs/utils';

export namespace nsMockManageApiManageMainApiProgramImportRepoGroupTree {
    export interface IParams {
        groupId: string;
    }


    export interface IReturn {
        type: string;
        name: string;
        id: number;
        hasChild: boolean;
        children: IReturn[];
        parentId: number;
        apiPath?: string;
    }


    export const remote = async (params: IParams): Promise<IReturn> => {
        return request.get('/api/mock/manage/apiManage/main/api/program/import/repo/group/tree', params)
            .then((result: any) => result || {});
    };
} 