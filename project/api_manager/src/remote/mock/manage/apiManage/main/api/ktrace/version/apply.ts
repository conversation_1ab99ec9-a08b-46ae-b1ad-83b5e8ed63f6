import { request } from '@libs/utils';

export namespace nsMockManageApiManageMainApiKtraceVersionApplyPost {

    export interface IParams {
        apiId: number;
    }
    export interface IReturn {

    }

    export const remote = async (params: IParams): Promise<IReturn> => {
        return request.post(`/api/mock/manage/apiManage/main/api/ktrace/version/apply?apiId=${params.apiId}`, {})
            .then((result: any) => result);
    };
}
