import { request } from '@libs/utils';

export namespace nsMockManageApiManageMainApiProgramSettingsEnvList {
    export interface IParams {
        programId: number;
    }

    export interface ICreatorInfo {
        id: number;
        name: string;
        username: string;
        email: string;
        photo: string;
    }

    export interface IEnvItem {
        id: number;
        name: string;
        groupId: number;
        remark: string;
        createTime: number;
        createUser: string;
        updateTime: number;
        updateUser: string;
        creator: ICreatorInfo;
    }


    export const remote = async (params: IParams): Promise<Array<IEnvItem>> => {
        return request.get('/api/mock/manage/apiManage/main/api/program/settings/env/list', params)
            .then((result: any) => result || {});
    };
} 