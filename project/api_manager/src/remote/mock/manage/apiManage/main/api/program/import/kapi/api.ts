import { request } from '@libs/utils';

export namespace nsMockManageApiManageMainApiProgramImportKapiApi {
    // 请求参数定义
    export interface IParams {
        apiIds: number[];
        targetProgram: number;
        targetCatalog: number;
        syncTestHistory: boolean;
        syncMockData: boolean;
    }

    // 返回数据项定义
    export interface IData {
        id: number;
        name: string;
        desc: string;
        isDelete: number;
        createBy: string;
        createTime: number;
        updateBy: string;
        updateTime: number;
    }

    export const remote = async (params: IParams): Promise<IData> => {
        const url = '/api/mock/manage/apiManage/main/api/program/import/kapi/api';
        return request.post(url, params)
            .then((result: any) => result || {});
    };
} 