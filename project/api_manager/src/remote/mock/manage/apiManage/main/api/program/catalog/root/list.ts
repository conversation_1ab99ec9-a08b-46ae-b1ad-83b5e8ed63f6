import { request } from '@libs/utils';

export namespace nsMockManageApiManageMainApiProgramCatalogRootList {
    export interface IParams {
        programId: number;
        locationId?: number;
        locationType?: string;
    }

    export interface IReturn_Item {
        id: number;
        type: 'CATALOG' | 'API';
        name: string;
        hasChild: boolean;
    }

    export const remote = async (params: IParams): Promise<Array<IReturn_Item>> => {
        return request.get('/api/mock/manage/apiManage/main/api/program/catalog/root/list', { ...params })
            .then((result: any) => result || []);
    };
} 