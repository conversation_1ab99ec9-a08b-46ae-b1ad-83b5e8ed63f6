import { request } from '@libs/utils';

export namespace nsMockManageApiManageMainApiProgramApiMockList {

    export interface IParams {
        apiId: number;
    }

    export interface ICreator {
        id: number;
        name: string;
        username: string;
        email: string;
        photo: string;
    }

    export interface IMockItem {
        apiId: number;
        id: number;
        name: string;
        request: string;
        response: string;
        desc: string;
        createTime: string;
        updateTime: string;
        key: string;
        enabled: boolean;
        creator: ICreator;
        default: boolean;
    }

    export interface IReturn {
        mockAddress: string;
        list: Array<IMockItem>;
        mockBackstopSwitch: number;
    }

    export const remote = async (params: IParams): Promise<IReturn> => {
        return request.get('/api/mock/manage/apiManage/main/api/program/api/mock/list', params)
            .then((result: any) => result || {});
    };
} 