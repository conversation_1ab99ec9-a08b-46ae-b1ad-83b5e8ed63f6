
import { request } from '@libs/utils';

export namespace nsMockManageApiManageMainApiProgramHot {
    export interface IPrams {
        groupId: number;
    }
    export interface IReturn_Item {
        id: number;
        name: string;
        desc: string;
        groupId: number;
        isDelete: number;
        createBy: string;
        createTime: number;
        updateBy: string;
        updateTime: number;
        pinned: boolean;
        admins: {
            id: number,
            name: string,
            username: string,
            email: string,
            photo: string
        }[]
    }

    export const remote = async (params: IPrams): Promise<Array<IReturn_Item>> => {
        return request.get('/api/mock/manage/apiManage/main/api/program/hot', params)
            .then((result: any) => result || {});
    };
} 