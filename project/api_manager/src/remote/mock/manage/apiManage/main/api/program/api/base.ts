import { request } from '@libs/utils';

export namespace nsMockManageApiManageMainApiProgramApiBase {
    export interface IParams {
        id: number;
        version?: number;
    }

    export interface IUserInfo {
        id: number;
        name: string;
        username: string;
        email: string;
        photo: string;
    }

    export interface IReturn {
        apiId: number;
        projectId: number;
        repoName: string;
        pathRepoName: string;
        name: string;
        path: string;
        schema: string;
        state: string;
        admin: IUserInfo[];
        subscription: number;
        createUser: string;
        updateUser: IUserInfo;
        updateTime: number;
        teams: any[];
        version: number;
        versionId: number;
        tagList: any[];
        tagIdList: any[];
        priority: string;
        diffWithKtrace: boolean;
        catalogId: number;
        catalogName: string;
        programId: number;
        programName: string
    }


    export const remote = async (params: IParams): Promise<IReturn> => {
        return request.get('/api/mock/manage/apiManage/main/api/program/api/base', params)
            .then((result: any) => result || {});
    };
} 