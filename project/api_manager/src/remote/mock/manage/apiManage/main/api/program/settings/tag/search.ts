import { request } from '@libs/utils';

export namespace nsMockManageApiManageMainApiProgramSettingsTagSearch {
    export interface IParams {
        programId: number;
        key: string;
    }

    export interface IReturn {
        // 由于接口文档中没有详细的返回值定义，这里使用any类型
        [key: string]: any;
    }

    export const remote = async (params: IParams): Promise<IReturn> => {
        return request.get('/api/mock/manage/apiManage/main/api/program/settings/tag/search', params)
            .then((result: any) => result || {});
    };
} 