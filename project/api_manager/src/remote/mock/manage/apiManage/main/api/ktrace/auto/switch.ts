import { request } from '@libs/utils';

export namespace nsMockManageApiManageMainApiKtraceAutoSyncSwitch {

    export interface IParams {
        apiId: number;
        autoSyncKtrace?: number;
    }
    export interface IReturn {

    }

    export const remote = async (params: IParams): Promise<IReturn> => {
        return request.put('/api/mock/manage/apiManage/main/api/ktrace/auto/sync/switch', params)
            .then((result: any) => result);
    };

    export const remoteGet = async (params: IParams): Promise<IReturn> => {
        return request.get('/api/mock/manage/apiManage/main/api/ktrace/auto/sync/switch', params)
            .then((result: any) => result);
    };
}