import { request } from '@libs/utils';
import { nsMockManageApiManageMainApiV1AddPOST } from '@/remote';

export namespace nsMockManageApiManageMainApiProgramApi {
    // 新建API的参数接口
    export interface ICreateParams {
        templateInfo: {
            components: Array<{
                id: string;
                enable: number;
                name: string;
                required: number;
                value: any[] | string;
                component: string;
                params: any[];
            }>;
        };
        baseInfo: {
            name: string;
            schema: string;
            path: string;
            state: string;
            version: number;
            desc: string;
            admin: Array<{
                id: number;
                name: string;
                username: string;
                email: string;
                photo: string;
            }>;
            repoName: string;
            pathRepoName: string;
            projectId: number;
            catalogId: number;
            tagIdList: number[];
        };
        request: {
            header: any[];
            query: any[];
            rest: any[];
            body: Array<{
                N: string;
                T: number;
                R: number;
                D: string;
                V: string;
                children: Array<{
                    N: string;
                    T: number;
                    R: number;
                    D: string;
                    V: string;
                }>;
            }>;
            bodyType: string;
            json5Body: string;
            bodyJsonViewType: string;
        };
    }

    // 更新API的参数接口
    export interface IUpdateParams extends ICreateParams {
        baseInfo: ICreateParams['baseInfo'] & { apiId: number };
    }

    // 删除API的参数接口
    export interface IDeleteParams {
        apiId: number;
        catalogId: number;
    }

    // 通用返回接口
    type IReturn = number

    // 新建API
    export const create = async (params: ICreateParams): Promise<IReturn> => {
        return request.post('/api/mock/manage/apiManage/main/api/program/api', params)
            .then((result: any) => result || {});
    };

    // 更新API
    export const update = async (params: IUpdateParams): Promise<IReturn> => {
        return request.put('/api/mock/manage/apiManage/main/api/program/api', params)
            .then((result: any) => result || {});
    };

    // 删除API
    export const remove = async (params: IDeleteParams): Promise<IReturn> => {
        return request.delete('/api/mock/manage/apiManage/main/api/program/api', params)
            .then((result: any) => result || {});
    };
} 