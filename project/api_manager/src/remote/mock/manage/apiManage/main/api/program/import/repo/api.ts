import { request } from '@libs/utils';

export namespace nsMockManageApiManageMainApiProgramImportRepoApi {
    export interface IParams {
        apiIds: number[];
        targetProgram: number;
        targetCatalog: number;
        syncTestHistory: boolean;
        syncMockData: boolean;
    }

    export interface IReturn {
        status: number;
        message: string;
        data: number;
        traceId: string;
    }

    /**
     * 导入仓库视角的API
     * @param params 请求参数
     * @returns 返回导入结果
     */
    export const remote = async (params: IParams): Promise<IReturn> => {
        return request.post('/api/mock/manage/apiManage/main/api/program/import/repo/api', params)
            .then((result: any) => result || {});
    };
}
