import { request } from '@libs/utils';

export namespace nsMockManageApiManageMainApiProgramImportKapiProject {
    // 请求参数定义
    export interface IBindProject {
        projectId: number;
    }
    export interface IParams {
        projectId: number;
        targetGroupId: number;
        bindProjects: IBindProject[];
    }

    // 返回值定义（接口返回空对象）
    export type IResponse = Record<string, never>;

    export const remote = async (params: IParams): Promise<IResponse> => {
        try {
            return await request.post('/api/mock/manage/apiManage/main/api/program/import/kapi/project', params);
        } catch (e) {
            // 可以根据需要自定义错误处理
            return {};
        }
    };
} 