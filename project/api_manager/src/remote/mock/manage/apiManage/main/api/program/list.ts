import { request } from '@libs/utils';

export namespace nsMockManageApiManageMainApiProgramList {
    export interface IParams {
        pageNumber?: number;
        pageSize?: number;
        groupId: number;
    }

    export interface ResponseItem {
        id: number;
        name: string;
        desc: string;
        groupId: number;
        isDelete: number;
        createBy: string;
        createTime: number;
        updateBy: string;
        updateTime: number;
        pinned: boolean;
        admins: {
            id: number,
            name: string,
            username: string,
            email: string,
            photo: string
        }[]
    }

    export interface IReturn {
        pageNumber: number;
        pageSize: number;
        total: number;
        pageCount: number;
        items: ResponseItem[];
    }

    export const remote = async (params: IParams): Promise<IReturn> => {
        return request.get('/api/mock/manage/apiManage/main/api/program/list', params);
    };
}

