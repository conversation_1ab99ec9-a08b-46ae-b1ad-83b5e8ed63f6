/*
 * @Description: 环境配置相关接口
 */
import { request } from '@libs/utils';

/**
 * 环境配置相关接口
 */
export namespace nsMockManageApiManageMainApiProgramSettingsEnv {
    /**
     * GET请求参数 - 获取环境详情
     */
    export interface IGETParams {
        id: number;       // 环境id
        programId: number; // 项目id
    }

    /**
     * POST请求参数 - 新建环境
     */
    export interface IPOSTParams {
        programId: number;
        name: string;
        remark: string;
        globalConfigDto: IGlobalConfigDto;
        httpConfigDto: IHttpConfigDto;
    }

    /**
     * PUT请求参数 - 更新环境
     */
    export interface IPUTParams {
        id: number;
        programId: number;
        name: string;
        remark: string;
        globalConfigDto: IGlobalConfigDto;
        httpConfigDto: IHttpConfigDto;
    }

    /**
     * DELETE请求参数 - 删除环境
     */
    export interface IDELETEParams {
        programId: number;
        envId: number;
    }

    export interface IVariable {
        N: string;  // 变量名称
        V: string;  // 变量值
        LV: string; // 变量本地值
        D: string;  // 变量描述
        key?: string; // 唯一标识
    }

    export interface IApiParam {
        N: string;   // 名称
        T: number;   // 类型
        R: number;   // 是否必填
        D: string;   // 描述
        V: string;   // 值
        ACL?: any[];  // 权限控制列表
        LV?: string;  // 本地值
        children?: IApiParam[];
    }

    export interface IGlobalConfigDto {
        variable: IVariable[];
    }

    export interface IHttpConfigDto {
        host: string;
        cookieDomain: string;
        header: IApiParam[];
        query: IApiParam[];
        rest: IApiParam[];
        body: IApiParam[];
        bodyType: string;
        json5Body: string;
        bodyJsonViewType?: string;
    }

    export interface ICreator {
        id: number;
        name: string;
        username: string;
        email: string;
        photo: string;
    }

    /**
     * GET/POST返回值 - 获取环境详情/新建环境
     */
    export interface IDetailReturn {
        id: number;
        name: string;
        groupId: number;
        remark: string;
        createTime: number;
        createUser: string;
        updateTime: number;
        updateUser: string;
        globalConfig: string;
        httpConfig: string;
        creator: ICreator;
        globalConfigDto: IGlobalConfigDto;
        httpConfigDto: IHttpConfigDto;
    }

    /**
     * DELETE返回值 - 删除环境
     */
    export interface IDeleteReturn {
        id: number;
        name: string;
        remark: string;
        creator: ICreator;
    }

    /**
     * GET - 获取环境详情
     */
    export const getEnv = async (params: IGETParams): Promise<IDetailReturn> => {
        return request.get('/api/mock/manage/apiManage/main/api/program/settings/env', params)
            .then((result: any) => result || {});
    };

    /**
     * POST - 新建环境
     */
    export const createEnv = async (params: IPOSTParams): Promise<IDetailReturn> => {
        return request.post('/api/mock/manage/apiManage/main/api/program/settings/env', params)
            .then((result: any) => result || {});
    };

    /**
     * PUT - 更新环境
     */
    export const updateEnv = async (params: IPUTParams): Promise<any> => {
        return request.put('/api/mock/manage/apiManage/main/api/program/settings/env', params)
            .then((result: any) => result || {});
    };

    /**
     * DELETE - 删除环境
     */
    export const deleteEnv = async (params: IDELETEParams): Promise<IDeleteReturn> => {
        return request.delete('/api/mock/manage/apiManage/main/api/program/settings/env', params)
            .then((result: any) => result || {});
    };
} 