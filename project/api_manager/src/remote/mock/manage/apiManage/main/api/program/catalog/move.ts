import { request } from '@libs/utils';

export namespace nsMockManageApiManageMainApiProgramCatalogMove {
    export interface IParams {
        /** 目录ID */
        catalogId: number;
        /** 目标父级目录ID */
        targetParentId: number;
    }

    export interface IReturn {
        status: number;
        message: string;
        data: any;
    }

    export const remote = async (params: IParams): Promise<IReturn> => {
        try {
            return await request.post('/api/mock/manage/apiManage/main/api/program/catalog/move', params);
        } catch (error) {
            // 可以根据需要自定义错误处理
            return { status: 500, message: '请求失败', data: null };
        }
    };
} 