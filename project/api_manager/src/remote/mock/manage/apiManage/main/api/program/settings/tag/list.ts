import { request } from '@libs/utils';


export namespace nsMockManageApiManageMainApiProgramSettingsTagList {
    export interface IUpdater {
        id: number;
        name: string;
        username: string;
        email: string;
        photo: string;
    }

    export interface ITagItem {
        id: number;
        name: string;
        desc: string;
        color: string;
        groupId: number;
        isDelete: number;
        createTime: number;
        createBy: string;
        updateTime: number;
        updateBy: string;
        updater: IUpdater;
    }

    export interface IGetProgramTagListParams {
        programId: number;
    }


    export async function getProgramTagList(params: IGetProgramTagListParams): Promise<ITagItem[]> {
        return request.get('/api/mock/manage/apiManage/main/api/program/settings/tag/list', params);
    }
}
