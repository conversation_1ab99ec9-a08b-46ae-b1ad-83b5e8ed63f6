import { request } from '@libs/utils';

export namespace nsMockManageApiManageMainApiProgramUnpin {
    export interface Request {
        id: number;
    }


    export function remote(params: Request): Promise<null> {
        return request.put(`/api/mock/manage/apiManage/main/api/program/unpin?id=${params.id}`, { ...params })
            .then((result: any) => {
                return result || {};
            });
    }
}
