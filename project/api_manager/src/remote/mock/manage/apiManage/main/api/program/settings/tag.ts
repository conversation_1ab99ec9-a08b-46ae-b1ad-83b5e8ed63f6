import { request } from '@libs/utils';

export namespace nsMockManageApiManageMainApiProgramSettingsTag {
    // 获取标签详情的请求参数类型
    export interface GetTagDetailParams {
        id: number;
        programId: number;
    }

    // 获取标签详情的响应类型
    export interface TagDetail {
        id: number;
        name: string;
        desc: string;
        color: string;
        groupId: number;
        isDelete: number;
        createTime: number;
        createBy: string;
        updateTime: number;
        updateBy: string;
        updater: {
            id: number;
            name: string;
            username: string;
            email: string;
            photo: string;
        };
    }

    // 创建标签的请求参数类型
    export interface CreateTagParams {
        programId: number;
        name: string;
        desc: string;
        color: string;
    }

    // 更新标签的请求参数类型
    export interface UpdateTagParams {
        id: number;
        programId: number;
        name: string;
        desc: string;
        color: string;
    }

    // 删除标签的请求参数类型
    export interface DeleteTagParams {
        tagId: number;
        programId: number;
    }

    // 通用响应类型
    export interface ApiResponse<T> {
        status: number;
        message: string;
        data: T;
        traceId: string;
    }

    // 获取标签详情
    export const getTagDetail = async (params: GetTagDetailParams): Promise<TagDetail> => {
        return request.get('/api/mock/manage/apiManage/main/api/program/settings/tag', params)
    }

    // 创建标签
    export const createTag = async (data: CreateTagParams): Promise<TagDetail> => {
        return request.post('/api/mock/manage/apiManage/main/api/program/settings/tag', data);
    }

    // 更新标签
    export const updateTag = async (data: UpdateTagParams): Promise<null> => {
        return request.put('/api/mock/manage/apiManage/main/api/program/settings/tag', data);
    }

    // 删除标签
    export const deleteTag = async (data: DeleteTagParams): Promise<TagDetail> => {
        return request.delete('/api/mock/manage/apiManage/main/api/program/settings/tag', data);
    }
} 