import { request } from '@libs/utils';
import { nsMockManageApiManageMainApiFollowPOST, nsMockManageApiManageMainCollectListGET } from '@/remote';

export namespace nsApiManagerSearch {
    export interface IParams {
        key: string;
        currentGroupId: number;
    }

    export enum EEditable {
        uneditable, // 不可编辑、删除
        editable // 可编辑、删除
    }

    export interface IItem {
        groupId: number;
        groupName: string;
        apiId: number;
        apiName: string;
        apiSchema: string;
        apiUrl: string;
        type: nsMockManageApiManageMainCollectListGET.ENodeType;
        diffWithKtrace?: boolean;
        isLeaf: boolean;
        parentId: number;
        editable: EEditable;
        follow: nsMockManageApiManageMainApiFollowPOST.EFollow;
        children?: Array<IItem>;
        priority?: string;
        hasChild?: boolean;
    }

    export interface IReturn {
        list: Array<IItem>;
    }

    export const remote = async (params: IParams): Promise<IReturn> => {
        const res = await request.get('/api/mock/manage/apiManage/main/group/search', params);
        return res as IReturn;
        // return request.get('/api/mock/manage/apiManage/main/group/search', params)
        //     .then((result: any) => result || {});
    };
}
