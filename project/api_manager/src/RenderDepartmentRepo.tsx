import React from 'react';
import { Input, Popover, Tooltip } from 'antd';
import { Bind } from 'lodash-decorators';
import { Debounce } from 'lodash-decorators/debounce';
import {
    departmentComponent,
    departmentCascader,
} from '@/business/global';
import {
    nsApiManagerList,
    nsMockManageApiManageMainGroupDepartmentSearch,
    nsApiManagerSearch,
    nsApiManagerGroupTree,
    nsMockManageApiManageMainGroupUserDepartmentInfo
} from '@/remote';
import { isShowSubMenu } from './routerConfig';
import css from './app.less';
import { common_system_right_arrow_surface, common_system_search, common_system_xiaojiantouyou } from '@kid/enterprise-icon/icon/output/icons';
import { KdevIconFont, KEmpty } from '@/business/commonComponents';
import { RepoAvatar } from './business/repo/RepoAvatar';
import { pushKey } from './index.config/tools';
import { KDEV_API_GROUP_REPO, KDEV_API_MENU_SELECTEDKEYS, KDEV_API_SEARCH_GROUP } from './pages/ApiManager/apiManager.config';
import { RepoMenuHeaderCard } from './RepoMenuHeaderCard';
import { getUrlSearch } from '@/index.config/tools';
// 判断两个元素是否相交
function getRelativePos(oneNode: Element, twoNode?: Element) {
    if (!twoNode) return 0;
    const oneNodeRect = oneNode.getBoundingClientRect();
    const twoNodeRect = twoNode.getBoundingClientRect();
    return twoNodeRect.top - oneNodeRect.top
}
interface IRenderDepartmentProps {
    // 可选的初始化参数
    initialFirstDepartmentGroupId?: number;
    initialSearch?: boolean;
    // 回调函数
    onGroupChange?: (groupInfo: { userGroup?: nsApiManagerSearch.IItem, selectedGroup: number, selectedSubGroup: number }) => void;
}
interface IRenderDepartmentState {
    groupList: nsApiManagerSearch.IItem[];
    selectedGroup: number;
    hoverGroup: number;
    selectedSubGroup: number;
    userGroup?: nsApiManagerSearch.IItem;
    popoverOpen: boolean;
    isSearch: boolean;
    searchKey: string;
    searchList: nsMockManageApiManageMainGroupDepartmentSearch.IItem[];
    search: boolean;
    firstDepartmentGroupId: number;
}
export class RenderDepartmentRepo extends React.Component<IRenderDepartmentProps, IRenderDepartmentState> {
    private groupListRef = React.createRef<HTMLDivElement>();
    private subGroupListRef = React.createRef<HTMLDivElement>();
    private hoverTimer: NodeJS.Timeout | null = null;
    public state: IRenderDepartmentState = {
        groupList: [],
        selectedGroup: 0,
        hoverGroup: 0,
        selectedSubGroup: 0,
        userGroup: undefined,
        popoverOpen: false,
        isSearch: false,
        searchKey: '',
        searchList: [],
        search: false,
        firstDepartmentGroupId: 0,
    };
    constructor(props: IRenderDepartmentProps) {
        super(props);
        const url = getUrlSearch() as { search: string };
        window.search = Boolean(url.search);
    }
    async componentDidMount(): Promise<void> {
        await this.getGroup();
        this.getSubGroup();
        document.addEventListener('addGroup', this.addGroupCb);
        const url = getUrlSearch() as { hideNavMenu: string, firstDepartmentGroupId: string, search: string };
        this.setState({
            firstDepartmentGroupId: Number(url.firstDepartmentGroupId) || this.props.initialFirstDepartmentGroupId || 0,
            search: Boolean(url.search) || this.props.initialSearch || false
        })
        if (this.state.firstDepartmentGroupId) {
            const item = this.state.groupList.find(i => i.groupId === this.state.firstDepartmentGroupId);
            if (item) {
                this.setState({
                    selectedGroup: item.groupId,
                    selectedSubGroup: 0,
                    userGroup: item
                }, () => {
                    this.getSubGroup()
                });
                if (this.state.search) {
                    localStorage.setItem(KDEV_API_SEARCH_GROUP, JSON.stringify(item));
                } else {
                    localStorage.setItem(KDEV_API_GROUP_REPO, JSON.stringify(item));
                }
                document.dispatchEvent(new CustomEvent('selectedGroup'))
            }
        }
        if (url.search) {
            window.search = true;
        }
    }
    componentWillUnmount(): void {
        if (this.hoverTimer) {
            clearTimeout(this.hoverTimer);
            this.hoverTimer = null;
        }
        document.removeEventListener('addGroup', this.addGroupCb);
    }
    @Bind
    private async addGroupCb(info: any): Promise<void> {
        info.detail.ancestorsId.children = [];
        const addGroup = info.detail.ancestorsId;
        this.setState({
            userGroup: addGroup,
            selectedGroup: addGroup.groupId,
        }, () => {
            pushKey({ groupId: info.detail.parentGroupId });
            localStorage.setItem(KDEV_API_GROUP_REPO, JSON.stringify(addGroup));
            document.dispatchEvent(new CustomEvent('selectedGroup', {
                // detail: {
                //     groupId: info.detail.parentGroupId
                // }
            }))
            this.getSubGroup();
        });
    }
    // 获取一级分组（加载时执行一次）
    @Bind
    private async getGroup(): Promise<void> {
        try {
            // 获取上次选择的分组
            const userLastGroupStr = localStorage.getItem(KDEV_API_GROUP_REPO) || '';
            let userLastGroup: nsApiManagerSearch.IItem
            // 没有上次的分组就获取用户所在分组
            if (!userLastGroupStr) {
                const res = await nsMockManageApiManageMainGroupUserDepartmentInfo.remote({});
                res.secondDepartmentGroup.groupName =
                    res.firstDepartmentGroup.groupName + '/' + res.secondDepartmentGroup.groupName
                userLastGroup = res.secondDepartmentGroup
            } else {
                userLastGroup = JSON.parse(userLastGroupStr);
            }
            if (userLastGroup.parentId === -1) { // 如果是一级目录
                this.setState({
                    selectedGroup: userLastGroup.groupId,
                    userGroup: userLastGroup
                });
            } else if (userLastGroup.parentId > 0) { // 如果是二级目录
                this.setState({
                    selectedGroup: userLastGroup.parentId,
                    selectedSubGroup: userLastGroup.groupId,
                    userGroup: userLastGroup
                });
            }
            // 获取全部一级分组
            const result = await nsApiManagerGroupTree.remote({});
            result.list.forEach((i: any) => { i.key = '' + i.groupId })
            this.setState({
                groupList: result.list,
            }, () => {
                localStorage.setItem(KDEV_API_GROUP_REPO, JSON.stringify(this.state.userGroup));
                // 通知父组件 group 变化
                this.props.onGroupChange?.({
                    userGroup: this.state.userGroup,
                    selectedGroup: this.state.selectedGroup,
                    selectedSubGroup: this.state.selectedSubGroup
                });
            });
        } catch { }
    }
    // 获取二级分组（点击一级分组后执行一次）
    @Bind
    private async getSubGroup(): Promise<void> {
        try {
            const groupId = this.state.hoverGroup || this.state.selectedGroup;
            const { item } = await nsApiManagerList.remote({
                groupId
            });
            item.children = item.children?.filter((i: any) => i.groupId);
            const index = this.state.groupList.findIndex((i: any) => i.groupId === groupId);
            const newGroupList = [...this.state.groupList];
            newGroupList[index] = item;
            this.setState({
                groupList: newGroupList,
            });
        } catch { }
    }

    private getCurrentSelectedGroup() {
        const groupId = this.state.hoverGroup;
        return this.state.groupList.find(
            item => item.groupId === groupId
        )
    }
    @Debounce(200)
    private async onSearch() {
        this.setState({
            isSearch: !!this.state.searchKey.trim()
        })
        const searchRes = await nsMockManageApiManageMainGroupDepartmentSearch.remote(
            { key: this.state.searchKey }
        );
        this.setState({
            searchList: searchRes
        })
    }
    renderPopoverContent() {
        return (
            <div className={css.bizSelectPopover}>
                <Input.Search
                    allowClear
                    className={[css.bizSelectSearch,
                    this.state.isSearch ? css.isSearch : ''].join(' ')}
                    placeholder={'搜索空间关键词'}
                    prefix={
                        <KdevIconFont
                            id={common_system_search}
                            style={{ color: '#D5D6D9' }}
                        />
                    }
                    value={this.state.searchKey}
                    onChange={async (event) => {
                        this.setState({
                            searchKey: event.target.value,
                            isSearch: !!event.target.value.trim()
                        });
                        this.onSearch();
                    }}
                />
                {
                    this.state.isSearch ?
                        <div className={css.searchList}>
                            {this.state.searchList.length ?
                                this.state.searchList.map((item, index) => (
                                    <p
                                        className={css.searchItem}
                                        key={index}
                                        dangerouslySetInnerHTML={{
                                            __html: item.groupPath.replace(
                                                new RegExp(this.state.searchKey.trim(), 'g'),
                                                (v) => `<span style="color: #326BFB">${v}</span>`
                                            )
                                        }}
                                        onClick={() => {
                                            const userGroup = item.secondDepartmentGroup ?
                                                {
                                                    ...item.secondDepartmentGroup,
                                                    groupName: `${item.firstDepartmentGroup.groupName}/${item.secondDepartmentGroup.groupName}`
                                                } : item.firstDepartmentGroup
                                            this.setState({
                                                selectedSubGroup: item.secondDepartmentGroup?.groupId || 0,
                                                selectedGroup: item.firstDepartmentGroup.groupId,
                                                popoverOpen: false,
                                                isSearch: false,
                                                searchKey: '',
                                                userGroup: userGroup
                                            });
                                            localStorage.setItem(KDEV_API_GROUP_REPO, JSON.stringify(
                                                userGroup
                                            ));
                                            document.dispatchEvent(new CustomEvent('selectedGroup'));
                                            // 通知父组件 group 变化
                                            this.props.onGroupChange?.({
                                                userGroup: userGroup,
                                                selectedGroup: item.firstDepartmentGroup.groupId,
                                                selectedSubGroup: item.secondDepartmentGroup?.groupId || 0
                                            });
                                        }}
                                    />
                                )) :
                                <KEmpty></KEmpty>}
                        </div>
                        :
                        <div className={css.bizOptionsBox}>
                            <div className={[css.left, 'scroll-hover'].join(' ')} ref={this.groupListRef}>
                                {
                                    this.state.groupList.map((item, index) => {
                                        return <div
                                            key={index}
                                            className={[
                                                css.group1,
                                                item.groupId === this.state.selectedGroup ? css.selected : ''
                                            ].join(' ')}
                                            onClick={async () => {
                                                this.setState({
                                                    selectedGroup: item.groupId,
                                                    selectedSubGroup: 0,
                                                    userGroup: item,
                                                    popoverOpen: false
                                                });
                                                pushKey({ groupId: '', apiId: '' })
                                                if (window.search) {
                                                    localStorage.setItem(KDEV_API_SEARCH_GROUP, JSON.stringify(item));
                                                } else {
                                                    localStorage.setItem(KDEV_API_GROUP_REPO, JSON.stringify(item));
                                                }
                                                localStorage.removeItem(KDEV_API_MENU_SELECTEDKEYS);
                                                document.dispatchEvent(new CustomEvent('selectedGroup'));
                                                // 通知父组件 group 变化
                                                this.props.onGroupChange?.({
                                                    userGroup: item,
                                                    selectedGroup: item.groupId,
                                                    selectedSubGroup: 0
                                                });
                                            }}
                                            onMouseEnter={() => {
                                                this.hoverTimer = setTimeout(() => {
                                                    this.setState({
                                                        hoverGroup: item.groupId,
                                                    });
                                                    this.getSubGroup()
                                                }, 500);
                                            }}
                                            onMouseLeave={() => {
                                                if (this.hoverTimer) {
                                                    clearTimeout(this.hoverTimer);
                                                    this.hoverTimer = null;
                                                }
                                            }}
                                        >
                                            <RepoAvatar
                                                name={item.groupName}
                                                className={css.avatar}
                                                size={24}
                                            />
                                            <Tooltip title={item.groupName} mouseEnterDelay={0.3}>
                                                <div className={css.groupName}>
                                                    {item.groupName}
                                                </div>
                                            </Tooltip>
                                            <KdevIconFont
                                                className={css.rightArrow}
                                                id={common_system_xiaojiantouyou}
                                                style={{ color: '#D5D6D9' }}
                                            />
                                        </div>
                                    })
                                }
                            </div>
                            <div className={css.line}></div>
                            <div className={[css.right, 'scroll-hover'].join(' ')} ref={this.subGroupListRef}>
                                {
                                    this.getCurrentSelectedGroup()?.children?.map((item, index) => (
                                        <div
                                            key={index}
                                            className={[
                                                css.group2,
                                                item.groupId === this.state.selectedSubGroup ? css.selected : ''
                                            ].join(' ')}
                                            onClick={async () => {
                                                const subGroup = {
                                                    ...item,
                                                    groupName: `${this.getCurrentSelectedGroup()?.groupName}/${item.groupName}`
                                                }
                                                this.setState({
                                                    selectedSubGroup: item.groupId,
                                                    userGroup: subGroup,
                                                    popoverOpen: false,
                                                    selectedGroup: item.parentId
                                                });
                                                pushKey({ groupId: '', apiId: '' })
                                                localStorage.setItem(KDEV_API_GROUP_REPO, JSON.stringify(subGroup));
                                                localStorage.removeItem(KDEV_API_MENU_SELECTEDKEYS);
                                                document.dispatchEvent(new CustomEvent('selectedGroup'));
                                                // 通知父组件 group 变化
                                                this.props.onGroupChange?.({
                                                    userGroup: subGroup,
                                                    selectedGroup: this.state.selectedGroup,
                                                    selectedSubGroup: item.groupId
                                                });
                                            }}
                                        >
                                            <Tooltip title={item.groupName} mouseEnterDelay={0.3}>
                                                <div className={css.groupName}>
                                                    {item.groupName}
                                                </div>
                                            </Tooltip>
                                        </div>
                                    ))
                                }
                            </div>
                        </div>
                }
            </div>
        );
    }
    @Bind
    render() {
        if (
            departmentComponent.departmentComponentModel.hidden
            && departmentCascader.departmentCascaderModel.hidden
        ) {
            return <Popover
                content={this.renderPopoverContent()}
                placement="bottomLeft"
                title={null}
                trigger="click"
                overlayClassName={css.menuOverlay}
                destroyTooltipOnHide
                open={this.state.popoverOpen}
                onOpenChange={(newOpen) => {
                    this.setState({
                        popoverOpen: newOpen,
                    });
                    if (newOpen) {
                        setTimeout(() => {
                            const selected = this.groupListRef.current?.querySelector('.' + css.selected);
                            this.groupListRef.current?.scrollTo({
                                top: getRelativePos(this.groupListRef.current, selected!)
                            } as any);

                            const subSelected = this.subGroupListRef.current?.querySelector('.' + css.selected)
                            this.subGroupListRef.current?.scrollTo({
                                top: getRelativePos(this.subGroupListRef.current, subSelected!)
                            } as any);
                        }, 0)
                    }
                    if (!newOpen) {
                        this.setState({
                            isSearch: false,
                            searchKey: ''
                        })
                    }
                }}
            >
                {
                    isShowSubMenu() ? <>
                        <div className={css.backProjectList}>
                            <KdevIconFont id={common_system_right_arrow_surface} style={{
                                marginRight: '4px',
                                transform: 'rotate(180deg)'
                            }} />
                            <span>返回项目列表</span>
                        </div>
                    </> : <></>
                }
                <>
                    <RepoMenuHeaderCard
                        popoverOpen={this.state.popoverOpen}
                        imageUrl={''}
                        name={this.state.userGroup?.groupName}
                        // name={ this.state.departmentPath || this.state.userGroup?.groupName }
                        toggleIcon={true}
                    />
                </>
            </Popover>
        }
        return (
            <div className={css.globalGroupV2}>
                {!departmentComponent.departmentComponentModel.hidden && (
                    <span>{departmentComponent.renderSelect()}</span>
                )}
                {!departmentCascader.departmentCascaderModel.hidden && (
                    <span>{departmentCascader.renderSelect()}</span>
                )}
            </div>
        );
    }
}