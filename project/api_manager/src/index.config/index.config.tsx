/**
 * App渲染节点、一些全局开关、登录拦截、路由拦截 等业务逻辑
 */
import { ERouter, isNewApiRouter } from 'CONFIG';// isOldApiRouter
import { router } from '@libs/mvvm';
import { Action, Location } from 'history';
import { AsyncLoader, AsyncMessage } from '@libs/a-component';
import { Route } from 'react-router';
import React from 'react';
import { configure } from 'mobx';
import { departmentComponent, departmentCascader } from '@/business/global';
import { initRequest } from './index.config.request';
import { setDefaultCollapsed } from '@libs/a-component/route/menus/sync/Menus';
import { initHaloNps } from './haloNps';
import { kconfStore, radar } from '@/index.config';
// import { fmp, getFmpRouterKeys } from 'kdev-fe-common/src/shared/common/fmpReport';
import { setOpenKeys as setOpenKeysV2 } from 'kdev-fe-common/src/component/KDevPageLayout/KDevPageLayout';
import { storeGrayFeatures } from 'kdev-fe-common/src/shared/store';
// import { ModuleQuery } from './index';
import { initConnectionMonitor } from '@kdev-fe-common/common/networkCheck/init';
import { initErrorHandle } from './index.config.errorHandle';
import { TrackReportRoot } from '@kdev-fe-common/common/trackReport';
import { UIwiseToolbar } from '@ks-dmo/uiwise-toolbar-react';

initRequest();
initHaloNps();
initErrorHandle();
storeGrayFeatures.init();
initConnectionMonitor();

/**
 * enforceActions: don't allow state modifications outside actions
 * 也被称为“严格模式”。
 * 在严格模式下，不允许在 action 外更改任何状态。 可接收的值:
 * "never" (默认): 可以在任意地方修改状态
 * "observed": 在某处观察到的所有状态都需要通过动作进行更改。在正式应用中推荐此严格模式。
 * "always": 状态始终需要通过动作来更新(实际上还包括创建)。
 *
 * * * 当使用 antd 时，为了和原始组件能良好的运行，不要使用 "always"
 * * * 同时，使用 "always" 却没有配套 严格测试 时，会引入 隐性 bug
 */
configure({ enforceActions: 'observed' });

AsyncMessage.config({
    top: 32,
    duration: 2,
    maxCount: 3,
});

// 顶部内容隐藏
function DepartmentHidden() {
    departmentComponent.departmentComponentModel.setFields({
        hidden: !(location.pathname === ERouter.API_MOCK_ORGANIZESPACE_DEPARTMENTMGR)
    });
    departmentCascader.departmentCascaderModel.setFields({
        hidden: !(location.pathname === ERouter.API_MOCK_ORGANIZESPACE_PROJECTMGR ||
            location.pathname === ERouter.API_MOCK_ORGANIZESPACE_SCENEMGR)
    });
}

// 菜单栏默认收起
function defaultCollapsed() {
    setDefaultCollapsed(() => {
        return location.pathname === ERouter.API_MOCK_REPO_MGR
            || location.pathname === ERouter.API_MOCK_MGR_REPO
            || location.pathname === ERouter.API_MOCK_REPO_EDIT_API;
    });
}

/**
 * 路由拦截
 */
function listenRouter() {
    router.routerProps.history.listen((location: Location, action: Action) => {
        /**
         * 此处可监听路由的切换
         */
        DepartmentHidden();

        // 新版导航，会重定向到首页，这里需要再一次重定向
        if (location.pathname === ERouter.API_MGR_ROOT) {
            kconfStore.redirectApiRouter();
        }
    });
    // 只用新版
    kconfStore.setVersion('new');

    // // 新旧版切换
    // if (isNewApiRouter(location.pathname)) {
    //     kconfStore.setVersion('new');
    // } else if (isOldApiRouter(location.pathname)) {
    //     kconfStore.setVersion('old');
    // }
    // 顶部内容隐藏
    DepartmentHidden();
    // 默认收起
    defaultCollapsed();
    setOpenKeysV2([
        ERouter.API_MOCK_AUTOMATICPARSINGAPI,
        ERouter.API_MOCK_REPO_MGR,
        ERouter.API_MOCK_API_VIEW,
        ERouter.API_MOCK_HTTP_PROXY,
        ERouter.API_MOCK_HTTP_PROXY_RULE,
        ERouter.API_MGR_ROOT,
        ERouter.API_MOCK_HTTP_PROXY + '-'
    ])
}

const prom: any = () => import('@/app')

class MainNode extends React.Component<any, any> {
    public componentDidMount(): void {
        // checkLogin();
        listenRouter();
    }

    public render(): React.ReactNode {
        // if () {
        //     return <Route
        //         path={ERouter.API_MGR_ROOT}
        //         render={
        //             () => <AsyncLoader
        //                 compoLazy={prom}
        //                 fallback={<div />}
        //             />
        //         }
        //     />
        // } else {
        return (
            <>
                <UIwiseToolbar />
                <TrackReportRoot>
                    <Route
                        path={ERouter.API_MGR_ROOT}
                        render={
                            () => <AsyncLoader
                                compoLazy={prom}
                                fallback={<div />}
                            />
                        }
                    />
                </TrackReportRoot>
            </>
        );
        // }
    }
}

const AppConfig = {
    /**
     * 渲染 App 的主节点
     */
    renderMainNode(): React.ReactNode {
        return <MainNode />;
    },
};

export default AppConfig;
