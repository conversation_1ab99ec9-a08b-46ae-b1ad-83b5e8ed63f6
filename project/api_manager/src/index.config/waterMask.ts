import { loadScript } from 'libs/utils';

// tslint:disable:no-conditional-assignment
function getExplore() {
    const Sys: IPlainObject = {};
    // 用于测试 ios 15 在 kim 上的 ua（yoda）
    // const uaSrc = 'Mozilla/5.0 (iPhone; CPU iPhone OS 15_0_2 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148 NetType/WIFI language/zh WebViewType/WK Kim/2.7.20923 Yoda/******* TitleHT/44 StatusHT/47 BHT/306 TBHT/423';
    const uaSrc = navigator.userAgent;
    const ua = uaSrc.toLowerCase();
    let s;
    (s = ua.match(/rv:([\d.]+)\) like gecko/)) ? Sys.ie = s[1] :
        (s = ua.match(/msie ([\d\.]+)/)) ? Sys.ie = s[1] :
            (s = ua.match(/edge\/([\d\.]+)/)) ? Sys.edge = s[1] :
                (s = ua.match(/firefox\/([\d\.]+)/)) ? Sys.firefox = s[1] :
                    (s = ua.match(/(?:opera|opr).([\d\.]+)/)) ? Sys.opera = s[1] :
                        (s = ua.match(/chrome\/([\d\.]+)/)) ? Sys.chrome = s[1] :
                            (s = ua.match(/version\/([\d\.]+).*safari/)) ? Sys.safari = s[1] :
                                (s = ua.match(/iphone os ([\d_]+).*like mac/)) ? Sys.safari = s[1] : 0;
    return Sys;
    // // 根据关系进行判断
    // if (Sys.ie) return ('IE: ' + Sys.ie);
    // if (Sys.edge) return ('EDGE: ' + Sys.edge);
    // if (Sys.firefox) return ('Firefox: ' + Sys.firefox);
    // if (Sys.chrome) return ('Chrome: ' + Sys.chrome);
    // if (Sys.opera) return ('Opera: ' + Sys.opera);
    // if (Sys.safari) return ('Safari: ' + Sys.safari);
    // return 'Unkonwn';
}

declare var KSWatermark: any;

class WaterMask {
    private WATER_MASK_DOM = document.createElement('div');
    private isInit = false;

    /**
     * 初始化，仅能执行一次
     * @param userName
     * @param chineseName
     */
    public init(userName, chineseName) {
        if (this.isInit) {
            return;
        }
        this.isInit = true;
        // 水印
        const div = this.WATER_MASK_DOM;
        div.style.position = 'fixed';
        Object.assign(div.style, {
            position: 'fixed',
            top: 0,
            bottom: 0,
            left: 0,
            right: 0,
            zIndex: 1000,
            opacity: 0.4,
            pointerEvents: 'none',
        });
        document.body.appendChild(div);
        const explore = getExplore();
        if (explore.safari && Number(explore.safari.split(/\.|_/)[0]) >= 15) {
            // safari 15 版本会出现 水印 导致浏览器崩溃的情况
            console.info('safari 15 及以上：不加载水印');
            return;
        }
        setTimeout(() => {
            this.loadWM(div, userName, chineseName);
        }, 3000);
    }

    private async loadWM(div, userName, chineseName) {
        await loadScript('https://cdnfile.corp.kuaishou.com/kc/files/a/QA-SERVEREE-FE-IN/fe/common' +
            '/ks-watermark/ks-watermark-1.0.3.cdn.es5.min.js');
        // const KSWatermark = tm.default; const tm = imp//ort('@ks/ks-watermark');
        new KSWatermark({
            container: div,
            zIndex: 1000,
            width: '200px',
            height: '200px',
            content: [chineseName + ' ' + userName],
            font: '12px "helvetica neue", -apple-system, BlinkMacSystemFont,"Microsoft YaHei", Arial,sans-serif',
            fillStyle: 'rgba(192, 192, 192, 0.2)',
            refreshTime: 1000,
            position: 'fixed'
        });
        console.info('水印已加载');
    }

    public hide() {
        this.WATER_MASK_DOM.style.display = 'none';
    }

    public show() {
        this.WATER_MASK_DOM.style.display = 'block';
    }
}

export const waterMask = new WaterMask();
