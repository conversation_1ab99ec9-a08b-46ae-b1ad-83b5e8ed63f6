import {
    nsMockManageApiManageOpenApiIsShowGet,
    nsMockManageApiManageOpenApiIsDepartmentInGrayListGet
} from '@/remote';
import { Bind } from 'lodash-decorators';
import { observable, action, runInAction } from 'mobx';
import { LOCAL_STORAGE_KEYS } from '@/index.config';
import { ERouter } from 'CONFIG';// , isOldApiRouter
import { router } from '@libs/mvvm';
import { ViewTypeMap } from '@/pages/ApiManager/ApiMenuEntry';

type TVersion = 'old' | 'new';

// 在白名单内要重定向的路由
function pathIsRedirect() {
    return location.pathname === ERouter.ROOT
        || location.pathname === ERouter.API_MGR_ROOT
        || location.pathname === ERouter.API_MGR_ROOT + '/';
}

const redirectNewPath = {
    [ERouter.ROOT]: ERouter.API_MOCK_REPO_MGR,
    [ERouter.API_MGR_ROOT]: ERouter.API_MOCK_REPO_MGR,
    [ERouter.API_MGR_ROOT + '/']: ERouter.API_MOCK_REPO_MGR
};

const redirectOldPath = {
    [ERouter.ROOT]: ERouter.API_MOCK_ORGANIZESPACE_PROJECTMGR,
    [ERouter.API_MGR_ROOT]: ERouter.API_MOCK_ORGANIZESPACE_PROJECTMGR,
    [ERouter.API_MGR_ROOT + '/']: ERouter.API_MOCK_ORGANIZESPACE_PROJECTMGR
};

class KconfStore {
    @observable public isLoaded: boolean = false;
    @observable public isShowNewVersion: boolean = true;
    @observable public showToggleVersionBtn: boolean = true;
    private timer?: number;

    constructor() {
        this.initKconfStore();
    }

    @Bind
    private componentWillUnmount() {
        this.timer && clearTimeout(this.timer);
    }

    @Bind
    private async getGrayKconf(): Promise<nsMockManageApiManageOpenApiIsShowGet.IReturn> {
        try {
            const result = await nsMockManageApiManageOpenApiIsShowGet.remote();
            return result;
        } catch {
            return {
                newApiPage: this.isShowNewVersion
            };
        }
    }

    @Bind
    private async isDepartmentInGrayList() {
        try {
            const result = await nsMockManageApiManageOpenApiIsDepartmentInGrayListGet.remote();
            return result;
        } catch {
            return false;
        }
    }

    @action.bound
    public setVersion(version: TVersion, isCache: boolean = false) {
        this.isShowNewVersion = version === 'new' ? true : false;
        isCache && localStorage.setItem(LOCAL_STORAGE_KEYS.API_MGR_IS_NEW_VERSION, version);
    }

    // 根路由重定向
    @Bind
    public redirectApiRouter() {
        const redirectPath = redirectNewPath[location.pathname];
        // this.isShowNewVersion
        //     ? redirectNewPath[location.pathname]
        //     : redirectOldPath[location.pathname];
        this.timer = setTimeout(() => {
            router.push(redirectPath);
        });
    }

    @action.bound
    public async initKconfStore() {
        /**
         * 进入老版API页面：判断是否推全新版API，推全则展示新版
         * 进入根路由：是否存在缓存，存在则按缓存跳转到对应的新旧版；否则判断是否在灰度用户中，根据灰度确定跳转到对应的新旧版
         * 其余情况：不进行重定向跳转
         */
        if (pathIsRedirect()) {// isOldApiRouter(location.pathname) ||
            const result = await this.isDepartmentInGrayList();
            if (result) {
                runInAction(() => {
                    this.isShowNewVersion = true;
                    this.isLoaded = true;
                    this.showToggleVersionBtn = false;
                    const lastVisitType = localStorage.getItem('apiManagerViewType');
                    if (lastVisitType === ViewTypeMap.PROJECT_VIEW) {
                        router.push(ERouter.API_MOCK_REPO_MGR_PROJECT);
                    } else {
                        router.push(ERouter.API_MOCK_REPO_MGR);
                    }
                });
                return;
            }
            if (pathIsRedirect()) {
                const newVersionStr = localStorage.getItem(LOCAL_STORAGE_KEYS.API_MGR_IS_NEW_VERSION);
                runInAction(() => {
                    this.isShowNewVersion = newVersionStr === 'new' ? true : false;
                    this.showToggleVersionBtn = newVersionStr === 'new' ? false : true;
                    this.isLoaded = true;
                });
                this.redirectApiRouter();
            } else {
                runInAction(() => this.isLoaded = true);
            }
        } else {
            runInAction(() => this.isLoaded = true);
        }
    }
}

export const kconfStore = new KconfStore();
