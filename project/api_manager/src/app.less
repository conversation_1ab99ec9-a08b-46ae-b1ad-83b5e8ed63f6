@import './business/commonFont.less';
@import '~@/antdCss/index.less';

.noticeMsg {
  text-align: center;
  background: linear-gradient(to right, #dbab74, #c76150);
  border: 0;
  border-radius: 0 !important;

  >span {
    color: #fff;
  }

  :global {
    .ant-alert-close-icon {
      top: 15px !important;

      .anticon-close {
        color: #fff;
        font-size: 16px;
      }
    }
  }
}

.head {
  color: #fff;
  padding: 0;
  color: rgba(255, 255, 255, 0.8);
  background: #272961;
  height: 48px;
  position: relative;

  :global {
    .ToolsMenueMainIconBox:hover {
      background-color: rgba(209, 209, 240, 0.2) !important;
      transition: cubic-bezier(0.77, 0, 0.175, 0.1);
      border-radius: 4px;
    }

    .ToolsMenueMainIcon {
      color: #fff !important;
    }
  }

  .headContent {
    display: flex;
    padding: 0 24px;
    line-height: 48px;

    .titleBox {
      display: flex;
      align-items: center;

      .titleLogo {
        width: 26px;
        //cursor: pointer;
      }

      .title {
        margin-left: 10px;
        font-size: 16px;
        font-family: Helvetica, Helvetica-Bold;
        font-weight: 700;
        color: #fff;
      }
    }

    .globalGroup {
      margin-left: 24px;
      flex: 1;
    }

    .headContentRigth {
      display: flex;
      align-items: center;

      .toggleBtn {
        margin-right: 24px;
      }

      .globalSearchInput {
        margin-right: 18px;
      }

      .kimOnCall {
        margin-left: 18px;
      }
    }
  }

}

.content {
  position: relative;
  min-height: 350px;

  .defaultEmpty {
    position: relative;
    top: 50%;
    transform: translateY(-50%);

    :global {
      .ant-empty-description {
        margin-top: 32px;
        opacity: .5;
      }
    }
  }
}

.sider {
  display: flex;
  flex-direction: row;

  :global {
    .ant-layout-sider {
      flex: 0 0 180px !important;
      max-width: 180px !important;
      min-width: 180px !important;
      width: 180px !important;
    }

    .ant-layout-sider-collapsed {
      flex: 0 0 60px !important;
      max-width: 60px !important;
      min-width: 60px !important;
      width: 60px !important;
    }

    .mainLayoutSider,
    .mainLayoutSiderMenue,
    .ant-menu-submenu>.ant-menu {
      background-color: #fafafa;
    }

    .ant-menu-inline .ant-menu-item {
      height: 48px;
      line-height: 48px;
      margin: 0;
    }

    .ant-menu-inline>.ant-menu-submenu>.ant-menu-submenu-title {
      height: 48px;
      line-height: 48px;
      margin: 0;
    }
  }
}

.userInfo {
  position: relative;
  display: flex;
  justify-content: space-around;

  .userIconBG {
    position: absolute;
    display: none;
    width: 21px;
    height: 21px;
    background: rgba(0, 0, 0, .65);
    border-radius: 100%;
  }

  .menu {
    line-height: 58px;
    border: 0;
    background: transparent;
    position: relative;
  }

  .subMenu,
  .subMenu:hover {
    border: 0;
    border-radius: 0;
  }

  :global {
    .anticon {
      margin-right: 3px;
    }

    .ant-menu-vertical.ant-menu-sub {
      min-width: 100px;
      margin-top: -6px;
      box-shadow: 0 3px 5px -2px #ccc;
      border-radius: 0;
      text-align: center;
    }

    .ant-btn>.anticon+span,
    .ant-btn>span+.anticon {
      margin-left: 0;
    }

    .ant-message-custom-content {
      >span:last-child {
        white-space: pre-wrap;
      }
    }
  }
}

:global {
  .ant-message-custom-content {
    >span:last-child {
      white-space: pre-wrap;
    }
  }

  .ant-layout-sider-trigger {
    position: absolute;
  }

  .ant-layout-sider-children {
    overflow: auto;
  }

  * {
    font-family: PingFang SC, PingFang SC-Regular;
  }

  .ace_editor * {
    font: 12px/normal 'Monaco', 'Menlo', 'Ubuntu Mono', 'Consolas', 'source-code-pro', monospace !important;
  }

  .ant-layout {
    overflow-x: hidden;
  }
}

:global {
  .fXAXjb:hover {
    border-left: 5px solid transparent !important;
    border-right: 5px solid transparent !important;
  }
}

// :global {
//   .ant-tooltip-inner {
//     word-break: break-all;
//   }
// }

// 根据 ui 要求重置 antd 样式
:global {
  .ant-drawer-content {
    .ant-drawer-header {
      border-bottom: none;
    }
  }
}

:global(.kdev-page-v2) {
  :global {
    .ant-menu-inline-collapsed .ant-menu-item {
      justify-content: flex-start !important;
    }
  }
}

.globalGroupV2 {
  width: 196px;
  margin-left: 12px;
}

.menuOverlay {
  padding-top: 4px;

  :global {
    .ant-popover-arrow {
      display: none;
    }

    .ant-popover-inner-content {
      padding: 0;
    }
  }

  .bizSelectPopover {
    width: 480px;
    padding: 12px 8px 0;

    :global {
      .ant-input-group-addon {
        display: none;
      }
    }
  }

  .bizSelectSearch {
    width: calc(100% - 16px);
    box-sizing: border-box;
    margin: 0 8px 4px;
    margin-bottom: 4px;
    height: 32px;
    // border-radius: 4px !important;

    :global {
      .ant-input-affix-wrapper {
        border-radius: 4px !important;
      }
    }
  }

  .searchList {
    max-height: 224px;
    overflow-y: scroll;
    padding: 0 8px;

    .searchItem {
      height: 36px;
      display: flex;
      align-items: center;
      margin: 0;
      cursor: pointer;
      padding: 0 8px;
      border-radius: 4px;

      &:hover {
        background: #F5F7FA;
      }
    }
  }

  .bizOptionsBox {
    max-height: 224px;
    display: flex;
    padding: 0 8px;

    .left {
      flex: 1 0 0;
      width: 216px;
      max-height: 224;
      overflow-y: scroll;

      .group1 {
        padding: 6px 0;
        display: flex;
        align-items: center;
        cursor: pointer;
        border-radius: 4px;
        color: #252626;

        &:hover {
          background: #F5F7FA;
        }

        &.selected {
          color: #326BFB;
          background: #F5F7FA;
        }

        .avatar {
          font-size: 12px !important;
          margin: 0 8px;
        }

        .groupName {
          flex: 1 0 0;
          width: 144px;
          font-size: 14px;
        }

        .rightArrow {
          margin-right: 8px;
        }
      }
    }

    .line {
      width: 1px;
      height: 224px;
      background: #F0F2F5;
      margin: 0 8px;
    }

    .right {
      flex: 1 0 0;
      width: 216px;
      max-height: 224;
      overflow-y: scroll;

      .group2 {
        height: 36px;
        display: flex;
        justify-content: flex-start;
        align-items: center;
        cursor: pointer;
        border-radius: 4px;
        padding-left: 8px;
        color: #252626;

        &:hover {
          background: #F5F7FA;
        }

        &.selected {
          background: #F5F7FA;
          color: #326BFB;
        }

        .avatar {
          font-size: 12px !important;
        }

        .groupName {
          font-size: 14px;
          white-space: nowrap;
          /* 不换行 */
          overflow: hidden;
          /* 超出部分隐藏 */
          text-overflow: ellipsis;
          /* 用省略号表示溢出部分 */
        }
      }
    }
  }
}

.backProjectList {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  height: 46px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  margin-bottom: 12px;
  margin-top: -12px;
  color: #898A8Cff;
  padding: 0 20px 0px 20px;
  border-bottom: 1px solid #E5E5E5;
  box-sizing: content-box;

  &:hover {
    opacity: 0.58;
  }
}

.projectName {
  color: var(---text_primary, #252626);
  font-family: "PingFang SC";
  font-size: 16px;
  font-style: normal;
  font-weight: 500;
  line-height: 24px;
  letter-spacing: 0.16px;
  flex: 1;
  min-width: 0;
  /* 这是确保 flex 项目可以收缩到比内容更小的关键 */
}

.app {
  :global {

    // 包含menuHeaderBox的div
    div[class*="menuHeaderBox"]:empty {
      display: none;
    }
  }
}