import React, { useEffect, useState } from 'react';
import { Collapse } from 'antd';
import classNames from 'classnames';
import css from './ApiParamKtrace.less';
import { KdevIconFont } from '@/business/commonComponents';
import { common_system_arrowxia, common_system_jiantouyou, common_system_arrowleft } from '@kid/enterprise-icon/icon/output/icons';

interface IApiParamCollapseProps {
    className?: string;
    header: string | React.ReactNode;
    children?: React.ReactNode;
    defaultActiveKey?: string[];
}

function ApiParamKtrace(props: IApiParamCollapseProps) {
    const [activeKey, setActiveKey] = useState<string[]>(['1']);

    useEffect(() => {
        props.defaultActiveKey && setActiveKey(props.defaultActiveKey);
    }, [props.defaultActiveKey]);

    const traceDiff = location.pathname.includes('traceDiff');

    const renderPanelHeader = () => {
        return (
            <span>
                {props.header}
                {
                    traceDiff ? null :
                        activeKey?.includes('1') ?
                            <KdevIconFont id={common_system_arrowxia} style={{ fontSize: 16, marginLeft: 4 }} />
                            : <KdevIconFont id={common_system_jiantouyou} style={{ fontSize: 16, marginLeft: 4 }} />
                }
            </span>
        );
    };

    return (
        <Collapse
            bordered={false}
            ghost
            className={classNames(css.apiParamCollapse, props.className)}
            activeKey={activeKey}
            onChange={(keys) => setActiveKey(keys as string[])}
            expandIcon={() => null}
            destroyInactivePanel
        >
            <Collapse.Panel className={css.apiParamCollapsePanel} header={renderPanelHeader()} key="1">
                {props.children}
            </Collapse.Panel>
        </Collapse>
    );
}

export { ApiParamKtrace };
